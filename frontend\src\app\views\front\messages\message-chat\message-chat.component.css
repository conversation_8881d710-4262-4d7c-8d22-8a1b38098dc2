/* ===== STYLES OPTIMISÉS POUR LE COMPOSANT MESSAGE-CHAT ===== */

::ng-deep .chat-image-custom {
  width: 200px !important;
  height: auto !important;
  max-height: 300px !important;
  object-fit: cover !important;
  display: block !important;
  border-radius: 12px !important;
  border: none !important;
  border-width: 0 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  background: transparent !important;
  outline: none !important;
  box-sizing: border-box !important;
}

/* Style spécifique pour forcer l'override */
:host ::ng-deep img.chat-image-custom {
  border: none !important;
  border-width: 0px !important;
  border-style: none !important;
  border-color: transparent !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border-radius: 12px !important;
}

/* Animations essentielles */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dragShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse-red {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0.3);
  }
  100% {
    box-shadow: 0 0 0 20px rgba(239, 68, 68, 0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Styles pour les réactions */
.reaction-picker {
  animation: slideUp 0.2s ease-out;
}

/* Styles optimisés pour le drag & drop */
.drag-over {
  background-color: rgba(34, 197, 94, 0.05) !important;
  border: 2px dashed #22c55e !important;
}

.drag-over::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 40%,
    rgba(34, 197, 94, 0.1) 50%,
    transparent 60%
  );
  animation: dragShimmer 2s infinite;
  pointer-events: none;
  z-index: 1;
}

.dark .drag-over {
  background-color: rgba(34, 197, 94, 0.1) !important;
}

.dark .drag-over::before {
  background: linear-gradient(
    45deg,
    transparent 40%,
    rgba(34, 197, 94, 0.2) 50%,
    transparent 60%
  );
}

/* Styles pour les messages */
.message-bubble {
  transition: all 0.2s ease;
}

.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Styles pour les progress bars */
.upload-progress {
  background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
  transition: width 0.3s ease;
}

/* Styles pour les émojis */
.emoji-button {
  transition: transform 0.1s ease;
}

.emoji-button:hover {
  transform: scale(1.2);
}

/* ===== STYLES OPTIMISÉS POUR LE BOUTON D'ENREGISTREMENT VOCAL ===== */

.voice-record-button {
  padding: 0.75rem !important;
  border-radius: 9999px !important;
  color: white !important;
  transition: all 0.3s ease !important;
  user-select: none !important;
  background-color: #22c55e !important;
  border: none !important;
  outline: none !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.voice-record-button:hover {
  background-color: #16a34a !important;
  transform: translateY(-1px) !important;
}

.voice-record-button:focus {
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.3) !important;
}

.voice-record-button.recording {
  background-color: #ef4444 !important;
  animation: pulse-red 1.5s infinite !important;
  transform: scale(1.1) !important;
  box-shadow: 0 10px 25px rgba(239, 68, 68, 0.5) !important;
}

.voice-record-button.recording:hover {
  background-color: #dc2626 !important;
}

.voice-record-button.processing {
  background-color: #6b7280 !important;
  cursor: not-allowed !important;
  opacity: 0.7 !important;
}

.voice-record-button.processing:hover {
  background-color: #6b7280 !important;
  transform: none !important;
}

.voice-record-button i {
  font-size: 1.25rem !important;
  color: white !important;
}

.voice-record-button .fa-spinner {
  animation: spin 1s linear infinite !important;
}

/* ===== STYLES POUR LES MESSAGES VOCAUX ===== */

.voice-message-container {
  animation: slideUp 0.3s ease-out;
}

.voice-wave {
  transition: all 0.2s ease;
  background-color: #d1d5db;
  border-radius: 2px;
}

.voice-wave.playing {
  background-color: #10b981;
  animation: pulse-red 0.8s infinite;
}

/* ===== STYLES POUR LES IMAGES ===== */

.image-viewer-zoom {
  transition: transform 0.3s ease, filter 0.3s ease;
  cursor: grab;
  max-width: 90vw;
  max-height: 90vh;
  border-radius: 8px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.image-viewer-zoom:active {
  cursor: grabbing;
}

.image-viewer-zoom.zoomed {
  cursor: move;
}

.image-viewer-zoom:hover {
  filter: brightness(1.05);
}

/* ===== STYLES RESPONSIFS ===== */

@media (max-width: 768px) {
  .voice-record-button {
    padding: 0.5rem !important;
  }

  .voice-record-button i {
    font-size: 1rem !important;
  }

  .image-viewer-zoom {
    max-width: 95vw;
    max-height: 95vh;
  }
}

/* ===== STYLES POUR L'ACCESSIBILITÉ ===== */

.voice-record-button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Réduction du mouvement pour les utilisateurs qui le préfèrent */
@media (prefers-reduced-motion: reduce) {
  .voice-record-button,
  .message-bubble,
  .emoji-button,
  .voice-message-container,
  .image-viewer-zoom {
    animation: none !important;
    transition: none !important;