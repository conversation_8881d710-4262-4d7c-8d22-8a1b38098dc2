"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[76],{8448:(_,l,e)=>{e.d(l,{I:()=>u});var i=e(7705),o=e(345);let u=(()=>{class s{constructor(r){this.sanitizer=r}transform(r){if(!r)return this.sanitizer.bypassSecurityTrustHtml("");const n=r.replace(/\(presence obligatoire\)/gi,'<span class="text-red-600 font-semibold">(presence obligatoire)</span>');return this.sanitizer.bypassSecurityTrustHtml(n)}static{this.\u0275fac=function(n){return new(n||s)(i.rXU(o.up,16))}}static{this.\u0275pipe=i.EJ8({name:"highlightPresence",type:s,pure:!0})}}return s})()},1683:(_,l,e)=>{e.d(l,{Y:()=>u});var i=e(177),o=e(7705);let u=(()=>{class s{static{this.\u0275fac=function(n){return new(n||s)}}static{this.\u0275mod=o.$C({type:s})}static{this.\u0275inj=o.G2t({imports:[i.MD]})}}return s})()},4704:(_,l,e)=>{e.d(l,{E:()=>u});var i=e(5312),o=e(7705);let u=(()=>{class s{constructor(){}getDownloadUrl(r){if(!r)return"";let n=r;if(r.includes("C:")||r.includes("/")||r.includes("\\")){const t=r.split(/[\/\\]/);n=t[t.length-1]}return`${i.c.urlBackend}projets/telecharger/${n}`}getFileName(r){if(!r)return"fichier";if(r.includes("/")||r.includes("\\")){const n=r.split(/[\/\\]/);return n[n.length-1]}return r}static{this.\u0275fac=function(n){return new(n||s)}}static{this.\u0275prov=o.jDH({token:s,factory:s.\u0275fac,providedIn:"root"})}}return s})()},1873:(_,l,e)=>{e.d(l,{e:()=>n});var i=e(1626),o=e(8810),u=e(8141),s=e(9437),p=e(5312),r=e(7705);let n=(()=>{class t{constructor(a){this.http=a,this.apiUrl=`${p.c.urlBackend}projets`}getHeaders(){const a=localStorage.getItem("token");return new i.Lr({Authorization:`Bearer ${a}`,"Content-Type":"application/json"})}getProjets(){return console.log("Appel API pour r\xe9cup\xe9rer les projets:",this.apiUrl),this.http.get(this.apiUrl,{headers:this.getHeaders()}).pipe((0,u.M)(a=>console.log("Projets r\xe9cup\xe9r\xe9s:",a)),(0,s.W)(a=>(console.error("Erreur lors de la r\xe9cup\xe9ration des projets:",a),(0,o.$)(()=>a))))}getProjetById(a){return this.http.get(`${this.apiUrl}/${a}`,{headers:this.getHeaders()}).pipe((0,s.W)(h=>(0,o.$)(()=>h)))}addProjet(a){const h=new i.Lr({Authorization:`Bearer ${localStorage.getItem("token")}`});return this.http.post(`${this.apiUrl}/create`,a,{headers:h}).pipe((0,u.M)(d=>console.log("Projet ajout\xe9:",d)),(0,s.W)(d=>(console.error("Erreur lors de l'ajout du projet:",d),(0,o.$)(()=>d))))}updateProjet(a,h){return this.http.put(`${this.apiUrl}/update/${a}`,h,{headers:this.getHeaders()}).pipe((0,s.W)(d=>(0,o.$)(()=>d)))}deleteProjet(a){return this.http.delete(`${this.apiUrl}/delete/${a}`,{headers:this.getHeaders()}).pipe((0,u.M)(h=>console.log("Projet supprim\xe9:",h)),(0,s.W)(h=>(console.error("Erreur lors de la suppression du projet:",h),(0,o.$)(()=>h))))}uploadFile(a){const h=new FormData;h.append("file",a);const d=new i.Lr({Authorization:`Bearer ${localStorage.getItem("token")}`});return this.http.post(`${this.apiUrl}/uploads`,h,{headers:d}).pipe((0,s.W)(g=>(0,o.$)(()=>g)))}static{this.\u0275fac=function(h){return new(h||t)(r.KVO(i.Qq))}}static{this.\u0275prov=r.jDH({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})()},7169:(_,l,e)=>{e.d(l,{R:()=>s});var i=e(5312),o=e(7705),u=e(1626);let s=(()=>{class p{constructor(n){this.http=n,this.apiUrl=`${i.c.urlBackend}rendus`}submitRendu(n){return this.http.post(`${this.apiUrl}/submit`,n)}checkRenduExists(n,t){return this.http.get(`${this.apiUrl}/check/${n}/${t}`)}getAllRendus(){return this.http.get(this.apiUrl)}getRenduById(n){return this.http.get(`${this.apiUrl}/${n}`)}evaluateRendu(n,t){return this.http.post(`${this.apiUrl}/evaluations/${n}`,t)}updateEvaluation(n,t){return this.http.put(`${this.apiUrl}/evaluations/${n}`,t)}static{this.\u0275fac=function(t){return new(t||p)(o.KVO(u.Qq))}}static{this.\u0275prov=o.jDH({token:p,factory:p.\u0275fac,providedIn:"root"})}}return p})()},78:(_,l,e)=>{e.d(l,{C:()=>p});var i=e(1626),o=e(5312),u=e(7705),s=e(7552);let p=(()=>{class r{constructor(t,c){this.http=t,this.jwtHelper=c}getUserHeaders(){const t=localStorage.getItem("token");if(!t||this.jwtHelper.isTokenExpired(t))throw new Error("Token invalide ou expir\xe9");return new i.Lr({Authorization:`Bearer ${t||""}`,"Content-Type":"application/json"})}getAllReunions(){return this.http.get(`${o.c.urlBackend}reunions/getall`,{headers:this.getUserHeaders()})}getReunionById(t){return this.http.get(`${o.c.urlBackend}reunions/getone/${t}`,{headers:this.getUserHeaders()})}createReunion(t){return this.http.post(`${o.c.urlBackend}reunions/add`,t,{headers:this.getUserHeaders()})}updateReunion(t,c){return this.http.put(`${o.c.urlBackend}reunions/update/${t}`,c,{headers:this.getUserHeaders()})}deleteReunion(t){return this.http.delete(`${o.c.urlBackend}reunions/delete/${t}`,{headers:this.getUserHeaders()})}getReunionsByPlanning(t){return this.http.get(`${o.c.urlBackend}reunions/planning/${t}`,{headers:this.getUserHeaders()})}getProchainesReunions(t){return this.http.get(`${o.c.urlBackend}reunions/user/${t}`,{headers:this.getUserHeaders()})}static{this.\u0275fac=function(c){return new(c||r)(u.KVO(i.Qq),u.KVO(s.X7))}}static{this.\u0275prov=u.jDH({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})()},8076:(_,l,e)=>{e.d(l,{Z:()=>p});var i=e(7705),o=e(7455),u=e(6647),s=e(4798);let p=(()=>{class r{constructor(t,c,a){this.MessageService=t,this.route=c,this.logger=a,this.subscriptions=[],this.context="messages"}ngOnInit(){this.context=this.route.snapshot.data.context||"messages","messages"===this.context&&this.subscriptions.push(this.MessageService.activeConversation$.subscribe(t=>{t&&(this.conversationId=t,this.subscriptions.forEach(c=>c.unsubscribe()),this.subscriptions=[],this.subscriptions.push(this.MessageService.subscribeToNewMessages(t).subscribe({next:c=>{},error:c=>this.logger.error("MessageLayout","Error in message subscription",c)})))}))}ngOnDestroy(){this.subscriptions.forEach(t=>t.unsubscribe())}static{this.\u0275fac=function(c){return new(c||r)(i.rXU(o.b),i.rXU(u.nX),i.rXU(s.g))}}static{this.\u0275cmp=i.VBU({type:r,selectors:[["app-message-layout"]],decls:3,vars:0,consts:[[1,"layout-container"],[1,"main-content"]],template:function(c,a){1&c&&(i.j41(0,"div",0)(1,"div",1),i.nrm(2,"router-outlet"),i.k0s()())},dependencies:[u.n3],styles:[".layout-container[_ngcontent-%COMP%]{display:flex;height:100vh;width:100%;overflow:hidden}.main-content[_ngcontent-%COMP%]{flex:1;width:100%;height:100%;overflow:hidden}"]})}}return r})()}}]);