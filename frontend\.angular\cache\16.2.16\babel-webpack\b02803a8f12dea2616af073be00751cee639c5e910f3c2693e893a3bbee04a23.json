{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { CallType } from '../../../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../services/message.service\";\nimport * as i4 from \"../../../../services/toast.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../call-interface/call-interface.component\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 52);\n  }\n}\nfunction MessageChatComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"span\");\n    i0.ɵɵtext(2, \"En train d'\\u00E9crire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 54);\n    i0.ɵɵelement(4, \"div\", 55)(5, \"div\", 56)(6, \"div\", 57);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.isOnline) ? \"En ligne\" : ctx_r2.formatLastActive(ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_23_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      ctx_r21.toggleSearch();\n      return i0.ɵɵresetView(ctx_r21.showMainMenu = false);\n    });\n    i0.ɵɵelement(3, \"i\", 61);\n    i0.ɵɵelementStart(4, \"span\", 62);\n    i0.ɵɵtext(5, \"Rechercher\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 63);\n    i0.ɵɵelement(7, \"i\", 64);\n    i0.ɵɵelementStart(8, \"span\", 62);\n    i0.ɵɵtext(9, \"Voir le profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 63);\n    i0.ɵɵelement(11, \"i\", 65);\n    i0.ɵɵelementStart(12, \"span\", 62);\n    i0.ɵɵtext(13, \"Notifications\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(14, \"hr\", 66);\n    i0.ɵɵelementStart(15, \"button\", 63);\n    i0.ɵɵelement(16, \"i\", 67);\n    i0.ɵɵelementStart(17, \"span\", 62);\n    i0.ɵɵtext(18, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction MessageChatComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"div\", 69);\n    i0.ɵɵelement(2, \"i\", 70);\n    i0.ɵɵelementStart(3, \"p\", 71);\n    i0.ɵɵtext(4, \" D\\u00E9posez vos fichiers ici \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 72);\n    i0.ɵɵtext(6, \" Images, vid\\u00E9os, documents... \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 73);\n    i0.ɵɵelement(8, \"span\", 74)(9, \"span\", 75)(10, \"span\", 76);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"div\", 78);\n    i0.ɵɵelementStart(2, \"span\", 79);\n    i0.ɵɵtext(3, \"Chargement des messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 81);\n    i0.ɵɵelement(2, \"i\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 83);\n    i0.ɵɵtext(4, \" Aucun message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 84);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Commencez votre conversation avec \", ctx_r7.otherParticipant == null ? null : ctx_r7.otherParticipant.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"div\", 103)(2, \"span\", 104);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r27.formatDateSeparator(message_r25.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"img\", 106);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_3_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const message_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.openUserProfile(message_r25.sender == null ? null : message_r25.sender.id));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r25 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r25.sender == null ? null : message_r25.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r25.sender == null ? null : message_r25.sender.username);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r29.getUserColor(message_r25.sender == null ? null : message_r25.sender.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r25.sender == null ? null : message_r25.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108);\n    i0.ɵɵelement(1, \"div\", 109);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r30.formatMessageContent(message_r25.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 113);\n  }\n  if (rf & 2) {\n    const message_r25 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", (message_r25.sender == null ? null : message_r25.sender.id) === ctx_r43.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵproperty(\"innerHTML\", ctx_r43.formatMessageContent(message_r25.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"img\", 111);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_7_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const message_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.openImageViewer(message_r25));\n    })(\"load\", function MessageChatComponent_div_29_ng_container_1_div_7_Template_img_load_1_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const message_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r48.onImageLoad($event, message_r25));\n    })(\"error\", function MessageChatComponent_div_29_ng_container_1_div_7_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const message_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r50 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r50.onImageError($event, message_r25));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_ng_container_1_div_7_div_2_Template, 1, 3, \"div\", 112);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r31.getImageUrl(message_r25), i0.ɵɵsanitizeUrl)(\"alt\", message_r25.content || \"Image\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r25.content);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_8_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 131);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_8_div_5_Template_div_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r60);\n      const i_r57 = restoredCtx.index;\n      const message_r25 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r58.seekVoiceMessage(message_r25, i_r57));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const wave_r56 = ctx.$implicit;\n    const i_r57 = ctx.index;\n    const message_r25 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", wave_r56, \"px\")(\"transform\", \"scaleY(\" + (ctx_r53.isVoicePlaying(message_r25.id) && i_r57 <= ctx_r53.getVoiceProgress(message_r25) ? \"1.3\" : \"1\") + \")\")(\"box-shadow\", ctx_r53.isVoicePlaying(message_r25.id) && i_r57 <= ctx_r53.getVoiceProgress(message_r25) ? \"0 0 8px rgba(59, 130, 246, 0.5)\" : \"none\");\n    i0.ɵɵclassProp(\"bg-gray-400\", !ctx_r53.isVoicePlaying(message_r25.id))(\"bg-blue-500\", ctx_r53.isVoicePlaying(message_r25.id) && i_r57 <= ctx_r53.getVoiceProgress(message_r25))(\"bg-gray-300\", ctx_r53.isVoicePlaying(message_r25.id) && i_r57 > ctx_r53.getVoiceProgress(message_r25.id))(\"dark:bg-gray-500\", ctx_r53.isVoicePlaying(message_r25.id) && i_r57 > ctx_r53.getVoiceProgress(message_r25.id))(\"animate-pulse\", ctx_r53.isVoicePlaying(message_r25.id) && i_r57 <= ctx_r53.getVoiceProgress(message_r25));\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_8_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r25 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r54.getVoicePlaybackData(message_r25.id).speed, \"x \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_8_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 133);\n    i0.ɵɵelement(1, \"div\", 134)(2, \"div\", 135)(3, \"div\", 136);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 114)(1, \"button\", 115);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_8_Template_button_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r65);\n      const message_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r63 = i0.ɵɵnextContext(2);\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r63.toggleVoicePlayback(message_r25));\n    });\n    i0.ɵɵelement(2, \"i\", 116);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 117)(4, \"div\", 118);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_29_ng_container_1_div_8_div_5_Template, 1, 16, \"div\", 119);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 120)(7, \"div\", 121)(8, \"span\", 122);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 123);\n    i0.ɵɵtext(11, \"/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 124);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 125);\n    i0.ɵɵtemplate(15, MessageChatComponent_div_29_ng_container_1_div_8_span_15_Template, 2, 1, \"span\", 126);\n    i0.ɵɵtemplate(16, MessageChatComponent_div_29_ng_container_1_div_8_div_16_Template, 4, 0, \"div\", 127);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 128)(18, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_8_Template_button_click_18_listener($event) {\n      i0.ɵɵrestoreView(_r65);\n      const message_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r66 = i0.ɵɵnextContext(2);\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r66.toggleVoiceSpeed(message_r25));\n    });\n    i0.ɵɵelement(19, \"i\", 130);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"shadow-lg\", ctx_r32.isVoicePlaying(message_r25.id))(\"ring-2\", ctx_r32.isVoicePlaying(message_r25.id))(\"ring-blue-300\", ctx_r32.isVoicePlaying(message_r25.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"transform\", ctx_r32.isVoicePlaying(message_r25.id) ? \"scale(1.05)\" : \"scale(1)\");\n    i0.ɵɵclassProp(\"bg-blue-500\", !ctx_r32.isVoicePlaying(message_r25.id))(\"hover:bg-blue-600\", !ctx_r32.isVoicePlaying(message_r25.id))(\"bg-red-500\", ctx_r32.isVoicePlaying(message_r25.id))(\"hover:bg-red-600\", ctx_r32.isVoicePlaying(message_r25.id))(\"animate-pulse\", ctx_r32.isVoicePlaying(message_r25.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"fa-play\", !ctx_r32.isVoicePlaying(message_r25.id))(\"fa-pause\", ctx_r32.isVoicePlaying(message_r25.id))(\"scale-110\", ctx_r32.isVoicePlaying(message_r25.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r32.getVoiceWaves(message_r25));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r32.getVoiceCurrentTime(message_r25), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r32.getVoiceDuration(message_r25), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.getVoicePlaybackData(message_r25.id).speed !== 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.isVoicePlaying(message_r25.id));\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 137);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const message_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r69 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r69.downloadFile(message_r25));\n    });\n    i0.ɵɵelementStart(1, \"div\", 138);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 117)(4, \"div\", 139);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 104);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 140);\n    i0.ɵɵelement(9, \"i\", 141);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r33.getFileIcon(message_r25));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r33.getFileName(message_r25), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r33.getFileSize(message_r25), \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_13_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 147);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_13_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 148);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_13_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 149);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_13_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 150);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_div_13_i_1_Template, 1, 0, \"i\", 143);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_ng_container_1_div_13_i_2_Template, 1, 0, \"i\", 144);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_29_ng_container_1_div_13_i_3_Template, 1, 0, \"i\", 145);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_29_ng_container_1_div_13_i_4_Template, 1, 0, \"i\", 146);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r25 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r25.status === \"SENDING\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r25.status === \"SENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r25.status === \"DELIVERED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r25.status === \"READ\");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_14_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r82 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 153);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_14_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r82);\n      const reaction_r79 = restoredCtx.$implicit;\n      const message_r25 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r80 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r80.toggleReaction(message_r25.id, reaction_r79.emoji));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reaction_r79 = ctx.$implicit;\n    const ctx_r78 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"bg-green-100\", ctx_r78.hasUserReacted(reaction_r79, ctx_r78.currentUserId || \"\"))(\"text-green-600\", ctx_r78.hasUserReacted(reaction_r79, ctx_r78.currentUserId || \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r79.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r79.count || 1);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 151);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_div_14_button_1_Template, 5, 6, \"button\", 152);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r25 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", message_r25.reactions);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r85 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_div_1_Template, 4, 1, \"div\", 88);\n    i0.ɵɵelementStart(2, \"div\", 89);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_Template_div_click_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r85);\n      const message_r25 = restoredCtx.$implicit;\n      const ctx_r84 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r84.onMessageClick(message_r25, $event));\n    })(\"contextmenu\", function MessageChatComponent_div_29_ng_container_1_Template_div_contextmenu_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r85);\n      const message_r25 = restoredCtx.$implicit;\n      const ctx_r86 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r86.onMessageContextMenu(message_r25, $event));\n    });\n    i0.ɵɵtemplate(3, MessageChatComponent_div_29_ng_container_1_div_3_Template, 2, 2, \"div\", 90);\n    i0.ɵɵelementStart(4, \"div\", 91);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_29_ng_container_1_div_5_Template, 2, 3, \"div\", 92);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_29_ng_container_1_div_6_Template, 2, 1, \"div\", 93);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_29_ng_container_1_div_7_Template, 3, 3, \"div\", 94);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_29_ng_container_1_div_8_Template, 20, 29, \"div\", 95);\n    i0.ɵɵtemplate(9, MessageChatComponent_div_29_ng_container_1_div_9_Template, 10, 4, \"div\", 96);\n    i0.ɵɵelementStart(10, \"div\", 97)(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, MessageChatComponent_div_29_ng_container_1_div_13_Template, 5, 4, \"div\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, MessageChatComponent_div_29_ng_container_1_div_14_Template, 2, 1, \"div\", 99);\n    i0.ɵɵelementStart(15, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_Template_button_click_15_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r85);\n      const message_r25 = restoredCtx.$implicit;\n      const ctx_r87 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r87.showQuickReactions(message_r25, $event));\n    });\n    i0.ɵɵelement(16, \"i\", 101);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r25 = ctx.$implicit;\n    const i_r26 = ctx.index;\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.shouldShowDateSeparator(i_r26));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"justify-end\", (message_r25.sender == null ? null : message_r25.sender.id) === ctx_r23.currentUserId)(\"justify-start\", (message_r25.sender == null ? null : message_r25.sender.id) !== ctx_r23.currentUserId);\n    i0.ɵɵproperty(\"id\", \"message-\" + message_r25.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r25.sender == null ? null : message_r25.sender.id) !== ctx_r23.currentUserId && ctx_r23.shouldShowAvatar(i_r26));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", (message_r25.sender == null ? null : message_r25.sender.id) === ctx_r23.currentUserId ? \"#3b82f6\" : \"#ffffff\")(\"color\", (message_r25.sender == null ? null : message_r25.sender.id) === ctx_r23.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.isGroupConversation() && (message_r25.sender == null ? null : message_r25.sender.id) !== ctx_r23.currentUserId && ctx_r23.shouldShowSenderName(i_r26));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.getMessageType(message_r25) === \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.hasImage(message_r25));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.getMessageType(message_r25) === \"audio\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.hasFile(message_r25) && ctx_r23.getMessageType(message_r25) !== \"audio\" && !ctx_r23.hasImage(message_r25));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r23.formatMessageTime(message_r25.timestamp));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r25.sender == null ? null : message_r25.sender.id) === ctx_r23.currentUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r25.reactions && message_r25.reactions.length > 0);\n  }\n}\nfunction MessageChatComponent_div_29_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 154);\n    i0.ɵɵelement(1, \"img\", 155);\n    i0.ɵɵelementStart(2, \"div\", 156)(3, \"div\", 157);\n    i0.ɵɵelement(4, \"div\", 158)(5, \"div\", 159)(6, \"div\", 160);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (ctx_r24.otherParticipant == null ? null : ctx_r24.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r24.otherParticipant == null ? null : ctx_r24.otherParticipant.username);\n  }\n}\nfunction MessageChatComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_Template, 17, 19, \"ng-container\", 86);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_div_2_Template, 7, 2, \"div\", 87);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.messages)(\"ngForTrackBy\", ctx_r8.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.otherUserIsTyping);\n  }\n}\nfunction MessageChatComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r89 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 161)(1, \"div\", 162)(2, \"div\", 37)(3, \"div\", 163)(4, \"span\");\n    i0.ɵɵtext(5, \"Envoi en cours...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 164);\n    i0.ɵɵelement(9, \"div\", 165);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 166);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_30_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r88 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r88.resetUploadState());\n    });\n    i0.ɵɵelement(11, \"i\", 167);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r9.uploadProgress.toFixed(0), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r9.uploadProgress, \"%\");\n  }\n}\nfunction MessageChatComponent_button_43_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 171);\n  }\n}\nfunction MessageChatComponent_button_43_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 172);\n  }\n}\nconst _c2 = function (a1, a2) {\n  return {\n    \"voice-record-button\": true,\n    recording: a1,\n    processing: a2\n  };\n};\nfunction MessageChatComponent_button_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r93 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 168);\n    i0.ɵɵlistener(\"mousedown\", function MessageChatComponent_button_43_Template_button_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r93);\n      const ctx_r92 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r92.onRecordStart($event));\n    })(\"mouseup\", function MessageChatComponent_button_43_Template_button_mouseup_0_listener($event) {\n      i0.ɵɵrestoreView(_r93);\n      const ctx_r94 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r94.onRecordEnd($event));\n    })(\"mouseleave\", function MessageChatComponent_button_43_Template_button_mouseleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r93);\n      const ctx_r95 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r95.onRecordCancel($event));\n    })(\"touchstart\", function MessageChatComponent_button_43_Template_button_touchstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r93);\n      const ctx_r96 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r96.onRecordStart($event));\n    })(\"touchend\", function MessageChatComponent_button_43_Template_button_touchend_0_listener($event) {\n      i0.ɵɵrestoreView(_r93);\n      const ctx_r97 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r97.onRecordEnd($event));\n    })(\"touchcancel\", function MessageChatComponent_button_43_Template_button_touchcancel_0_listener($event) {\n      i0.ɵɵrestoreView(_r93);\n      const ctx_r98 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r98.onRecordCancel($event));\n    });\n    i0.ɵɵtemplate(1, MessageChatComponent_button_43_i_1_Template, 1, 0, \"i\", 169);\n    i0.ɵɵtemplate(2, MessageChatComponent_button_43_i_2_Template, 1, 0, \"i\", 170);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c2, ctx_r11.isRecordingVoice, ctx_r11.voiceRecordingState === \"processing\"))(\"disabled\", ctx_r11.voiceRecordingState === \"processing\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.voiceRecordingState !== \"processing\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.voiceRecordingState === \"processing\");\n  }\n}\nfunction MessageChatComponent_button_44_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 176);\n  }\n}\nfunction MessageChatComponent_button_44_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 177);\n  }\n}\nfunction MessageChatComponent_button_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r102 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 173);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_button_44_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r101 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r101.sendMessage());\n    });\n    i0.ɵɵtemplate(1, MessageChatComponent_button_44_i_1_Template, 1, 0, \"i\", 174);\n    i0.ɵɵtemplate(2, MessageChatComponent_button_44_i_2_Template, 1, 0, \"i\", 175);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r12.isSendingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.isSendingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.isSendingMessage);\n  }\n}\nfunction MessageChatComponent_div_45_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 195);\n  }\n  if (rf & 2) {\n    const wave_r104 = ctx.$implicit;\n    const i_r105 = ctx.index;\n    i0.ɵɵstyleProp(\"width\", 2, \"px\")(\"height\", wave_r104, \"px\")(\"animation-delay\", i_r105 * 100, \"ms\");\n  }\n}\nfunction MessageChatComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r107 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 178)(1, \"div\", 179)(2, \"button\", 180);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r107);\n      const ctx_r106 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r106.cancelVoiceRecording());\n    });\n    i0.ɵɵelement(3, \"i\", 181);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 182)(5, \"div\", 183);\n    i0.ɵɵelement(6, \"i\", 184)(7, \"div\", 185);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 125);\n    i0.ɵɵtemplate(9, MessageChatComponent_div_45_div_9_Template, 1, 6, \"div\", 186);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 187);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 188);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r107);\n      const ctx_r108 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r108.stopVoiceRecording());\n    });\n    i0.ɵɵelement(13, \"i\", 176);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 189);\n    i0.ɵɵelement(15, \"div\", 190);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 191)(17, \"div\", 192);\n    i0.ɵɵelement(18, \"i\", 193);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"Rel\\u00E2chez pour envoyer \\u2022 Glissez vers le haut pour annuler\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 194);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.voiceWaves);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.formatRecordingDuration(ctx_r13.voiceRecordingDuration), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"width\", ctx_r13.voiceRecordingDuration / 60 * 100, \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" Dur\\u00E9e max: 60 secondes \\u2022 Format: \", ctx_r13.getRecordingFormat(), \" \");\n  }\n}\nfunction MessageChatComponent_div_46_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r113 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 202);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_46_button_3_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r113);\n      const category_r111 = restoredCtx.$implicit;\n      const ctx_r112 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r112.selectEmojiCategory(category_r111));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r111 = ctx.$implicit;\n    const ctx_r109 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-green-100\", ctx_r109.selectedEmojiCategory === category_r111)(\"text-green-600\", ctx_r109.selectedEmojiCategory === category_r111)(\"hover:bg-gray-100\", ctx_r109.selectedEmojiCategory !== category_r111)(\"dark:hover:bg-gray-700\", ctx_r109.selectedEmojiCategory !== category_r111);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r111.icon, \" \");\n  }\n}\nfunction MessageChatComponent_div_46_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r116 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 203);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_46_button_5_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r116);\n      const emoji_r114 = restoredCtx.$implicit;\n      const ctx_r115 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r115.insertEmoji(emoji_r114));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r114 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r114.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r114.emoji, \" \");\n  }\n}\nfunction MessageChatComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 196)(1, \"div\", 197)(2, \"div\", 198);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_46_button_3_Template, 2, 9, \"button\", 199);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 200);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_46_button_5_Template, 2, 2, \"button\", 201);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.emojiCategories);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.getEmojisForCategory(ctx_r14.selectedEmojiCategory));\n  }\n}\nfunction MessageChatComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r118 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 204)(1, \"div\", 197)(2, \"div\", 205)(3, \"button\", 206);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_47_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r118);\n      const ctx_r117 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r117.triggerFileInput(\"image\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 207);\n    i0.ɵɵelement(5, \"i\", 208);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 209);\n    i0.ɵɵtext(7, \"Photos\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 206);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_47_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r118);\n      const ctx_r119 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r119.triggerFileInput(\"video\"));\n    });\n    i0.ɵɵelementStart(9, \"div\", 210);\n    i0.ɵɵelement(10, \"i\", 211);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 209);\n    i0.ɵɵtext(12, \"Vid\\u00E9os\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 206);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_47_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r118);\n      const ctx_r120 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r120.triggerFileInput(\"document\"));\n    });\n    i0.ɵɵelementStart(14, \"div\", 212);\n    i0.ɵɵelement(15, \"i\", 213);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 209);\n    i0.ɵɵtext(17, \"Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 206);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_47_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r118);\n      const ctx_r121 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r121.openCamera());\n    });\n    i0.ɵɵelementStart(19, \"div\", 214);\n    i0.ɵɵelement(20, \"i\", 215);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 209);\n    i0.ɵɵtext(22, \"Cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_50_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r125 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 218);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_50_button_2_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r125);\n      const emoji_r123 = restoredCtx.$implicit;\n      const ctx_r124 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r124.quickReact(emoji_r123));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r123 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r123, \" \");\n  }\n}\nconst _c3 = function () {\n  return [\"\\u2764\\uFE0F\", \"\\uD83D\\uDE02\", \"\\uD83D\\uDE2E\", \"\\uD83D\\uDE22\", \"\\uD83D\\uDE21\", \"\\uD83D\\uDC4D\"];\n};\nfunction MessageChatComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 216)(1, \"div\", 32);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_50_button_2_Template, 2, 1, \"button\", 217);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"left\", ctx_r17.contextMenuPosition.x - 100, \"px\")(\"top\", ctx_r17.contextMenuPosition.y - 60, \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(5, _c3));\n  }\n}\nfunction MessageChatComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r127 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 219)(1, \"div\", 59)(2, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_51_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r127);\n      const ctx_r126 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r126.replyToMessage(ctx_r126.selectedMessage));\n    });\n    i0.ɵɵelement(3, \"i\", 220);\n    i0.ɵɵelementStart(4, \"span\", 62);\n    i0.ɵɵtext(5, \"R\\u00E9pondre\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_51_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r127);\n      const ctx_r128 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r128.forwardMessage(ctx_r128.selectedMessage));\n    });\n    i0.ɵɵelement(7, \"i\", 221);\n    i0.ɵɵelementStart(8, \"span\", 62);\n    i0.ɵɵtext(9, \"Transf\\u00E9rer\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_51_Template_button_click_10_listener($event) {\n      i0.ɵɵrestoreView(_r127);\n      const ctx_r129 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r129.showQuickReactions(ctx_r129.selectedMessage, $event));\n    });\n    i0.ɵɵelement(11, \"i\", 222);\n    i0.ɵɵelementStart(12, \"span\", 62);\n    i0.ɵɵtext(13, \"R\\u00E9agir\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(14, \"hr\", 66);\n    i0.ɵɵelementStart(15, \"button\", 223);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_51_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r127);\n      const ctx_r130 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r130.deleteMessage(ctx_r130.selectedMessage));\n    });\n    i0.ɵɵelement(16, \"i\", 224);\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18, \"Supprimer\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"left\", ctx_r18.contextMenuPosition.x, \"px\")(\"top\", ctx_r18.contextMenuPosition.y, \"px\");\n  }\n}\nfunction MessageChatComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r132 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 225);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_52_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r132);\n      const ctx_r131 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r131.closeAllMenus());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r134 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 226);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_54_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r134);\n      const ctx_r133 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r133.closeImageViewer());\n    });\n    i0.ɵɵelementStart(1, \"div\", 227)(2, \"button\", 228);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_54_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r134);\n      const ctx_r135 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r135.closeImageViewer());\n    });\n    i0.ɵɵelement(3, \"i\", 229);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 230);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_54_Template_button_click_4_listener($event) {\n      i0.ɵɵrestoreView(_r134);\n      const ctx_r136 = i0.ɵɵnextContext();\n      ctx_r136.downloadImage();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(5, \"i\", 231);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"img\", 232);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_54_Template_img_click_6_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 233);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_54_Template_div_click_7_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(8, \"div\", 234)(9, \"div\")(10, \"p\", 235);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 236);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 32)(15, \"button\", 237);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_54_Template_button_click_15_listener($event) {\n      i0.ɵɵrestoreView(_r134);\n      const ctx_r139 = i0.ɵɵnextContext();\n      ctx_r139.zoomImage(1.2);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(16, \"i\", 238);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 239);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_54_Template_button_click_17_listener($event) {\n      i0.ɵɵrestoreView(_r134);\n      const ctx_r140 = i0.ɵɵnextContext();\n      ctx_r140.zoomImage(0.8);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(18, \"i\", 240);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 241);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_54_Template_button_click_19_listener($event) {\n      i0.ɵɵrestoreView(_r134);\n      const ctx_r141 = i0.ɵɵnextContext();\n      ctx_r141.resetZoom();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(20, \"i\", 242);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r20.selectedImage == null ? null : ctx_r20.selectedImage.url, i0.ɵɵsanitizeUrl)(\"alt\", (ctx_r20.selectedImage == null ? null : ctx_r20.selectedImage.name) || \"Image\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r20.selectedImage == null ? null : ctx_r20.selectedImage.name) || \"Image\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r20.selectedImage == null ? null : ctx_r20.selectedImage.size) || \"Taille inconnue\", \" \");\n  }\n}\nexport class MessageChatComponent {\n  constructor(fb, route, MessageService, toastService, cdr) {\n    this.fb = fb;\n    this.route = route;\n    this.MessageService = MessageService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    // === DONNÉES PRINCIPALES ===\n    this.conversation = null;\n    this.messages = [];\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    // === ÉTATS DE L'INTERFACE ===\n    this.isLoading = false;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.searchMode = false;\n    this.isSendingMessage = false;\n    this.otherUserIsTyping = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n    this.contextMenuPosition = {\n      x: 0,\n      y: 0\n    };\n    this.showReactionPicker = false;\n    this.reactionPickerMessage = null;\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    this.uploadProgress = 0;\n    this.isUploading = false;\n    this.isDragOver = false;\n    // === GESTION VOCALE OPTIMISÉE ===\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.recordingTimer = null;\n    this.voiceWaves = [4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8];\n    // Lecture des messages vocaux\n    this.currentAudio = null;\n    this.playingMessageId = null;\n    this.voicePlayback = {};\n    // === APPELS WEBRTC ===\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // État de l'appel WebRTC\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    // === ÉMOJIS ===\n    this.emojiCategories = [{\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [{\n        emoji: '😀',\n        name: 'grinning face'\n      }, {\n        emoji: '😃',\n        name: 'grinning face with big eyes'\n      }, {\n        emoji: '😄',\n        name: 'grinning face with smiling eyes'\n      }, {\n        emoji: '😁',\n        name: 'beaming face with smiling eyes'\n      }, {\n        emoji: '😆',\n        name: 'grinning squinting face'\n      }, {\n        emoji: '😅',\n        name: 'grinning face with sweat'\n      }, {\n        emoji: '😂',\n        name: 'face with tears of joy'\n      }, {\n        emoji: '🤣',\n        name: 'rolling on the floor laughing'\n      }, {\n        emoji: '😊',\n        name: 'smiling face with smiling eyes'\n      }, {\n        emoji: '😇',\n        name: 'smiling face with halo'\n      }]\n    }, {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [{\n        emoji: '👶',\n        name: 'baby'\n      }, {\n        emoji: '🧒',\n        name: 'child'\n      }, {\n        emoji: '👦',\n        name: 'boy'\n      }, {\n        emoji: '👧',\n        name: 'girl'\n      }, {\n        emoji: '🧑',\n        name: 'person'\n      }, {\n        emoji: '👨',\n        name: 'man'\n      }, {\n        emoji: '👩',\n        name: 'woman'\n      }, {\n        emoji: '👴',\n        name: 'old man'\n      }, {\n        emoji: '👵',\n        name: 'old woman'\n      }]\n    }, {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [{\n        emoji: '🐶',\n        name: 'dog face'\n      }, {\n        emoji: '🐱',\n        name: 'cat face'\n      }, {\n        emoji: '🐭',\n        name: 'mouse face'\n      }, {\n        emoji: '🐹',\n        name: 'hamster'\n      }, {\n        emoji: '🐰',\n        name: 'rabbit face'\n      }, {\n        emoji: '🦊',\n        name: 'fox'\n      }, {\n        emoji: '🐻',\n        name: 'bear'\n      }, {\n        emoji: '🐼',\n        name: 'panda'\n      }]\n    }];\n    this.selectedEmojiCategory = this.emojiCategories[0];\n    // === PAGINATION ===\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.currentPage = 1;\n    // === AUTRES ÉTATS ===\n    this.isTyping = false;\n    this.isUserTyping = false;\n    this.typingTimeout = null;\n    this.subscriptions = new Subscription();\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\n  isInputDisabled() {\n    return !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage;\n  }\n  // Méthode pour gérer l'état du contrôle de saisie\n  updateInputState() {\n    const contentControl = this.messageForm.get('content');\n    if (this.isInputDisabled()) {\n      contentControl?.disable();\n    } else {\n      contentControl?.enable();\n    }\n  }\n  ngOnInit() {\n    console.log('🚀 MessageChatComponent initialized');\n    this.initializeComponent();\n  }\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupCallSubscriptions();\n  }\n  setupCallSubscriptions() {\n    // S'abonner aux appels entrants\n    this.subscriptions.add(this.MessageService.incomingCall$.subscribe({\n      next: incomingCall => {\n        if (incomingCall) {\n          console.log('📞 Incoming call received:', incomingCall);\n          this.handleIncomingCall(incomingCall);\n        }\n      },\n      error: error => {\n        console.error('❌ Error in incoming call subscription:', error);\n      }\n    }));\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(this.MessageService.activeCall$.subscribe({\n      next: call => {\n        if (call) {\n          console.log('📞 Active call updated:', call);\n          this.activeCall = call;\n        }\n      },\n      error: error => {\n        console.error('❌ Error in active call subscription:', error);\n      }\n    }));\n  }\n  handleIncomingCall(incomingCall) {\n    // Afficher une notification ou modal d'appel entrant\n    // Pour l'instant, on log juste\n    console.log('🔔 Handling incoming call from:', incomingCall.caller.username);\n    // Jouer la sonnerie\n    this.MessageService.play('ringtone');\n    // Ici on pourrait afficher une modal ou notification\n    // Pour l'instant, on accepte automatiquement pour tester\n    // this.acceptCall(incomingCall);\n  }\n\n  loadCurrentUser() {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('🔍 Raw user from localStorage:', userString);\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n      const user = JSON.parse(userString);\n      console.log('🔍 Parsed user object:', user);\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      console.log('🔍 Trying to extract user ID:', {\n        _id: user._id,\n        id: user.id,\n        userId: user.userId,\n        extracted: userId\n      });\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n        console.log('✅ Current user loaded successfully:', {\n          id: this.currentUserId,\n          username: this.currentUsername\n        });\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n  loadConversation() {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: conversation => {\n        console.log('🔍 Conversation loaded successfully:', conversation);\n        console.log('🔍 Conversation structure:', {\n          id: conversation?.id,\n          participants: conversation?.participants,\n          participantsCount: conversation?.participants?.length,\n          isGroup: conversation?.isGroup,\n          messages: conversation?.messages,\n          messagesCount: conversation?.messages?.length\n        });\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n        // Configurer les subscriptions temps réel après le chargement de la conversation\n        this.setupSubscriptions();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError('Erreur lors du chargement de la conversation');\n        this.isLoading = false;\n      }\n    });\n  }\n  setOtherParticipant() {\n    if (!this.conversation?.participants || this.conversation.participants.length === 0) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n    console.log('Setting other participant...');\n    console.log('Current user ID:', this.currentUserId);\n    console.log('All participants:', this.conversation.participants);\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        console.log('Comparing participant ID:', participantId, 'with current user ID:', this.currentUserId);\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      console.log('Fallback: using first participant');\n      this.otherParticipant = this.conversation.participants[0];\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId = this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log('First participant is current user, using second participant');\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      console.log('✅ Other participant set successfully:', {\n        id: this.otherParticipant.id || this.otherParticipant._id,\n        username: this.otherParticipant.username,\n        image: this.otherParticipant.image,\n        isOnline: this.otherParticipant.isOnline\n      });\n      // Log très visible pour debug\n      console.log('🎯 FINAL RESULT: otherParticipant =', this.otherParticipant.username);\n      console.log('🎯 Should display in sidebar:', this.otherParticipant.username);\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      console.log('Conversation participants:', this.conversation.participants);\n      console.log('Current user ID:', this.currentUserId);\n      // Log très visible pour debug\n      console.log('🚨 ERROR: No otherParticipant found!');\n    }\n    // Mettre à jour l'état du champ de saisie\n    this.updateInputState();\n  }\n  loadMessages() {\n    if (!this.conversation?.id) return;\n    // Les messages sont déjà chargés avec la conversation\n    let messages = this.conversation.messages || [];\n    // Trier les messages par timestamp (plus anciens en premier)\n    this.messages = messages.sort((a, b) => {\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\n    });\n\n    console.log('📋 Messages loaded and sorted:', {\n      total: this.messages.length,\n      first: this.messages[0]?.content,\n      last: this.messages[this.messages.length - 1]?.content\n    });\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id) return;\n    this.isLoadingMore = true;\n    this.currentPage++;\n    // Calculer l'offset basé sur les messages déjà chargés\n    const offset = this.messages.length;\n    this.MessageService.getMessages(this.currentUserId,\n    // senderId\n    this.otherParticipant?.id || this.otherParticipant?._id,\n    // receiverId\n    this.conversation.id, this.currentPage, this.MAX_MESSAGES_TO_LOAD).subscribe({\n      next: newMessages => {\n        if (newMessages && newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste\n          this.messages = [...newMessages.reverse(), ...this.messages];\n          this.hasMoreMessages = newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      }\n    });\n  }\n\n  setupSubscriptions() {\n    if (!this.conversation?.id) {\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\n      return;\n    }\n    console.log('🔄 Setting up real-time subscriptions for conversation:', this.conversation.id);\n    // Subscription pour les nouveaux messages\n    console.log('📨 Setting up message subscription...');\n    this.subscriptions.add(this.MessageService.subscribeToNewMessages(this.conversation.id).subscribe({\n      next: newMessage => {\n        console.log('📨 New message received via subscription:', newMessage);\n        console.log('📨 Message structure:', {\n          id: newMessage.id,\n          type: newMessage.type,\n          content: newMessage.content,\n          sender: newMessage.sender,\n          senderId: newMessage.senderId,\n          receiverId: newMessage.receiverId,\n          attachments: newMessage.attachments\n        });\n        // Debug des attachments\n        console.log('📨 [Debug] Message type detected:', this.getMessageType(newMessage));\n        console.log('📨 [Debug] Has image:', this.hasImage(newMessage));\n        console.log('📨 [Debug] Has file:', this.hasFile(newMessage));\n        console.log('📨 [Debug] Image URL:', this.getImageUrl(newMessage));\n        if (newMessage.attachments) {\n          newMessage.attachments.forEach((att, index) => {\n            console.log(`📨 [Debug] Attachment ${index}:`, {\n              type: att.type,\n              url: att.url,\n              path: att.path,\n              name: att.name,\n              size: att.size\n            });\n          });\n        }\n        // Ajouter le message à la liste s'il n'existe pas déjà\n        const messageExists = this.messages.some(msg => msg.id === newMessage.id);\n        if (!messageExists) {\n          // Ajouter le nouveau message à la fin (en bas)\n          this.messages.push(newMessage);\n          console.log('✅ Message added to list, total messages:', this.messages.length);\n          // Forcer la détection de changements\n          this.cdr.detectChanges();\n          // Scroll vers le bas après un court délai\n          setTimeout(() => {\n            this.scrollToBottom();\n          }, 50);\n          // Marquer comme lu si ce n'est pas notre message\n          const senderId = newMessage.sender?.id || newMessage.senderId;\n          console.log('📨 Checking if message should be marked as read:', {\n            senderId,\n            currentUserId: this.currentUserId,\n            shouldMarkAsRead: senderId !== this.currentUserId\n          });\n          if (senderId && senderId !== this.currentUserId) {\n            this.markMessageAsRead(newMessage.id);\n          }\n        }\n      },\n      error: error => {\n        console.error('❌ Error in message subscription:', error);\n      }\n    }));\n    // Subscription pour les indicateurs de frappe\n    console.log('📝 Setting up typing indicator subscription...');\n    this.subscriptions.add(this.MessageService.subscribeToTypingIndicator(this.conversation.id).subscribe({\n      next: typingData => {\n        console.log('📝 Typing indicator received:', typingData);\n        // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n        if (typingData.userId !== this.currentUserId) {\n          this.otherUserIsTyping = typingData.isTyping;\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in typing subscription:', error);\n      }\n    }));\n    // Subscription pour les mises à jour de conversation\n    this.subscriptions.add(this.MessageService.subscribeToConversationUpdates(this.conversation.id).subscribe({\n      next: conversationUpdate => {\n        console.log('📋 Conversation update:', conversationUpdate);\n        // Mettre à jour la conversation si nécessaire\n        if (conversationUpdate.id === this.conversation.id) {\n          this.conversation = {\n            ...this.conversation,\n            ...conversationUpdate\n          };\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in conversation subscription:', error);\n      }\n    }));\n  }\n  markMessageAsRead(messageId) {\n    this.MessageService.markMessageAsRead(messageId).subscribe({\n      next: () => {\n        console.log('✅ Message marked as read:', messageId);\n      },\n      error: error => {\n        console.error('❌ Error marking message as read:', error);\n      }\n    });\n  }\n  // === ENVOI DE MESSAGES ===\n  sendMessage() {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    // Désactiver le bouton d'envoi\n    this.isSendingMessage = true;\n    this.updateInputState();\n    console.log('📤 Sending message:', {\n      content,\n      receiverId,\n      conversationId: this.conversation.id\n    });\n    this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({\n      next: message => {\n        console.log('✅ Message sent successfully:', message);\n        // Ajouter le message à la liste s'il n'y est pas déjà\n        const messageExists = this.messages.some(msg => msg.id === message.id);\n        if (!messageExists) {\n          this.messages.push(message);\n          console.log('📋 Message added to local list, total:', this.messages.length);\n        }\n        // Réinitialiser le formulaire\n        this.messageForm.reset();\n        this.isSendingMessage = false;\n        this.updateInputState();\n        // Forcer la détection de changements et scroll\n        this.cdr.detectChanges();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 50);\n      },\n      error: error => {\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        this.isSendingMessage = false;\n        this.updateInputState();\n      }\n    });\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const diffMins = Math.floor((Date.now() - new Date(lastActive).getTime()) / 60000);\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\n  }\n  // Méthodes utilitaires pour les messages vocaux\n  getVoicePlaybackData(messageId) {\n    return this.voicePlayback[messageId] || {\n      progress: 0,\n      duration: 0,\n      currentTime: 0,\n      speed: 1\n    };\n  }\n  setVoicePlaybackData(messageId, data) {\n    this.voicePlayback[messageId] = {\n      ...this.getVoicePlaybackData(messageId),\n      ...data\n    };\n  }\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // isVoicePlaying, playVoiceMessage, toggleVoicePlayback - définies plus loin\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n  startVideoCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    this.callType = 'VIDEO';\n    this.isInCall = true;\n    console.log('📹 Starting video call with:', this.otherParticipant.username);\n  }\n  startVoiceCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    this.callType = 'AUDIO';\n    this.isInCall = true;\n    console.log('📞 Starting voice call with:', this.otherParticipant.username);\n  }\n  endCall() {\n    this.isInCall = false;\n    this.callType = null;\n    this.activeCall = null;\n    console.log('📞 Call ended');\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // onCallAccepted, onCallRejected - définies plus loin\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n  formatFileSize(bytes) {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  downloadFile(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (fileAttachment?.url) {\n      const link = document.createElement('a');\n      link.href = fileAttachment.url;\n      link.download = fileAttachment.name || 'file';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n  toggleTheme() {\n    const newTheme = this.isDarkMode ? 'light' : 'dark';\n    this.selectTheme(newTheme);\n  }\n  toggleSearch() {\n    this.searchMode = !this.searchMode;\n    this.showSearch = this.searchMode;\n  }\n  toggleMainMenu() {\n    this.showMainMenu = !this.showMainMenu;\n  }\n  goBackToConversations() {\n    // Navigation vers la liste des conversations\n    console.log('🔙 Going back to conversations');\n  }\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.showReactionPicker = false;\n    this.showThemeSelector = false;\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    this.selectedMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showMessageContextMenu = true;\n  }\n  showQuickReactions(message, event) {\n    event.stopPropagation();\n    this.reactionPickerMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showReactionPicker = true;\n  }\n  quickReact(emoji) {\n    if (this.reactionPickerMessage) {\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\n    }\n    this.showReactionPicker = false;\n  }\n  toggleReaction(messageId, emoji) {\n    console.log('👍 Toggling reaction:', emoji, 'for message:', messageId);\n    // Implémentation de la réaction\n  }\n\n  hasUserReacted(reaction, userId) {\n    return reaction.userId === userId;\n  }\n  replyToMessage(message) {\n    console.log('↩️ Replying to message:', message.id);\n    this.closeAllMenus();\n  }\n  forwardMessage(message) {\n    console.log('➡️ Forwarding message:', message.id);\n    this.closeAllMenus();\n  }\n  deleteMessage(message) {\n    console.log('🗑️ Deleting message:', message.id);\n    this.closeAllMenus();\n  }\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n  selectEmojiCategory(category) {\n    this.selectedEmojiCategory = category;\n  }\n  getEmojisForCategory(category) {\n    return category?.emojis || [];\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.showEmojiPicker = false;\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODES POUR LA GESTION DE LA FRAPPE ===\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // handleTypingIndicator - définie plus loin\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n  trackByMessageId(index, message) {\n    return message.id || message._id || index.toString();\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  testAddMessage() {\n    console.log('🧪 Test: Adding message');\n    const testMessage = {\n      id: `test-${Date.now()}`,\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\n      timestamp: new Date().toISOString(),\n      sender: {\n        id: this.otherParticipant?.id || 'test-user',\n        username: this.otherParticipant?.username || 'Test User',\n        image: this.otherParticipant?.image || 'assets/images/default-avatar.png'\n      },\n      type: 'TEXT',\n      isRead: false\n    };\n    this.messages.push(testMessage);\n    this.cdr.detectChanges();\n    setTimeout(() => this.scrollToBottom(), 50);\n  }\n  isGroupConversation() {\n    return this.conversation?.isGroup || this.conversation?.participants?.length > 2 || false;\n  }\n  openCamera() {\n    console.log('📷 Opening camera');\n    this.showAttachmentMenu = false;\n    // TODO: Implémenter l'ouverture de la caméra\n  }\n\n  zoomImage(factor) {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      const currentTransform = imageElement.style.transform || 'scale(1)';\n      const currentScale = parseFloat(currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1');\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n      imageElement.style.transform = `scale(${newScale})`;\n      if (newScale > 1) {\n        imageElement.classList.add('zoomed');\n      } else {\n        imageElement.classList.remove('zoomed');\n      }\n    }\n  }\n  resetZoom() {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      imageElement.style.transform = 'scale(1)';\n      imageElement.classList.remove('zoomed');\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  triggerFileInput(type) {\n    const input = this.fileInput?.nativeElement;\n    if (!input) {\n      console.error('File input element not found');\n      return;\n    }\n    // Configurer le type de fichier accepté\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    } else {\n      input.accept = '*/*';\n    }\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\n    input.value = '';\n    // Déclencher la sélection de fichier\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n  formatMessageContent(content) {\n    if (!content) return '';\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(urlRegex, '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>');\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  shouldShowAvatar(index) {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    if (!nextMessage) return true;\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n  shouldShowSenderName(index) {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!previousMessage) return true;\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n  getMessageType(message) {\n    // Vérifier d'abord le type de message explicite\n    if (message.type) {\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\n    }\n    // Ensuite vérifier les attachments\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    // Vérifier si c'est un message vocal basé sur les propriétés\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n    return 'text';\n  }\n  hasImage(message) {\n    // Vérifier le type de message\n    if (message.type === 'IMAGE' || message.type === 'image') {\n      return true;\n    }\n    // Vérifier les attachments\n    const hasImageAttachment = message.attachments?.some(att => {\n      return att.type?.startsWith('image/') || att.type === 'IMAGE';\n    }) || false;\n    // Vérifier les propriétés directes d'image\n    const hasImageUrl = !!(message.imageUrl || message.image);\n    return hasImageAttachment || hasImageUrl;\n  }\n  hasFile(message) {\n    // Vérifier le type de message\n    if (message.type === 'FILE' || message.type === 'file') {\n      return true;\n    }\n    // Vérifier les attachments non-image\n    const hasFileAttachment = message.attachments?.some(att => {\n      return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n    }) || false;\n    return hasFileAttachment;\n  }\n  getImageUrl(message) {\n    // Vérifier les propriétés directes d'image\n    if (message.imageUrl) {\n      return message.imageUrl;\n    }\n    if (message.image) {\n      return message.image;\n    }\n    // Vérifier les attachments\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/') || att.type === 'IMAGE');\n    if (imageAttachment) {\n      return imageAttachment.url || imageAttachment.path || '';\n    }\n    return '';\n  }\n  getFileName(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    return fileAttachment?.name || 'Fichier';\n  }\n  getFileSize(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.size) return '';\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  getFileIcon(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.type) return 'fas fa-file';\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n  getUserColor(userId) {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message, event) {\n    console.log('Message clicked:', message);\n  }\n  onInputChange(event) {\n    // Gérer les changements dans le champ de saisie\n    this.handleTypingIndicator();\n  }\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  onInputFocus() {\n    // Gérer le focus sur le champ de saisie\n  }\n  onInputBlur() {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n  onScroll(event) {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {\n      this.loadMoreMessages();\n    }\n  }\n  openUserProfile(userId) {\n    console.log('Opening user profile for:', userId);\n  }\n  onImageLoad(event, message) {\n    console.log('🖼️ [Debug] Image loaded successfully for message:', message.id, event.target.src);\n  }\n  onImageError(event, message) {\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n      src: event.target.src,\n      error: event\n    });\n    // Optionnel : afficher une image de remplacement\n    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n  }\n  openImageViewer(message) {\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));\n    if (imageAttachment?.url) {\n      this.selectedImage = {\n        url: imageAttachment.url,\n        name: imageAttachment.name || 'Image',\n        size: this.formatFileSize(imageAttachment.size || 0),\n        message: message\n      };\n      this.showImageViewer = true;\n      console.log('🖼️ [ImageViewer] Opening image:', this.selectedImage);\n    }\n  }\n  closeImageViewer() {\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    console.log('🖼️ [ImageViewer] Closed');\n  }\n  downloadImage() {\n    if (this.selectedImage?.url) {\n      const link = document.createElement('a');\n      link.href = this.selectedImage.url;\n      link.download = this.selectedImage.name || 'image';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n      console.log('🖼️ [ImageViewer] Download started:', this.selectedImage.name);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  searchMessages() {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n    this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()) || message.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n  onSearchQueryChange() {\n    this.searchMessages();\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n  }\n  jumpToMessage(messageId) {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      // Highlight temporairement le message\n      messageElement.classList.add('highlight');\n      setTimeout(() => {\n        messageElement.classList.remove('highlight');\n      }, 2000);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  closeContextMenu() {\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // triggerFileInput - définie plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n  // goBackToConversations, startVideoCall, startVoiceCall\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  initiateCall(callType) {\n    if (!this.otherParticipant) {\n      this.toastService.showError('Aucun destinataire sélectionné');\n      return;\n    }\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n    if (!recipientId) {\n      this.toastService.showError('ID du destinataire introuvable');\n      return;\n    }\n    console.log(`🔄 Initiating ${callType} call to user:`, recipientId);\n    this.isInCall = true;\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n    this.callDuration = 0;\n    // Démarrer le timer d'appel\n    this.startCallTimer();\n    // Utiliser le vrai service WebRTC\n    this.MessageService.initiateCall(recipientId, callType, this.conversation?.id).subscribe({\n      next: call => {\n        console.log('✅ Call initiated successfully:', call);\n        this.activeCall = call;\n        this.isCallConnected = false;\n        this.toastService.showSuccess(`Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`);\n      },\n      error: error => {\n        console.error('❌ Error initiating call:', error);\n        this.endCall();\n        this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n      }\n    });\n  }\n  acceptCall(incomingCall) {\n    console.log('🔄 Accepting incoming call:', incomingCall);\n    this.MessageService.acceptCall(incomingCall).subscribe({\n      next: call => {\n        console.log('✅ Call accepted successfully:', call);\n        this.activeCall = call;\n        this.isInCall = true;\n        this.isCallConnected = true;\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n        this.startCallTimer();\n        this.toastService.showSuccess('Appel accepté');\n      },\n      error: error => {\n        console.error('❌ Error accepting call:', error);\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\n      }\n    });\n  }\n  rejectCall(incomingCall) {\n    console.log('🔄 Rejecting incoming call:', incomingCall);\n    this.MessageService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n      next: () => {\n        console.log('✅ Call rejected successfully');\n        this.toastService.showSuccess('Appel rejeté');\n      },\n      error: error => {\n        console.error('❌ Error rejecting call:', error);\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n      }\n    });\n  }\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // endCall - Cette méthode est déjà définie plus loin dans le fichier\n  startCallTimer() {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n  resetCallState() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n  }\n  // === CONTRÔLES D'APPEL ===\n  toggleMute() {\n    if (!this.activeCall) return;\n    this.isMuted = !this.isMuted;\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(this.activeCall.id, undefined,\n    // video unchanged\n    !this.isMuted // audio state\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isMuted ? 'Micro coupé' : 'Micro activé');\n      },\n      error: error => {\n        console.error('❌ Error toggling mute:', error);\n        // Revert state on error\n        this.isMuted = !this.isMuted;\n        this.toastService.showError('Erreur lors du changement du micro');\n      }\n    });\n  }\n  toggleVideo() {\n    if (!this.activeCall) return;\n    this.isVideoEnabled = !this.isVideoEnabled;\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(this.activeCall.id, this.isVideoEnabled,\n    // video state\n    undefined // audio unchanged\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      error: error => {\n        console.error('❌ Error toggling video:', error);\n        // Revert state on error\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.toastService.showError('Erreur lors du changement de la caméra');\n      }\n    });\n  }\n  formatCallDuration(duration) {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor(duration % 3600 / 60);\n    const seconds = duration % 60;\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎤 [Voice] Starting voice recording...');\n      try {\n        // Vérifier le support du navigateur\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n          throw new Error(\"Votre navigateur ne supporte pas l'enregistrement audio\");\n        }\n        // Vérifier si MediaRecorder est supporté\n        if (!window.MediaRecorder) {\n          throw new Error(\"MediaRecorder n'est pas supporté par votre navigateur\");\n        }\n        console.log('🎤 [Voice] Requesting microphone access...');\n        // Demander l'accès au microphone avec des contraintes optimisées\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true,\n            sampleRate: 44100,\n            channelCount: 1\n          }\n        });\n        console.log('🎤 [Voice] Microphone access granted');\n        // Vérifier les types MIME supportés\n        let mimeType = 'audio/webm;codecs=opus';\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\n          mimeType = 'audio/webm';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = 'audio/mp4';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n              mimeType = ''; // Laisser le navigateur choisir\n            }\n          }\n        }\n\n        console.log('🎤 [Voice] Using MIME type:', mimeType);\n        // Créer le MediaRecorder\n        _this.mediaRecorder = new MediaRecorder(stream, {\n          mimeType: mimeType || undefined\n        });\n        // Initialiser les variables\n        _this.audioChunks = [];\n        _this.isRecordingVoice = true;\n        _this.voiceRecordingDuration = 0;\n        _this.voiceRecordingState = 'recording';\n        console.log('🎤 [Voice] MediaRecorder created, starting timer...');\n        // Démarrer le timer\n        _this.recordingTimer = setInterval(() => {\n          _this.voiceRecordingDuration++;\n          // Animer les waves\n          _this.animateVoiceWaves();\n          _this.cdr.detectChanges();\n        }, 1000);\n        // Gérer les événements du MediaRecorder\n        _this.mediaRecorder.ondataavailable = event => {\n          console.log('🎤 [Voice] Data available:', event.data.size, 'bytes');\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        _this.mediaRecorder.onstop = () => {\n          console.log('🎤 [Voice] MediaRecorder stopped, processing audio...');\n          _this.processRecordedAudio();\n        };\n        _this.mediaRecorder.onerror = event => {\n          console.error('🎤 [Voice] MediaRecorder error:', event.error);\n          _this.toastService.showError(\"Erreur lors de l'enregistrement\");\n          _this.cancelVoiceRecording();\n        };\n        // Démarrer l'enregistrement\n        _this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n        console.log('🎤 [Voice] Recording started successfully');\n        _this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n      } catch (error) {\n        console.error('🎤 [Voice] Error starting recording:', error);\n        let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n        if (error.name === 'NotAllowedError') {\n          errorMessage = \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n        } else if (error.name === 'NotFoundError') {\n          errorMessage = 'Aucun microphone détecté. Veuillez connecter un microphone.';\n        } else if (error.name === 'NotSupportedError') {\n          errorMessage = \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n        _this.toastService.showError(errorMessage);\n        _this.cancelVoiceRecording();\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n  cancelVoiceRecording() {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      this.mediaRecorder = null;\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n  processRecordedAudio() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎤 [Voice] Processing recorded audio...');\n      try {\n        // Vérifier qu'on a des données audio\n        if (_this2.audioChunks.length === 0) {\n          console.error('🎤 [Voice] No audio chunks available');\n          _this2.toastService.showError('Aucun audio enregistré');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        console.log('🎤 [Voice] Audio chunks:', _this2.audioChunks.length, 'Duration:', _this2.voiceRecordingDuration);\n        // Vérifier la durée minimale\n        if (_this2.voiceRecordingDuration < 1) {\n          console.error('🎤 [Voice] Recording too short:', _this2.voiceRecordingDuration);\n          _this2.toastService.showError('Enregistrement trop court (minimum 1 seconde)');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        // Déterminer le type MIME du blob\n        let mimeType = 'audio/webm;codecs=opus';\n        if (_this2.mediaRecorder?.mimeType) {\n          mimeType = _this2.mediaRecorder.mimeType;\n        }\n        console.log('🎤 [Voice] Creating audio blob with MIME type:', mimeType);\n        // Créer le blob audio\n        const audioBlob = new Blob(_this2.audioChunks, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio blob created:', {\n          size: audioBlob.size,\n          type: audioBlob.type\n        });\n        // Déterminer l'extension du fichier\n        let extension = '.webm';\n        if (mimeType.includes('mp4')) {\n          extension = '.mp4';\n        } else if (mimeType.includes('wav')) {\n          extension = '.wav';\n        } else if (mimeType.includes('ogg')) {\n          extension = '.ogg';\n        }\n        // Créer le fichier\n        const audioFile = new File([audioBlob], `voice_${Date.now()}${extension}`, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio file created:', {\n          name: audioFile.name,\n          size: audioFile.size,\n          type: audioFile.type\n        });\n        // Envoyer le message vocal\n        _this2.voiceRecordingState = 'processing';\n        yield _this2.sendVoiceMessage(audioFile);\n        console.log('🎤 [Voice] Voice message sent successfully');\n        _this2.toastService.showSuccess('🎤 Message vocal envoyé');\n      } catch (error) {\n        console.error('🎤 [Voice] Error processing audio:', error);\n        _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal: \" + (error.message || 'Erreur inconnue'));\n      } finally {\n        // Nettoyer l'état\n        _this2.voiceRecordingState = 'idle';\n        _this2.voiceRecordingDuration = 0;\n        _this2.audioChunks = [];\n        _this2.isRecordingVoice = false;\n        console.log('🎤 [Voice] Audio processing completed, state reset');\n      }\n    })();\n  }\n  sendVoiceMessage(audioFile) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const receiverId = _this3.otherParticipant?.id || _this3.otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this3.MessageService.sendMessage(receiverId, '', audioFile, 'AUDIO', _this3.conversation.id).subscribe({\n          next: message => {\n            _this3.messages.push(message);\n            _this3.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n  onRecordStart(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record start triggered');\n    console.log('🎤 [Voice] Current state:', {\n      isRecordingVoice: this.isRecordingVoice,\n      voiceRecordingState: this.voiceRecordingState,\n      voiceRecordingDuration: this.voiceRecordingDuration,\n      mediaRecorder: !!this.mediaRecorder\n    });\n    // Vérifier si on peut enregistrer\n    if (this.voiceRecordingState === 'processing') {\n      console.log('🎤 [Voice] Already processing, ignoring start');\n      this.toastService.showWarning('Traitement en cours...');\n      return;\n    }\n    if (this.isRecordingVoice) {\n      console.log('🎤 [Voice] Already recording, ignoring start');\n      this.toastService.showWarning('Enregistrement déjà en cours...');\n      return;\n    }\n    // Afficher un message de début\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n    // Démarrer l'enregistrement\n    this.startVoiceRecording().catch(error => {\n      console.error('🎤 [Voice] Failed to start recording:', error);\n      this.toastService.showError(\"Impossible de démarrer l'enregistrement vocal: \" + (error.message || 'Erreur inconnue'));\n    });\n  }\n  onRecordEnd(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record end triggered');\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring end');\n      return;\n    }\n    // Arrêter l'enregistrement et envoyer\n    this.stopVoiceRecording();\n  }\n  onRecordCancel(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record cancel triggered');\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring cancel');\n      return;\n    }\n    // Annuler l'enregistrement\n    this.cancelVoiceRecording();\n  }\n  getRecordingFormat() {\n    if (this.mediaRecorder?.mimeType) {\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n    }\n    return 'Auto';\n  }\n  // === ANIMATION DES WAVES VOCALES ===\n  animateVoiceWaves() {\n    // Animer les waves pendant l'enregistrement\n    this.voiceWaves = this.voiceWaves.map(() => {\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n    });\n  }\n\n  onFileSelected(event) {\n    console.log('📁 [Upload] File selection triggered');\n    const files = event.target.files;\n    if (!files || files.length === 0) {\n      console.log('📁 [Upload] No files selected');\n      return;\n    }\n    console.log(`📁 [Upload] ${files.length} file(s) selected:`, files);\n    for (let file of files) {\n      console.log(`📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`);\n      this.uploadFile(file);\n    }\n  }\n  uploadFile(file) {\n    console.log(`📁 [Upload] Starting upload for file: ${file.name}`);\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      console.error('📁 [Upload] No receiver ID found');\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    console.log(`📁 [Upload] Receiver ID: ${receiverId}`);\n    // Vérifier la taille du fichier (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\n      return;\n    }\n    // 🖼️ Compression d'image si nécessaire\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n      // > 1MB\n      console.log('🖼️ [Compression] Compressing image:', file.name, 'Original size:', file.size);\n      this.compressImage(file).then(compressedFile => {\n        console.log('🖼️ [Compression] ✅ Image compressed successfully. New size:', compressedFile.size);\n        this.sendFileToServer(compressedFile, receiverId);\n      }).catch(error => {\n        console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n        // Envoyer le fichier original en cas d'erreur\n        this.sendFileToServer(file, receiverId);\n      });\n      return;\n    }\n    // Envoyer le fichier sans compression\n    this.sendFileToServer(file, receiverId);\n  }\n  sendFileToServer(file, receiverId) {\n    const messageType = this.getFileMessageType(file);\n    console.log(`📁 [Upload] Message type determined: ${messageType}`);\n    console.log(`📁 [Upload] Conversation ID: ${this.conversation.id}`);\n    this.isSendingMessage = true;\n    this.isUploading = true;\n    this.uploadProgress = 0;\n    console.log('📁 [Upload] Calling MessageService.sendMessage...');\n    // Simuler la progression d'upload\n    const progressInterval = setInterval(() => {\n      this.uploadProgress += Math.random() * 15;\n      if (this.uploadProgress >= 90) {\n        clearInterval(progressInterval);\n      }\n      this.cdr.detectChanges();\n    }, 300);\n    this.MessageService.sendMessage(receiverId, '', file, messageType, this.conversation.id).subscribe({\n      next: message => {\n        console.log('📁 [Upload] ✅ File sent successfully:', message);\n        console.log('📁 [Debug] Sent message structure:', {\n          id: message.id,\n          type: message.type,\n          attachments: message.attachments,\n          hasImage: this.hasImage(message),\n          hasFile: this.hasFile(message),\n          imageUrl: this.getImageUrl(message)\n        });\n        clearInterval(progressInterval);\n        this.uploadProgress = 100;\n        setTimeout(() => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Fichier envoyé avec succès');\n          this.resetUploadState();\n        }, 500);\n      },\n      error: error => {\n        console.error('📁 [Upload] ❌ Error sending file:', error);\n        clearInterval(progressInterval);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n        this.resetUploadState();\n      }\n    });\n  }\n  getFileMessageType(file) {\n    if (file.type.startsWith('image/')) return 'IMAGE';\n    if (file.type.startsWith('video/')) return 'VIDEO';\n    if (file.type.startsWith('audio/')) return 'AUDIO';\n    return 'FILE';\n  }\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  resetUploadState() {\n    this.isSendingMessage = false;\n    this.isUploading = false;\n    this.uploadProgress = 0;\n  }\n  // === DRAG & DROP ===\n  onDragOver(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n  onDragLeave(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\n    const rect = event.currentTarget.getBoundingClientRect();\n    const x = event.clientX;\n    const y = event.clientY;\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      this.isDragOver = false;\n    }\n  }\n  onDrop(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      console.log('📁 [Drag&Drop] Files dropped:', files.length);\n      // Traiter chaque fichier\n      Array.from(files).forEach(file => {\n        console.log('📁 [Drag&Drop] Processing file:', file.name, file.type, file.size);\n        this.uploadFile(file);\n      });\n      this.toastService.showSuccess(`${files.length} fichier(s) en cours d'envoi`);\n    }\n  }\n  // === COMPRESSION D'IMAGES ===\n  compressImage(file, quality = 0.8) {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n      img.onload = () => {\n        // Calculer les nouvelles dimensions (max 1920x1080)\n        const maxWidth = 1920;\n        const maxHeight = 1080;\n        let {\n          width,\n          height\n        } = img;\n        if (width > maxWidth || height > maxHeight) {\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\n          width *= ratio;\n          height *= ratio;\n        }\n        canvas.width = width;\n        canvas.height = height;\n        // Dessiner l'image redimensionnée\n        ctx?.drawImage(img, 0, 0, width, height);\n        // Convertir en blob avec compression\n        canvas.toBlob(blob => {\n          if (blob) {\n            const compressedFile = new File([blob], file.name, {\n              type: file.type,\n              lastModified: Date.now()\n            });\n            resolve(compressedFile);\n          } else {\n            reject(new Error('Failed to compress image'));\n          }\n        }, file.type, quality);\n      };\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  }\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n  handleTypingIndicator() {\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\n      this.sendTypingIndicator(true);\n    }\n    // Reset le timer\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Arrêter l'indicateur de frappe\n      this.sendTypingIndicator(false);\n    }, 2000);\n  }\n  sendTypingIndicator(isTyping) {\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (receiverId && this.conversation?.id) {\n      console.log(`📝 Sending typing indicator: ${isTyping} to user ${receiverId}`);\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n  onCallAccepted(call) {\n    console.log('🔄 Call accepted from interface:', call);\n    this.activeCall = call;\n    this.isInCall = true;\n    this.isCallConnected = true;\n    this.startCallTimer();\n    this.toastService.showSuccess('Appel accepté');\n  }\n  onCallRejected() {\n    console.log('🔄 Call rejected from interface');\n    this.endCall();\n    this.toastService.showInfo('Appel rejeté');\n  }\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n  playVoiceMessage(message) {\n    console.log('🎵 [Voice] Playing voice message:', message.id);\n    this.toggleVoicePlayback(message);\n  }\n  isVoicePlaying(messageId) {\n    return this.playingMessageId === messageId;\n  }\n  toggleVoicePlayback(message) {\n    const messageId = message.id;\n    const audioUrl = this.getVoiceUrl(message);\n    if (!audioUrl) {\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\n      this.toastService.showError('Fichier audio introuvable');\n      return;\n    }\n    // Si c'est déjà en cours de lecture, arrêter\n    if (this.isVoicePlaying(messageId)) {\n      this.stopVoicePlayback();\n      return;\n    }\n    // Arrêter toute autre lecture en cours\n    this.stopVoicePlayback();\n    // Démarrer la nouvelle lecture\n    this.startVoicePlayback(message, audioUrl);\n  }\n  startVoicePlayback(message, audioUrl) {\n    const messageId = message.id;\n    try {\n      console.log('🎵 [Voice] Starting playback for:', messageId, 'URL:', audioUrl);\n      this.currentAudio = new Audio(audioUrl);\n      this.playingMessageId = messageId;\n      // Initialiser les valeurs par défaut avec la nouvelle structure\n      const currentData = this.getVoicePlaybackData(messageId);\n      this.setVoicePlaybackData(messageId, {\n        progress: 0,\n        currentTime: 0,\n        speed: currentData.speed || 1,\n        duration: currentData.duration || 0\n      });\n      // Configurer la vitesse de lecture\n      this.currentAudio.playbackRate = currentData.speed || 1;\n      // Événements audio\n      this.currentAudio.addEventListener('loadedmetadata', () => {\n        if (this.currentAudio) {\n          this.setVoicePlaybackData(messageId, {\n            duration: this.currentAudio.duration\n          });\n          console.log('🎵 [Voice] Audio loaded, duration:', this.currentAudio.duration);\n        }\n      });\n      this.currentAudio.addEventListener('timeupdate', () => {\n        if (this.currentAudio && this.playingMessageId === messageId) {\n          const currentTime = this.currentAudio.currentTime;\n          const progress = currentTime / this.currentAudio.duration * 100;\n          this.setVoicePlaybackData(messageId, {\n            currentTime,\n            progress\n          });\n          this.cdr.detectChanges();\n        }\n      });\n      this.currentAudio.addEventListener('ended', () => {\n        console.log('🎵 [Voice] Playback ended for:', messageId);\n        this.stopVoicePlayback();\n      });\n      this.currentAudio.addEventListener('error', error => {\n        console.error('🎵 [Voice] Audio error:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      });\n      // Démarrer la lecture\n      this.currentAudio.play().then(() => {\n        console.log('🎵 [Voice] Playback started successfully');\n        this.toastService.showSuccess('🎵 Lecture du message vocal');\n      }).catch(error => {\n        console.error('🎵 [Voice] Error starting playback:', error);\n        this.toastService.showError('Impossible de lire le message vocal');\n        this.stopVoicePlayback();\n      });\n    } catch (error) {\n      console.error('🎵 [Voice] Error creating audio:', error);\n      this.toastService.showError('Erreur lors de la lecture audio');\n      this.stopVoicePlayback();\n    }\n  }\n  stopVoicePlayback() {\n    if (this.currentAudio) {\n      console.log('🎵 [Voice] Stopping playback for:', this.playingMessageId);\n      this.currentAudio.pause();\n      this.currentAudio.currentTime = 0;\n      this.currentAudio = null;\n    }\n    this.playingMessageId = null;\n    this.cdr.detectChanges();\n  }\n  getVoiceUrl(message) {\n    // Vérifier les propriétés directes d'audio\n    if (message.voiceUrl) return message.voiceUrl;\n    if (message.audioUrl) return message.audioUrl;\n    if (message.voice) return message.voice;\n    // Vérifier les attachments audio\n    const audioAttachment = message.attachments?.find(att => att.type?.startsWith('audio/') || att.type === 'AUDIO');\n    if (audioAttachment) {\n      return audioAttachment.url || audioAttachment.path || '';\n    }\n    return '';\n  }\n  getVoiceWaves(message) {\n    // Générer des waves basées sur l'ID du message pour la cohérence\n    const messageId = message.id || '';\n    const seed = messageId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);\n    const waves = [];\n    for (let i = 0; i < 16; i++) {\n      const height = 4 + (seed + i * 7) % 20;\n      waves.push(height);\n    }\n    return waves;\n  }\n  getVoiceProgress(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const totalWaves = 16;\n    return Math.floor(data.progress / 100 * totalWaves);\n  }\n  getVoiceCurrentTime(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    return this.formatAudioTime(data.currentTime);\n  }\n  getVoiceDuration(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const duration = data.duration || message.metadata?.duration || 0;\n    if (typeof duration === 'string') {\n      return duration; // Déjà formaté\n    }\n\n    return this.formatAudioTime(duration);\n  }\n  formatAudioTime(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  seekVoiceMessage(message, waveIndex) {\n    const messageId = message.id;\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\n      return;\n    }\n    const totalWaves = 16;\n    const seekPercentage = waveIndex / totalWaves * 100;\n    const seekTime = seekPercentage / 100 * this.currentAudio.duration;\n    this.currentAudio.currentTime = seekTime;\n    console.log('🎵 [Voice] Seeking to:', seekTime, 'seconds');\n  }\n  toggleVoiceSpeed(message) {\n    const messageId = message.id;\n    const data = this.getVoicePlaybackData(messageId);\n    // Cycle entre 1x, 1.5x, 2x\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n    this.setVoicePlaybackData(messageId, {\n      speed: newSpeed\n    });\n    if (this.currentAudio && this.playingMessageId === messageId) {\n      this.currentAudio.playbackRate = newSpeed;\n    }\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n    // Nettoyer les timers\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    // Nettoyer les ressources audio\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream?.getTracks().forEach(track => track.stop());\n    }\n    // Nettoyer la lecture audio\n    this.stopVoicePlayback();\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 55,\n      vars: 49,\n      consts: [[2, \"display\", \"flex\", \"flex-direction\", \"column\", \"height\", \"100vh\", \"background\", \"linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)\", \"color\", \"#1f2937\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"padding\", \"12px 16px\", \"background\", \"#ffffff\", \"border-bottom\", \"1px solid #e5e7eb\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"margin-right\", \"12px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", 2, \"color\", \"#6b7280\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"flex\", \"1\", \"min-width\", \"0\"], [2, \"position\", \"relative\", \"margin-right\", \"12px\"], [\"onmouseover\", \"this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", \"border\", \"2px solid #10b981\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\"], [\"style\", \"\\n            position: absolute;\\n            bottom: 0;\\n            right: 0;\\n            width: 12px;\\n            height: 12px;\\n            background: #10b981;\\n            border: 2px solid #ffffff;\\n            border-radius: 50%;\\n            animation: pulse 2s infinite;\\n          \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"min-width\", \"0\"], [2, \"font-weight\", \"600\", \"color\", \"#111827\", \"margin\", \"0\", \"white-space\", \"nowrap\", \"overflow\", \"hidden\", \"text-overflow\", \"ellipsis\"], [2, \"font-size\", \"14px\", \"color\", \"#6b7280\"], [\"style\", \"display: flex; align-items: center; gap: 4px; color: #10b981\", 4, \"ngIf\"], [4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"title\", \"Appel vid\\u00E9o\", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [\"title\", \"Appel vocal\", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Rechercher\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"title\", \"Menu\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"position\", \"relative\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [\"class\", \"absolute top-16 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-48\", 4, \"ngIf\"], [2, \"flex\", \"1\", \"overflow-y\", \"auto\", \"padding\", \"16px\", \"position\", \"relative\", 3, \"scroll\", \"dragover\", \"dragleave\", \"drop\"], [\"messagesContainer\", \"\"], [\"class\", \"absolute inset-0 bg-green-500 bg-opacity-20 border-2 border-dashed border-green-500 rounded-lg flex items-center justify-center z-50 animate-pulse\", \"style\", \"\\n        backdrop-filter: blur(2px);\\n        background: linear-gradient(\\n          45deg,\\n          rgba(34, 197, 94, 0.1) 0%,\\n          rgba(34, 197, 94, 0.2) 50%,\\n          rgba(34, 197, 94, 0.1) 100%\\n        );\\n        animation: dragShimmer 2s infinite;\\n      \", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center py-8\", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"space-y-2\", 4, \"ngIf\"], [\"class\", \"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2\", 4, \"ngIf\"], [1, \"bg-white\", \"dark:bg-gray-800\", \"border-t\", \"border-gray-200\", \"dark:border-gray-700\", \"p-4\"], [1, \"flex\", \"items-end\", \"gap-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"gap-2\"], [\"type\", \"button\", \"title\", \"\\u00C9mojis\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"type\", \"button\", \"title\", \"Joindre un fichier\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [1, \"flex-1\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", \"rows\", \"1\", \"maxlength\", \"4096\", \"autocomplete\", \"off\", \"spellcheck\", \"true\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-gray-50\", \"dark:bg-gray-700\", \"border\", \"border-gray-200\", \"dark:border-gray-600\", \"rounded-2xl\", \"resize-none\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-green-500\", \"focus:border-transparent\", \"text-gray-900\", \"dark:text-white\", \"placeholder-gray-500\", \"dark:placeholder-gray-400\", 3, \"input\", \"keydown\", \"focus\", \"blur\"], [\"messageTextarea\", \"\"], [\"type\", \"button\", \"title\", \"Maintenir pour enregistrer un message vocal\", 3, \"ngClass\", \"disabled\", \"mousedown\", \"mouseup\", \"mouseleave\", \"touchstart\", \"touchend\", \"touchcancel\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50\", \"title\", \"Envoyer\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4 z-50\", 4, \"ngIf\"], [\"class\", \"absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\", 4, \"ngIf\"], [\"class\", \"absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 1, \"hidden\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"class\", \"fixed bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 p-3\", 3, \"left\", \"top\", 4, \"ngIf\"], [\"class\", \"fixed bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-48\", 3, \"left\", \"top\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-25 z-40\", 3, \"click\", 4, \"ngIf\"], [3, \"isVisible\", \"activeCall\", \"callType\", \"otherParticipant\", \"callEnded\", \"callAccepted\", \"callRejected\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-95 z-50 flex items-center justify-center\", 3, \"click\", 4, \"ngIf\"], [2, \"position\", \"absolute\", \"bottom\", \"0\", \"right\", \"0\", \"width\", \"12px\", \"height\", \"12px\", \"background\", \"#10b981\", \"border\", \"2px solid #ffffff\", \"border-radius\", \"50%\", \"animation\", \"pulse 2s infinite\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", \"color\", \"#10b981\"], [2, \"display\", \"flex\", \"gap\", \"2px\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.1s\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.2s\"], [1, \"absolute\", \"top-16\", \"right-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\", \"min-w-48\"], [1, \"p-2\"], [1, \"w-full\", \"flex\", \"items-center\", \"gap-3\", \"px-3\", \"py-2\", \"rounded-lg\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-left\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"text-blue-500\"], [1, \"text-gray-700\", \"dark:text-gray-300\"], [1, \"w-full\", \"flex\", \"items-center\", \"gap-3\", \"px-3\", \"py-2\", \"rounded-lg\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-left\"], [1, \"fas\", \"fa-user\", \"text-green-500\"], [1, \"fas\", \"fa-bell\", \"text-yellow-500\"], [1, \"my-2\", \"border-gray-200\", \"dark:border-gray-600\"], [1, \"fas\", \"fa-cog\", \"text-gray-500\"], [1, \"absolute\", \"inset-0\", \"bg-green-500\", \"bg-opacity-20\", \"border-2\", \"border-dashed\", \"border-green-500\", \"rounded-lg\", \"flex\", \"items-center\", \"justify-center\", \"z-50\", \"animate-pulse\", 2, \"backdrop-filter\", \"blur(2px)\", \"background\", \"linear-gradient(\\n          45deg,\\n          rgba(34, 197, 94, 0.1) 0%,\\n          rgba(34, 197, 94, 0.2) 50%,\\n          rgba(34, 197, 94, 0.1) 100%\\n        )\", \"animation\", \"dragShimmer 2s infinite\"], [1, \"text-center\", \"bg-white\", \"dark:bg-gray-800\", \"p-6\", \"rounded-xl\", \"shadow-lg\", \"border\", \"border-green-300\", \"dark:border-green-600\"], [1, \"fas\", \"fa-cloud-upload-alt\", \"text-5xl\", \"text-green-600\", \"mb-3\", \"animate-bounce\"], [1, \"text-xl\", \"font-bold\", \"text-green-700\", \"dark:text-green-400\", \"mb-2\"], [1, \"text-sm\", \"text-green-600\", \"dark:text-green-300\"], [1, \"flex\", \"justify-center\", \"gap-2\", \"mt-3\"], [1, \"w-2\", \"h-2\", \"bg-green-500\", \"rounded-full\", \"animate-ping\"], [1, \"w-2\", \"h-2\", \"bg-green-500\", \"rounded-full\", \"animate-ping\", 2, \"animation-delay\", \"0.2s\"], [1, \"w-2\", \"h-2\", \"bg-green-500\", \"rounded-full\", \"animate-ping\", 2, \"animation-delay\", \"0.4s\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-8\", \"w-8\", \"border-b-2\", \"border-green-500\", \"mb-4\"], [1, \"text-gray-500\", \"dark:text-gray-400\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"text-6xl\", \"text-gray-300\", \"dark:text-gray-600\", \"mb-4\"], [1, \"fas\", \"fa-comments\"], [1, \"text-xl\", \"font-semibold\", \"text-gray-700\", \"dark:text-gray-300\", \"mb-2\"], [1, \"text-gray-500\", \"dark:text-gray-400\", \"text-center\"], [1, \"space-y-2\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"flex items-start gap-2\", 4, \"ngIf\"], [\"class\", \"flex justify-center my-4\", 4, \"ngIf\"], [1, \"flex\", 3, \"id\", \"click\", \"contextmenu\"], [\"class\", \"mr-2 flex-shrink-0\", 4, \"ngIf\"], [2, \"max-width\", \"320px\", \"padding\", \"12px 16px\", \"border-radius\", \"18px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\", \"position\", \"relative\", \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\", \"border\", \"none\"], [\"class\", \"text-xs font-semibold mb-1 opacity-75\", 3, \"color\", 4, \"ngIf\"], [\"class\", \"break-words\", 4, \"ngIf\"], [\"style\", \"margin: 8px 0\", 4, \"ngIf\"], [\"class\", \"flex items-center gap-3 p-3 rounded-xl min-w-[250px] max-w-xs cursor-pointer transition-all duration-300 hover:shadow-lg group border bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600\", \"style\", \"position: relative; overflow: hidden\", 3, \"shadow-lg\", \"ring-2\", \"ring-blue-300\", 4, \"ngIf\"], [\"class\", \"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-end\", \"gap-1\", \"mt-1\", \"text-xs\", \"opacity-75\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"flex gap-1 mt-2\", 4, \"ngIf\"], [\"title\", \"Ajouter une r\\u00E9action\", 1, \"absolute\", \"-bottom-2\", \"right-2\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"bg-white\", \"dark:bg-gray-700\", \"border\", \"border-gray-200\", \"dark:border-gray-600\", \"rounded-full\", \"p-1\", \"shadow-sm\", \"hover:shadow-md\", 3, \"click\"], [1, \"fas\", \"fa-smile\", \"text-gray-500\", \"dark:text-gray-400\", \"text-xs\"], [1, \"flex\", \"justify-center\", \"my-4\"], [1, \"bg-white\", \"dark:bg-gray-700\", \"px-3\", \"py-1\", \"rounded-full\", \"shadow-sm\"], [1, \"text-xs\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"mr-2\", \"flex-shrink-0\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", \"cursor-pointer\", \"hover:scale-105\", \"transition-transform\", 3, \"src\", \"alt\", \"click\"], [1, \"text-xs\", \"font-semibold\", \"mb-1\", \"opacity-75\"], [1, \"break-words\"], [3, \"innerHTML\"], [2, \"margin\", \"8px 0\"], [\"onmouseover\", \"this.style.transform='scale(1.02)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"max-width\", \"280px\", \"height\", \"auto\", \"border-radius\", \"12px\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\", \"load\", \"error\"], [\"style\", \"font-size: 14px; margin-top: 8px; line-height: 1.4\", 3, \"color\", \"innerHTML\", 4, \"ngIf\"], [2, \"font-size\", \"14px\", \"margin-top\", \"8px\", \"line-height\", \"1.4\", 3, \"innerHTML\"], [1, \"flex\", \"items-center\", \"gap-3\", \"p-3\", \"rounded-xl\", \"min-w-[250px]\", \"max-w-xs\", \"cursor-pointer\", \"transition-all\", \"duration-300\", \"hover:shadow-lg\", \"group\", \"border\", \"bg-white\", \"dark:bg-gray-700\", \"border-gray-200\", \"dark:border-gray-600\", 2, \"position\", \"relative\", \"overflow\", \"hidden\"], [1, \"p-2\", \"rounded-full\", \"text-white\", \"transition-all\", \"duration-300\", \"flex-shrink-0\", \"border-none\", \"outline-none\", \"cursor-pointer\", 2, \"width\", \"40px\", \"height\", \"40px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"box-shadow\", \"0 2px 8px rgba(0, 0, 0, 0.2)\", 3, \"click\"], [1, \"fas\", \"text-sm\", \"transition-transform\", \"duration-200\"], [1, \"flex-1\", \"min-w-0\"], [1, \"flex\", \"items-center\", \"gap-1\", \"h-8\", \"mb-2\", \"px-1\"], [\"class\", \"w-1 rounded-full transition-all duration-300 cursor-pointer hover:scale-110 hover:opacity-80\", \"style\", \"\\n                      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n                      min-height: 4px;\\n                    \", 3, \"bg-gray-400\", \"bg-blue-500\", \"bg-gray-300\", \"dark:bg-gray-500\", \"height\", \"animate-pulse\", \"transform\", \"box-shadow\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"text-xs\", \"mt-1\"], [1, \"flex\", \"items-center\", \"gap-2\"], [1, \"text-gray-600\", \"dark:text-gray-300\", \"font-mono\", \"text-xs\"], [1, \"text-gray-400\", \"dark:text-gray-500\"], [1, \"text-gray-500\", \"dark:text-gray-400\", \"font-mono\", \"text-xs\"], [1, \"flex\", \"items-center\", \"gap-1\"], [\"class\", \"text-green-600 dark:text-green-400 font-semibold text-xs px-1 py-0.5 bg-green-100 dark:bg-green-900 rounded\", 4, \"ngIf\"], [\"class\", \"flex items-center gap-0.5\", 4, \"ngIf\"], [1, \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"duration-300\", \"flex\", \"flex-col\", \"gap-1\"], [\"title\", \"Vitesse de lecture\", 1, \"p-1.5\", \"rounded-full\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-500\", \"text-gray-500\", \"dark:text-gray-400\", \"transition-all\", \"duration-200\", 2, \"border\", \"none\", \"outline\", \"none\", \"cursor\", \"pointer\", 3, \"click\"], [1, \"fas\", \"fa-tachometer-alt\", \"text-xs\"], [1, \"w-1\", \"rounded-full\", \"transition-all\", \"duration-300\", \"cursor-pointer\", \"hover:scale-110\", \"hover:opacity-80\", 2, \"transition\", \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\", \"min-height\", \"4px\", 3, \"click\"], [1, \"text-green-600\", \"dark:text-green-400\", \"font-semibold\", \"text-xs\", \"px-1\", \"py-0.5\", \"bg-green-100\", \"dark:bg-green-900\", \"rounded\"], [1, \"flex\", \"items-center\", \"gap-0.5\"], [1, \"w-1\", \"h-1\", \"bg-green-500\", \"rounded-full\", \"animate-pulse\"], [1, \"w-1\", \"h-1\", \"bg-green-500\", \"rounded-full\", \"animate-pulse\", 2, \"animation-delay\", \"0.2s\"], [1, \"w-1\", \"h-1\", \"bg-green-500\", \"rounded-full\", \"animate-pulse\", 2, \"animation-delay\", \"0.4s\"], [1, \"flex\", \"items-center\", \"gap-3\", \"p-2\", \"bg-gray-50\", \"dark:bg-gray-600\", \"rounded-lg\", \"cursor-pointer\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-500\", \"transition-colors\", 3, \"click\"], [1, \"text-2xl\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"font-medium\", \"text-sm\", \"truncate\"], [1, \"p-1\", \"rounded-full\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-400\", \"transition-colors\"], [1, \"fas\", \"fa-download\", \"text-sm\"], [1, \"flex\", \"items-center\"], [\"class\", \"fas fa-clock\", \"title\", \"Envoi en cours\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", \"title\", \"Envoy\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"title\", \"Livr\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double text-blue-400\", \"title\", \"Lu\", 4, \"ngIf\"], [\"title\", \"Envoi en cours\", 1, \"fas\", \"fa-clock\"], [\"title\", \"Envoy\\u00E9\", 1, \"fas\", \"fa-check\"], [\"title\", \"Livr\\u00E9\", 1, \"fas\", \"fa-check-double\"], [\"title\", \"Lu\", 1, \"fas\", \"fa-check-double\", \"text-blue-400\"], [1, \"flex\", \"gap-1\", \"mt-2\"], [\"class\", \"flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors\", 3, \"bg-green-100\", \"text-green-600\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"gap-1\", \"px-2\", \"py-1\", \"bg-gray-100\", \"dark:bg-gray-600\", \"rounded-full\", \"text-xs\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-500\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-start\", \"gap-2\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"bg-white\", \"dark:bg-gray-700\", \"px-4\", \"py-2\", \"rounded-2xl\", \"shadow-sm\"], [1, \"flex\", \"gap-1\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [1, \"bg-white\", \"dark:bg-gray-800\", \"border-t\", \"border-gray-200\", \"dark:border-gray-700\", \"px-4\", \"py-2\"], [1, \"flex\", \"items-center\", \"gap-3\"], [1, \"flex\", \"justify-between\", \"text-sm\", \"text-gray-600\", \"dark:text-gray-400\", \"mb-1\"], [1, \"w-full\", \"bg-gray-200\", \"dark:bg-gray-700\", \"rounded-full\", \"h-2\"], [1, \"bg-green-500\", \"h-2\", \"rounded-full\", \"transition-all\", \"duration-300\"], [\"title\", \"Annuler\", 1, \"p-1\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-500\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-sm\"], [\"type\", \"button\", \"title\", \"Maintenir pour enregistrer un message vocal\", 3, \"ngClass\", \"disabled\", \"mousedown\", \"mouseup\", \"mouseleave\", \"touchstart\", \"touchend\", \"touchcancel\"], [\"class\", \"fas fa-microphone\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner\", 4, \"ngIf\"], [1, \"fas\", \"fa-microphone\"], [1, \"fas\", \"fa-spinner\"], [\"type\", \"button\", \"title\", \"Envoyer\", 1, \"p-3\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"text-white\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"absolute\", \"bottom-0\", \"left-0\", \"right-0\", \"bg-white\", \"dark:bg-gray-800\", \"border-t\", \"border-gray-200\", \"dark:border-gray-700\", \"p-4\", \"z-50\"], [1, \"flex\", \"items-center\", \"gap-4\"], [\"title\", \"Annuler\", 1, \"p-2\", \"rounded-full\", \"bg-red-100\", \"dark:bg-red-900\", \"text-red-600\", \"dark:text-red-400\", \"hover:bg-red-200\", \"dark:hover:bg-red-800\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"flex\", \"items-center\", \"gap-3\", \"flex-1\"], [1, \"relative\"], [1, \"fas\", \"fa-microphone\", \"text-red-500\", \"text-xl\", \"animate-pulse\"], [1, \"absolute\", \"-top-1\", \"-right-1\", \"w-3\", \"h-3\", \"bg-red-500\", \"rounded-full\", \"animate-ping\"], [\"class\", \"bg-green-500 rounded-full transition-all duration-150\", 3, \"width\", \"height\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-gray-600\", \"dark:text-gray-300\", \"font-mono\"], [\"title\", \"Envoyer l'enregistrement\", 1, \"p-3\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"text-white\", \"transition-colors\", 3, \"click\"], [1, \"mt-3\", \"w-full\", \"bg-gray-200\", \"dark:bg-gray-700\", \"rounded-full\", \"h-1\"], [1, \"bg-green-500\", \"h-1\", \"rounded-full\", \"transition-all\", \"duration-300\"], [1, \"mt-2\", \"text-center\", \"text-sm\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"flex\", \"items-center\", \"justify-center\", \"gap-2\"], [1, \"fas\", \"fa-info-circle\"], [1, \"mt-1\", \"text-xs\"], [1, \"bg-green-500\", \"rounded-full\", \"transition-all\", \"duration-150\"], [1, \"absolute\", \"bottom-20\", \"left-4\", \"right-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\"], [1, \"p-4\"], [1, \"flex\", \"gap-2\", \"mb-4\", \"overflow-x-auto\"], [\"class\", \"px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0\", 3, \"bg-green-100\", \"text-green-600\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\", \"grid-cols-8\", \"gap-2\", \"max-h-48\", \"overflow-y-auto\"], [\"class\", \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"px-3\", \"py-2\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"transition-colors\", \"flex-shrink-0\", 3, \"click\"], [1, \"p-2\", \"rounded-lg\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-xl\", 3, \"title\", \"click\"], [1, \"absolute\", \"bottom-20\", \"left-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\"], [1, \"grid\", \"grid-cols-2\", \"gap-3\"], [1, \"flex\", \"flex-col\", \"items-center\", \"gap-2\", \"p-4\", \"rounded-xl\", \"hover:bg-gray-50\", \"dark:hover:bg-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"w-12\", \"h-12\", \"bg-blue-100\", \"dark:bg-blue-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-image\", \"text-blue-600\", \"dark:text-blue-400\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\", \"dark:text-gray-300\"], [1, \"w-12\", \"h-12\", \"bg-purple-100\", \"dark:bg-purple-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-video\", \"text-purple-600\", \"dark:text-purple-400\"], [1, \"w-12\", \"h-12\", \"bg-orange-100\", \"dark:bg-orange-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-file\", \"text-orange-600\", \"dark:text-orange-400\"], [1, \"w-12\", \"h-12\", \"bg-green-100\", \"dark:bg-green-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-camera\", \"text-green-600\", \"dark:text-green-400\"], [1, \"fixed\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\", \"p-3\"], [\"class\", \"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-xl\", 3, \"click\"], [1, \"fixed\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\", \"min-w-48\"], [1, \"fas\", \"fa-reply\", \"text-blue-500\"], [1, \"fas\", \"fa-share\", \"text-green-500\"], [1, \"fas\", \"fa-smile\", \"text-yellow-500\"], [1, \"w-full\", \"flex\", \"items-center\", \"gap-3\", \"px-3\", \"py-2\", \"rounded-lg\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-left\", \"text-red-600\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"text-red-500\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-25\", \"z-40\", 3, \"click\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-95\", \"z-50\", \"flex\", \"items-center\", \"justify-center\", 3, \"click\"], [1, \"relative\", \"max-w-full\", \"max-h-full\", \"p-4\"], [\"title\", \"Fermer\", 1, \"absolute\", \"top-4\", \"right-4\", \"z-10\", \"p-2\", \"rounded-full\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"hover:bg-opacity-70\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-xl\"], [\"title\", \"T\\u00E9l\\u00E9charger\", 1, \"absolute\", \"top-4\", \"left-4\", \"z-10\", \"p-2\", \"rounded-full\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"hover:bg-opacity-70\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-download\", \"text-xl\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", \"rounded-lg\", \"shadow-2xl\", \"image-viewer-zoom\", 2, \"max-height\", \"90vh\", \"max-width\", \"90vw\", 3, \"src\", \"alt\", \"click\"], [1, \"absolute\", \"bottom-4\", \"left-4\", \"right-4\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"p-3\", \"rounded-lg\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"font-medium\"], [1, \"text-sm\", \"opacity-75\"], [\"title\", \"Zoom +\", 1, \"p-2\", \"rounded-full\", \"bg-white\", \"bg-opacity-20\", \"hover:bg-opacity-30\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-search-plus\"], [\"title\", \"Zoom -\", 1, \"p-2\", \"rounded-full\", \"bg-white\", \"bg-opacity-20\", \"hover:bg-opacity-30\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-search-minus\"], [\"title\", \"Taille originale\", 1, \"p-2\", \"rounded-full\", \"bg-white\", \"bg-opacity-20\", \"hover:bg-opacity-30\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-expand-arrows-alt\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_2_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"img\", 6);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_img_click_6_listener() {\n            return ctx.openUserProfile(ctx.otherParticipant == null ? null : ctx.otherParticipant.id);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, MessageChatComponent_div_7_Template, 1, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"h3\", 9);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10);\n          i0.ɵɵtemplate(12, MessageChatComponent_div_12_Template, 7, 0, \"div\", 11);\n          i0.ɵɵtemplate(13, MessageChatComponent_span_13_Template, 2, 1, \"span\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_15_listener() {\n            return ctx.startVideoCall();\n          });\n          i0.ɵɵelement(16, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_17_listener() {\n            return ctx.startVoiceCall();\n          });\n          i0.ɵɵelement(18, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_19_listener() {\n            return ctx.toggleSearch();\n          });\n          i0.ɵɵelement(20, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_21_listener() {\n            return ctx.toggleMainMenu();\n          });\n          i0.ɵɵelement(22, \"i\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, MessageChatComponent_div_23_Template, 19, 0, \"div\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"main\", 23, 24);\n          i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_main_scroll_24_listener($event) {\n            return ctx.onScroll($event);\n          })(\"dragover\", function MessageChatComponent_Template_main_dragover_24_listener($event) {\n            return ctx.onDragOver($event);\n          })(\"dragleave\", function MessageChatComponent_Template_main_dragleave_24_listener($event) {\n            return ctx.onDragLeave($event);\n          })(\"drop\", function MessageChatComponent_Template_main_drop_24_listener($event) {\n            return ctx.onDrop($event);\n          });\n          i0.ɵɵtemplate(26, MessageChatComponent_div_26_Template, 11, 0, \"div\", 25);\n          i0.ɵɵtemplate(27, MessageChatComponent_div_27_Template, 4, 0, \"div\", 26);\n          i0.ɵɵtemplate(28, MessageChatComponent_div_28_Template, 7, 1, \"div\", 27);\n          i0.ɵɵtemplate(29, MessageChatComponent_div_29_Template, 3, 3, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(30, MessageChatComponent_div_30_Template, 12, 3, \"div\", 29);\n          i0.ɵɵelementStart(31, \"footer\", 30)(32, \"form\", 31);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_32_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(33, \"div\", 32)(34, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_34_listener() {\n            return ctx.toggleEmojiPicker();\n          });\n          i0.ɵɵelement(35, \"i\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_36_listener() {\n            return ctx.toggleAttachmentMenu();\n          });\n          i0.ɵɵelement(37, \"i\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 37)(39, \"textarea\", 38, 39);\n          i0.ɵɵlistener(\"input\", function MessageChatComponent_Template_textarea_input_39_listener($event) {\n            return ctx.onInputChange($event);\n          })(\"keydown\", function MessageChatComponent_Template_textarea_keydown_39_listener($event) {\n            return ctx.onInputKeyDown($event);\n          })(\"focus\", function MessageChatComponent_Template_textarea_focus_39_listener() {\n            return ctx.onInputFocus();\n          })(\"blur\", function MessageChatComponent_Template_textarea_blur_39_listener() {\n            return ctx.onInputBlur();\n          });\n          i0.ɵɵtext(41, \"        \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 32);\n          i0.ɵɵtemplate(43, MessageChatComponent_button_43_Template, 3, 7, \"button\", 40);\n          i0.ɵɵtemplate(44, MessageChatComponent_button_44_Template, 3, 3, \"button\", 41);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(45, MessageChatComponent_div_45_Template, 23, 5, \"div\", 42);\n          i0.ɵɵtemplate(46, MessageChatComponent_div_46_Template, 6, 2, \"div\", 43);\n          i0.ɵɵtemplate(47, MessageChatComponent_div_47_Template, 23, 0, \"div\", 44);\n          i0.ɵɵelementStart(48, \"input\", 45, 46);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_48_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(50, MessageChatComponent_div_50_Template, 3, 6, \"div\", 47);\n          i0.ɵɵtemplate(51, MessageChatComponent_div_51_Template, 19, 4, \"div\", 48);\n          i0.ɵɵtemplate(52, MessageChatComponent_div_52_Template, 1, 0, \"div\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"app-call-interface\", 50);\n          i0.ɵɵlistener(\"callEnded\", function MessageChatComponent_Template_app_call_interface_callEnded_53_listener() {\n            return ctx.endCall();\n          })(\"callAccepted\", function MessageChatComponent_Template_app_call_interface_callAccepted_53_listener($event) {\n            return ctx.onCallAccepted($event);\n          })(\"callRejected\", function MessageChatComponent_Template_app_call_interface_callRejected_53_listener() {\n            return ctx.onCallRejected();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(54, MessageChatComponent_div_54_Template, 21, 4, \"div\", 51);\n        }\n        if (rf & 2) {\n          let tmp_24_0;\n          let tmp_25_0;\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.otherParticipant == null ? null : ctx.otherParticipant.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.otherParticipant == null ? null : ctx.otherParticipant.username) || \"Utilisateur\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUserTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUserTyping);\n          i0.ɵɵadvance(6);\n          i0.ɵɵstyleProp(\"background\", ctx.searchMode ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.searchMode ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showMainMenu ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showMainMenu ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMainMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background\", ctx.isDragOver ? \"rgba(34, 197, 94, 0.1)\" : \"transparent\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDragOver);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUploading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.showEmojiPicker)(\"text-green-600\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.showAttachmentMenu)(\"text-green-600\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"opacity-50\", ctx.isInputDisabled())(\"cursor-not-allowed\", ctx.isInputDisabled());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !((tmp_24_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_24_0.value == null ? null : tmp_24_0.value.trim()));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_25_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_25_0.value == null ? null : tmp_25_0.value.trim());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"accept\", ctx.getFileAcceptTypes());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showReactionPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessageContextMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker || ctx.showAttachmentMenu || ctx.showMainMenu || ctx.showMessageContextMenu || ctx.showReactionPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"isVisible\", ctx.isInCall)(\"activeCall\", ctx.activeCall)(\"callType\", ctx.callType)(\"otherParticipant\", ctx.otherParticipant);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showImageViewer);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i6.CallInterfaceComponent],\n      styles: [\".dark[_ngcontent-%COMP%]   header[_ngcontent-%COMP%] {\\n        background: #1f2937;\\n        border-color: #374151;\\n      }\\n      .dark[_ngcontent-%COMP%] {\\n        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);\\n        color: #f8fafc;\\n      }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "CallType", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "otherParticipant", "isOnline", "formatLastActive", "lastActive", "ɵɵlistener", "MessageChatComponent_div_23_Template_button_click_2_listener", "ɵɵrestoreView", "_r22", "ctx_r21", "ɵɵnextContext", "toggleSearch", "ɵɵresetView", "showMainMenu", "ctx_r7", "username", "ctx_r27", "formatDateSeparator", "message_r25", "timestamp", "MessageChatComponent_div_29_ng_container_1_div_3_Template_img_click_1_listener", "_r39", "$implicit", "ctx_r37", "openUserProfile", "sender", "id", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "ɵɵstyleProp", "ctx_r29", "getUserColor", "ctx_r30", "formatMessageContent", "content", "ɵɵsanitizeHtml", "ctx_r43", "currentUserId", "MessageChatComponent_div_29_ng_container_1_div_7_Template_img_click_1_listener", "_r47", "ctx_r45", "openImageViewer", "MessageChatComponent_div_29_ng_container_1_div_7_Template_img_load_1_listener", "$event", "ctx_r48", "onImageLoad", "MessageChatComponent_div_29_ng_container_1_div_7_Template_img_error_1_listener", "ctx_r50", "onImageError", "ɵɵtemplate", "MessageChatComponent_div_29_ng_container_1_div_7_div_2_Template", "ctx_r31", "getImageUrl", "MessageChatComponent_div_29_ng_container_1_div_8_div_5_Template_div_click_0_listener", "restoredCtx", "_r60", "i_r57", "index", "ctx_r58", "stopPropagation", "seekVoiceMessage", "wave_r56", "ctx_r53", "isVoicePlaying", "getVoiceProgress", "ɵɵclassProp", "ctx_r54", "getVoicePlaybackData", "speed", "MessageChatComponent_div_29_ng_container_1_div_8_Template_button_click_1_listener", "_r65", "ctx_r63", "toggleVoicePlayback", "MessageChatComponent_div_29_ng_container_1_div_8_div_5_Template", "MessageChatComponent_div_29_ng_container_1_div_8_span_15_Template", "MessageChatComponent_div_29_ng_container_1_div_8_div_16_Template", "MessageChatComponent_div_29_ng_container_1_div_8_Template_button_click_18_listener", "ctx_r66", "toggleVoiceSpeed", "ctx_r32", "getVoiceWaves", "getVoiceCurrentTime", "getVoiceDuration", "MessageChatComponent_div_29_ng_container_1_div_9_Template_div_click_0_listener", "_r71", "ctx_r69", "downloadFile", "ɵɵclassMap", "ctx_r33", "getFileIcon", "getFileName", "getFileSize", "MessageChatComponent_div_29_ng_container_1_div_13_i_1_Template", "MessageChatComponent_div_29_ng_container_1_div_13_i_2_Template", "MessageChatComponent_div_29_ng_container_1_div_13_i_3_Template", "MessageChatComponent_div_29_ng_container_1_div_13_i_4_Template", "status", "MessageChatComponent_div_29_ng_container_1_div_14_button_1_Template_button_click_0_listener", "_r82", "reaction_r79", "ctx_r80", "toggleReaction", "emoji", "ctx_r78", "hasUserReacted", "ɵɵtextInterpolate", "count", "MessageChatComponent_div_29_ng_container_1_div_14_button_1_Template", "reactions", "ɵɵelementContainerStart", "MessageChatComponent_div_29_ng_container_1_div_1_Template", "MessageChatComponent_div_29_ng_container_1_Template_div_click_2_listener", "_r85", "ctx_r84", "onMessageClick", "MessageChatComponent_div_29_ng_container_1_Template_div_contextmenu_2_listener", "ctx_r86", "onMessageContextMenu", "MessageChatComponent_div_29_ng_container_1_div_3_Template", "MessageChatComponent_div_29_ng_container_1_div_5_Template", "MessageChatComponent_div_29_ng_container_1_div_6_Template", "MessageChatComponent_div_29_ng_container_1_div_7_Template", "MessageChatComponent_div_29_ng_container_1_div_8_Template", "MessageChatComponent_div_29_ng_container_1_div_9_Template", "MessageChatComponent_div_29_ng_container_1_div_13_Template", "MessageChatComponent_div_29_ng_container_1_div_14_Template", "MessageChatComponent_div_29_ng_container_1_Template_button_click_15_listener", "ctx_r87", "showQuickReactions", "ɵɵelementContainerEnd", "ctx_r23", "shouldShowDateSeparator", "i_r26", "shouldShowAvatar", "isGroupConversation", "shouldShowSenderName", "getMessageType", "hasImage", "hasFile", "formatMessageTime", "length", "ctx_r24", "MessageChatComponent_div_29_ng_container_1_Template", "MessageChatComponent_div_29_div_2_Template", "ctx_r8", "messages", "trackByMessageId", "otherUserIsTyping", "MessageChatComponent_div_30_Template_button_click_10_listener", "_r89", "ctx_r88", "resetUploadState", "ctx_r9", "uploadProgress", "toFixed", "MessageChatComponent_button_43_Template_button_mousedown_0_listener", "_r93", "ctx_r92", "onRecordStart", "MessageChatComponent_button_43_Template_button_mouseup_0_listener", "ctx_r94", "onRecordEnd", "MessageChatComponent_button_43_Template_button_mouseleave_0_listener", "ctx_r95", "onRecordCancel", "MessageChatComponent_button_43_Template_button_touchstart_0_listener", "ctx_r96", "MessageChatComponent_button_43_Template_button_touchend_0_listener", "ctx_r97", "MessageChatComponent_button_43_Template_button_touchcancel_0_listener", "ctx_r98", "MessageChatComponent_button_43_i_1_Template", "MessageChatComponent_button_43_i_2_Template", "ɵɵpureFunction2", "_c2", "ctx_r11", "isRecordingVoice", "voiceRecordingState", "MessageChatComponent_button_44_Template_button_click_0_listener", "_r102", "ctx_r101", "sendMessage", "MessageChatComponent_button_44_i_1_Template", "MessageChatComponent_button_44_i_2_Template", "ctx_r12", "isSendingMessage", "wave_r104", "i_r105", "MessageChatComponent_div_45_Template_button_click_2_listener", "_r107", "ctx_r106", "cancelVoiceRecording", "MessageChatComponent_div_45_div_9_Template", "MessageChatComponent_div_45_Template_button_click_12_listener", "ctx_r108", "stopVoiceRecording", "ctx_r13", "voiceWaves", "formatRecordingDuration", "voiceRecordingDuration", "getRecordingFormat", "MessageChatComponent_div_46_button_3_Template_button_click_0_listener", "_r113", "category_r111", "ctx_r112", "selectEmojiCategory", "ctx_r109", "selectedEmojiCategory", "icon", "MessageChatComponent_div_46_button_5_Template_button_click_0_listener", "_r116", "emoji_r114", "ctx_r115", "insert<PERSON><PERSON><PERSON>", "name", "MessageChatComponent_div_46_button_3_Template", "MessageChatComponent_div_46_button_5_Template", "ctx_r14", "emojiCategories", "getEmojisForCategory", "MessageChatComponent_div_47_Template_button_click_3_listener", "_r118", "ctx_r117", "triggerFileInput", "MessageChatComponent_div_47_Template_button_click_8_listener", "ctx_r119", "MessageChatComponent_div_47_Template_button_click_13_listener", "ctx_r120", "MessageChatComponent_div_47_Template_button_click_18_listener", "ctx_r121", "openCamera", "MessageChatComponent_div_50_button_2_Template_button_click_0_listener", "_r125", "emoji_r123", "ctx_r124", "quickReact", "MessageChatComponent_div_50_button_2_Template", "ctx_r17", "contextMenuPosition", "x", "y", "ɵɵpureFunction0", "_c3", "MessageChatComponent_div_51_Template_button_click_2_listener", "_r127", "ctx_r126", "replyToMessage", "selectedMessage", "MessageChatComponent_div_51_Template_button_click_6_listener", "ctx_r128", "forwardMessage", "MessageChatComponent_div_51_Template_button_click_10_listener", "ctx_r129", "MessageChatComponent_div_51_Template_button_click_15_listener", "ctx_r130", "deleteMessage", "ctx_r18", "MessageChatComponent_div_52_Template_div_click_0_listener", "_r132", "ctx_r131", "closeAllMenus", "MessageChatComponent_div_54_Template_div_click_0_listener", "_r134", "ctx_r133", "closeImageViewer", "MessageChatComponent_div_54_Template_button_click_2_listener", "ctx_r135", "MessageChatComponent_div_54_Template_button_click_4_listener", "ctx_r136", "downloadImage", "MessageChatComponent_div_54_Template_img_click_6_listener", "MessageChatComponent_div_54_Template_div_click_7_listener", "MessageChatComponent_div_54_Template_button_click_15_listener", "ctx_r139", "zoomImage", "MessageChatComponent_div_54_Template_button_click_17_listener", "ctx_r140", "MessageChatComponent_div_54_Template_button_click_19_listener", "ctx_r141", "resetZoom", "ctx_r20", "selectedImage", "url", "size", "MessageChatComponent", "constructor", "fb", "route", "MessageService", "toastService", "cdr", "conversation", "currentUsername", "isLoading", "isLoadingMore", "hasMoreMessages", "showEmojiPicker", "showAttachmentMenu", "showSearch", "searchQuery", "searchResults", "searchMode", "showMessageContextMenu", "showReactionPicker", "reactionPickerMessage", "showImageViewer", "isUploading", "isDragOver", "mediaRecorder", "audioChunks", "recordingTimer", "currentAudio", "playingMessageId", "voicePlayback", "isInCall", "callType", "callDuration", "callTimer", "activeCall", "isCallConnected", "isMuted", "isVideoEnabled", "localVideoElement", "remoteVideoElement", "emojis", "MAX_MESSAGES_TO_LOAD", "currentPage", "isTyping", "isUserTyping", "typingTimeout", "subscriptions", "messageForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "isInputDisabled", "updateInputState", "contentControl", "get", "disable", "enable", "ngOnInit", "console", "log", "initializeComponent", "loadCurrentUser", "loadConversation", "setupCallSubscriptions", "add", "incomingCall$", "subscribe", "next", "incomingCall", "handleIncomingCall", "error", "activeCall$", "call", "caller", "play", "userString", "localStorage", "getItem", "user", "JSON", "parse", "userId", "_id", "extracted", "conversationId", "snapshot", "paramMap", "showError", "getConversation", "participants", "participantsCount", "isGroup", "messagesCount", "setOtherParticipant", "loadMessages", "setupSubscriptions", "warn", "find", "p", "participantId", "String", "firstParticipantId", "sort", "a", "b", "dateA", "Date", "createdAt", "getTime", "dateB", "total", "first", "last", "scrollToBottom", "loadMoreMessages", "offset", "getMessages", "newMessages", "reverse", "subscribeToNewMessages", "newMessage", "type", "senderId", "receiverId", "attachments", "for<PERSON>ach", "att", "path", "messageExists", "some", "msg", "push", "detectChanges", "setTimeout", "shouldMarkAsRead", "markMessageAsRead", "subscribeToTypingIndicator", "typingData", "subscribeToConversationUpdates", "conversationUpdate", "messageId", "valid", "value", "trim", "undefined", "message", "reset", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "diffMins", "Math", "floor", "now", "progress", "duration", "currentTime", "setVoicePlaybackData", "data", "startVideoCall", "startVoiceCall", "endCall", "formatFileSize", "bytes", "round", "fileAttachment", "startsWith", "link", "document", "createElement", "href", "download", "target", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "showSuccess", "toggleTheme", "newTheme", "isDarkMode", "selectTheme", "toggleMainMenu", "goBackToConversations", "showThemeSelector", "event", "preventDefault", "clientX", "clientY", "reaction", "toggleEmojiPicker", "category", "currentC<PERSON>nt", "newContent", "patchValue", "toggleAttachmentMenu", "toString", "testAddMessage", "testMessage", "toLocaleTimeString", "toISOString", "isRead", "factor", "imageElement", "querySelector", "currentTransform", "style", "transform", "currentScale", "parseFloat", "match", "newScale", "max", "min", "classList", "remove", "input", "fileInput", "accept", "date", "hour", "minute", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "urlRegex", "replace", "currentMessage", "previousMessage", "currentDate", "previousDate", "nextMessage", "attachment", "voiceUrl", "audioUrl", "voice", "hasImageAttachment", "hasImageUrl", "imageUrl", "hasFileAttachment", "imageAttachment", "includes", "colors", "charCodeAt", "onInputChange", "handleTypingIndicator", "onInputKeyDown", "key", "shift<PERSON>ey", "onInputFocus", "onInputBlur", "onScroll", "src", "searchMessages", "filter", "toLowerCase", "onSearchQueryChange", "clearSearch", "jumpToMessage", "messageElement", "getElementById", "scrollIntoView", "behavior", "block", "closeContextMenu", "initiateCall", "recipientId", "VIDEO", "startCallTimer", "acceptCall", "rejectCall", "setInterval", "resetCallState", "clearInterval", "toggleMute", "toggleMedia", "toggleVideo", "formatCallDuration", "hours", "minutes", "seconds", "padStart", "startVoiceRecording", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "Error", "window", "MediaRecorder", "stream", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "channelCount", "mimeType", "isTypeSupported", "animateVoice<PERSON>aves", "ondataavailable", "onstop", "processRecordedAudio", "onerror", "start", "errorMessage", "state", "stop", "getTracks", "track", "_this2", "audioBlob", "Blob", "extension", "audioFile", "File", "sendVoiceMessage", "_this3", "Promise", "resolve", "reject", "showWarning", "showInfo", "catch", "map", "random", "onFileSelected", "files", "file", "uploadFile", "maxSize", "compressImage", "then", "compressedFile", "sendFileToServer", "messageType", "getFileMessageType", "progressInterval", "getFileAcceptTypes", "onDragOver", "onDragLeave", "rect", "currentTarget", "getBoundingClientRect", "left", "right", "top", "bottom", "onDrop", "dataTransfer", "Array", "from", "quality", "canvas", "ctx", "getContext", "img", "Image", "onload", "max<PERSON><PERSON><PERSON>", "maxHeight", "width", "height", "ratio", "drawImage", "toBlob", "blob", "lastModified", "URL", "createObjectURL", "sendTypingIndicator", "clearTimeout", "onCallAccepted", "onCallRejected", "playVoiceMessage", "getVoiceUrl", "stopVoicePlayback", "startVoicePlayback", "Audio", "currentData", "playbackRate", "addEventListener", "pause", "audioAttachment", "seed", "split", "reduce", "acc", "char", "waves", "i", "totalWaves", "formatAudioTime", "metadata", "remainingSeconds", "waveIndex", "seekPercentage", "seekTime", "newSpeed", "ngOnDestroy", "unsubscribe", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "i3", "i4", "ToastService", "ChangeDetectorRef", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "MessageChatComponent_Template_button_click_2_listener", "MessageChatComponent_Template_img_click_6_listener", "MessageChatComponent_div_7_Template", "MessageChatComponent_div_12_Template", "MessageChatComponent_span_13_Template", "MessageChatComponent_Template_button_click_15_listener", "MessageChatComponent_Template_button_click_17_listener", "MessageChatComponent_Template_button_click_19_listener", "MessageChatComponent_Template_button_click_21_listener", "MessageChatComponent_div_23_Template", "MessageChatComponent_Template_main_scroll_24_listener", "MessageChatComponent_Template_main_dragover_24_listener", "MessageChatComponent_Template_main_dragleave_24_listener", "MessageChatComponent_Template_main_drop_24_listener", "MessageChatComponent_div_26_Template", "MessageChatComponent_div_27_Template", "MessageChatComponent_div_28_Template", "MessageChatComponent_div_29_Template", "MessageChatComponent_div_30_Template", "MessageChatComponent_Template_form_ngSubmit_32_listener", "MessageChatComponent_Template_button_click_34_listener", "MessageChatComponent_Template_button_click_36_listener", "MessageChatComponent_Template_textarea_input_39_listener", "MessageChatComponent_Template_textarea_keydown_39_listener", "MessageChatComponent_Template_textarea_focus_39_listener", "MessageChatComponent_Template_textarea_blur_39_listener", "MessageChatComponent_button_43_Template", "MessageChatComponent_button_44_Template", "MessageChatComponent_div_45_Template", "MessageChatComponent_div_46_Template", "MessageChatComponent_div_47_Template", "MessageChatComponent_Template_input_change_48_listener", "MessageChatComponent_div_50_Template", "MessageChatComponent_div_51_Template", "MessageChatComponent_div_52_Template", "MessageChatComponent_Template_app_call_interface_callEnded_53_listener", "MessageChatComponent_Template_app_call_interface_callAccepted_53_listener", "MessageChatComponent_Template_app_call_interface_callRejected_53_listener", "MessageChatComponent_div_54_Template", "tmp_24_0", "tmp_25_0"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON>Child,\n  ElementRef,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { MessageService } from '../../../../services/message.service';\nimport { ToastService } from '../../../../services/toast.service';\nimport { CallType, Call, IncomingCall } from '../../../../models/message.model';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: './message-chat.component.html',\n})\nexport class MessageChatComponent implements OnInit, OnDestroy {\n  // === RÉFÉRENCES DOM ===\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  // === DONNÉES PRINCIPALES ===\n  conversation: any = null;\n  messages: any[] = [];\n  currentUserId: string | null = null;\n  currentUsername = 'You';\n  messageForm: FormGroup;\n  otherParticipant: any = null;\n\n  // === ÉTATS DE L'INTERFACE ===\n  isLoading = false;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  showEmojiPicker = false;\n  showAttachmentMenu = false;\n  showSearch = false;\n  searchQuery = '';\n  searchResults: any[] = [];\n  searchMode = false;\n  isSendingMessage = false;\n  otherUserIsTyping = false;\n  showMainMenu = false;\n  showMessageContextMenu = false;\n  selectedMessage: any = null;\n  contextMenuPosition = { x: 0, y: 0 };\n  showReactionPicker = false;\n  reactionPickerMessage: any = null;\n\n  showImageViewer = false;\n  selectedImage: any = null;\n  uploadProgress = 0;\n  isUploading = false;\n  isDragOver = false;\n\n  // === GESTION VOCALE OPTIMISÉE ===\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';\n  private mediaRecorder: MediaRecorder | null = null;\n  private audioChunks: Blob[] = [];\n  private recordingTimer: any = null;\n  voiceWaves: number[] = [\n    4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8,\n  ];\n\n  // Lecture des messages vocaux\n  private currentAudio: HTMLAudioElement | null = null;\n  private playingMessageId: string | null = null;\n  private voicePlayback: {\n    [messageId: string]: {\n      progress: number;\n      duration: number;\n      currentTime: number;\n      speed: number;\n    };\n  } = {};\n\n  // === APPELS WEBRTC ===\n  isInCall = false;\n  callType: 'VIDEO' | 'AUDIO' | null = null;\n  callDuration = 0;\n  private callTimer: any = null;\n\n  // État de l'appel WebRTC\n  activeCall: any = null;\n  isCallConnected = false;\n  isMuted = false;\n  isVideoEnabled = true;\n  localVideoElement: HTMLVideoElement | null = null;\n  remoteVideoElement: HTMLVideoElement | null = null;\n\n  // === ÉMOJIS ===\n  emojiCategories: any[] = [\n    {\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [\n        { emoji: '😀', name: 'grinning face' },\n        { emoji: '😃', name: 'grinning face with big eyes' },\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\n        { emoji: '😆', name: 'grinning squinting face' },\n        { emoji: '😅', name: 'grinning face with sweat' },\n        { emoji: '😂', name: 'face with tears of joy' },\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\n        { emoji: '😇', name: 'smiling face with halo' },\n      ],\n    },\n    {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [\n        { emoji: '👶', name: 'baby' },\n        { emoji: '🧒', name: 'child' },\n        { emoji: '👦', name: 'boy' },\n        { emoji: '👧', name: 'girl' },\n        { emoji: '🧑', name: 'person' },\n        { emoji: '👨', name: 'man' },\n        { emoji: '👩', name: 'woman' },\n        { emoji: '👴', name: 'old man' },\n        { emoji: '👵', name: 'old woman' },\n      ],\n    },\n    {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [\n        { emoji: '🐶', name: 'dog face' },\n        { emoji: '🐱', name: 'cat face' },\n        { emoji: '🐭', name: 'mouse face' },\n        { emoji: '🐹', name: 'hamster' },\n        { emoji: '🐰', name: 'rabbit face' },\n        { emoji: '🦊', name: 'fox' },\n        { emoji: '🐻', name: 'bear' },\n        { emoji: '🐼', name: 'panda' },\n      ],\n    },\n  ];\n  selectedEmojiCategory = this.emojiCategories[0];\n\n  // === PAGINATION ===\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private currentPage = 1;\n\n  // === AUTRES ÉTATS ===\n  isTyping = false;\n  isUserTyping = false;\n  private typingTimeout: any = null;\n  private subscriptions = new Subscription();\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private MessageService: MessageService,\n    private toastService: ToastService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]],\n    });\n  }\n\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\n  isInputDisabled(): boolean {\n    return (\n      !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage\n    );\n  }\n\n  // Méthode pour gérer l'état du contrôle de saisie\n  private updateInputState(): void {\n    const contentControl = this.messageForm.get('content');\n    if (this.isInputDisabled()) {\n      contentControl?.disable();\n    } else {\n      contentControl?.enable();\n    }\n  }\n\n  ngOnInit(): void {\n    console.log('🚀 MessageChatComponent initialized');\n    this.initializeComponent();\n  }\n\n  private initializeComponent(): void {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupCallSubscriptions();\n  }\n\n  private setupCallSubscriptions(): void {\n    // S'abonner aux appels entrants\n    this.subscriptions.add(\n      this.MessageService.incomingCall$.subscribe({\n        next: (incomingCall) => {\n          if (incomingCall) {\n            console.log('📞 Incoming call received:', incomingCall);\n            this.handleIncomingCall(incomingCall);\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in incoming call subscription:', error);\n        },\n      })\n    );\n\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(\n      this.MessageService.activeCall$.subscribe({\n        next: (call) => {\n          if (call) {\n            console.log('📞 Active call updated:', call);\n            this.activeCall = call;\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in active call subscription:', error);\n        },\n      })\n    );\n  }\n\n  private handleIncomingCall(incomingCall: IncomingCall): void {\n    // Afficher une notification ou modal d'appel entrant\n    // Pour l'instant, on log juste\n    console.log(\n      '🔔 Handling incoming call from:',\n      incomingCall.caller.username\n    );\n\n    // Jouer la sonnerie\n    this.MessageService.play('ringtone');\n\n    // Ici on pourrait afficher une modal ou notification\n    // Pour l'instant, on accepte automatiquement pour tester\n    // this.acceptCall(incomingCall);\n  }\n\n  private loadCurrentUser(): void {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('🔍 Raw user from localStorage:', userString);\n\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n\n      const user = JSON.parse(userString);\n      console.log('🔍 Parsed user object:', user);\n\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      console.log('🔍 Trying to extract user ID:', {\n        _id: user._id,\n        id: user.id,\n        userId: user.userId,\n        extracted: userId,\n      });\n\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n        console.log('✅ Current user loaded successfully:', {\n          id: this.currentUserId,\n          username: this.currentUsername,\n        });\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n\n  private loadConversation(): void {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: (conversation) => {\n        console.log('🔍 Conversation loaded successfully:', conversation);\n        console.log('🔍 Conversation structure:', {\n          id: conversation?.id,\n          participants: conversation?.participants,\n          participantsCount: conversation?.participants?.length,\n          isGroup: conversation?.isGroup,\n          messages: conversation?.messages,\n          messagesCount: conversation?.messages?.length,\n        });\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n\n        // Configurer les subscriptions temps réel après le chargement de la conversation\n        this.setupSubscriptions();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError(\n          'Erreur lors du chargement de la conversation'\n        );\n        this.isLoading = false;\n      },\n    });\n  }\n\n  private setOtherParticipant(): void {\n    if (\n      !this.conversation?.participants ||\n      this.conversation.participants.length === 0\n    ) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n\n    console.log('Setting other participant...');\n    console.log('Current user ID:', this.currentUserId);\n    console.log('All participants:', this.conversation.participants);\n\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\n        const participantId = p.id || p._id;\n        console.log(\n          'Comparing participant ID:',\n          participantId,\n          'with current user ID:',\n          this.currentUserId\n        );\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      console.log('Fallback: using first participant');\n      this.otherParticipant = this.conversation.participants[0];\n\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId =\n          this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log(\n            'First participant is current user, using second participant'\n          );\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      console.log('✅ Other participant set successfully:', {\n        id: this.otherParticipant.id || this.otherParticipant._id,\n        username: this.otherParticipant.username,\n        image: this.otherParticipant.image,\n        isOnline: this.otherParticipant.isOnline,\n      });\n\n      // Log très visible pour debug\n      console.log(\n        '🎯 FINAL RESULT: otherParticipant =',\n        this.otherParticipant.username\n      );\n      console.log(\n        '🎯 Should display in sidebar:',\n        this.otherParticipant.username\n      );\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      console.log('Conversation participants:', this.conversation.participants);\n      console.log('Current user ID:', this.currentUserId);\n\n      // Log très visible pour debug\n      console.log('🚨 ERROR: No otherParticipant found!');\n    }\n\n    // Mettre à jour l'état du champ de saisie\n    this.updateInputState();\n  }\n\n  private loadMessages(): void {\n    if (!this.conversation?.id) return;\n\n    // Les messages sont déjà chargés avec la conversation\n    let messages = this.conversation.messages || [];\n\n    // Trier les messages par timestamp (plus anciens en premier)\n    this.messages = messages.sort((a: any, b: any) => {\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\n    });\n\n    console.log('📋 Messages loaded and sorted:', {\n      total: this.messages.length,\n      first: this.messages[0]?.content,\n      last: this.messages[this.messages.length - 1]?.content,\n    });\n\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id)\n      return;\n\n    this.isLoadingMore = true;\n    this.currentPage++;\n\n    // Calculer l'offset basé sur les messages déjà chargés\n    const offset = this.messages.length;\n\n    this.MessageService.getMessages(\n      this.currentUserId!, // senderId\n      this.otherParticipant?.id || this.otherParticipant?._id!, // receiverId\n      this.conversation.id,\n      this.currentPage,\n      this.MAX_MESSAGES_TO_LOAD\n    ).subscribe({\n      next: (newMessages: any[]) => {\n        if (newMessages && newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste\n          this.messages = [...newMessages.reverse(), ...this.messages];\n          this.hasMoreMessages =\n            newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      },\n    });\n  }\n\n  private setupSubscriptions(): void {\n    if (!this.conversation?.id) {\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\n      return;\n    }\n\n    console.log(\n      '🔄 Setting up real-time subscriptions for conversation:',\n      this.conversation.id\n    );\n\n    // Subscription pour les nouveaux messages\n    console.log('📨 Setting up message subscription...');\n    this.subscriptions.add(\n      this.MessageService.subscribeToNewMessages(\n        this.conversation.id\n      ).subscribe({\n        next: (newMessage: any) => {\n          console.log('📨 New message received via subscription:', newMessage);\n          console.log('📨 Message structure:', {\n            id: newMessage.id,\n            type: newMessage.type,\n            content: newMessage.content,\n            sender: newMessage.sender,\n            senderId: newMessage.senderId,\n            receiverId: newMessage.receiverId,\n            attachments: newMessage.attachments,\n          });\n\n          // Debug des attachments\n          console.log(\n            '📨 [Debug] Message type detected:',\n            this.getMessageType(newMessage)\n          );\n          console.log('📨 [Debug] Has image:', this.hasImage(newMessage));\n          console.log('📨 [Debug] Has file:', this.hasFile(newMessage));\n          console.log('📨 [Debug] Image URL:', this.getImageUrl(newMessage));\n          if (newMessage.attachments) {\n            newMessage.attachments.forEach((att: any, index: number) => {\n              console.log(`📨 [Debug] Attachment ${index}:`, {\n                type: att.type,\n                url: att.url,\n                path: att.path,\n                name: att.name,\n                size: att.size,\n              });\n            });\n          }\n\n          // Ajouter le message à la liste s'il n'existe pas déjà\n          const messageExists = this.messages.some(\n            (msg) => msg.id === newMessage.id\n          );\n          if (!messageExists) {\n            // Ajouter le nouveau message à la fin (en bas)\n            this.messages.push(newMessage);\n            console.log(\n              '✅ Message added to list, total messages:',\n              this.messages.length\n            );\n\n            // Forcer la détection de changements\n            this.cdr.detectChanges();\n\n            // Scroll vers le bas après un court délai\n            setTimeout(() => {\n              this.scrollToBottom();\n            }, 50);\n\n            // Marquer comme lu si ce n'est pas notre message\n            const senderId = newMessage.sender?.id || newMessage.senderId;\n            console.log('📨 Checking if message should be marked as read:', {\n              senderId,\n              currentUserId: this.currentUserId,\n              shouldMarkAsRead: senderId !== this.currentUserId,\n            });\n\n            if (senderId && senderId !== this.currentUserId) {\n              this.markMessageAsRead(newMessage.id);\n            }\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in message subscription:', error);\n        },\n      })\n    );\n\n    // Subscription pour les indicateurs de frappe\n    console.log('📝 Setting up typing indicator subscription...');\n    this.subscriptions.add(\n      this.MessageService.subscribeToTypingIndicator(\n        this.conversation.id\n      ).subscribe({\n        next: (typingData: any) => {\n          console.log('📝 Typing indicator received:', typingData);\n\n          // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n          if (typingData.userId !== this.currentUserId) {\n            this.otherUserIsTyping = typingData.isTyping;\n            this.cdr.detectChanges();\n          }\n        },\n        error: (error: any) => {\n          console.error('❌ Error in typing subscription:', error);\n        },\n      })\n    );\n\n    // Subscription pour les mises à jour de conversation\n    this.subscriptions.add(\n      this.MessageService.subscribeToConversationUpdates(\n        this.conversation.id\n      ).subscribe({\n        next: (conversationUpdate: any) => {\n          console.log('📋 Conversation update:', conversationUpdate);\n\n          // Mettre à jour la conversation si nécessaire\n          if (conversationUpdate.id === this.conversation.id) {\n            this.conversation = { ...this.conversation, ...conversationUpdate };\n            this.cdr.detectChanges();\n          }\n        },\n        error: (error: any) => {\n          console.error('❌ Error in conversation subscription:', error);\n        },\n      })\n    );\n  }\n\n  private markMessageAsRead(messageId: string): void {\n    this.MessageService.markMessageAsRead(messageId).subscribe({\n      next: () => {\n        console.log('✅ Message marked as read:', messageId);\n      },\n      error: (error) => {\n        console.error('❌ Error marking message as read:', error);\n      },\n    });\n  }\n\n  // === ENVOI DE MESSAGES ===\n  sendMessage(): void {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    // Désactiver le bouton d'envoi\n    this.isSendingMessage = true;\n    this.updateInputState();\n\n    console.log('📤 Sending message:', {\n      content,\n      receiverId,\n      conversationId: this.conversation.id,\n    });\n\n    this.MessageService.sendMessage(\n      receiverId,\n      content,\n      undefined,\n      'TEXT' as any,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        console.log('✅ Message sent successfully:', message);\n\n        // Ajouter le message à la liste s'il n'y est pas déjà\n        const messageExists = this.messages.some(\n          (msg) => msg.id === message.id\n        );\n        if (!messageExists) {\n          this.messages.push(message);\n          console.log(\n            '📋 Message added to local list, total:',\n            this.messages.length\n          );\n        }\n\n        // Réinitialiser le formulaire\n        this.messageForm.reset();\n        this.isSendingMessage = false;\n        this.updateInputState();\n\n        // Forcer la détection de changements et scroll\n        this.cdr.detectChanges();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 50);\n      },\n      error: (error: any) => {\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        this.isSendingMessage = false;\n        this.updateInputState();\n      },\n    });\n  }\n\n  scrollToBottom(): void {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n  formatLastActive(lastActive: string | Date | null): string {\n    if (!lastActive) return 'Hors ligne';\n\n    const diffMins = Math.floor(\n      (Date.now() - new Date(lastActive).getTime()) / 60000\n    );\n\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\n  }\n\n  // Méthodes utilitaires pour les messages vocaux\n  getVoicePlaybackData(messageId: string) {\n    return (\n      this.voicePlayback[messageId] || {\n        progress: 0,\n        duration: 0,\n        currentTime: 0,\n        speed: 1,\n      }\n    );\n  }\n\n  private setVoicePlaybackData(\n    messageId: string,\n    data: Partial<(typeof this.voicePlayback)[string]>\n  ) {\n    this.voicePlayback[messageId] = {\n      ...this.getVoicePlaybackData(messageId),\n      ...data,\n    };\n  }\n\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // isVoicePlaying, playVoiceMessage, toggleVoicePlayback - définies plus loin\n\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n\n  startVideoCall(): void {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n\n    this.callType = 'VIDEO';\n    this.isInCall = true;\n    console.log('📹 Starting video call with:', this.otherParticipant.username);\n  }\n\n  startVoiceCall(): void {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n\n    this.callType = 'AUDIO';\n    this.isInCall = true;\n    console.log('📞 Starting voice call with:', this.otherParticipant.username);\n  }\n\n  endCall(): void {\n    this.isInCall = false;\n    this.callType = null;\n    this.activeCall = null;\n    console.log('📞 Call ended');\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // onCallAccepted, onCallRejected - définies plus loin\n\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n\n  formatFileSize(bytes: number): string {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n\n  downloadFile(message: any): void {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (fileAttachment?.url) {\n      const link = document.createElement('a');\n      link.href = fileAttachment.url;\n      link.download = fileAttachment.name || 'file';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n    }\n  }\n\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n\n  toggleTheme(): void {\n    const newTheme = this.isDarkMode ? 'light' : 'dark';\n    this.selectTheme(newTheme);\n  }\n\n  toggleSearch(): void {\n    this.searchMode = !this.searchMode;\n    this.showSearch = this.searchMode;\n  }\n\n  toggleMainMenu(): void {\n    this.showMainMenu = !this.showMainMenu;\n  }\n\n  goBackToConversations(): void {\n    // Navigation vers la liste des conversations\n    console.log('🔙 Going back to conversations');\n  }\n\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n\n  closeAllMenus(): void {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.showReactionPicker = false;\n    this.showThemeSelector = false;\n  }\n\n  onMessageContextMenu(message: any, event: MouseEvent): void {\n    event.preventDefault();\n    this.selectedMessage = message;\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\n    this.showMessageContextMenu = true;\n  }\n\n  showQuickReactions(message: any, event: MouseEvent): void {\n    event.stopPropagation();\n    this.reactionPickerMessage = message;\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\n    this.showReactionPicker = true;\n  }\n\n  quickReact(emoji: string): void {\n    if (this.reactionPickerMessage) {\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\n    }\n    this.showReactionPicker = false;\n  }\n\n  toggleReaction(messageId: string, emoji: string): void {\n    console.log('👍 Toggling reaction:', emoji, 'for message:', messageId);\n    // Implémentation de la réaction\n  }\n\n  hasUserReacted(reaction: any, userId: string): boolean {\n    return reaction.userId === userId;\n  }\n\n  replyToMessage(message: any): void {\n    console.log('↩️ Replying to message:', message.id);\n    this.closeAllMenus();\n  }\n\n  forwardMessage(message: any): void {\n    console.log('➡️ Forwarding message:', message.id);\n    this.closeAllMenus();\n  }\n\n  deleteMessage(message: any): void {\n    console.log('🗑️ Deleting message:', message.id);\n    this.closeAllMenus();\n  }\n\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n\n  toggleEmojiPicker(): void {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n\n  selectEmojiCategory(category: any): void {\n    this.selectedEmojiCategory = category;\n  }\n\n  getEmojisForCategory(category: any): any[] {\n    return category?.emojis || [];\n  }\n\n  insertEmoji(emoji: any): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({ content: newContent });\n    this.showEmojiPicker = false;\n  }\n\n  toggleAttachmentMenu(): void {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  // === MÉTHODES POUR LA GESTION DE LA FRAPPE ===\n\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // handleTypingIndicator - définie plus loin\n\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n\n  trackByMessageId(index: number, message: any): string {\n    return message.id || message._id || index.toString();\n  }\n\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n\n  testAddMessage(): void {\n    console.log('🧪 Test: Adding message');\n    const testMessage = {\n      id: `test-${Date.now()}`,\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\n      timestamp: new Date().toISOString(),\n      sender: {\n        id: this.otherParticipant?.id || 'test-user',\n        username: this.otherParticipant?.username || 'Test User',\n        image:\n          this.otherParticipant?.image || 'assets/images/default-avatar.png',\n      },\n      type: 'TEXT',\n      isRead: false,\n    };\n    this.messages.push(testMessage);\n    this.cdr.detectChanges();\n    setTimeout(() => this.scrollToBottom(), 50);\n  }\n\n  isGroupConversation(): boolean {\n    return (\n      this.conversation?.isGroup ||\n      this.conversation?.participants?.length > 2 ||\n      false\n    );\n  }\n\n  openCamera(): void {\n    console.log('📷 Opening camera');\n    this.showAttachmentMenu = false;\n    // TODO: Implémenter l'ouverture de la caméra\n  }\n\n  zoomImage(factor: number): void {\n    const imageElement = document.querySelector(\n      '.image-viewer-zoom'\n    ) as HTMLElement;\n    if (imageElement) {\n      const currentTransform = imageElement.style.transform || 'scale(1)';\n      const currentScale = parseFloat(\n        currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1'\n      );\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n      imageElement.style.transform = `scale(${newScale})`;\n      if (newScale > 1) {\n        imageElement.classList.add('zoomed');\n      } else {\n        imageElement.classList.remove('zoomed');\n      }\n    }\n  }\n\n  resetZoom(): void {\n    const imageElement = document.querySelector(\n      '.image-viewer-zoom'\n    ) as HTMLElement;\n    if (imageElement) {\n      imageElement.style.transform = 'scale(1)';\n      imageElement.classList.remove('zoomed');\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  triggerFileInput(type?: string): void {\n    const input = this.fileInput?.nativeElement;\n    if (!input) {\n      console.error('File input element not found');\n      return;\n    }\n\n    // Configurer le type de fichier accepté\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    } else {\n      input.accept = '*/*';\n    }\n\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\n    input.value = '';\n\n    // Déclencher la sélection de fichier\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n\n  formatMessageTime(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  }\n\n  formatDateSeparator(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n\n  formatMessageContent(content: string): string {\n    if (!content) return '';\n\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(\n      urlRegex,\n      '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>'\n    );\n  }\n\n  shouldShowDateSeparator(index: number): boolean {\n    if (index === 0) return true;\n\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n\n    return currentDate !== previousDate;\n  }\n\n  shouldShowAvatar(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n\n    if (!nextMessage) return true;\n\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n\n  shouldShowSenderName(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!previousMessage) return true;\n\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n\n  getMessageType(message: any): string {\n    // Vérifier d'abord le type de message explicite\n    if (message.type) {\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\n    }\n\n    // Ensuite vérifier les attachments\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n\n    // Vérifier si c'est un message vocal basé sur les propriétés\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n\n    return 'text';\n  }\n\n  hasImage(message: any): boolean {\n    // Vérifier le type de message\n    if (message.type === 'IMAGE' || message.type === 'image') {\n      return true;\n    }\n\n    // Vérifier les attachments\n    const hasImageAttachment =\n      message.attachments?.some((att: any) => {\n        return att.type?.startsWith('image/') || att.type === 'IMAGE';\n      }) || false;\n\n    // Vérifier les propriétés directes d'image\n    const hasImageUrl = !!(message.imageUrl || message.image);\n\n    return hasImageAttachment || hasImageUrl;\n  }\n\n  hasFile(message: any): boolean {\n    // Vérifier le type de message\n    if (message.type === 'FILE' || message.type === 'file') {\n      return true;\n    }\n\n    // Vérifier les attachments non-image\n    const hasFileAttachment =\n      message.attachments?.some((att: any) => {\n        return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n      }) || false;\n\n    return hasFileAttachment;\n  }\n\n  getImageUrl(message: any): string {\n    // Vérifier les propriétés directes d'image\n    if (message.imageUrl) {\n      return message.imageUrl;\n    }\n    if (message.image) {\n      return message.image;\n    }\n\n    // Vérifier les attachments\n    const imageAttachment = message.attachments?.find(\n      (att: any) => att.type?.startsWith('image/') || att.type === 'IMAGE'\n    );\n\n    if (imageAttachment) {\n      return imageAttachment.url || imageAttachment.path || '';\n    }\n\n    return '';\n  }\n\n  getFileName(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    return fileAttachment?.name || 'Fichier';\n  }\n\n  getFileSize(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.size) return '';\n\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n\n  getFileIcon(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.type) return 'fas fa-file';\n\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n\n  getUserColor(userId: string): string {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = [\n      '#FF6B6B',\n      '#4ECDC4',\n      '#45B7D1',\n      '#96CEB4',\n      '#FFEAA7',\n      '#DDA0DD',\n      '#98D8C8',\n    ];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message: any, event: any): void {\n    console.log('Message clicked:', message);\n  }\n\n  onInputChange(event: any): void {\n    // Gérer les changements dans le champ de saisie\n    this.handleTypingIndicator();\n  }\n\n  onInputKeyDown(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  onInputFocus(): void {\n    // Gérer le focus sur le champ de saisie\n  }\n\n  onInputBlur(): void {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n\n  onScroll(event: any): void {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (\n      element.scrollTop === 0 &&\n      this.hasMoreMessages &&\n      !this.isLoadingMore\n    ) {\n      this.loadMoreMessages();\n    }\n  }\n\n  openUserProfile(userId: string): void {\n    console.log('Opening user profile for:', userId);\n  }\n\n  onImageLoad(event: any, message: any): void {\n    console.log(\n      '🖼️ [Debug] Image loaded successfully for message:',\n      message.id,\n      event.target.src\n    );\n  }\n\n  onImageError(event: any, message: any): void {\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n      src: event.target.src,\n      error: event,\n    });\n    // Optionnel : afficher une image de remplacement\n    event.target.src =\n      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n  }\n\n  openImageViewer(message: any): void {\n    const imageAttachment = message.attachments?.find((att: any) =>\n      att.type?.startsWith('image/')\n    );\n    if (imageAttachment?.url) {\n      this.selectedImage = {\n        url: imageAttachment.url,\n        name: imageAttachment.name || 'Image',\n        size: this.formatFileSize(imageAttachment.size || 0),\n        message: message,\n      };\n      this.showImageViewer = true;\n      console.log('🖼️ [ImageViewer] Opening image:', this.selectedImage);\n    }\n  }\n\n  closeImageViewer(): void {\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    console.log('🖼️ [ImageViewer] Closed');\n  }\n\n  downloadImage(): void {\n    if (this.selectedImage?.url) {\n      const link = document.createElement('a');\n      link.href = this.selectedImage.url;\n      link.download = this.selectedImage.name || 'image';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n      console.log(\n        '🖼️ [ImageViewer] Download started:',\n        this.selectedImage.name\n      );\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  searchMessages(): void {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n\n    this.searchResults = this.messages.filter(\n      (message) =>\n        message.content\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase()) ||\n        message.sender?.username\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase())\n    );\n  }\n\n  onSearchQueryChange(): void {\n    this.searchMessages();\n  }\n\n  clearSearch(): void {\n    this.searchQuery = '';\n    this.searchResults = [];\n  }\n\n  jumpToMessage(messageId: string): void {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\n      // Highlight temporairement le message\n      messageElement.classList.add('highlight');\n      setTimeout(() => {\n        messageElement.classList.remove('highlight');\n      }, 2000);\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  closeContextMenu(): void {\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // triggerFileInput - définie plus loin\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n  // goBackToConversations, startVideoCall, startVoiceCall\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  private initiateCall(callType: CallType): void {\n    if (!this.otherParticipant) {\n      this.toastService.showError('Aucun destinataire sélectionné');\n      return;\n    }\n\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n    if (!recipientId) {\n      this.toastService.showError('ID du destinataire introuvable');\n      return;\n    }\n\n    console.log(`🔄 Initiating ${callType} call to user:`, recipientId);\n\n    this.isInCall = true;\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n    this.callDuration = 0;\n\n    // Démarrer le timer d'appel\n    this.startCallTimer();\n\n    // Utiliser le vrai service WebRTC\n    this.MessageService.initiateCall(\n      recipientId,\n      callType,\n      this.conversation?.id\n    ).subscribe({\n      next: (call: Call) => {\n        console.log('✅ Call initiated successfully:', call);\n        this.activeCall = call;\n        this.isCallConnected = false;\n        this.toastService.showSuccess(\n          `Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`\n        );\n      },\n      error: (error) => {\n        console.error('❌ Error initiating call:', error);\n        this.endCall();\n        this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n      },\n    });\n  }\n\n  acceptCall(incomingCall: IncomingCall): void {\n    console.log('🔄 Accepting incoming call:', incomingCall);\n\n    this.MessageService.acceptCall(incomingCall).subscribe({\n      next: (call: Call) => {\n        console.log('✅ Call accepted successfully:', call);\n        this.activeCall = call;\n        this.isInCall = true;\n        this.isCallConnected = true;\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n        this.startCallTimer();\n        this.toastService.showSuccess('Appel accepté');\n      },\n      error: (error) => {\n        console.error('❌ Error accepting call:', error);\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\n      },\n    });\n  }\n\n  rejectCall(incomingCall: IncomingCall): void {\n    console.log('🔄 Rejecting incoming call:', incomingCall);\n\n    this.MessageService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n      next: () => {\n        console.log('✅ Call rejected successfully');\n        this.toastService.showSuccess('Appel rejeté');\n      },\n      error: (error) => {\n        console.error('❌ Error rejecting call:', error);\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n      },\n    });\n  }\n\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // endCall - Cette méthode est déjà définie plus loin dans le fichier\n\n  private startCallTimer(): void {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n\n  private resetCallState(): void {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n  }\n\n  // === CONTRÔLES D'APPEL ===\n  toggleMute(): void {\n    if (!this.activeCall) return;\n\n    this.isMuted = !this.isMuted;\n\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(\n      this.activeCall.id,\n      undefined, // video unchanged\n      !this.isMuted // audio state\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(\n          this.isMuted ? 'Micro coupé' : 'Micro activé'\n        );\n      },\n      error: (error) => {\n        console.error('❌ Error toggling mute:', error);\n        // Revert state on error\n        this.isMuted = !this.isMuted;\n        this.toastService.showError('Erreur lors du changement du micro');\n      },\n    });\n  }\n\n  toggleVideo(): void {\n    if (!this.activeCall) return;\n\n    this.isVideoEnabled = !this.isVideoEnabled;\n\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(\n      this.activeCall.id,\n      this.isVideoEnabled, // video state\n      undefined // audio unchanged\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(\n          this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\n        );\n      },\n      error: (error) => {\n        console.error('❌ Error toggling video:', error);\n        // Revert state on error\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.toastService.showError('Erreur lors du changement de la caméra');\n      },\n    });\n  }\n\n  formatCallDuration(duration: number): string {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor((duration % 3600) / 60);\n    const seconds = duration % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds\n        .toString()\n        .padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n\n  async startVoiceRecording(): Promise<void> {\n    console.log('🎤 [Voice] Starting voice recording...');\n\n    try {\n      // Vérifier le support du navigateur\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n        throw new Error(\n          \"Votre navigateur ne supporte pas l'enregistrement audio\"\n        );\n      }\n\n      // Vérifier si MediaRecorder est supporté\n      if (!window.MediaRecorder) {\n        throw new Error(\n          \"MediaRecorder n'est pas supporté par votre navigateur\"\n        );\n      }\n\n      console.log('🎤 [Voice] Requesting microphone access...');\n\n      // Demander l'accès au microphone avec des contraintes optimisées\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n          sampleRate: 44100,\n          channelCount: 1,\n        },\n      });\n\n      console.log('🎤 [Voice] Microphone access granted');\n\n      // Vérifier les types MIME supportés\n      let mimeType = 'audio/webm;codecs=opus';\n      if (!MediaRecorder.isTypeSupported(mimeType)) {\n        mimeType = 'audio/webm';\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\n          mimeType = 'audio/mp4';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = ''; // Laisser le navigateur choisir\n          }\n        }\n      }\n\n      console.log('🎤 [Voice] Using MIME type:', mimeType);\n\n      // Créer le MediaRecorder\n      this.mediaRecorder = new MediaRecorder(stream, {\n        mimeType: mimeType || undefined,\n      });\n\n      // Initialiser les variables\n      this.audioChunks = [];\n      this.isRecordingVoice = true;\n      this.voiceRecordingDuration = 0;\n      this.voiceRecordingState = 'recording';\n\n      console.log('🎤 [Voice] MediaRecorder created, starting timer...');\n\n      // Démarrer le timer\n      this.recordingTimer = setInterval(() => {\n        this.voiceRecordingDuration++;\n        // Animer les waves\n        this.animateVoiceWaves();\n        this.cdr.detectChanges();\n      }, 1000);\n\n      // Gérer les événements du MediaRecorder\n      this.mediaRecorder.ondataavailable = (event) => {\n        console.log('🎤 [Voice] Data available:', event.data.size, 'bytes');\n        if (event.data.size > 0) {\n          this.audioChunks.push(event.data);\n        }\n      };\n\n      this.mediaRecorder.onstop = () => {\n        console.log('🎤 [Voice] MediaRecorder stopped, processing audio...');\n        this.processRecordedAudio();\n      };\n\n      this.mediaRecorder.onerror = (event: any) => {\n        console.error('🎤 [Voice] MediaRecorder error:', event.error);\n        this.toastService.showError(\"Erreur lors de l'enregistrement\");\n        this.cancelVoiceRecording();\n      };\n\n      // Démarrer l'enregistrement\n      this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n      console.log('🎤 [Voice] Recording started successfully');\n\n      this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n    } catch (error: any) {\n      console.error('🎤 [Voice] Error starting recording:', error);\n\n      let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n\n      if (error.name === 'NotAllowedError') {\n        errorMessage =\n          \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n      } else if (error.name === 'NotFoundError') {\n        errorMessage =\n          'Aucun microphone détecté. Veuillez connecter un microphone.';\n      } else if (error.name === 'NotSupportedError') {\n        errorMessage =\n          \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      this.toastService.showError(errorMessage);\n      this.cancelVoiceRecording();\n    }\n  }\n\n  stopVoiceRecording(): void {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n\n  cancelVoiceRecording(): void {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n      this.mediaRecorder = null;\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n\n  private async processRecordedAudio(): Promise<void> {\n    console.log('🎤 [Voice] Processing recorded audio...');\n\n    try {\n      // Vérifier qu'on a des données audio\n      if (this.audioChunks.length === 0) {\n        console.error('🎤 [Voice] No audio chunks available');\n        this.toastService.showError('Aucun audio enregistré');\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      console.log(\n        '🎤 [Voice] Audio chunks:',\n        this.audioChunks.length,\n        'Duration:',\n        this.voiceRecordingDuration\n      );\n\n      // Vérifier la durée minimale\n      if (this.voiceRecordingDuration < 1) {\n        console.error(\n          '🎤 [Voice] Recording too short:',\n          this.voiceRecordingDuration\n        );\n        this.toastService.showError(\n          'Enregistrement trop court (minimum 1 seconde)'\n        );\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      // Déterminer le type MIME du blob\n      let mimeType = 'audio/webm;codecs=opus';\n      if (this.mediaRecorder?.mimeType) {\n        mimeType = this.mediaRecorder.mimeType;\n      }\n\n      console.log('🎤 [Voice] Creating audio blob with MIME type:', mimeType);\n\n      // Créer le blob audio\n      const audioBlob = new Blob(this.audioChunks, {\n        type: mimeType,\n      });\n\n      console.log('🎤 [Voice] Audio blob created:', {\n        size: audioBlob.size,\n        type: audioBlob.type,\n      });\n\n      // Déterminer l'extension du fichier\n      let extension = '.webm';\n      if (mimeType.includes('mp4')) {\n        extension = '.mp4';\n      } else if (mimeType.includes('wav')) {\n        extension = '.wav';\n      } else if (mimeType.includes('ogg')) {\n        extension = '.ogg';\n      }\n\n      // Créer le fichier\n      const audioFile = new File(\n        [audioBlob],\n        `voice_${Date.now()}${extension}`,\n        {\n          type: mimeType,\n        }\n      );\n\n      console.log('🎤 [Voice] Audio file created:', {\n        name: audioFile.name,\n        size: audioFile.size,\n        type: audioFile.type,\n      });\n\n      // Envoyer le message vocal\n      this.voiceRecordingState = 'processing';\n      await this.sendVoiceMessage(audioFile);\n\n      console.log('🎤 [Voice] Voice message sent successfully');\n      this.toastService.showSuccess('🎤 Message vocal envoyé');\n    } catch (error: any) {\n      console.error('🎤 [Voice] Error processing audio:', error);\n      this.toastService.showError(\n        \"Erreur lors de l'envoi du message vocal: \" +\n          (error.message || 'Erreur inconnue')\n      );\n    } finally {\n      // Nettoyer l'état\n      this.voiceRecordingState = 'idle';\n      this.voiceRecordingDuration = 0;\n      this.audioChunks = [];\n      this.isRecordingVoice = false;\n\n      console.log('🎤 [Voice] Audio processing completed, state reset');\n    }\n  }\n\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        '',\n        audioFile,\n        'AUDIO' as any,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n          reject(error);\n        },\n      });\n    });\n  }\n\n  formatRecordingDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n\n  onRecordStart(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record start triggered');\n    console.log('🎤 [Voice] Current state:', {\n      isRecordingVoice: this.isRecordingVoice,\n      voiceRecordingState: this.voiceRecordingState,\n      voiceRecordingDuration: this.voiceRecordingDuration,\n      mediaRecorder: !!this.mediaRecorder,\n    });\n\n    // Vérifier si on peut enregistrer\n    if (this.voiceRecordingState === 'processing') {\n      console.log('🎤 [Voice] Already processing, ignoring start');\n      this.toastService.showWarning('Traitement en cours...');\n      return;\n    }\n\n    if (this.isRecordingVoice) {\n      console.log('🎤 [Voice] Already recording, ignoring start');\n      this.toastService.showWarning('Enregistrement déjà en cours...');\n      return;\n    }\n\n    // Afficher un message de début\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n\n    // Démarrer l'enregistrement\n    this.startVoiceRecording().catch((error) => {\n      console.error('🎤 [Voice] Failed to start recording:', error);\n      this.toastService.showError(\n        \"Impossible de démarrer l'enregistrement vocal: \" +\n          (error.message || 'Erreur inconnue')\n      );\n    });\n  }\n\n  onRecordEnd(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record end triggered');\n\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring end');\n      return;\n    }\n\n    // Arrêter l'enregistrement et envoyer\n    this.stopVoiceRecording();\n  }\n\n  onRecordCancel(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record cancel triggered');\n\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring cancel');\n      return;\n    }\n\n    // Annuler l'enregistrement\n    this.cancelVoiceRecording();\n  }\n\n  getRecordingFormat(): string {\n    if (this.mediaRecorder?.mimeType) {\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n    }\n    return 'Auto';\n  }\n\n  // === ANIMATION DES WAVES VOCALES ===\n\n  private animateVoiceWaves(): void {\n    // Animer les waves pendant l'enregistrement\n    this.voiceWaves = this.voiceWaves.map(() => {\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n    });\n  }\n\n  onFileSelected(event: any): void {\n    console.log('📁 [Upload] File selection triggered');\n    const files = event.target.files;\n\n    if (!files || files.length === 0) {\n      console.log('📁 [Upload] No files selected');\n      return;\n    }\n\n    console.log(`📁 [Upload] ${files.length} file(s) selected:`, files);\n\n    for (let file of files) {\n      console.log(\n        `📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`\n      );\n      this.uploadFile(file);\n    }\n  }\n\n  private uploadFile(file: File): void {\n    console.log(`📁 [Upload] Starting upload for file: ${file.name}`);\n\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      console.error('📁 [Upload] No receiver ID found');\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    console.log(`📁 [Upload] Receiver ID: ${receiverId}`);\n\n    // Vérifier la taille du fichier (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\n      return;\n    }\n\n    // 🖼️ Compression d'image si nécessaire\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n      // > 1MB\n      console.log(\n        '🖼️ [Compression] Compressing image:',\n        file.name,\n        'Original size:',\n        file.size\n      );\n      this.compressImage(file)\n        .then((compressedFile) => {\n          console.log(\n            '🖼️ [Compression] ✅ Image compressed successfully. New size:',\n            compressedFile.size\n          );\n          this.sendFileToServer(compressedFile, receiverId);\n        })\n        .catch((error) => {\n          console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n          // Envoyer le fichier original en cas d'erreur\n          this.sendFileToServer(file, receiverId);\n        });\n      return;\n    }\n\n    // Envoyer le fichier sans compression\n    this.sendFileToServer(file, receiverId);\n  }\n\n  private sendFileToServer(file: File, receiverId: string): void {\n    const messageType = this.getFileMessageType(file);\n    console.log(`📁 [Upload] Message type determined: ${messageType}`);\n    console.log(`📁 [Upload] Conversation ID: ${this.conversation.id}`);\n\n    this.isSendingMessage = true;\n    this.isUploading = true;\n    this.uploadProgress = 0;\n    console.log('📁 [Upload] Calling MessageService.sendMessage...');\n\n    // Simuler la progression d'upload\n    const progressInterval = setInterval(() => {\n      this.uploadProgress += Math.random() * 15;\n      if (this.uploadProgress >= 90) {\n        clearInterval(progressInterval);\n      }\n      this.cdr.detectChanges();\n    }, 300);\n\n    this.MessageService.sendMessage(\n      receiverId,\n      '',\n      file,\n      messageType,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        console.log('📁 [Upload] ✅ File sent successfully:', message);\n        console.log('📁 [Debug] Sent message structure:', {\n          id: message.id,\n          type: message.type,\n          attachments: message.attachments,\n          hasImage: this.hasImage(message),\n          hasFile: this.hasFile(message),\n          imageUrl: this.getImageUrl(message),\n        });\n\n        clearInterval(progressInterval);\n        this.uploadProgress = 100;\n\n        setTimeout(() => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Fichier envoyé avec succès');\n          this.resetUploadState();\n        }, 500);\n      },\n      error: (error: any) => {\n        console.error('📁 [Upload] ❌ Error sending file:', error);\n        clearInterval(progressInterval);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n        this.resetUploadState();\n      },\n    });\n  }\n\n  private getFileMessageType(file: File): any {\n    if (file.type.startsWith('image/')) return 'IMAGE' as any;\n    if (file.type.startsWith('video/')) return 'VIDEO' as any;\n    if (file.type.startsWith('audio/')) return 'AUDIO' as any;\n    return 'FILE' as any;\n  }\n\n  getFileAcceptTypes(): string {\n    return '*/*';\n  }\n\n  resetUploadState(): void {\n    this.isSendingMessage = false;\n    this.isUploading = false;\n    this.uploadProgress = 0;\n  }\n\n  // === DRAG & DROP ===\n\n  onDragOver(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n\n  onDragLeave(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\n    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\n    const x = event.clientX;\n    const y = event.clientY;\n\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      this.isDragOver = false;\n    }\n  }\n\n  onDrop(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      console.log('📁 [Drag&Drop] Files dropped:', files.length);\n\n      // Traiter chaque fichier\n      Array.from(files).forEach((file) => {\n        console.log(\n          '📁 [Drag&Drop] Processing file:',\n          file.name,\n          file.type,\n          file.size\n        );\n        this.uploadFile(file);\n      });\n\n      this.toastService.showSuccess(\n        `${files.length} fichier(s) en cours d'envoi`\n      );\n    }\n  }\n\n  // === COMPRESSION D'IMAGES ===\n\n  private compressImage(file: File, quality: number = 0.8): Promise<File> {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n\n      img.onload = () => {\n        // Calculer les nouvelles dimensions (max 1920x1080)\n        const maxWidth = 1920;\n        const maxHeight = 1080;\n        let { width, height } = img;\n\n        if (width > maxWidth || height > maxHeight) {\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\n          width *= ratio;\n          height *= ratio;\n        }\n\n        canvas.width = width;\n        canvas.height = height;\n\n        // Dessiner l'image redimensionnée\n        ctx?.drawImage(img, 0, 0, width, height);\n\n        // Convertir en blob avec compression\n        canvas.toBlob(\n          (blob) => {\n            if (blob) {\n              const compressedFile = new File([blob], file.name, {\n                type: file.type,\n                lastModified: Date.now(),\n              });\n              resolve(compressedFile);\n            } else {\n              reject(new Error('Failed to compress image'));\n            }\n          },\n          file.type,\n          quality\n        );\n      };\n\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  }\n\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n\n  private handleTypingIndicator(): void {\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\n      this.sendTypingIndicator(true);\n    }\n\n    // Reset le timer\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Arrêter l'indicateur de frappe\n      this.sendTypingIndicator(false);\n    }, 2000);\n  }\n\n  private sendTypingIndicator(isTyping: boolean): void {\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (receiverId && this.conversation?.id) {\n      console.log(\n        `📝 Sending typing indicator: ${isTyping} to user ${receiverId}`\n      );\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n    }\n  }\n\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n\n  onCallAccepted(call: Call): void {\n    console.log('🔄 Call accepted from interface:', call);\n    this.activeCall = call;\n    this.isInCall = true;\n    this.isCallConnected = true;\n    this.startCallTimer();\n    this.toastService.showSuccess('Appel accepté');\n  }\n\n  onCallRejected(): void {\n    console.log('🔄 Call rejected from interface');\n    this.endCall();\n    this.toastService.showInfo('Appel rejeté');\n  }\n\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n\n  playVoiceMessage(message: any): void {\n    console.log('🎵 [Voice] Playing voice message:', message.id);\n    this.toggleVoicePlayback(message);\n  }\n\n  isVoicePlaying(messageId: string): boolean {\n    return this.playingMessageId === messageId;\n  }\n\n  toggleVoicePlayback(message: any): void {\n    const messageId = message.id;\n    const audioUrl = this.getVoiceUrl(message);\n\n    if (!audioUrl) {\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\n      this.toastService.showError('Fichier audio introuvable');\n      return;\n    }\n\n    // Si c'est déjà en cours de lecture, arrêter\n    if (this.isVoicePlaying(messageId)) {\n      this.stopVoicePlayback();\n      return;\n    }\n\n    // Arrêter toute autre lecture en cours\n    this.stopVoicePlayback();\n\n    // Démarrer la nouvelle lecture\n    this.startVoicePlayback(message, audioUrl);\n  }\n\n  private startVoicePlayback(message: any, audioUrl: string): void {\n    const messageId = message.id;\n\n    try {\n      console.log(\n        '🎵 [Voice] Starting playback for:',\n        messageId,\n        'URL:',\n        audioUrl\n      );\n\n      this.currentAudio = new Audio(audioUrl);\n      this.playingMessageId = messageId;\n\n      // Initialiser les valeurs par défaut avec la nouvelle structure\n      const currentData = this.getVoicePlaybackData(messageId);\n      this.setVoicePlaybackData(messageId, {\n        progress: 0,\n        currentTime: 0,\n        speed: currentData.speed || 1,\n        duration: currentData.duration || 0,\n      });\n\n      // Configurer la vitesse de lecture\n      this.currentAudio.playbackRate = currentData.speed || 1;\n\n      // Événements audio\n      this.currentAudio.addEventListener('loadedmetadata', () => {\n        if (this.currentAudio) {\n          this.setVoicePlaybackData(messageId, {\n            duration: this.currentAudio.duration,\n          });\n          console.log(\n            '🎵 [Voice] Audio loaded, duration:',\n            this.currentAudio.duration\n          );\n        }\n      });\n\n      this.currentAudio.addEventListener('timeupdate', () => {\n        if (this.currentAudio && this.playingMessageId === messageId) {\n          const currentTime = this.currentAudio.currentTime;\n          const progress = (currentTime / this.currentAudio.duration) * 100;\n          this.setVoicePlaybackData(messageId, { currentTime, progress });\n          this.cdr.detectChanges();\n        }\n      });\n\n      this.currentAudio.addEventListener('ended', () => {\n        console.log('🎵 [Voice] Playback ended for:', messageId);\n        this.stopVoicePlayback();\n      });\n\n      this.currentAudio.addEventListener('error', (error) => {\n        console.error('🎵 [Voice] Audio error:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      });\n\n      // Démarrer la lecture\n      this.currentAudio\n        .play()\n        .then(() => {\n          console.log('🎵 [Voice] Playback started successfully');\n          this.toastService.showSuccess('🎵 Lecture du message vocal');\n        })\n        .catch((error) => {\n          console.error('🎵 [Voice] Error starting playback:', error);\n          this.toastService.showError('Impossible de lire le message vocal');\n          this.stopVoicePlayback();\n        });\n    } catch (error) {\n      console.error('🎵 [Voice] Error creating audio:', error);\n      this.toastService.showError('Erreur lors de la lecture audio');\n      this.stopVoicePlayback();\n    }\n  }\n\n  private stopVoicePlayback(): void {\n    if (this.currentAudio) {\n      console.log('🎵 [Voice] Stopping playback for:', this.playingMessageId);\n      this.currentAudio.pause();\n      this.currentAudio.currentTime = 0;\n      this.currentAudio = null;\n    }\n    this.playingMessageId = null;\n    this.cdr.detectChanges();\n  }\n\n  getVoiceUrl(message: any): string {\n    // Vérifier les propriétés directes d'audio\n    if (message.voiceUrl) return message.voiceUrl;\n    if (message.audioUrl) return message.audioUrl;\n    if (message.voice) return message.voice;\n\n    // Vérifier les attachments audio\n    const audioAttachment = message.attachments?.find(\n      (att: any) => att.type?.startsWith('audio/') || att.type === 'AUDIO'\n    );\n\n    if (audioAttachment) {\n      return audioAttachment.url || audioAttachment.path || '';\n    }\n\n    return '';\n  }\n\n  getVoiceWaves(message: any): number[] {\n    // Générer des waves basées sur l'ID du message pour la cohérence\n    const messageId = message.id || '';\n    const seed = messageId\n      .split('')\n      .reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);\n    const waves: number[] = [];\n\n    for (let i = 0; i < 16; i++) {\n      const height = 4 + ((seed + i * 7) % 20);\n      waves.push(height);\n    }\n\n    return waves;\n  }\n\n  getVoiceProgress(message: any): number {\n    const data = this.getVoicePlaybackData(message.id);\n    const totalWaves = 16;\n    return Math.floor((data.progress / 100) * totalWaves);\n  }\n\n  getVoiceCurrentTime(message: any): string {\n    const data = this.getVoicePlaybackData(message.id);\n    return this.formatAudioTime(data.currentTime);\n  }\n\n  getVoiceDuration(message: any): string {\n    const data = this.getVoicePlaybackData(message.id);\n    const duration = data.duration || message.metadata?.duration || 0;\n\n    if (typeof duration === 'string') {\n      return duration; // Déjà formaté\n    }\n\n    return this.formatAudioTime(duration);\n  }\n\n  private formatAudioTime(seconds: number): string {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  seekVoiceMessage(message: any, waveIndex: number): void {\n    const messageId = message.id;\n\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\n      return;\n    }\n\n    const totalWaves = 16;\n    const seekPercentage = (waveIndex / totalWaves) * 100;\n    const seekTime = (seekPercentage / 100) * this.currentAudio.duration;\n\n    this.currentAudio.currentTime = seekTime;\n    console.log('🎵 [Voice] Seeking to:', seekTime, 'seconds');\n  }\n\n  toggleVoiceSpeed(message: any): void {\n    const messageId = message.id;\n    const data = this.getVoicePlaybackData(messageId);\n\n    // Cycle entre 1x, 1.5x, 2x\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n\n    this.setVoicePlaybackData(messageId, { speed: newSpeed });\n\n    if (this.currentAudio && this.playingMessageId === messageId) {\n      this.currentAudio.playbackRate = newSpeed;\n    }\n\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.unsubscribe();\n\n    // Nettoyer les timers\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    // Nettoyer les ressources audio\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream?.getTracks().forEach((track) => track.stop());\n    }\n\n    // Nettoyer la lecture audio\n    this.stopVoicePlayback();\n  }\n}\n", "<!-- Chat Moderne Futuriste -->\n<div\n  style=\"\n    display: flex;\n    flex-direction: column;\n    height: 100vh;\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n    color: #1f2937;\n  \"\n>\n  <!-- En-tête -->\n  <header\n    style=\"\n      display: flex;\n      align-items: center;\n      padding: 12px 16px;\n      background: #ffffff;\n      border-bottom: 1px solid #e5e7eb;\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n    \"\n  >\n    <!-- Mode sombre -->\n    <style>\n      .dark header {\n        background: #1f2937;\n        border-color: #374151;\n      }\n      .dark {\n        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);\n        color: #f8fafc;\n      }\n    </style>\n    <!-- Bouton retour -->\n    <button\n      (click)=\"goBackToConversations()\"\n      style=\"\n        padding: 8px;\n        margin-right: 12px;\n        border-radius: 50%;\n        border: none;\n        background: transparent;\n        cursor: pointer;\n        transition: all 0.2s;\n      \"\n      onmouseover=\"this.style.background='#f3f4f6'\"\n      onmouseout=\"this.style.background='transparent'\"\n    >\n      <i class=\"fas fa-arrow-left\" style=\"color: #6b7280\"></i>\n    </button>\n\n    <!-- Info utilisateur -->\n    <div style=\"display: flex; align-items: center; flex: 1; min-width: 0\">\n      <div style=\"position: relative; margin-right: 12px\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          style=\"\n            width: 40px;\n            height: 40px;\n            border-radius: 50%;\n            object-fit: cover;\n            border: 2px solid #10b981;\n            cursor: pointer;\n            transition: transform 0.2s;\n          \"\n          (click)=\"openUserProfile(otherParticipant?.id!)\"\n          onmouseover=\"this.style.transform='scale(1.05)'\"\n          onmouseout=\"this.style.transform='scale(1)'\"\n        />\n        <div\n          *ngIf=\"otherParticipant?.isOnline\"\n          style=\"\n            position: absolute;\n            bottom: 0;\n            right: 0;\n            width: 12px;\n            height: 12px;\n            background: #10b981;\n            border: 2px solid #ffffff;\n            border-radius: 50%;\n            animation: pulse 2s infinite;\n          \"\n        ></div>\n      </div>\n\n      <div style=\"flex: 1; min-width: 0\">\n        <h3\n          style=\"\n            font-weight: 600;\n            color: #111827;\n            margin: 0;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n          \"\n        >\n          {{ otherParticipant?.username || \"Utilisateur\" }}\n        </h3>\n        <div style=\"font-size: 14px; color: #6b7280\">\n          <div\n            *ngIf=\"isUserTyping\"\n            style=\"display: flex; align-items: center; gap: 4px; color: #10b981\"\n          >\n            <span>En train d'écrire</span>\n            <div style=\"display: flex; gap: 2px\">\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite;\n                \"\n              ></div>\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite 0.1s;\n                \"\n              ></div>\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite 0.2s;\n                \"\n              ></div>\n            </div>\n          </div>\n          <span *ngIf=\"!isUserTyping\">\n            {{\n              otherParticipant?.isOnline\n                ? \"En ligne\"\n                : formatLastActive(otherParticipant?.lastActive)\n            }}\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Actions -->\n    <div style=\"display: flex; align-items: center; gap: 8px\">\n      <button\n        (click)=\"startVideoCall()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n        \"\n        title=\"Appel vidéo\"\n        onmouseover=\"this.style.background='#f3f4f6'\"\n        onmouseout=\"this.style.background='transparent'\"\n      >\n        <i class=\"fas fa-video\"></i>\n      </button>\n      <button\n        (click)=\"startVoiceCall()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n        \"\n        title=\"Appel vocal\"\n        onmouseover=\"this.style.background='#f3f4f6'\"\n        onmouseout=\"this.style.background='transparent'\"\n      >\n        <i class=\"fas fa-phone\"></i>\n      </button>\n      <button\n        (click)=\"toggleSearch()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n        \"\n        [style.background]=\"searchMode ? '#dcfce7' : 'transparent'\"\n        [style.color]=\"searchMode ? '#16a34a' : '#6b7280'\"\n        title=\"Rechercher\"\n        onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\n        onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\n      >\n        <i class=\"fas fa-search\"></i>\n      </button>\n\n      <button\n        (click)=\"toggleMainMenu()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n          position: relative;\n        \"\n        [style.background]=\"showMainMenu ? '#dcfce7' : 'transparent'\"\n        [style.color]=\"showMainMenu ? '#16a34a' : '#6b7280'\"\n        title=\"Menu\"\n        onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\n        onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\n      >\n        <i class=\"fas fa-ellipsis-v\"></i>\n      </button>\n    </div>\n\n    <!-- Menu principal dropdown -->\n    <div\n      *ngIf=\"showMainMenu\"\n      class=\"absolute top-16 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-48\"\n    >\n      <div class=\"p-2\">\n        <button\n          (click)=\"toggleSearch(); showMainMenu = false\"\n          class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n        >\n          <i class=\"fas fa-search text-blue-500\"></i>\n          <span class=\"text-gray-700 dark:text-gray-300\">Rechercher</span>\n        </button>\n        <button\n          class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n        >\n          <i class=\"fas fa-user text-green-500\"></i>\n          <span class=\"text-gray-700 dark:text-gray-300\">Voir le profil</span>\n        </button>\n        <button\n          class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n        >\n          <i class=\"fas fa-bell text-yellow-500\"></i>\n          <span class=\"text-gray-700 dark:text-gray-300\">Notifications</span>\n        </button>\n\n        <hr class=\"my-2 border-gray-200 dark:border-gray-600\" />\n        <button\n          class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n        >\n          <i class=\"fas fa-cog text-gray-500\"></i>\n          <span class=\"text-gray-700 dark:text-gray-300\">Paramètres</span>\n        </button>\n      </div>\n    </div>\n  </header>\n\n  <!-- Zone de messages avec drag & drop -->\n  <main\n    style=\"flex: 1; overflow-y: auto; padding: 16px; position: relative\"\n    #messagesContainer\n    (scroll)=\"onScroll($event)\"\n    (dragover)=\"onDragOver($event)\"\n    (dragleave)=\"onDragLeave($event)\"\n    (drop)=\"onDrop($event)\"\n    [style.background]=\"isDragOver ? 'rgba(34, 197, 94, 0.1)' : 'transparent'\"\n  >\n    <!-- Overlay drag & drop -->\n    <div\n      *ngIf=\"isDragOver\"\n      class=\"absolute inset-0 bg-green-500 bg-opacity-20 border-2 border-dashed border-green-500 rounded-lg flex items-center justify-center z-50 animate-pulse\"\n      style=\"\n        backdrop-filter: blur(2px);\n        background: linear-gradient(\n          45deg,\n          rgba(34, 197, 94, 0.1) 0%,\n          rgba(34, 197, 94, 0.2) 50%,\n          rgba(34, 197, 94, 0.1) 100%\n        );\n        animation: dragShimmer 2s infinite;\n      \"\n    >\n      <div\n        class=\"text-center bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-green-300 dark:border-green-600\"\n      >\n        <i\n          class=\"fas fa-cloud-upload-alt text-5xl text-green-600 mb-3 animate-bounce\"\n        ></i>\n        <p class=\"text-xl font-bold text-green-700 dark:text-green-400 mb-2\">\n          Déposez vos fichiers ici\n        </p>\n        <p class=\"text-sm text-green-600 dark:text-green-300\">\n          Images, vidéos, documents...\n        </p>\n        <div class=\"flex justify-center gap-2 mt-3\">\n          <span class=\"w-2 h-2 bg-green-500 rounded-full animate-ping\"></span>\n          <span\n            class=\"w-2 h-2 bg-green-500 rounded-full animate-ping\"\n            style=\"animation-delay: 0.2s\"\n          ></span>\n          <span\n            class=\"w-2 h-2 bg-green-500 rounded-full animate-ping\"\n            style=\"animation-delay: 0.4s\"\n          ></span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Styles CSS maintenant externalisés dans message-chat.component.css -->\n    <!-- Chargement -->\n    <div\n      *ngIf=\"isLoading\"\n      class=\"flex flex-col items-center justify-center py-8\"\n    >\n      <div\n        class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mb-4\"\n      ></div>\n      <span class=\"text-gray-500 dark:text-gray-400\"\n        >Chargement des messages...</span\n      >\n    </div>\n\n    <!-- État vide -->\n    <div\n      *ngIf=\"!isLoading && messages.length === 0\"\n      class=\"flex flex-col items-center justify-center py-16\"\n    >\n      <div class=\"text-6xl text-gray-300 dark:text-gray-600 mb-4\">\n        <i class=\"fas fa-comments\"></i>\n      </div>\n      <h3 class=\"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2\">\n        Aucun message\n      </h3>\n      <p class=\"text-gray-500 dark:text-gray-400 text-center\">\n        Commencez votre conversation avec {{ otherParticipant?.username }}\n      </p>\n    </div>\n\n    <!-- Messages -->\n    <div *ngIf=\"!isLoading && messages.length > 0\" class=\"space-y-2\">\n      <ng-container\n        *ngFor=\"\n          let message of messages;\n          let i = index;\n          trackBy: trackByMessageId\n        \"\n      >\n        <!-- Séparateur de date -->\n        <div\n          *ngIf=\"shouldShowDateSeparator(i)\"\n          class=\"flex justify-center my-4\"\n        >\n          <div\n            class=\"bg-white dark:bg-gray-700 px-3 py-1 rounded-full shadow-sm\"\n          >\n            <span class=\"text-xs text-gray-500 dark:text-gray-400\">\n              {{ formatDateSeparator(message.timestamp) }}\n            </span>\n          </div>\n        </div>\n\n        <!-- Message -->\n        <div\n          class=\"flex\"\n          [class.justify-end]=\"message.sender?.id === currentUserId\"\n          [class.justify-start]=\"message.sender?.id !== currentUserId\"\n          [id]=\"'message-' + message.id\"\n          (click)=\"onMessageClick(message, $event)\"\n          (contextmenu)=\"onMessageContextMenu(message, $event)\"\n        >\n          <!-- Avatar pour les autres -->\n          <div\n            *ngIf=\"message.sender?.id !== currentUserId && shouldShowAvatar(i)\"\n            class=\"mr-2 flex-shrink-0\"\n          >\n            <img\n              [src]=\"\n                message.sender?.image || 'assets/images/default-avatar.png'\n              \"\n              [alt]=\"message.sender?.username\"\n              class=\"w-8 h-8 rounded-full object-cover cursor-pointer hover:scale-105 transition-transform\"\n              (click)=\"openUserProfile(message.sender?.id!)\"\n            />\n          </div>\n\n          <!-- Bulle de message -->\n          <div\n            [style.background-color]=\"\n              message.sender?.id === currentUserId ? '#3b82f6' : '#ffffff'\n            \"\n            [style.color]=\"\n              message.sender?.id === currentUserId ? '#ffffff' : '#111827'\n            \"\n            style=\"\n              max-width: 320px;\n              padding: 12px 16px;\n              border-radius: 18px;\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n              position: relative;\n              word-wrap: break-word;\n              overflow-wrap: break-word;\n              border: none;\n            \"\n          >\n            <!-- Nom expéditeur (groupes) -->\n            <div\n              *ngIf=\"\n                isGroupConversation() &&\n                message.sender?.id !== currentUserId &&\n                shouldShowSenderName(i)\n              \"\n              class=\"text-xs font-semibold mb-1 opacity-75\"\n              [style.color]=\"getUserColor(message.sender?.id!)\"\n            >\n              {{ message.sender?.username }}\n            </div>\n\n            <!-- Contenu texte -->\n            <div *ngIf=\"getMessageType(message) === 'text'\" class=\"break-words\">\n              <div [innerHTML]=\"formatMessageContent(message.content)\"></div>\n            </div>\n\n            <!-- Image dans l'espace de chat -->\n            <div *ngIf=\"hasImage(message)\" style=\"margin: 8px 0\">\n              <img\n                [src]=\"getImageUrl(message)\"\n                [alt]=\"message.content || 'Image'\"\n                (click)=\"openImageViewer(message)\"\n                (load)=\"onImageLoad($event, message)\"\n                (error)=\"onImageError($event, message)\"\n                style=\"\n                  max-width: 280px;\n                  height: auto;\n                  border-radius: 12px;\n                  cursor: pointer;\n                  transition: transform 0.2s;\n                \"\n                onmouseover=\"this.style.transform='scale(1.02)'\"\n                onmouseout=\"this.style.transform='scale(1)'\"\n              />\n              <!-- Légende de l'image -->\n              <div\n                *ngIf=\"message.content\"\n                [style.color]=\"\n                  message.sender?.id === currentUserId ? '#ffffff' : '#111827'\n                \"\n                style=\"font-size: 14px; margin-top: 8px; line-height: 1.4\"\n                [innerHTML]=\"formatMessageContent(message.content)\"\n              ></div>\n            </div>\n\n            <!-- Message vocal -->\n            <div\n              *ngIf=\"getMessageType(message) === 'audio'\"\n              class=\"flex items-center gap-3 p-3 rounded-xl min-w-[250px] max-w-xs cursor-pointer transition-all duration-300 hover:shadow-lg group border bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600\"\n              [class.shadow-lg]=\"isVoicePlaying(message.id)\"\n              [class.ring-2]=\"isVoicePlaying(message.id)\"\n              [class.ring-blue-300]=\"isVoicePlaying(message.id)\"\n              style=\"position: relative; overflow: hidden\"\n            >\n              <!-- Bouton play/pause -->\n              <button\n                class=\"p-2 rounded-full text-white transition-all duration-300 flex-shrink-0 border-none outline-none cursor-pointer\"\n                [class.bg-blue-500]=\"!isVoicePlaying(message.id)\"\n                [class.hover:bg-blue-600]=\"!isVoicePlaying(message.id)\"\n                [class.bg-red-500]=\"isVoicePlaying(message.id)\"\n                [class.hover:bg-red-600]=\"isVoicePlaying(message.id)\"\n                [class.animate-pulse]=\"isVoicePlaying(message.id)\"\n                (click)=\"$event.stopPropagation(); toggleVoicePlayback(message)\"\n                style=\"\n                  width: 40px;\n                  height: 40px;\n                  display: flex;\n                  align-items: center;\n                  justify-content: center;\n                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n                \"\n                [style.transform]=\"\n                  isVoicePlaying(message.id) ? 'scale(1.05)' : 'scale(1)'\n                \"\n              >\n                <i\n                  class=\"fas text-sm transition-transform duration-200\"\n                  [class.fa-play]=\"!isVoicePlaying(message.id)\"\n                  [class.fa-pause]=\"isVoicePlaying(message.id)\"\n                  [class.scale-110]=\"isVoicePlaying(message.id)\"\n                ></i>\n              </button>\n\n              <div class=\"flex-1 min-w-0\">\n                <!-- Waves audio WhatsApp style avec progression -->\n                <div class=\"flex items-center gap-1 h-8 mb-2 px-1\">\n                  <div\n                    *ngFor=\"let wave of getVoiceWaves(message); let i = index\"\n                    class=\"w-1 rounded-full transition-all duration-300 cursor-pointer hover:scale-110 hover:opacity-80\"\n                    [class.bg-gray-400]=\"!isVoicePlaying(message.id)\"\n                    [class.bg-blue-500]=\"\n                      isVoicePlaying(message.id) &&\n                      i <= getVoiceProgress(message)\n                    \"\n                    [class.bg-gray-300]=\"\n                      isVoicePlaying(message.id) &&\n                      i > getVoiceProgress(message.id)\n                    \"\n                    [class.dark:bg-gray-500]=\"\n                      isVoicePlaying(message.id) &&\n                      i > getVoiceProgress(message.id)\n                    \"\n                    [style.height.px]=\"wave\"\n                    [class.animate-pulse]=\"\n                      isVoicePlaying(message.id) &&\n                      i <= getVoiceProgress(message)\n                    \"\n                    (click)=\"\n                      $event.stopPropagation(); seekVoiceMessage(message, i)\n                    \"\n                    style=\"\n                      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n                      min-height: 4px;\n                    \"\n                    [style.transform]=\"\n                      'scaleY(' +\n                      (isVoicePlaying(message.id) &&\n                      i <= getVoiceProgress(message)\n                        ? '1.3'\n                        : '1') +\n                      ')'\n                    \"\n                    [style.box-shadow]=\"\n                      isVoicePlaying(message.id) &&\n                      i <= getVoiceProgress(message)\n                        ? '0 0 8px rgba(59, 130, 246, 0.5)'\n                        : 'none'\n                    \"\n                  ></div>\n                </div>\n\n                <!-- Durée et progression -->\n                <div class=\"flex justify-between items-center text-xs mt-1\">\n                  <div class=\"flex items-center gap-2\">\n                    <span\n                      class=\"text-gray-600 dark:text-gray-300 font-mono text-xs\"\n                    >\n                      {{ getVoiceCurrentTime(message) }}\n                    </span>\n                    <span class=\"text-gray-400 dark:text-gray-500\">/</span>\n                    <span\n                      class=\"text-gray-500 dark:text-gray-400 font-mono text-xs\"\n                    >\n                      {{ getVoiceDuration(message) }}\n                    </span>\n                  </div>\n                  <div class=\"flex items-center gap-1\">\n                    <!-- Indicateur de vitesse -->\n                    <span\n                      *ngIf=\"getVoicePlaybackData(message.id).speed !== 1\"\n                      class=\"text-green-600 dark:text-green-400 font-semibold text-xs px-1 py-0.5 bg-green-100 dark:bg-green-900 rounded\"\n                    >\n                      {{ getVoicePlaybackData(message.id).speed }}x\n                    </span>\n                    <!-- Indicateur de lecture -->\n                    <div\n                      *ngIf=\"isVoicePlaying(message.id)\"\n                      class=\"flex items-center gap-0.5\"\n                    >\n                      <div\n                        class=\"w-1 h-1 bg-green-500 rounded-full animate-pulse\"\n                      ></div>\n                      <div\n                        class=\"w-1 h-1 bg-green-500 rounded-full animate-pulse\"\n                        style=\"animation-delay: 0.2s\"\n                      ></div>\n                      <div\n                        class=\"w-1 h-1 bg-green-500 rounded-full animate-pulse\"\n                        style=\"animation-delay: 0.4s\"\n                      ></div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Menu vitesse (apparaît au hover) -->\n              <div\n                class=\"opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col gap-1\"\n              >\n                <button\n                  class=\"p-1.5 rounded-full hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-500 dark:text-gray-400 transition-all duration-200\"\n                  (click)=\"$event.stopPropagation(); toggleVoiceSpeed(message)\"\n                  title=\"Vitesse de lecture\"\n                  style=\"border: none; outline: none; cursor: pointer\"\n                >\n                  <i class=\"fas fa-tachometer-alt text-xs\"></i>\n                </button>\n              </div>\n            </div>\n\n            <!-- Fichier -->\n            <div\n              *ngIf=\"\n                hasFile(message) &&\n                getMessageType(message) !== 'audio' &&\n                !hasImage(message)\n              \"\n              class=\"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors\"\n              (click)=\"downloadFile(message)\"\n            >\n              <div class=\"text-2xl text-gray-500 dark:text-gray-400\">\n                <i [class]=\"getFileIcon(message)\"></i>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <div class=\"font-medium text-sm truncate\">\n                  {{ getFileName(message) }}\n                </div>\n                <div class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {{ getFileSize(message) }}\n                </div>\n              </div>\n              <button\n                class=\"p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-400 transition-colors\"\n              >\n                <i class=\"fas fa-download text-sm\"></i>\n              </button>\n            </div>\n\n            <!-- Métadonnées -->\n            <div\n              class=\"flex items-center justify-end gap-1 mt-1 text-xs opacity-75\"\n            >\n              <span>{{ formatMessageTime(message.timestamp) }}</span>\n              <div\n                *ngIf=\"message.sender?.id === currentUserId\"\n                class=\"flex items-center\"\n              >\n                <i\n                  class=\"fas fa-clock\"\n                  *ngIf=\"message.status === 'SENDING'\"\n                  title=\"Envoi en cours\"\n                ></i>\n                <i\n                  class=\"fas fa-check\"\n                  *ngIf=\"message.status === 'SENT'\"\n                  title=\"Envoyé\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  *ngIf=\"message.status === 'DELIVERED'\"\n                  title=\"Livré\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double text-blue-400\"\n                  *ngIf=\"message.status === 'READ'\"\n                  title=\"Lu\"\n                ></i>\n              </div>\n            </div>\n\n            <!-- Réactions -->\n            <div\n              *ngIf=\"message.reactions && message.reactions.length > 0\"\n              class=\"flex gap-1 mt-2\"\n            >\n              <button\n                *ngFor=\"let reaction of message.reactions\"\n                (click)=\"toggleReaction(message.id!, reaction.emoji)\"\n                class=\"flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors\"\n                [class.bg-green-100]=\"\n                  hasUserReacted(reaction, currentUserId || '')\n                \"\n                [class.text-green-600]=\"\n                  hasUserReacted(reaction, currentUserId || '')\n                \"\n              >\n                <span>{{ reaction.emoji }}</span>\n                <span>{{ reaction.count || 1 }}</span>\n              </button>\n            </div>\n\n            <!-- Bouton de réaction rapide (apparaît au hover) -->\n            <button\n              (click)=\"showQuickReactions(message, $event)\"\n              class=\"absolute -bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-full p-1 shadow-sm hover:shadow-md\"\n              title=\"Ajouter une réaction\"\n            >\n              <i\n                class=\"fas fa-smile text-gray-500 dark:text-gray-400 text-xs\"\n              ></i>\n            </button>\n          </div>\n        </div>\n      </ng-container>\n\n      <!-- Indicateur de frappe (seulement quand l'autre personne tape) -->\n      <div *ngIf=\"otherUserIsTyping\" class=\"flex items-start gap-2\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-8 h-8 rounded-full object-cover\"\n        />\n        <div class=\"bg-white dark:bg-gray-700 px-4 py-2 rounded-2xl shadow-sm\">\n          <div class=\"flex gap-1\">\n            <div class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n            <div\n              class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n              style=\"animation-delay: 0.1s\"\n            ></div>\n            <div\n              class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n              style=\"animation-delay: 0.2s\"\n            ></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </main>\n\n  <!-- Progress bar pour upload -->\n  <div\n    *ngIf=\"isUploading\"\n    class=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2\"\n  >\n    <div class=\"flex items-center gap-3\">\n      <div class=\"flex-1\">\n        <div\n          class=\"flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1\"\n        >\n          <span>Envoi en cours...</span>\n          <span>{{ uploadProgress.toFixed(0) }}%</span>\n        </div>\n        <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n          <div\n            class=\"bg-green-500 h-2 rounded-full transition-all duration-300\"\n            [style.width.%]=\"uploadProgress\"\n          ></div>\n        </div>\n      </div>\n      <button\n        (click)=\"resetUploadState()\"\n        class=\"p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500\"\n        title=\"Annuler\"\n      >\n        <i class=\"fas fa-times text-sm\"></i>\n      </button>\n    </div>\n  </div>\n\n  <!-- Zone d'input -->\n  <footer\n    class=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4\"\n  >\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      class=\"flex items-end gap-3\"\n    >\n      <!-- Actions gauche -->\n      <div class=\"flex gap-2\">\n        <button\n          type=\"button\"\n          (click)=\"toggleEmojiPicker()\"\n          class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n          [class.bg-green-100]=\"showEmojiPicker\"\n          [class.text-green-600]=\"showEmojiPicker\"\n          title=\"Émojis\"\n        >\n          <i class=\"fas fa-smile\"></i>\n        </button>\n        <button\n          type=\"button\"\n          (click)=\"toggleAttachmentMenu()\"\n          class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n          [class.bg-green-100]=\"showAttachmentMenu\"\n          [class.text-green-600]=\"showAttachmentMenu\"\n          title=\"Joindre un fichier\"\n        >\n          <i class=\"fas fa-paperclip\"></i>\n        </button>\n      </div>\n\n      <!-- Champ de saisie -->\n      <div class=\"flex-1\">\n        <textarea\n          formControlName=\"content\"\n          #messageTextarea\n          placeholder=\"Tapez votre message...\"\n          class=\"w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400\"\n          [class.opacity-50]=\"isInputDisabled()\"\n          [class.cursor-not-allowed]=\"isInputDisabled()\"\n          (input)=\"onInputChange($event)\"\n          (keydown)=\"onInputKeyDown($event)\"\n          (focus)=\"onInputFocus()\"\n          (blur)=\"onInputBlur()\"\n          rows=\"1\"\n          maxlength=\"4096\"\n          autocomplete=\"off\"\n          spellcheck=\"true\"\n        >\n        </textarea>\n      </div>\n\n      <!-- Actions droite -->\n      <div class=\"flex gap-2\">\n        <!-- Enregistrement vocal -->\n        <button\n          *ngIf=\"!messageForm.get('content')?.value?.trim()\"\n          type=\"button\"\n          (mousedown)=\"onRecordStart($event)\"\n          (mouseup)=\"onRecordEnd($event)\"\n          (mouseleave)=\"onRecordCancel($event)\"\n          (touchstart)=\"onRecordStart($event)\"\n          (touchend)=\"onRecordEnd($event)\"\n          (touchcancel)=\"onRecordCancel($event)\"\n          [ngClass]=\"{\n            'voice-record-button': true,\n            recording: isRecordingVoice,\n            processing: voiceRecordingState === 'processing'\n          }\"\n          [disabled]=\"voiceRecordingState === 'processing'\"\n          title=\"Maintenir pour enregistrer un message vocal\"\n        >\n          <i\n            class=\"fas fa-microphone\"\n            *ngIf=\"voiceRecordingState !== 'processing'\"\n          ></i>\n          <i\n            class=\"fas fa-spinner\"\n            *ngIf=\"voiceRecordingState === 'processing'\"\n          ></i>\n        </button>\n\n        <!-- Bouton d'envoi -->\n        <button\n          *ngIf=\"messageForm.get('content')?.value?.trim()\"\n          type=\"button\"\n          (click)=\"sendMessage()\"\n          class=\"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50\"\n          [disabled]=\"isSendingMessage\"\n          title=\"Envoyer\"\n        >\n          <i class=\"fas fa-paper-plane\" *ngIf=\"!isSendingMessage\"></i>\n          <i class=\"fas fa-spinner fa-spin\" *ngIf=\"isSendingMessage\"></i>\n        </button>\n      </div>\n    </form>\n  </footer>\n\n  <!-- Interface d'enregistrement vocal style WhatsApp -->\n  <div\n    *ngIf=\"isRecordingVoice\"\n    class=\"absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4 z-50\"\n  >\n    <div class=\"flex items-center gap-4\">\n      <!-- Bouton annuler -->\n      <button\n        (click)=\"cancelVoiceRecording()\"\n        class=\"p-2 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-800 transition-colors\"\n        title=\"Annuler\"\n      >\n        <i class=\"fas fa-times\"></i>\n      </button>\n\n      <!-- Indicateur d'enregistrement -->\n      <div class=\"flex items-center gap-3 flex-1\">\n        <!-- Icône micro animée -->\n        <div class=\"relative\">\n          <i class=\"fas fa-microphone text-red-500 text-xl animate-pulse\"></i>\n          <div\n            class=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping\"\n          ></div>\n        </div>\n\n        <!-- Waves d'animation -->\n        <div class=\"flex items-center gap-1\">\n          <div\n            *ngFor=\"let wave of voiceWaves; let i = index\"\n            class=\"bg-green-500 rounded-full transition-all duration-150\"\n            [style.width.px]=\"2\"\n            [style.height.px]=\"wave\"\n            [style.animation-delay.ms]=\"i * 100\"\n          ></div>\n        </div>\n\n        <!-- Durée d'enregistrement -->\n        <div class=\"text-gray-600 dark:text-gray-300 font-mono\">\n          {{ formatRecordingDuration(voiceRecordingDuration) }}\n        </div>\n      </div>\n\n      <!-- Bouton envoyer -->\n      <button\n        (click)=\"stopVoiceRecording()\"\n        class=\"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors\"\n        title=\"Envoyer l'enregistrement\"\n      >\n        <i class=\"fas fa-paper-plane\"></i>\n      </button>\n    </div>\n\n    <!-- Barre de progression -->\n    <div class=\"mt-3 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1\">\n      <div\n        class=\"bg-green-500 h-1 rounded-full transition-all duration-300\"\n        [style.width.%]=\"(voiceRecordingDuration / 60) * 100\"\n      ></div>\n    </div>\n\n    <!-- Instructions -->\n    <div class=\"mt-2 text-center text-sm text-gray-500 dark:text-gray-400\">\n      <div class=\"flex items-center justify-center gap-2\">\n        <i class=\"fas fa-info-circle\"></i>\n        <span>Relâchez pour envoyer • Glissez vers le haut pour annuler</span>\n      </div>\n      <div class=\"mt-1 text-xs\">\n        Durée max: 60 secondes • Format: {{ getRecordingFormat() }}\n      </div>\n    </div>\n  </div>\n\n  <!-- Sélecteur d'émojis -->\n  <div\n    *ngIf=\"showEmojiPicker\"\n    class=\"absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n  >\n    <div class=\"p-4\">\n      <div class=\"flex gap-2 mb-4 overflow-x-auto\">\n        <button\n          *ngFor=\"let category of emojiCategories\"\n          (click)=\"selectEmojiCategory(category)\"\n          class=\"px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0\"\n          [class.bg-green-100]=\"selectedEmojiCategory === category\"\n          [class.text-green-600]=\"selectedEmojiCategory === category\"\n          [class.hover:bg-gray-100]=\"selectedEmojiCategory !== category\"\n          [class.dark:hover:bg-gray-700]=\"selectedEmojiCategory !== category\"\n        >\n          {{ category.icon }}\n        </button>\n      </div>\n      <div class=\"grid grid-cols-8 gap-2 max-h-48 overflow-y-auto\">\n        <button\n          *ngFor=\"let emoji of getEmojisForCategory(selectedEmojiCategory)\"\n          (click)=\"insertEmoji(emoji)\"\n          class=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\"\n          [title]=\"emoji.name\"\n        >\n          {{ emoji.emoji }}\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Menu des pièces jointes -->\n  <div\n    *ngIf=\"showAttachmentMenu\"\n    class=\"absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n  >\n    <div class=\"p-4\">\n      <div class=\"grid grid-cols-2 gap-3\">\n        <button\n          (click)=\"triggerFileInput('image')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-image text-blue-600 dark:text-blue-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Photos</span\n          >\n        </button>\n        <button\n          (click)=\"triggerFileInput('video')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-video text-purple-600 dark:text-purple-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Vidéos</span\n          >\n        </button>\n        <button\n          (click)=\"triggerFileInput('document')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-file text-orange-600 dark:text-orange-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Documents</span\n          >\n        </button>\n        <button\n          (click)=\"openCamera()\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-camera text-green-600 dark:text-green-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Caméra</span\n          >\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Input caché pour fichiers -->\n  <input\n    #fileInput\n    type=\"file\"\n    class=\"hidden\"\n    (change)=\"onFileSelected($event)\"\n    [accept]=\"getFileAcceptTypes()\"\n    multiple\n  />\n\n  <!-- Sélecteur de réaction rapide -->\n  <div\n    *ngIf=\"showReactionPicker\"\n    class=\"fixed bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 p-3\"\n    [style.left.px]=\"contextMenuPosition.x - 100\"\n    [style.top.px]=\"contextMenuPosition.y - 60\"\n  >\n    <div class=\"flex gap-2\">\n      <button\n        *ngFor=\"let emoji of ['❤️', '😂', '😮', '😢', '😡', '👍']\"\n        (click)=\"quickReact(emoji)\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\"\n      >\n        {{ emoji }}\n      </button>\n    </div>\n  </div>\n\n  <!-- Menu contextuel pour messages -->\n  <div\n    *ngIf=\"showMessageContextMenu\"\n    class=\"fixed bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-48\"\n    [style.left.px]=\"contextMenuPosition.x\"\n    [style.top.px]=\"contextMenuPosition.y\"\n  >\n    <div class=\"p-2\">\n      <button\n        (click)=\"replyToMessage(selectedMessage)\"\n        class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n      >\n        <i class=\"fas fa-reply text-blue-500\"></i>\n        <span class=\"text-gray-700 dark:text-gray-300\">Répondre</span>\n      </button>\n      <button\n        (click)=\"forwardMessage(selectedMessage)\"\n        class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n      >\n        <i class=\"fas fa-share text-green-500\"></i>\n        <span class=\"text-gray-700 dark:text-gray-300\">Transférer</span>\n      </button>\n      <button\n        (click)=\"showQuickReactions(selectedMessage, $event)\"\n        class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n      >\n        <i class=\"fas fa-smile text-yellow-500\"></i>\n        <span class=\"text-gray-700 dark:text-gray-300\">Réagir</span>\n      </button>\n      <hr class=\"my-2 border-gray-200 dark:border-gray-600\" />\n      <button\n        (click)=\"deleteMessage(selectedMessage)\"\n        class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left text-red-600\"\n      >\n        <i class=\"fas fa-trash text-red-500\"></i>\n        <span>Supprimer</span>\n      </button>\n    </div>\n  </div>\n\n  <!-- Overlay pour fermer les menus -->\n  <div\n    *ngIf=\"\n      showEmojiPicker ||\n      showAttachmentMenu ||\n      showMainMenu ||\n      showMessageContextMenu ||\n      showReactionPicker\n    \"\n    class=\"fixed inset-0 bg-black bg-opacity-25 z-40\"\n    (click)=\"closeAllMenus()\"\n  ></div>\n</div>\n\n<!-- Interface d'appel WebRTC -->\n<app-call-interface\n  [isVisible]=\"isInCall\"\n  [activeCall]=\"activeCall\"\n  [callType]=\"callType\"\n  [otherParticipant]=\"otherParticipant\"\n  (callEnded)=\"endCall()\"\n  (callAccepted)=\"onCallAccepted($event)\"\n  (callRejected)=\"onCallRejected()\"\n></app-call-interface>\n\n<!-- Visionneuse d'images plein écran -->\n<div\n  *ngIf=\"showImageViewer\"\n  class=\"fixed inset-0 bg-black bg-opacity-95 z-50 flex items-center justify-center\"\n  (click)=\"closeImageViewer()\"\n>\n  <div class=\"relative max-w-full max-h-full p-4\">\n    <!-- Bouton fermer -->\n    <button\n      (click)=\"closeImageViewer()\"\n      class=\"absolute top-4 right-4 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all\"\n      title=\"Fermer\"\n    >\n      <i class=\"fas fa-times text-xl\"></i>\n    </button>\n\n    <!-- Bouton télécharger -->\n    <button\n      (click)=\"downloadImage(); $event.stopPropagation()\"\n      class=\"absolute top-4 left-4 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all\"\n      title=\"Télécharger\"\n    >\n      <i class=\"fas fa-download text-xl\"></i>\n    </button>\n\n    <!-- Image -->\n    <img\n      [src]=\"selectedImage?.url\"\n      [alt]=\"selectedImage?.name || 'Image'\"\n      class=\"max-w-full max-h-full object-contain rounded-lg shadow-2xl image-viewer-zoom\"\n      (click)=\"$event.stopPropagation()\"\n      style=\"max-height: 90vh; max-width: 90vw\"\n    />\n\n    <!-- Informations de l'image -->\n    <div\n      class=\"absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 text-white p-3 rounded-lg\"\n      (click)=\"$event.stopPropagation()\"\n    >\n      <div class=\"flex items-center justify-between\">\n        <div>\n          <p class=\"font-medium\">{{ selectedImage?.name || \"Image\" }}</p>\n          <p class=\"text-sm opacity-75\">\n            {{ selectedImage?.size || \"Taille inconnue\" }}\n          </p>\n        </div>\n        <div class=\"flex gap-2\">\n          <!-- Bouton zoom -->\n          <button\n            (click)=\"zoomImage(1.2); $event.stopPropagation()\"\n            class=\"p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all\"\n            title=\"Zoom +\"\n          >\n            <i class=\"fas fa-search-plus\"></i>\n          </button>\n          <button\n            (click)=\"zoomImage(0.8); $event.stopPropagation()\"\n            class=\"p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all\"\n            title=\"Zoom -\"\n          >\n            <i class=\"fas fa-search-minus\"></i>\n          </button>\n          <!-- Bouton reset zoom -->\n          <button\n            (click)=\"resetZoom(); $event.stopPropagation()\"\n            class=\"p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all\"\n            title=\"Taille originale\"\n          >\n            <i class=\"fas fa-expand-arrows-alt\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";AAQA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,QAAQ,MAAM;AAGnC,SAASC,QAAQ,QAA4B,kCAAkC;;;;;;;;;;;;ICwDvEC,EAAA,CAAAC,SAAA,cAaO;;;;;IAiBLD,EAAA,CAAAE,cAAA,cAGC;IACOF,EAAA,CAAAG,MAAA,6BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9BJ,EAAA,CAAAE,cAAA,cAAqC;IACnCF,EAAA,CAAAC,SAAA,cAQO;IAmBTD,EAAA,CAAAI,YAAA,EAAM;;;;;IAERJ,EAAA,CAAAE,cAAA,WAA4B;IAC1BF,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IALLJ,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,OAAAC,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAC,QAAA,iBAAAF,MAAA,CAAAG,gBAAA,CAAAH,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAG,UAAA,OAKF;;;;;;IAoFNX,EAAA,CAAAE,cAAA,cAGC;IAGKF,EAAA,CAAAY,UAAA,mBAAAC,6DAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAASD,OAAA,CAAAE,YAAA,EAAc;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAH,OAAA,CAAAI,YAAA,GAAiB,KAAK;IAAA,EAAC;IAG9CpB,EAAA,CAAAC,SAAA,YAA2C;IAC3CD,EAAA,CAAAE,cAAA,eAA+C;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAElEJ,EAAA,CAAAE,cAAA,iBAEC;IACCF,EAAA,CAAAC,SAAA,YAA0C;IAC1CD,EAAA,CAAAE,cAAA,eAA+C;IAAAF,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEtEJ,EAAA,CAAAE,cAAA,kBAEC;IACCF,EAAA,CAAAC,SAAA,aAA2C;IAC3CD,EAAA,CAAAE,cAAA,gBAA+C;IAAAF,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGrEJ,EAAA,CAAAC,SAAA,cAAwD;IACxDD,EAAA,CAAAE,cAAA,kBAEC;IACCF,EAAA,CAAAC,SAAA,aAAwC;IACxCD,EAAA,CAAAE,cAAA,gBAA+C;IAAAF,EAAA,CAAAG,MAAA,uBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAiBtEJ,EAAA,CAAAE,cAAA,cAaC;IAIGF,EAAA,CAAAC,SAAA,YAEK;IACLD,EAAA,CAAAE,cAAA,YAAqE;IACnEF,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,YAAsD;IACpDF,EAAA,CAAAG,MAAA,0CACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,cAA4C;IAC1CF,EAAA,CAAAC,SAAA,eAAoE;IAStED,EAAA,CAAAI,YAAA,EAAM;;;;;IAMVJ,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,cAEO;IACPD,EAAA,CAAAE,cAAA,eACG;IAAAF,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAC5B;;;;;IAIHJ,EAAA,CAAAE,cAAA,cAGC;IAEGF,EAAA,CAAAC,SAAA,YAA+B;IACjCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAAwE;IACtEF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAAwD;IACtDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,wCAAAe,MAAA,CAAAb,gBAAA,kBAAAa,MAAA,CAAAb,gBAAA,CAAAc,QAAA,MACF;;;;;IAaEtB,EAAA,CAAAE,cAAA,eAGC;IAKKF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAiB,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAAC,SAAA,OACF;;;;;;IAcF1B,EAAA,CAAAE,cAAA,eAGC;IAOGF,EAAA,CAAAY,UAAA,mBAAAe,+EAAA;MAAA3B,EAAA,CAAAc,aAAA,CAAAc,IAAA;MAAA,MAAAH,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAW,OAAA,CAAAC,eAAA,CAAAN,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,CAAoC;IAAA,EAAC;IANhDjC,EAAA,CAAAI,YAAA,EAOE;;;;IANAJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAG,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAEC,QAAAX,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA;;;;;IA2BHtB,EAAA,CAAAE,cAAA,eAQC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAHJJ,EAAA,CAAAqC,WAAA,UAAAC,OAAA,CAAAC,YAAA,CAAAd,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,EAAiD;IAEjDjC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAmB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA,MACF;;;;;IAGAtB,EAAA,CAAAE,cAAA,eAAoE;IAClEF,EAAA,CAAAC,SAAA,eAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;IADCJ,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAkC,UAAA,cAAAM,OAAA,CAAAC,oBAAA,CAAAhB,WAAA,CAAAiB,OAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAAmD;;;;;IAsBxD3C,EAAA,CAAAC,SAAA,eAOO;;;;;IALLD,EAAA,CAAAqC,WAAA,WAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAW,OAAA,CAAAC,aAAA,yBAEC;IAED7C,EAAA,CAAAkC,UAAA,cAAAU,OAAA,CAAAH,oBAAA,CAAAhB,WAAA,CAAAiB,OAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAAmD;;;;;;IAxBvD3C,EAAA,CAAAE,cAAA,eAAqD;IAIjDF,EAAA,CAAAY,UAAA,mBAAAkC,+EAAA;MAAA9C,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAmB,OAAA,GAAAhD,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA6B,OAAA,CAAAC,eAAA,CAAAxB,WAAA,CAAwB;IAAA,EAAC,kBAAAyB,8EAAAC,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAuB,OAAA,GAAApD,EAAA,CAAAiB,aAAA;MAAA,OAC1BjB,EAAA,CAAAmB,WAAA,CAAAiC,OAAA,CAAAC,WAAA,CAAAF,MAAA,EAAA1B,WAAA,CAA4B;IAAA,EADF,mBAAA6B,+EAAAH,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAA0B,OAAA,GAAAvD,EAAA,CAAAiB,aAAA;MAAA,OAEzBjB,EAAA,CAAAmB,WAAA,CAAAoC,OAAA,CAAAC,YAAA,CAAAL,MAAA,EAAA1B,WAAA,CAA6B;IAAA,EAFJ;IAHpCzB,EAAA,CAAAI,YAAA,EAeE;IAEFJ,EAAA,CAAAyD,UAAA,IAAAC,+DAAA,mBAOO;IACT1D,EAAA,CAAAI,YAAA,EAAM;;;;;IAxBFJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAkC,UAAA,QAAAyB,OAAA,CAAAC,WAAA,CAAAnC,WAAA,GAAAzB,EAAA,CAAAoC,aAAA,CAA4B,QAAAX,WAAA,CAAAiB,OAAA;IAiB3B1C,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAiB,OAAA,CAAqB;;;;;;IAkDpB1C,EAAA,CAAAE,cAAA,eA0CC;IArBCF,EAAA,CAAAY,UAAA,mBAAAiD,qFAAAV,MAAA;MAAA,MAAAW,WAAA,GAAA9D,EAAA,CAAAc,aAAA,CAAAiD,IAAA;MAAA,MAAAC,KAAA,GAAAF,WAAA,CAAAG,KAAA;MAAA,MAAAxC,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,IAAAY,SAAA;MAAA,MAAAqC,OAAA,GAAAlE,EAAA,CAAAiB,aAAA;MACyBkC,MAAA,CAAAgB,eAAA,EAAwB;MAAA,OAAEnE,EAAA,CAAAmB,WAAA,CAAA+C,OAAA,CAAAE,gBAAA,CAAA3C,WAAA,EAAAuC,KAAA,CAEvE;IAAA,EADqB;IAmBFhE,EAAA,CAAAI,YAAA,EAAM;;;;;;;IA1BLJ,EAAA,CAAAqC,WAAA,WAAAgC,QAAA,OAAwB,2BAAAC,OAAA,CAAAC,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,KAAA+B,KAAA,IAAAM,OAAA,CAAAE,gBAAA,CAAA/C,WAAA,sCAAA6C,OAAA,CAAAC,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,KAAA+B,KAAA,IAAAM,OAAA,CAAAE,gBAAA,CAAA/C,WAAA;IAbxBzB,EAAA,CAAAyE,WAAA,iBAAAH,OAAA,CAAAC,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,EAAiD,gBAAAqC,OAAA,CAAAC,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,KAAA+B,KAAA,IAAAM,OAAA,CAAAE,gBAAA,CAAA/C,WAAA,kBAAA6C,OAAA,CAAAC,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,KAAA+B,KAAA,GAAAM,OAAA,CAAAE,gBAAA,CAAA/C,WAAA,CAAAQ,EAAA,uBAAAqC,OAAA,CAAAC,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,KAAA+B,KAAA,GAAAM,OAAA,CAAAE,gBAAA,CAAA/C,WAAA,CAAAQ,EAAA,oBAAAqC,OAAA,CAAAC,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,KAAA+B,KAAA,IAAAM,OAAA,CAAAE,gBAAA,CAAA/C,WAAA;;;;;IA2DjDzB,EAAA,CAAAE,cAAA,gBAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAoE,OAAA,CAAAC,oBAAA,CAAAlD,WAAA,CAAAQ,EAAA,EAAA2C,KAAA,OACF;;;;;IAEA5E,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAC,SAAA,eAEO;IASTD,EAAA,CAAAI,YAAA,EAAM;;;;;;IA5HdJ,EAAA,CAAAE,cAAA,eAOC;IASGF,EAAA,CAAAY,UAAA,mBAAAiE,kFAAA1B,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAgE,IAAA;MAAA,MAAArD,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAkD,OAAA,GAAA/E,EAAA,CAAAiB,aAAA;MAASkC,MAAA,CAAAgB,eAAA,EAAwB;MAAA,OAAEnE,EAAA,CAAAmB,WAAA,CAAA4D,OAAA,CAAAC,mBAAA,CAAAvD,WAAA,CAA4B;IAAA,EAAC;IAahEzB,EAAA,CAAAC,SAAA,aAKK;IACPD,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAE,cAAA,eAA4B;IAGxBF,EAAA,CAAAyD,UAAA,IAAAwB,+DAAA,oBA0CO;IACTjF,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAA4D;IAKtDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,iBAA+C;IAAAF,EAAA,CAAAG,MAAA,SAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAE,cAAA,iBAEC;IACCF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAETJ,EAAA,CAAAE,cAAA,gBAAqC;IAEnCF,EAAA,CAAAyD,UAAA,KAAAyB,iEAAA,oBAKO;IAEPlF,EAAA,CAAAyD,UAAA,KAAA0B,gEAAA,mBAeM;IACRnF,EAAA,CAAAI,YAAA,EAAM;IAKVJ,EAAA,CAAAE,cAAA,gBAEC;IAGGF,EAAA,CAAAY,UAAA,mBAAAwE,mFAAAjC,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAgE,IAAA;MAAA,MAAArD,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAwD,OAAA,GAAArF,EAAA,CAAAiB,aAAA;MAASkC,MAAA,CAAAgB,eAAA,EAAwB;MAAA,OAAEnE,EAAA,CAAAmB,WAAA,CAAAkE,OAAA,CAAAC,gBAAA,CAAA7D,WAAA,CAAyB;IAAA,EAAC;IAI7DzB,EAAA,CAAAC,SAAA,cAA6C;IAC/CD,EAAA,CAAAI,YAAA,EAAS;;;;;IAzIXJ,EAAA,CAAAyE,WAAA,cAAAc,OAAA,CAAAhB,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,EAA8C,WAAAsD,OAAA,CAAAhB,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,oBAAAsD,OAAA,CAAAhB,cAAA,CAAA9C,WAAA,CAAAQ,EAAA;IAsB5CjC,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAqC,WAAA,cAAAkD,OAAA,CAAAhB,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,+BAEC;IAhBDjC,EAAA,CAAAyE,WAAA,iBAAAc,OAAA,CAAAhB,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,EAAiD,uBAAAsD,OAAA,CAAAhB,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,iBAAAsD,OAAA,CAAAhB,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,uBAAAsD,OAAA,CAAAhB,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,oBAAAsD,OAAA,CAAAhB,cAAA,CAAA9C,WAAA,CAAAQ,EAAA;IAoB/CjC,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAyE,WAAA,aAAAc,OAAA,CAAAhB,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,EAA6C,aAAAsD,OAAA,CAAAhB,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,gBAAAsD,OAAA,CAAAhB,cAAA,CAAA9C,WAAA,CAAAQ,EAAA;IAU1BjC,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAkC,UAAA,YAAAqD,OAAA,CAAAC,aAAA,CAAA/D,WAAA,EAA2B;IAkD1CzB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAiF,OAAA,CAAAE,mBAAA,CAAAhE,WAAA,OACF;IAKEzB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAiF,OAAA,CAAAG,gBAAA,CAAAjE,WAAA,OACF;IAKGzB,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAAkC,UAAA,SAAAqD,OAAA,CAAAZ,oBAAA,CAAAlD,WAAA,CAAAQ,EAAA,EAAA2C,KAAA,OAAkD;IAOlD5E,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAkC,UAAA,SAAAqD,OAAA,CAAAhB,cAAA,CAAA9C,WAAA,CAAAQ,EAAA,EAAgC;;;;;;IAmC3CjC,EAAA,CAAAE,cAAA,eAQC;IADCF,EAAA,CAAAY,UAAA,mBAAA+E,+EAAA;MAAA3F,EAAA,CAAAc,aAAA,CAAA8E,IAAA;MAAA,MAAAnE,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAgE,OAAA,GAAA7F,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA0E,OAAA,CAAAC,YAAA,CAAArE,WAAA,CAAqB;IAAA,EAAC;IAE/BzB,EAAA,CAAAE,cAAA,eAAuD;IACrDF,EAAA,CAAAC,SAAA,QAAsC;IACxCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAA4B;IAExBF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAAsD;IACpDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAE,cAAA,kBAEC;IACCF,EAAA,CAAAC,SAAA,aAAuC;IACzCD,EAAA,CAAAI,YAAA,EAAS;;;;;IAdJJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA+F,UAAA,CAAAC,OAAA,CAAAC,WAAA,CAAAxE,WAAA,EAA8B;IAI/BzB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA0F,OAAA,CAAAE,WAAA,CAAAzE,WAAA,OACF;IAEEzB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA0F,OAAA,CAAAG,WAAA,CAAA1E,WAAA,OACF;;;;;IAkBAzB,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IAvBPD,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAyD,UAAA,IAAA2C,8DAAA,iBAIK;IACLpG,EAAA,CAAAyD,UAAA,IAAA4C,8DAAA,iBAIK;IACLrG,EAAA,CAAAyD,UAAA,IAAA6C,8DAAA,iBAIK;IACLtG,EAAA,CAAAyD,UAAA,IAAA8C,8DAAA,iBAIK;IACPvG,EAAA,CAAAI,YAAA,EAAM;;;;IAlBDJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA+E,MAAA,eAAkC;IAKlCxG,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA+E,MAAA,YAA+B;IAK/BxG,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA+E,MAAA,iBAAoC;IAKpCxG,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA+E,MAAA,YAA+B;;;;;;IAWpCxG,EAAA,CAAAE,cAAA,kBAUC;IARCF,EAAA,CAAAY,UAAA,mBAAA6F,4FAAA;MAAA,MAAA3C,WAAA,GAAA9D,EAAA,CAAAc,aAAA,CAAA4F,IAAA;MAAA,MAAAC,YAAA,GAAA7C,WAAA,CAAAjC,SAAA;MAAA,MAAAJ,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,IAAAY,SAAA;MAAA,MAAA+E,OAAA,GAAA5G,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAyF,OAAA,CAAAC,cAAA,CAAApF,WAAA,CAAAQ,EAAA,EAAA0E,YAAA,CAAAG,KAAA,CAA2C;IAAA,EAAC;IASrD9G,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjCJ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IARtCJ,EAAA,CAAAyE,WAAA,iBAAAsC,OAAA,CAAAC,cAAA,CAAAL,YAAA,EAAAI,OAAA,CAAAlE,aAAA,QAEC,mBAAAkE,OAAA,CAAAC,cAAA,CAAAL,YAAA,EAAAI,OAAA,CAAAlE,aAAA;IAKK7C,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAiH,iBAAA,CAAAN,YAAA,CAAAG,KAAA,CAAoB;IACpB9G,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAiH,iBAAA,CAAAN,YAAA,CAAAO,KAAA,MAAyB;;;;;IAhBnClH,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAyD,UAAA,IAAA0D,mEAAA,sBAaS;IACXnH,EAAA,CAAAI,YAAA,EAAM;;;;IAbmBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAkC,UAAA,YAAAT,WAAA,CAAA2F,SAAA,CAAoB;;;;;;IAlUnDpH,EAAA,CAAAqH,uBAAA,GAMC;IAECrH,EAAA,CAAAyD,UAAA,IAAA6D,yDAAA,kBAWM;IAGNtH,EAAA,CAAAE,cAAA,cAOC;IAFCF,EAAA,CAAAY,UAAA,mBAAA2G,yEAAApE,MAAA;MAAA,MAAAW,WAAA,GAAA9D,EAAA,CAAAc,aAAA,CAAA0G,IAAA;MAAA,MAAA/F,WAAA,GAAAqC,WAAA,CAAAjC,SAAA;MAAA,MAAA4F,OAAA,GAAAzH,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAsG,OAAA,CAAAC,cAAA,CAAAjG,WAAA,EAAA0B,MAAA,CAA+B;IAAA,EAAC,yBAAAwE,+EAAAxE,MAAA;MAAA,MAAAW,WAAA,GAAA9D,EAAA,CAAAc,aAAA,CAAA0G,IAAA;MAAA,MAAA/F,WAAA,GAAAqC,WAAA,CAAAjC,SAAA;MAAA,MAAA+F,OAAA,GAAA5H,EAAA,CAAAiB,aAAA;MAAA,OAC1BjB,EAAA,CAAAmB,WAAA,CAAAyG,OAAA,CAAAC,oBAAA,CAAApG,WAAA,EAAA0B,MAAA,CAAqC;IAAA,EADX;IAIzCnD,EAAA,CAAAyD,UAAA,IAAAqE,yDAAA,kBAYM;IAGN9H,EAAA,CAAAE,cAAA,cAiBC;IAECF,EAAA,CAAAyD,UAAA,IAAAsE,yDAAA,kBAUM;IAGN/H,EAAA,CAAAyD,UAAA,IAAAuE,yDAAA,kBAEM;IAGNhI,EAAA,CAAAyD,UAAA,IAAAwE,yDAAA,kBA0BM;IAGNjI,EAAA,CAAAyD,UAAA,IAAAyE,yDAAA,oBA8IM;IAGNlI,EAAA,CAAAyD,UAAA,IAAA0E,yDAAA,mBAyBM;IAGNnI,EAAA,CAAAE,cAAA,eAEC;IACOF,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAyD,UAAA,KAAA2E,0DAAA,kBAwBM;IACRpI,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAyD,UAAA,KAAA4E,0DAAA,kBAkBM;IAGNrI,EAAA,CAAAE,cAAA,mBAIC;IAHCF,EAAA,CAAAY,UAAA,mBAAA0H,6EAAAnF,MAAA;MAAA,MAAAW,WAAA,GAAA9D,EAAA,CAAAc,aAAA,CAAA0G,IAAA;MAAA,MAAA/F,WAAA,GAAAqC,WAAA,CAAAjC,SAAA;MAAA,MAAA0G,OAAA,GAAAvI,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAoH,OAAA,CAAAC,kBAAA,CAAA/G,WAAA,EAAA0B,MAAA,CAAmC;IAAA,EAAC;IAI7CnD,EAAA,CAAAC,SAAA,cAEK;IACPD,EAAA,CAAAI,YAAA,EAAS;IAGfJ,EAAA,CAAAyI,qBAAA,EAAe;;;;;;IApVVzI,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAkC,UAAA,SAAAwG,OAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAejC5I,EAAA,CAAAK,SAAA,GAA0D;IAA1DL,EAAA,CAAAyE,WAAA,iBAAAhD,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAyG,OAAA,CAAA7F,aAAA,CAA0D,mBAAApB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAyG,OAAA,CAAA7F,aAAA;IAE1D7C,EAAA,CAAAkC,UAAA,oBAAAT,WAAA,CAAAQ,EAAA,CAA8B;IAM3BjC,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAAkC,UAAA,UAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAyG,OAAA,CAAA7F,aAAA,IAAA6F,OAAA,CAAAG,gBAAA,CAAAD,KAAA,EAAiE;IAelE5I,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAqC,WAAA,sBAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAyG,OAAA,CAAA7F,aAAA,yBAEC,WAAApB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAyG,OAAA,CAAA7F,aAAA;IAiBE7C,EAAA,CAAAK,SAAA,GAKf;IALeL,EAAA,CAAAkC,UAAA,SAAAwG,OAAA,CAAAI,mBAAA,OAAArH,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAyG,OAAA,CAAA7F,aAAA,IAAA6F,OAAA,CAAAK,oBAAA,CAAAH,KAAA,EAKf;IAOkB5I,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAkC,UAAA,SAAAwG,OAAA,CAAAM,cAAA,CAAAvH,WAAA,aAAwC;IAKxCzB,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAAwG,OAAA,CAAAO,QAAA,CAAAxH,WAAA,EAAuB;IA8B1BzB,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAkC,UAAA,SAAAwG,OAAA,CAAAM,cAAA,CAAAvH,WAAA,cAAyC;IAiJzCzB,EAAA,CAAAK,SAAA,GAKf;IALeL,EAAA,CAAAkC,UAAA,SAAAwG,OAAA,CAAAQ,OAAA,CAAAzH,WAAA,KAAAiH,OAAA,CAAAM,cAAA,CAAAvH,WAAA,kBAAAiH,OAAA,CAAAO,QAAA,CAAAxH,WAAA,EAKf;IAyBoBzB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAiH,iBAAA,CAAAyB,OAAA,CAAAS,iBAAA,CAAA1H,WAAA,CAAAC,SAAA,EAA0C;IAE7C1B,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAkC,UAAA,UAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAyG,OAAA,CAAA7F,aAAA,CAA0C;IA4B5C7C,EAAA,CAAAK,SAAA,GAAuD;IAAvDL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA2F,SAAA,IAAA3F,WAAA,CAAA2F,SAAA,CAAAgC,MAAA,KAAuD;;;;;IAkChEpJ,EAAA,CAAAE,cAAA,eAA8D;IAC5DF,EAAA,CAAAC,SAAA,eAIE;IACFD,EAAA,CAAAE,cAAA,eAAuE;IAEnEF,EAAA,CAAAC,SAAA,eAAmE;IASrED,EAAA,CAAAI,YAAA,EAAM;;;;IAfNJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAkC,UAAA,SAAAmH,OAAA,CAAA7I,gBAAA,kBAAA6I,OAAA,CAAA7I,gBAAA,CAAA2B,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAAqE,QAAAiH,OAAA,CAAA7I,gBAAA,kBAAA6I,OAAA,CAAA7I,gBAAA,CAAAc,QAAA;;;;;IAnW3EtB,EAAA,CAAAE,cAAA,cAAiE;IAC/DF,EAAA,CAAAyD,UAAA,IAAA6F,mDAAA,6BA6Ve;IAGftJ,EAAA,CAAAyD,UAAA,IAAA8F,0CAAA,kBAmBM;IACRvJ,EAAA,CAAAI,YAAA,EAAM;;;;IAlXuBJ,EAAA,CAAAK,SAAA,GACZ;IADYL,EAAA,CAAAkC,UAAA,YAAAsH,MAAA,CAAAC,QAAA,CACZ,iBAAAD,MAAA,CAAAE,gBAAA;IA6VT1J,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAAsH,MAAA,CAAAG,iBAAA,CAAuB;;;;;;IAwBjC3J,EAAA,CAAAE,cAAA,eAGC;IAMaF,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9BJ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE/CJ,EAAA,CAAAE,cAAA,eAAkE;IAChEF,EAAA,CAAAC,SAAA,eAGO;IACTD,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAE,cAAA,mBAIC;IAHCF,EAAA,CAAAY,UAAA,mBAAAgJ,8DAAA;MAAA5J,EAAA,CAAAc,aAAA,CAAA+I,IAAA;MAAA,MAAAC,OAAA,GAAA9J,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA2I,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAI5B/J,EAAA,CAAAC,SAAA,cAAoC;IACtCD,EAAA,CAAAI,YAAA,EAAS;;;;IAfCJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,kBAAA,KAAA0J,MAAA,CAAAC,cAAA,CAAAC,OAAA,SAAgC;IAKpClK,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAqC,WAAA,UAAA2H,MAAA,CAAAC,cAAA,MAAgC;;;;;IAwFlCjK,EAAA,CAAAC,SAAA,aAGK;;;;;IACLD,EAAA,CAAAC,SAAA,aAGK;;;;;;;;;;;;;IAxBPD,EAAA,CAAAE,cAAA,kBAgBC;IAbCF,EAAA,CAAAY,UAAA,uBAAAuJ,oEAAAhH,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAsJ,IAAA;MAAA,MAAAC,OAAA,GAAArK,EAAA,CAAAiB,aAAA;MAAA,OAAajB,EAAA,CAAAmB,WAAA,CAAAkJ,OAAA,CAAAC,aAAA,CAAAnH,MAAA,CAAqB;IAAA,EAAC,qBAAAoH,kEAAApH,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAsJ,IAAA;MAAA,MAAAI,OAAA,GAAAxK,EAAA,CAAAiB,aAAA;MAAA,OACxBjB,EAAA,CAAAmB,WAAA,CAAAqJ,OAAA,CAAAC,WAAA,CAAAtH,MAAA,CAAmB;IAAA,EADK,wBAAAuH,qEAAAvH,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAsJ,IAAA;MAAA,MAAAO,OAAA,GAAA3K,EAAA,CAAAiB,aAAA;MAAA,OAErBjB,EAAA,CAAAmB,WAAA,CAAAwJ,OAAA,CAAAC,cAAA,CAAAzH,MAAA,CAAsB;IAAA,EAFD,wBAAA0H,qEAAA1H,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAsJ,IAAA;MAAA,MAAAU,OAAA,GAAA9K,EAAA,CAAAiB,aAAA;MAAA,OAGrBjB,EAAA,CAAAmB,WAAA,CAAA2J,OAAA,CAAAR,aAAA,CAAAnH,MAAA,CAAqB;IAAA,EAHA,sBAAA4H,mEAAA5H,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAsJ,IAAA;MAAA,MAAAY,OAAA,GAAAhL,EAAA,CAAAiB,aAAA;MAAA,OAIvBjB,EAAA,CAAAmB,WAAA,CAAA6J,OAAA,CAAAP,WAAA,CAAAtH,MAAA,CAAmB;IAAA,EAJI,yBAAA8H,sEAAA9H,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAsJ,IAAA;MAAA,MAAAc,OAAA,GAAAlL,EAAA,CAAAiB,aAAA;MAAA,OAKpBjB,EAAA,CAAAmB,WAAA,CAAA+J,OAAA,CAAAN,cAAA,CAAAzH,MAAA,CAAsB;IAAA,EALF;IAcnCnD,EAAA,CAAAyD,UAAA,IAAA0H,2CAAA,iBAGK;IACLnL,EAAA,CAAAyD,UAAA,IAAA2H,2CAAA,iBAGK;IACPpL,EAAA,CAAAI,YAAA,EAAS;;;;IAhBPJ,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAAqL,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,gBAAA,EAAAD,OAAA,CAAAE,mBAAA,mBAIE,aAAAF,OAAA,CAAAE,mBAAA;IAMCzL,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAkC,UAAA,SAAAqJ,OAAA,CAAAE,mBAAA,kBAA0C;IAI1CzL,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAkC,UAAA,SAAAqJ,OAAA,CAAAE,mBAAA,kBAA0C;;;;;IAa7CzL,EAAA,CAAAC,SAAA,aAA4D;;;;;IAC5DD,EAAA,CAAAC,SAAA,aAA+D;;;;;;IATjED,EAAA,CAAAE,cAAA,kBAOC;IAJCF,EAAA,CAAAY,UAAA,mBAAA8K,gEAAA;MAAA1L,EAAA,CAAAc,aAAA,CAAA6K,KAAA;MAAA,MAAAC,QAAA,GAAA5L,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAyK,QAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAKvB7L,EAAA,CAAAyD,UAAA,IAAAqI,2CAAA,iBAA4D;IAC5D9L,EAAA,CAAAyD,UAAA,IAAAsI,2CAAA,iBAA+D;IACjE/L,EAAA,CAAAI,YAAA,EAAS;;;;IALPJ,EAAA,CAAAkC,UAAA,aAAA8J,OAAA,CAAAC,gBAAA,CAA6B;IAGEjM,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,UAAA8J,OAAA,CAAAC,gBAAA,CAAuB;IACnBjM,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAkC,UAAA,SAAA8J,OAAA,CAAAC,gBAAA,CAAsB;;;;;IAiCzDjM,EAAA,CAAAC,SAAA,eAMO;;;;;IAHLD,EAAA,CAAAqC,WAAA,kBAAoB,WAAA6J,SAAA,2BAAAC,MAAA;;;;;;IA7B9BnM,EAAA,CAAAE,cAAA,eAGC;IAIKF,EAAA,CAAAY,UAAA,mBAAAwL,6DAAA;MAAApM,EAAA,CAAAc,aAAA,CAAAuL,KAAA;MAAA,MAAAC,QAAA,GAAAtM,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAmL,QAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAIhCvM,EAAA,CAAAC,SAAA,aAA4B;IAC9BD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,eAA4C;IAGxCF,EAAA,CAAAC,SAAA,aAAoE;IAItED,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAAqC;IACnCF,EAAA,CAAAyD,UAAA,IAAA+I,0CAAA,mBAMO;IACTxM,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,gBAAwD;IACtDF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAE,cAAA,mBAIC;IAHCF,EAAA,CAAAY,UAAA,mBAAA6L,8DAAA;MAAAzM,EAAA,CAAAc,aAAA,CAAAuL,KAAA;MAAA,MAAAK,QAAA,GAAA1M,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAuL,QAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAI9B3M,EAAA,CAAAC,SAAA,cAAkC;IACpCD,EAAA,CAAAI,YAAA,EAAS;IAIXJ,EAAA,CAAAE,cAAA,gBAAuE;IACrEF,EAAA,CAAAC,SAAA,gBAGO;IACTD,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,gBAAuE;IAEnEF,EAAA,CAAAC,SAAA,cAAkC;IAClCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,2EAAyD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAExEJ,EAAA,CAAAE,cAAA,gBAA0B;IACxBF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IAxCiBJ,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAkC,UAAA,YAAA0K,OAAA,CAAAC,UAAA,CAAe;IAUlC7M,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAsM,OAAA,CAAAE,uBAAA,CAAAF,OAAA,CAAAG,sBAAA,OACF;IAiBA/M,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAqC,WAAA,UAAAuK,OAAA,CAAAG,sBAAA,iBAAqD;IAWrD/M,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,iDAAAsM,OAAA,CAAAI,kBAAA,QACF;;;;;;IAWEhN,EAAA,CAAAE,cAAA,kBAQC;IANCF,EAAA,CAAAY,UAAA,mBAAAqM,sEAAA;MAAA,MAAAnJ,WAAA,GAAA9D,EAAA,CAAAc,aAAA,CAAAoM,KAAA;MAAA,MAAAC,aAAA,GAAArJ,WAAA,CAAAjC,SAAA;MAAA,MAAAuL,QAAA,GAAApN,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAiM,QAAA,CAAAC,mBAAA,CAAAF,aAAA,CAA6B;IAAA,EAAC;IAOvCnN,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IANPJ,EAAA,CAAAyE,WAAA,iBAAA6I,QAAA,CAAAC,qBAAA,KAAAJ,aAAA,CAAyD,mBAAAG,QAAA,CAAAC,qBAAA,KAAAJ,aAAA,uBAAAG,QAAA,CAAAC,qBAAA,KAAAJ,aAAA,4BAAAG,QAAA,CAAAC,qBAAA,KAAAJ,aAAA;IAKzDnN,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA6M,aAAA,CAAAK,IAAA,MACF;;;;;;IAGAxN,EAAA,CAAAE,cAAA,kBAKC;IAHCF,EAAA,CAAAY,UAAA,mBAAA6M,sEAAA;MAAA,MAAA3J,WAAA,GAAA9D,EAAA,CAAAc,aAAA,CAAA4M,KAAA;MAAA,MAAAC,UAAA,GAAA7J,WAAA,CAAAjC,SAAA;MAAA,MAAA+L,QAAA,GAAA5N,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAyM,QAAA,CAAAC,WAAA,CAAAF,UAAA,CAAkB;IAAA,EAAC;IAI5B3N,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAHPJ,EAAA,CAAAkC,UAAA,UAAAyL,UAAA,CAAAG,IAAA,CAAoB;IAEpB9N,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAqN,UAAA,CAAA7G,KAAA,MACF;;;;;IA1BN9G,EAAA,CAAAE,cAAA,eAGC;IAGKF,EAAA,CAAAyD,UAAA,IAAAsK,6CAAA,sBAUS;IACX/N,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAA6D;IAC3DF,EAAA,CAAAyD,UAAA,IAAAuK,6CAAA,sBAOS;IACXhO,EAAA,CAAAI,YAAA,EAAM;;;;IApBmBJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAkC,UAAA,YAAA+L,OAAA,CAAAC,eAAA,CAAkB;IAarBlO,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAkC,UAAA,YAAA+L,OAAA,CAAAE,oBAAA,CAAAF,OAAA,CAAAV,qBAAA,EAA8C;;;;;;IAYxEvN,EAAA,CAAAE,cAAA,eAGC;IAIOF,EAAA,CAAAY,UAAA,mBAAAwN,6DAAA;MAAApO,EAAA,CAAAc,aAAA,CAAAuN,KAAA;MAAA,MAAAC,QAAA,GAAAtO,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAmN,QAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnCvO,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAC,SAAA,aAA6D;IAC/DD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBACG;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAEHJ,EAAA,CAAAE,cAAA,kBAGC;IAFCF,EAAA,CAAAY,UAAA,mBAAA4N,6DAAA;MAAAxO,EAAA,CAAAc,aAAA,CAAAuN,KAAA;MAAA,MAAAI,QAAA,GAAAzO,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAsN,QAAA,CAAAF,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnCvO,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAC,SAAA,cAAiE;IACnED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAEHJ,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAY,UAAA,mBAAA8N,8DAAA;MAAA1O,EAAA,CAAAc,aAAA,CAAAuN,KAAA;MAAA,MAAAM,QAAA,GAAA3O,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAwN,QAAA,CAAAJ,gBAAA,CAAiB,UAAU,CAAC;IAAA,EAAC;IAGtCvO,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAC,SAAA,cAAgE;IAClED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EACX;IAEHJ,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAY,UAAA,mBAAAgO,8DAAA;MAAA5O,EAAA,CAAAc,aAAA,CAAAuN,KAAA;MAAA,MAAAQ,QAAA,GAAA7O,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA0N,QAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAGtB9O,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAC,SAAA,cAAgE;IAClED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;;;;;;IAwBLJ,EAAA,CAAAE,cAAA,kBAIC;IAFCF,EAAA,CAAAY,UAAA,mBAAAmO,sEAAA;MAAA,MAAAjL,WAAA,GAAA9D,EAAA,CAAAc,aAAA,CAAAkO,KAAA;MAAA,MAAAC,UAAA,GAAAnL,WAAA,CAAAjC,SAAA;MAAA,MAAAqN,QAAA,GAAAlP,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA+N,QAAA,CAAAC,UAAA,CAAAF,UAAA,CAAiB;IAAA,EAAC;IAG3BjP,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IADPJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA2O,UAAA,MACF;;;;;;;;IAbJjP,EAAA,CAAAE,cAAA,eAKC;IAEGF,EAAA,CAAAyD,UAAA,IAAA2L,6CAAA,sBAMS;IACXpP,EAAA,CAAAI,YAAA,EAAM;;;;IAXNJ,EAAA,CAAAqC,WAAA,SAAAgN,OAAA,CAAAC,mBAAA,CAAAC,CAAA,aAA6C,QAAAF,OAAA,CAAAC,mBAAA,CAAAE,CAAA;IAKvBxP,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAAyP,eAAA,IAAAC,GAAA,EAAuC;;;;;;IAU/D1P,EAAA,CAAAE,cAAA,eAKC;IAGKF,EAAA,CAAAY,UAAA,mBAAA+O,6DAAA;MAAA3P,EAAA,CAAAc,aAAA,CAAA8O,KAAA;MAAA,MAAAC,QAAA,GAAA7P,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA0O,QAAA,CAAAC,cAAA,CAAAD,QAAA,CAAAE,eAAA,CAA+B;IAAA,EAAC;IAGzC/P,EAAA,CAAAC,SAAA,aAA0C;IAC1CD,EAAA,CAAAE,cAAA,eAA+C;IAAAF,EAAA,CAAAG,MAAA,oBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEhEJ,EAAA,CAAAE,cAAA,iBAGC;IAFCF,EAAA,CAAAY,UAAA,mBAAAoP,6DAAA;MAAAhQ,EAAA,CAAAc,aAAA,CAAA8O,KAAA;MAAA,MAAAK,QAAA,GAAAjQ,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA8O,QAAA,CAAAC,cAAA,CAAAD,QAAA,CAAAF,eAAA,CAA+B;IAAA,EAAC;IAGzC/P,EAAA,CAAAC,SAAA,aAA2C;IAC3CD,EAAA,CAAAE,cAAA,eAA+C;IAAAF,EAAA,CAAAG,MAAA,sBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAElEJ,EAAA,CAAAE,cAAA,kBAGC;IAFCF,EAAA,CAAAY,UAAA,mBAAAuP,8DAAAhN,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAA8O,KAAA;MAAA,MAAAQ,QAAA,GAAApQ,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAiP,QAAA,CAAA5H,kBAAA,CAAA4H,QAAA,CAAAL,eAAA,EAAA5M,MAAA,CAA2C;IAAA,EAAC;IAGrDnD,EAAA,CAAAC,SAAA,cAA4C;IAC5CD,EAAA,CAAAE,cAAA,gBAA+C;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE9DJ,EAAA,CAAAC,SAAA,cAAwD;IACxDD,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAY,UAAA,mBAAAyP,8DAAA;MAAArQ,EAAA,CAAAc,aAAA,CAAA8O,KAAA;MAAA,MAAAU,QAAA,GAAAtQ,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAmP,QAAA,CAAAC,aAAA,CAAAD,QAAA,CAAAP,eAAA,CAA8B;IAAA,EAAC;IAGxC/P,EAAA,CAAAC,SAAA,cAAyC;IACzCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IA/B1BJ,EAAA,CAAAqC,WAAA,SAAAmO,OAAA,CAAAlB,mBAAA,CAAAC,CAAA,OAAuC,QAAAiB,OAAA,CAAAlB,mBAAA,CAAAE,CAAA;;;;;;IAqCzCxP,EAAA,CAAAE,cAAA,eAUC;IADCF,EAAA,CAAAY,UAAA,mBAAA6P,0DAAA;MAAAzQ,EAAA,CAAAc,aAAA,CAAA4P,KAAA;MAAA,MAAAC,QAAA,GAAA3Q,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAwP,QAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1B5Q,EAAA,CAAAI,YAAA,EAAM;;;;;;IAeTJ,EAAA,CAAAE,cAAA,eAIC;IADCF,EAAA,CAAAY,UAAA,mBAAAiQ,0DAAA;MAAA7Q,EAAA,CAAAc,aAAA,CAAAgQ,KAAA;MAAA,MAAAC,QAAA,GAAA/Q,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA4P,QAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAE5BhR,EAAA,CAAAE,cAAA,eAAgD;IAG5CF,EAAA,CAAAY,UAAA,mBAAAqQ,6DAAA;MAAAjR,EAAA,CAAAc,aAAA,CAAAgQ,KAAA;MAAA,MAAAI,QAAA,GAAAlR,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA+P,QAAA,CAAAF,gBAAA,EAAkB;IAAA,EAAC;IAI5BhR,EAAA,CAAAC,SAAA,aAAoC;IACtCD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,kBAIC;IAHCF,EAAA,CAAAY,UAAA,mBAAAuQ,6DAAAhO,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAgQ,KAAA;MAAA,MAAAM,QAAA,GAAApR,EAAA,CAAAiB,aAAA;MAASmQ,QAAA,CAAAC,aAAA,EAAe;MAAA,OAAErR,EAAA,CAAAmB,WAAA,CAAAgC,MAAA,CAAAgB,eAAA,EAAwB;IAAA,EAAC;IAInDnE,EAAA,CAAAC,SAAA,aAAuC;IACzCD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,eAME;IAFAF,EAAA,CAAAY,UAAA,mBAAA0Q,0DAAAnO,MAAA;MAAA,OAASA,MAAA,CAAAgB,eAAA,EAAwB;IAAA,EAAC;IAJpCnE,EAAA,CAAAI,YAAA,EAME;IAGFJ,EAAA,CAAAE,cAAA,eAGC;IADCF,EAAA,CAAAY,UAAA,mBAAA2Q,0DAAApO,MAAA;MAAA,OAASA,MAAA,CAAAgB,eAAA,EAAwB;IAAA,EAAC;IAElCnE,EAAA,CAAAE,cAAA,eAA+C;IAEpBF,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC/DJ,EAAA,CAAAE,cAAA,cAA8B;IAC5BF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAENJ,EAAA,CAAAE,cAAA,eAAwB;IAGpBF,EAAA,CAAAY,UAAA,mBAAA4Q,8DAAArO,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAgQ,KAAA;MAAA,MAAAW,QAAA,GAAAzR,EAAA,CAAAiB,aAAA;MAASwQ,QAAA,CAAAC,SAAA,CAAU,GAAG,CAAC;MAAA,OAAE1R,EAAA,CAAAmB,WAAA,CAAAgC,MAAA,CAAAgB,eAAA,EAAwB;IAAA,EAAC;IAIlDnE,EAAA,CAAAC,SAAA,cAAkC;IACpCD,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,mBAIC;IAHCF,EAAA,CAAAY,UAAA,mBAAA+Q,8DAAAxO,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAgQ,KAAA;MAAA,MAAAc,QAAA,GAAA5R,EAAA,CAAAiB,aAAA;MAAS2Q,QAAA,CAAAF,SAAA,CAAU,GAAG,CAAC;MAAA,OAAE1R,EAAA,CAAAmB,WAAA,CAAAgC,MAAA,CAAAgB,eAAA,EAAwB;IAAA,EAAC;IAIlDnE,EAAA,CAAAC,SAAA,cAAmC;IACrCD,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAE,cAAA,mBAIC;IAHCF,EAAA,CAAAY,UAAA,mBAAAiR,8DAAA1O,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAgQ,KAAA;MAAA,MAAAgB,QAAA,GAAA9R,EAAA,CAAAiB,aAAA;MAAS6Q,QAAA,CAAAC,SAAA,EAAW;MAAA,OAAE/R,EAAA,CAAAmB,WAAA,CAAAgC,MAAA,CAAAgB,eAAA,EAAwB;IAAA,EAAC;IAI/CnE,EAAA,CAAAC,SAAA,cAAwC;IAC1CD,EAAA,CAAAI,YAAA,EAAS;;;;IA1CbJ,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAkC,UAAA,QAAA8P,OAAA,CAAAC,aAAA,kBAAAD,OAAA,CAAAC,aAAA,CAAAC,GAAA,EAAAlS,EAAA,CAAAoC,aAAA,CAA0B,SAAA4P,OAAA,CAAAC,aAAA,kBAAAD,OAAA,CAAAC,aAAA,CAAAnE,IAAA;IAcC9N,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAiH,iBAAA,EAAA+K,OAAA,CAAAC,aAAA,kBAAAD,OAAA,CAAAC,aAAA,CAAAnE,IAAA,aAAoC;IAEzD9N,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,OAAA0R,OAAA,CAAAC,aAAA,kBAAAD,OAAA,CAAAC,aAAA,CAAAE,IAAA,4BACF;;;AD7mCV,OAAM,MAAOC,oBAAoB;EA2I/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,cAA8B,EAC9BC,YAA0B,EAC1BC,GAAsB;IAJtB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IA1Ib;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAlJ,QAAQ,GAAU,EAAE;IACpB,KAAA5G,aAAa,GAAkB,IAAI;IACnC,KAAA+P,eAAe,GAAG,KAAK;IAEvB,KAAApS,gBAAgB,GAAQ,IAAI;IAE5B;IACA,KAAAqS,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAApH,gBAAgB,GAAG,KAAK;IACxB,KAAAtC,iBAAiB,GAAG,KAAK;IACzB,KAAAvI,YAAY,GAAG,KAAK;IACpB,KAAAkS,sBAAsB,GAAG,KAAK;IAC9B,KAAAvD,eAAe,GAAQ,IAAI;IAC3B,KAAAT,mBAAmB,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IACpC,KAAA+D,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,qBAAqB,GAAQ,IAAI;IAEjC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAxB,aAAa,GAAQ,IAAI;IACzB,KAAAhI,cAAc,GAAG,CAAC;IAClB,KAAAyJ,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAElB;IACA,KAAAnI,gBAAgB,GAAG,KAAK;IACxB,KAAAuB,sBAAsB,GAAG,CAAC;IAC1B,KAAAtB,mBAAmB,GAAwC,MAAM;IACzD,KAAAmI,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAClC,KAAAjH,UAAU,GAAa,CACrB,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CACzD;IAED;IACQ,KAAAkH,YAAY,GAA4B,IAAI;IAC5C,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,aAAa,GAOjB,EAAE;IAEN;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAA6B,IAAI;IACzC,KAAAC,YAAY,GAAG,CAAC;IACR,KAAAC,SAAS,GAAQ,IAAI;IAE7B;IACA,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAElD;IACA,KAAAzG,eAAe,GAAU,CACvB;MACEjM,EAAE,EAAE,SAAS;MACb6L,IAAI,EAAE,SAAS;MACfN,IAAI,EAAE,IAAI;MACVoH,MAAM,EAAE,CACN;QAAE9N,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAe,CAAE,EACtC;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAwB,CAAE;KAElD,EACD;MACE7L,EAAE,EAAE,QAAQ;MACZ6L,IAAI,EAAE,QAAQ;MACdN,IAAI,EAAE,IAAI;MACVoH,MAAM,EAAE,CACN;QAAE9N,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAW,CAAE;KAErC,EACD;MACE7L,EAAE,EAAE,QAAQ;MACZ6L,IAAI,EAAE,QAAQ;MACdN,IAAI,EAAE,IAAI;MACVoH,MAAM,EAAE,CACN;QAAE9N,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAY,CAAE,EACnC;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAa,CAAE,EACpC;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEhH,KAAK,EAAE,IAAI;QAAEgH,IAAI,EAAE;MAAO,CAAE;KAEjC,CACF;IACD,KAAAP,qBAAqB,GAAG,IAAI,CAACW,eAAe,CAAC,CAAC,CAAC;IAE/C;IACiB,KAAA2G,oBAAoB,GAAG,EAAE;IAClC,KAAAC,WAAW,GAAG,CAAC;IAEvB;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,KAAK;IACZ,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAG,IAAIpV,YAAY,EAAE;IASxC,IAAI,CAACqV,WAAW,GAAG,IAAI,CAAC7C,EAAE,CAAC8C,KAAK,CAAC;MAC/B1S,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7C,UAAU,CAACwV,QAAQ,EAAExV,UAAU,CAACyV,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEA;EACAC,eAAeA,CAAA;IACb,OACE,CAAC,IAAI,CAAC/U,gBAAgB,IAAI,IAAI,CAACgL,gBAAgB,IAAI,IAAI,CAACS,gBAAgB;EAE5E;EAEA;EACQuJ,gBAAgBA,CAAA;IACtB,MAAMC,cAAc,GAAG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC;IACtD,IAAI,IAAI,CAACH,eAAe,EAAE,EAAE;MAC1BE,cAAc,EAAEE,OAAO,EAAE;KAC1B,MAAM;MACLF,cAAc,EAAEG,MAAM,EAAE;;EAE5B;EAEAC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA,IAAI,CAACjB,aAAa,CAACkB,GAAG,CACpB,IAAI,CAAC5D,cAAc,CAAC6D,aAAa,CAACC,SAAS,CAAC;MAC1CC,IAAI,EAAGC,YAAY,IAAI;QACrB,IAAIA,YAAY,EAAE;UAChBV,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAES,YAAY,CAAC;UACvD,IAAI,CAACC,kBAAkB,CAACD,YAAY,CAAC;;MAEzC,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACxB,aAAa,CAACkB,GAAG,CACpB,IAAI,CAAC5D,cAAc,CAACmE,WAAW,CAACL,SAAS,CAAC;MACxCC,IAAI,EAAGK,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACRd,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEa,IAAI,CAAC;UAC5C,IAAI,CAACtC,UAAU,GAAGsC,IAAI;;MAE1B,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC9D;KACD,CAAC,CACH;EACH;EAEQD,kBAAkBA,CAACD,YAA0B;IACnD;IACA;IACAV,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCS,YAAY,CAACK,MAAM,CAACvV,QAAQ,CAC7B;IAED;IACA,IAAI,CAACkR,cAAc,CAACsE,IAAI,CAAC,UAAU,CAAC;IAEpC;IACA;IACA;EACF;;EAEQb,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMc,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/CnB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEgB,UAAU,CAAC;MAEzD,IAAI,CAACA,UAAU,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,WAAW,EAAE;QACtEjB,OAAO,CAACY,KAAK,CAAC,gCAAgC,CAAC;QAC/C,IAAI,CAAC7T,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC+P,eAAe,GAAG,KAAK;QAC5B;;MAGF,MAAMsE,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;MACnCjB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmB,IAAI,CAAC;MAE3C;MACA,MAAMG,MAAM,GAAGH,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAACjV,EAAE,IAAIiV,IAAI,CAACG,MAAM;MACjDvB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CuB,GAAG,EAAEJ,IAAI,CAACI,GAAG;QACbrV,EAAE,EAAEiV,IAAI,CAACjV,EAAE;QACXoV,MAAM,EAAEH,IAAI,CAACG,MAAM;QACnBE,SAAS,EAAEF;OACZ,CAAC;MAEF,IAAIA,MAAM,EAAE;QACV,IAAI,CAACxU,aAAa,GAAGwU,MAAM;QAC3B,IAAI,CAACzE,eAAe,GAAGsE,IAAI,CAAC5V,QAAQ,IAAI4V,IAAI,CAACpJ,IAAI,IAAI,KAAK;QAC1DgI,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;UACjD9T,EAAE,EAAE,IAAI,CAACY,aAAa;UACtBvB,QAAQ,EAAE,IAAI,CAACsR;SAChB,CAAC;OACH,MAAM;QACLkD,OAAO,CAACY,KAAK,CAAC,0CAA0C,EAAEQ,IAAI,CAAC;QAC/D,IAAI,CAACrU,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC+P,eAAe,GAAG,KAAK;;KAE/B,CAAC,OAAO8D,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAI,CAAC7T,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC+P,eAAe,GAAG,KAAK;;EAEhC;EAEQsD,gBAAgBA,CAAA;IACtB,MAAMsB,cAAc,GAAG,IAAI,CAACjF,KAAK,CAACkF,QAAQ,CAACC,QAAQ,CAAChC,GAAG,CAAC,IAAI,CAAC;IAC7DI,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEyB,cAAc,CAAC;IAE5D,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAAC/E,YAAY,CAACkF,SAAS,CAAC,6BAA6B,CAAC;MAC1D;;IAGF,IAAI,CAAC9E,SAAS,GAAG,IAAI;IACrB,IAAI,CAACL,cAAc,CAACoF,eAAe,CAACJ,cAAc,CAAC,CAAClB,SAAS,CAAC;MAC5DC,IAAI,EAAG5D,YAAY,IAAI;QACrBmD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEpD,YAAY,CAAC;QACjEmD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;UACxC9T,EAAE,EAAE0Q,YAAY,EAAE1Q,EAAE;UACpB4V,YAAY,EAAElF,YAAY,EAAEkF,YAAY;UACxCC,iBAAiB,EAAEnF,YAAY,EAAEkF,YAAY,EAAEzO,MAAM;UACrD2O,OAAO,EAAEpF,YAAY,EAAEoF,OAAO;UAC9BtO,QAAQ,EAAEkJ,YAAY,EAAElJ,QAAQ;UAChCuO,aAAa,EAAErF,YAAY,EAAElJ,QAAQ,EAAEL;SACxC,CAAC;QACF,IAAI,CAACuJ,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACsF,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;QAEnB;QACA,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC;MACDzB,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAACjE,YAAY,CAACkF,SAAS,CACzB,8CAA8C,CAC/C;QACD,IAAI,CAAC9E,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQoF,mBAAmBA,CAAA;IACzB,IACE,CAAC,IAAI,CAACtF,YAAY,EAAEkF,YAAY,IAChC,IAAI,CAAClF,YAAY,CAACkF,YAAY,CAACzO,MAAM,KAAK,CAAC,EAC3C;MACA0M,OAAO,CAACsC,IAAI,CAAC,uCAAuC,CAAC;MACrD,IAAI,CAAC5X,gBAAgB,GAAG,IAAI;MAC5B;;IAGFsV,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAClT,aAAa,CAAC;IACnDiT,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACpD,YAAY,CAACkF,YAAY,CAAC;IAEhE;IACA;IAEA,IAAI,IAAI,CAAClF,YAAY,CAACoF,OAAO,EAAE;MAC7B;MACA;MACA,IAAI,CAACvX,gBAAgB,GAAG,IAAI,CAACmS,YAAY,CAACkF,YAAY,CAACQ,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAACrW,EAAE,IAAIqW,CAAC,CAAChB,GAAG;QACnC,OAAOkB,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAAC3V,aAAa,CAAC;MAC7D,CAAC,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACrC,gBAAgB,GAAG,IAAI,CAACmS,YAAY,CAACkF,YAAY,CAACQ,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAACrW,EAAE,IAAIqW,CAAC,CAAChB,GAAG;QACnCxB,OAAO,CAACC,GAAG,CACT,2BAA2B,EAC3BwC,aAAa,EACb,uBAAuB,EACvB,IAAI,CAAC1V,aAAa,CACnB;QACD,OAAO2V,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAAC3V,aAAa,CAAC;MAC7D,CAAC,CAAC;;IAGJ;IACA,IAAI,CAAC,IAAI,CAACrC,gBAAgB,IAAI,IAAI,CAACmS,YAAY,CAACkF,YAAY,CAACzO,MAAM,GAAG,CAAC,EAAE;MACvE0M,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,IAAI,CAACvV,gBAAgB,GAAG,IAAI,CAACmS,YAAY,CAACkF,YAAY,CAAC,CAAC,CAAC;MAEzD;MACA,IAAI,IAAI,CAAClF,YAAY,CAACkF,YAAY,CAACzO,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMqP,kBAAkB,GACtB,IAAI,CAACjY,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAAC8W,GAAG;QACvD,IAAIkB,MAAM,CAACC,kBAAkB,CAAC,KAAKD,MAAM,CAAC,IAAI,CAAC3V,aAAa,CAAC,EAAE;UAC7DiT,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACD,IAAI,CAACvV,gBAAgB,GAAG,IAAI,CAACmS,YAAY,CAACkF,YAAY,CAAC,CAAC,CAAC;;;;IAK/D;IACA,IAAI,IAAI,CAACrX,gBAAgB,EAAE;MACzBsV,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QACnD9T,EAAE,EAAE,IAAI,CAACzB,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAAC8W,GAAG;QACzDhW,QAAQ,EAAE,IAAI,CAACd,gBAAgB,CAACc,QAAQ;QACxCa,KAAK,EAAE,IAAI,CAAC3B,gBAAgB,CAAC2B,KAAK;QAClC1B,QAAQ,EAAE,IAAI,CAACD,gBAAgB,CAACC;OACjC,CAAC;MAEF;MACAqV,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAACvV,gBAAgB,CAACc,QAAQ,CAC/B;MACDwU,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/B,IAAI,CAACvV,gBAAgB,CAACc,QAAQ,CAC/B;KACF,MAAM;MACLwU,OAAO,CAACY,KAAK,CAAC,uDAAuD,CAAC;MACtEZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACpD,YAAY,CAACkF,YAAY,CAAC;MACzE/B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAClT,aAAa,CAAC;MAEnD;MACAiT,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;IAGrD;IACA,IAAI,CAACP,gBAAgB,EAAE;EACzB;EAEQ0C,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACvF,YAAY,EAAE1Q,EAAE,EAAE;IAE5B;IACA,IAAIwH,QAAQ,GAAG,IAAI,CAACkJ,YAAY,CAAClJ,QAAQ,IAAI,EAAE;IAE/C;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACiP,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;MAC/C,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACjX,SAAS,IAAIiX,CAAC,CAACI,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,MAAMC,KAAK,GAAG,IAAIH,IAAI,CAACF,CAAC,CAAClX,SAAS,IAAIkX,CAAC,CAACG,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,OAAOH,KAAK,GAAGI,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC;;IAEFnD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CmD,KAAK,EAAE,IAAI,CAACzP,QAAQ,CAACL,MAAM;MAC3B+P,KAAK,EAAE,IAAI,CAAC1P,QAAQ,CAAC,CAAC,CAAC,EAAE/G,OAAO;MAChC0W,IAAI,EAAE,IAAI,CAAC3P,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACL,MAAM,GAAG,CAAC,CAAC,EAAE1G;KAChD,CAAC;IAEF,IAAI,CAACqQ,eAAe,GAAG,IAAI,CAACtJ,QAAQ,CAACL,MAAM,KAAK,IAAI,CAACyL,oBAAoB;IACzE,IAAI,CAAChC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACwG,cAAc,EAAE;EACvB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACxG,aAAa,IAAI,CAAC,IAAI,CAACC,eAAe,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAE1Q,EAAE,EACvE;IAEF,IAAI,CAAC6Q,aAAa,GAAG,IAAI;IACzB,IAAI,CAACgC,WAAW,EAAE;IAElB;IACA,MAAMyE,MAAM,GAAG,IAAI,CAAC9P,QAAQ,CAACL,MAAM;IAEnC,IAAI,CAACoJ,cAAc,CAACgH,WAAW,CAC7B,IAAI,CAAC3W,aAAc;IAAE;IACrB,IAAI,CAACrC,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAE8W,GAAI;IAAE;IAC1D,IAAI,CAAC3E,YAAY,CAAC1Q,EAAE,EACpB,IAAI,CAAC6S,WAAW,EAChB,IAAI,CAACD,oBAAoB,CAC1B,CAACyB,SAAS,CAAC;MACVC,IAAI,EAAGkD,WAAkB,IAAI;QAC3B,IAAIA,WAAW,IAAIA,WAAW,CAACrQ,MAAM,GAAG,CAAC,EAAE;UACzC;UACA,IAAI,CAACK,QAAQ,GAAG,CAAC,GAAGgQ,WAAW,CAACC,OAAO,EAAE,EAAE,GAAG,IAAI,CAACjQ,QAAQ,CAAC;UAC5D,IAAI,CAACsJ,eAAe,GAClB0G,WAAW,CAACrQ,MAAM,KAAK,IAAI,CAACyL,oBAAoB;SACnD,MAAM;UACL,IAAI,CAAC9B,eAAe,GAAG,KAAK;;QAE9B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACD4D,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAACjE,YAAY,CAACkF,SAAS,CAAC,wCAAwC,CAAC;QACrE,IAAI,CAAC7E,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACgC,WAAW,EAAE,CAAC,CAAC;MACtB;KACD,CAAC;EACJ;;EAEQqD,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACxF,YAAY,EAAE1Q,EAAE,EAAE;MAC1B6T,OAAO,CAACsC,IAAI,CAAC,kDAAkD,CAAC;MAChE;;IAGFtC,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzD,IAAI,CAACpD,YAAY,CAAC1Q,EAAE,CACrB;IAED;IACA6T,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACpD,IAAI,CAACb,aAAa,CAACkB,GAAG,CACpB,IAAI,CAAC5D,cAAc,CAACmH,sBAAsB,CACxC,IAAI,CAAChH,YAAY,CAAC1Q,EAAE,CACrB,CAACqU,SAAS,CAAC;MACVC,IAAI,EAAGqD,UAAe,IAAI;QACxB9D,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE6D,UAAU,CAAC;QACpE9D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;UACnC9T,EAAE,EAAE2X,UAAU,CAAC3X,EAAE;UACjB4X,IAAI,EAAED,UAAU,CAACC,IAAI;UACrBnX,OAAO,EAAEkX,UAAU,CAAClX,OAAO;UAC3BV,MAAM,EAAE4X,UAAU,CAAC5X,MAAM;UACzB8X,QAAQ,EAAEF,UAAU,CAACE,QAAQ;UAC7BC,UAAU,EAAEH,UAAU,CAACG,UAAU;UACjCC,WAAW,EAAEJ,UAAU,CAACI;SACzB,CAAC;QAEF;QACAlE,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnC,IAAI,CAAC/M,cAAc,CAAC4Q,UAAU,CAAC,CAChC;QACD9D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC9M,QAAQ,CAAC2Q,UAAU,CAAC,CAAC;QAC/D9D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC7M,OAAO,CAAC0Q,UAAU,CAAC,CAAC;QAC7D9D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACnS,WAAW,CAACgW,UAAU,CAAC,CAAC;QAClE,IAAIA,UAAU,CAACI,WAAW,EAAE;UAC1BJ,UAAU,CAACI,WAAW,CAACC,OAAO,CAAC,CAACC,GAAQ,EAAEjW,KAAa,KAAI;YACzD6R,OAAO,CAACC,GAAG,CAAC,yBAAyB9R,KAAK,GAAG,EAAE;cAC7C4V,IAAI,EAAEK,GAAG,CAACL,IAAI;cACd3H,GAAG,EAAEgI,GAAG,CAAChI,GAAG;cACZiI,IAAI,EAAED,GAAG,CAACC,IAAI;cACdrM,IAAI,EAAEoM,GAAG,CAACpM,IAAI;cACdqE,IAAI,EAAE+H,GAAG,CAAC/H;aACX,CAAC;UACJ,CAAC,CAAC;;QAGJ;QACA,MAAMiI,aAAa,GAAG,IAAI,CAAC3Q,QAAQ,CAAC4Q,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAACrY,EAAE,KAAK2X,UAAU,CAAC3X,EAAE,CAClC;QACD,IAAI,CAACmY,aAAa,EAAE;UAClB;UACA,IAAI,CAAC3Q,QAAQ,CAAC8Q,IAAI,CAACX,UAAU,CAAC;UAC9B9D,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAACtM,QAAQ,CAACL,MAAM,CACrB;UAED;UACA,IAAI,CAACsJ,GAAG,CAAC8H,aAAa,EAAE;UAExB;UACAC,UAAU,CAAC,MAAK;YACd,IAAI,CAACpB,cAAc,EAAE;UACvB,CAAC,EAAE,EAAE,CAAC;UAEN;UACA,MAAMS,QAAQ,GAAGF,UAAU,CAAC5X,MAAM,EAAEC,EAAE,IAAI2X,UAAU,CAACE,QAAQ;UAC7DhE,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;YAC9D+D,QAAQ;YACRjX,aAAa,EAAE,IAAI,CAACA,aAAa;YACjC6X,gBAAgB,EAAEZ,QAAQ,KAAK,IAAI,CAACjX;WACrC,CAAC;UAEF,IAAIiX,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAACjX,aAAa,EAAE;YAC/C,IAAI,CAAC8X,iBAAiB,CAACf,UAAU,CAAC3X,EAAE,CAAC;;;MAG3C,CAAC;MACDyU,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC,CACH;IAED;IACAZ,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7D,IAAI,CAACb,aAAa,CAACkB,GAAG,CACpB,IAAI,CAAC5D,cAAc,CAACoI,0BAA0B,CAC5C,IAAI,CAACjI,YAAY,CAAC1Q,EAAE,CACrB,CAACqU,SAAS,CAAC;MACVC,IAAI,EAAGsE,UAAe,IAAI;QACxB/E,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE8E,UAAU,CAAC;QAExD;QACA,IAAIA,UAAU,CAACxD,MAAM,KAAK,IAAI,CAACxU,aAAa,EAAE;UAC5C,IAAI,CAAC8G,iBAAiB,GAAGkR,UAAU,CAAC9F,QAAQ;UAC5C,IAAI,CAACrC,GAAG,CAAC8H,aAAa,EAAE;;MAE5B,CAAC;MACD9D,KAAK,EAAGA,KAAU,IAAI;QACpBZ,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACxB,aAAa,CAACkB,GAAG,CACpB,IAAI,CAAC5D,cAAc,CAACsI,8BAA8B,CAChD,IAAI,CAACnI,YAAY,CAAC1Q,EAAE,CACrB,CAACqU,SAAS,CAAC;MACVC,IAAI,EAAGwE,kBAAuB,IAAI;QAChCjF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgF,kBAAkB,CAAC;QAE1D;QACA,IAAIA,kBAAkB,CAAC9Y,EAAE,KAAK,IAAI,CAAC0Q,YAAY,CAAC1Q,EAAE,EAAE;UAClD,IAAI,CAAC0Q,YAAY,GAAG;YAAE,GAAG,IAAI,CAACA,YAAY;YAAE,GAAGoI;UAAkB,CAAE;UACnE,IAAI,CAACrI,GAAG,CAAC8H,aAAa,EAAE;;MAE5B,CAAC;MACD9D,KAAK,EAAGA,KAAU,IAAI;QACpBZ,OAAO,CAACY,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D;KACD,CAAC,CACH;EACH;EAEQiE,iBAAiBA,CAACK,SAAiB;IACzC,IAAI,CAACxI,cAAc,CAACmI,iBAAiB,CAACK,SAAS,CAAC,CAAC1E,SAAS,CAAC;MACzDC,IAAI,EAAEA,CAAA,KAAK;QACTT,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEiF,SAAS,CAAC;MACrD,CAAC;MACDtE,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC;EACJ;EAEA;EACA7K,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACsJ,WAAW,CAAC8F,KAAK,IAAI,CAAC,IAAI,CAACtI,YAAY,EAAE1Q,EAAE,EAAE;IAEvD,MAAMS,OAAO,GAAG,IAAI,CAACyS,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAEwF,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAACzY,OAAO,EAAE;IAEd,MAAMqX,UAAU,GAAG,IAAI,CAACvZ,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAE8W,GAAG;IAE1E,IAAI,CAACyC,UAAU,EAAE;MACf,IAAI,CAACtH,YAAY,CAACkF,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF;IACA,IAAI,CAAC1L,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACuJ,gBAAgB,EAAE;IAEvBM,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCrT,OAAO;MACPqX,UAAU;MACVvC,cAAc,EAAE,IAAI,CAAC7E,YAAY,CAAC1Q;KACnC,CAAC;IAEF,IAAI,CAACuQ,cAAc,CAAC3G,WAAW,CAC7BkO,UAAU,EACVrX,OAAO,EACP0Y,SAAS,EACT,MAAa,EACb,IAAI,CAACzI,YAAY,CAAC1Q,EAAE,CACrB,CAACqU,SAAS,CAAC;MACVC,IAAI,EAAG8E,OAAY,IAAI;QACrBvF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEsF,OAAO,CAAC;QAEpD;QACA,MAAMjB,aAAa,GAAG,IAAI,CAAC3Q,QAAQ,CAAC4Q,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAACrY,EAAE,KAAKoZ,OAAO,CAACpZ,EAAE,CAC/B;QACD,IAAI,CAACmY,aAAa,EAAE;UAClB,IAAI,CAAC3Q,QAAQ,CAAC8Q,IAAI,CAACc,OAAO,CAAC;UAC3BvF,OAAO,CAACC,GAAG,CACT,wCAAwC,EACxC,IAAI,CAACtM,QAAQ,CAACL,MAAM,CACrB;;QAGH;QACA,IAAI,CAAC+L,WAAW,CAACmG,KAAK,EAAE;QACxB,IAAI,CAACrP,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACuJ,gBAAgB,EAAE;QAEvB;QACA,IAAI,CAAC9C,GAAG,CAAC8H,aAAa,EAAE;QACxBC,UAAU,CAAC,MAAK;UACd,IAAI,CAACpB,cAAc,EAAE;QACvB,CAAC,EAAE,EAAE,CAAC;MACR,CAAC;MACD3C,KAAK,EAAGA,KAAU,IAAI;QACpBZ,OAAO,CAACY,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D,IAAI,CAACjE,YAAY,CAACkF,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAAC1L,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACuJ,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEA6D,cAAcA,CAAA;IACZoB,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACc,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAjb,gBAAgBA,CAACC,UAAgC;IAC/C,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IAEpC,MAAMib,QAAQ,GAAGC,IAAI,CAACC,KAAK,CACzB,CAAChD,IAAI,CAACiD,GAAG,EAAE,GAAG,IAAIjD,IAAI,CAACnY,UAAU,CAAC,CAACqY,OAAO,EAAE,IAAI,KAAK,CACtD;IAED,IAAI4C,QAAQ,GAAG,CAAC,EAAE,OAAO,aAAa;IACtC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,UAAUA,QAAQ,MAAM;IAClD,IAAIA,QAAQ,GAAG,IAAI,EAAE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC,GAAG;IAClE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,IAAI,CAAC,GAAG;EACjD;EAEA;EACAjX,oBAAoBA,CAACqW,SAAiB;IACpC,OACE,IAAI,CAAC/G,aAAa,CAAC+G,SAAS,CAAC,IAAI;MAC/BgB,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,CAAC;MACdtX,KAAK,EAAE;KACR;EAEL;EAEQuX,oBAAoBA,CAC1BnB,SAAiB,EACjBoB,IAAkD;IAElD,IAAI,CAACnI,aAAa,CAAC+G,SAAS,CAAC,GAAG;MAC9B,GAAG,IAAI,CAACrW,oBAAoB,CAACqW,SAAS,CAAC;MACvC,GAAGoB;KACJ;EACH;EAEA;EAEA;EACA;EAEA;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC7b,gBAAgB,EAAEyB,EAAE,EAAE;MAC9B,IAAI,CAACwQ,YAAY,CAACkF,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,IAAI,CAACxD,QAAQ,GAAG,OAAO;IACvB,IAAI,CAACD,QAAQ,GAAG,IAAI;IACpB4B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACvV,gBAAgB,CAACc,QAAQ,CAAC;EAC7E;EAEAgb,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC9b,gBAAgB,EAAEyB,EAAE,EAAE;MAC9B,IAAI,CAACwQ,YAAY,CAACkF,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,IAAI,CAACxD,QAAQ,GAAG,OAAO;IACvB,IAAI,CAACD,QAAQ,GAAG,IAAI;IACpB4B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACvV,gBAAgB,CAACc,QAAQ,CAAC;EAC7E;EAEAib,OAAOA,CAAA;IACL,IAAI,CAACrI,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACG,UAAU,GAAG,IAAI;IACtBwB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;EAEA;EACA;EAEA;EAEAyG,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOZ,IAAI,CAACa,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOZ,IAAI,CAACa,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEA3W,YAAYA,CAACuV,OAAY;IACvB,MAAMsB,cAAc,GAAGtB,OAAO,CAACrB,WAAW,EAAE3B,IAAI,CAC7C6B,GAAQ,IAAK,CAACA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAID,cAAc,EAAEzK,GAAG,EAAE;MACvB,MAAM2K,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGL,cAAc,CAACzK,GAAG;MAC9B2K,IAAI,CAACI,QAAQ,GAAGN,cAAc,CAAC7O,IAAI,IAAI,MAAM;MAC7C+O,IAAI,CAACK,MAAM,GAAG,QAAQ;MACtBJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,EAAE;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/B,IAAI,CAACpK,YAAY,CAAC8K,WAAW,CAAC,wBAAwB,CAAC;;EAE3D;EAEA;EAEAC,WAAWA,CAAA;IACT,MAAMC,QAAQ,GAAG,IAAI,CAACC,UAAU,GAAG,OAAO,GAAG,MAAM;IACnD,IAAI,CAACC,WAAW,CAACF,QAAQ,CAAC;EAC5B;EAEAvc,YAAYA,CAAA;IACV,IAAI,CAACmS,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACH,UAAU,GAAG,IAAI,CAACG,UAAU;EACnC;EAEAuK,cAAcA,CAAA;IACZ,IAAI,CAACxc,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAyc,qBAAqBA,CAAA;IACnB;IACA/H,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;EAC/C;EAEA;EAEAnF,aAAaA,CAAA;IACX,IAAI,CAACoC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAAC7R,YAAY,GAAG,KAAK;IACzB,IAAI,CAACkS,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACuK,iBAAiB,GAAG,KAAK;EAChC;EAEAjW,oBAAoBA,CAACwT,OAAY,EAAE0C,KAAiB;IAClDA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACjO,eAAe,GAAGsL,OAAO;IAC9B,IAAI,CAAC/L,mBAAmB,GAAG;MAAEC,CAAC,EAAEwO,KAAK,CAACE,OAAO;MAAEzO,CAAC,EAAEuO,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAAC5K,sBAAsB,GAAG,IAAI;EACpC;EAEA9K,kBAAkBA,CAAC6S,OAAY,EAAE0C,KAAiB;IAChDA,KAAK,CAAC5Z,eAAe,EAAE;IACvB,IAAI,CAACqP,qBAAqB,GAAG6H,OAAO;IACpC,IAAI,CAAC/L,mBAAmB,GAAG;MAAEC,CAAC,EAAEwO,KAAK,CAACE,OAAO;MAAEzO,CAAC,EAAEuO,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAAC3K,kBAAkB,GAAG,IAAI;EAChC;EAEApE,UAAUA,CAACrI,KAAa;IACtB,IAAI,IAAI,CAAC0M,qBAAqB,EAAE;MAC9B,IAAI,CAAC3M,cAAc,CAAC,IAAI,CAAC2M,qBAAqB,CAACvR,EAAE,EAAE6E,KAAK,CAAC;;IAE3D,IAAI,CAACyM,kBAAkB,GAAG,KAAK;EACjC;EAEA1M,cAAcA,CAACmU,SAAiB,EAAElU,KAAa;IAC7CgP,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEjP,KAAK,EAAE,cAAc,EAAEkU,SAAS,CAAC;IACtE;EACF;;EAEAhU,cAAcA,CAACmX,QAAa,EAAE9G,MAAc;IAC1C,OAAO8G,QAAQ,CAAC9G,MAAM,KAAKA,MAAM;EACnC;EAEAvH,cAAcA,CAACuL,OAAY;IACzBvF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEsF,OAAO,CAACpZ,EAAE,CAAC;IAClD,IAAI,CAAC2O,aAAa,EAAE;EACtB;EAEAV,cAAcA,CAACmL,OAAY;IACzBvF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEsF,OAAO,CAACpZ,EAAE,CAAC;IACjD,IAAI,CAAC2O,aAAa,EAAE;EACtB;EAEAL,aAAaA,CAAC8K,OAAY;IACxBvF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsF,OAAO,CAACpZ,EAAE,CAAC;IAChD,IAAI,CAAC2O,aAAa,EAAE;EACtB;EAEA;EAEAwN,iBAAiBA,CAAA;IACf,IAAI,CAACpL,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA3F,mBAAmBA,CAACgR,QAAa;IAC/B,IAAI,CAAC9Q,qBAAqB,GAAG8Q,QAAQ;EACvC;EAEAlQ,oBAAoBA,CAACkQ,QAAa;IAChC,OAAOA,QAAQ,EAAEzJ,MAAM,IAAI,EAAE;EAC/B;EAEA/G,WAAWA,CAAC/G,KAAU;IACpB,MAAMwX,cAAc,GAAG,IAAI,CAACnJ,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAEwF,KAAK,IAAI,EAAE;IACnE,MAAMqD,UAAU,GAAGD,cAAc,GAAGxX,KAAK,CAACA,KAAK;IAC/C,IAAI,CAACqO,WAAW,CAACqJ,UAAU,CAAC;MAAE9b,OAAO,EAAE6b;IAAU,CAAE,CAAC;IACpD,IAAI,CAACvL,eAAe,GAAG,KAAK;EAC9B;EAEAyL,oBAAoBA,CAAA;IAClB,IAAI,CAACxL,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA;EACA;EAEA;EACA;EAEA;EAEA;EACA;EAEA;EAEAvJ,gBAAgBA,CAACzF,KAAa,EAAEoX,OAAY;IAC1C,OAAOA,OAAO,CAACpZ,EAAE,IAAIoZ,OAAO,CAAC/D,GAAG,IAAIrT,KAAK,CAACya,QAAQ,EAAE;EACtD;EAEA;EAEAC,cAAcA,CAAA;IACZ7I,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC,MAAM6I,WAAW,GAAG;MAClB3c,EAAE,EAAE,QAAQ6W,IAAI,CAACiD,GAAG,EAAE,EAAE;MACxBrZ,OAAO,EAAE,mBAAmB,IAAIoW,IAAI,EAAE,CAAC+F,kBAAkB,EAAE,EAAE;MAC7Dnd,SAAS,EAAE,IAAIoX,IAAI,EAAE,CAACgG,WAAW,EAAE;MACnC9c,MAAM,EAAE;QACNC,EAAE,EAAE,IAAI,CAACzB,gBAAgB,EAAEyB,EAAE,IAAI,WAAW;QAC5CX,QAAQ,EAAE,IAAI,CAACd,gBAAgB,EAAEc,QAAQ,IAAI,WAAW;QACxDa,KAAK,EACH,IAAI,CAAC3B,gBAAgB,EAAE2B,KAAK,IAAI;OACnC;MACD0X,IAAI,EAAE,MAAM;MACZkF,MAAM,EAAE;KACT;IACD,IAAI,CAACtV,QAAQ,CAAC8Q,IAAI,CAACqE,WAAW,CAAC;IAC/B,IAAI,CAAClM,GAAG,CAAC8H,aAAa,EAAE;IACxBC,UAAU,CAAC,MAAM,IAAI,CAACpB,cAAc,EAAE,EAAE,EAAE,CAAC;EAC7C;EAEAvQ,mBAAmBA,CAAA;IACjB,OACE,IAAI,CAAC6J,YAAY,EAAEoF,OAAO,IAC1B,IAAI,CAACpF,YAAY,EAAEkF,YAAY,EAAEzO,MAAM,GAAG,CAAC,IAC3C,KAAK;EAET;EAEA0F,UAAUA,CAAA;IACRgH,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC,IAAI,CAAC9C,kBAAkB,GAAG,KAAK;IAC/B;EACF;;EAEAvB,SAASA,CAACsN,MAAc;IACtB,MAAMC,YAAY,GAAGnC,QAAQ,CAACoC,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChB,MAAME,gBAAgB,GAAGF,YAAY,CAACG,KAAK,CAACC,SAAS,IAAI,UAAU;MACnE,MAAMC,YAAY,GAAGC,UAAU,CAC7BJ,gBAAgB,CAACK,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CACvD;MACD,MAAMC,QAAQ,GAAG5D,IAAI,CAAC6D,GAAG,CAAC,GAAG,EAAE7D,IAAI,CAAC8D,GAAG,CAAC,CAAC,EAAEL,YAAY,GAAGN,MAAM,CAAC,CAAC;MAClEC,YAAY,CAACG,KAAK,CAACC,SAAS,GAAG,SAASI,QAAQ,GAAG;MACnD,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChBR,YAAY,CAACW,SAAS,CAACxJ,GAAG,CAAC,QAAQ,CAAC;OACrC,MAAM;QACL6I,YAAY,CAACW,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;;EAG7C;EAEA9N,SAASA,CAAA;IACP,MAAMkN,YAAY,GAAGnC,QAAQ,CAACoC,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChBA,YAAY,CAACG,KAAK,CAACC,SAAS,GAAG,UAAU;MACzCJ,YAAY,CAACW,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;EAE3C;EAEA;EACA;EACA;EACA;EAEAtR,gBAAgBA,CAACsL,IAAa;IAC5B,MAAMiG,KAAK,GAAG,IAAI,CAACC,SAAS,EAAEtE,aAAa;IAC3C,IAAI,CAACqE,KAAK,EAAE;MACVhK,OAAO,CAACY,KAAK,CAAC,8BAA8B,CAAC;MAC7C;;IAGF;IACA,IAAImD,IAAI,KAAK,OAAO,EAAE;MACpBiG,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAInG,IAAI,KAAK,OAAO,EAAE;MAC3BiG,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAInG,IAAI,KAAK,UAAU,EAAE;MAC9BiG,KAAK,CAACE,MAAM,GAAG,iCAAiC;KACjD,MAAM;MACLF,KAAK,CAACE,MAAM,GAAG,KAAK;;IAGtB;IACAF,KAAK,CAAC5E,KAAK,GAAG,EAAE;IAEhB;IACA4E,KAAK,CAACzC,KAAK,EAAE;IACb,IAAI,CAACpK,kBAAkB,GAAG,KAAK;EACjC;EAEA9J,iBAAiBA,CAACzH,SAAwB;IACxC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMue,IAAI,GAAG,IAAInH,IAAI,CAACpX,SAAS,CAAC;IAChC,OAAOue,IAAI,CAACpB,kBAAkB,CAAC,OAAO,EAAE;MACtCqB,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA3e,mBAAmBA,CAACE,SAAwB;IAC1C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMue,IAAI,GAAG,IAAInH,IAAI,CAACpX,SAAS,CAAC;IAChC,MAAM0e,KAAK,GAAG,IAAItH,IAAI,EAAE;IACxB,MAAMuH,SAAS,GAAG,IAAIvH,IAAI,CAACsH,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIN,IAAI,CAACO,YAAY,EAAE,KAAKJ,KAAK,CAACI,YAAY,EAAE,EAAE;MAChD,OAAO,aAAa;KACrB,MAAM,IAAIP,IAAI,CAACO,YAAY,EAAE,KAAKH,SAAS,CAACG,YAAY,EAAE,EAAE;MAC3D,OAAO,MAAM;KACd,MAAM;MACL,OAAOP,IAAI,CAACQ,kBAAkB,CAAC,OAAO,CAAC;;EAE3C;EAEAhe,oBAAoBA,CAACC,OAAe;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB;IACA,MAAMge,QAAQ,GAAG,sBAAsB;IACvC,OAAOhe,OAAO,CAACie,OAAO,CACpBD,QAAQ,EACR,qEAAqE,CACtE;EACH;EAEA/X,uBAAuBA,CAAC1E,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,MAAM2c,cAAc,GAAG,IAAI,CAACnX,QAAQ,CAACxF,KAAK,CAAC;IAC3C,MAAM4c,eAAe,GAAG,IAAI,CAACpX,QAAQ,CAACxF,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAAC2c,cAAc,EAAElf,SAAS,IAAI,CAACmf,eAAe,EAAEnf,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAMof,WAAW,GAAG,IAAIhI,IAAI,CAAC8H,cAAc,CAAClf,SAAS,CAAC,CAAC8e,YAAY,EAAE;IACrE,MAAMO,YAAY,GAAG,IAAIjI,IAAI,CAAC+H,eAAe,CAACnf,SAAS,CAAC,CAAC8e,YAAY,EAAE;IAEvE,OAAOM,WAAW,KAAKC,YAAY;EACrC;EAEAlY,gBAAgBA,CAAC5E,KAAa;IAC5B,MAAM2c,cAAc,GAAG,IAAI,CAACnX,QAAQ,CAACxF,KAAK,CAAC;IAC3C,MAAM+c,WAAW,GAAG,IAAI,CAACvX,QAAQ,CAACxF,KAAK,GAAG,CAAC,CAAC;IAE5C,IAAI,CAAC+c,WAAW,EAAE,OAAO,IAAI;IAE7B,OAAOJ,cAAc,CAAC5e,MAAM,EAAEC,EAAE,KAAK+e,WAAW,CAAChf,MAAM,EAAEC,EAAE;EAC7D;EAEA8G,oBAAoBA,CAAC9E,KAAa;IAChC,MAAM2c,cAAc,GAAG,IAAI,CAACnX,QAAQ,CAACxF,KAAK,CAAC;IAC3C,MAAM4c,eAAe,GAAG,IAAI,CAACpX,QAAQ,CAACxF,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAAC4c,eAAe,EAAE,OAAO,IAAI;IAEjC,OAAOD,cAAc,CAAC5e,MAAM,EAAEC,EAAE,KAAK4e,eAAe,CAAC7e,MAAM,EAAEC,EAAE;EACjE;EAEA+G,cAAcA,CAACqS,OAAY;IACzB;IACA,IAAIA,OAAO,CAACxB,IAAI,EAAE;MAChB,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAIwB,OAAO,CAACxB,IAAI,KAAK,eAAe,EAAE,OAAO,OAAO;MACpD,IAAIwB,OAAO,CAACxB,IAAI,KAAK,MAAM,IAAIwB,OAAO,CAACxB,IAAI,KAAK,MAAM,EAAE,OAAO,MAAM;;IAGvE;IACA,IAAIwB,OAAO,CAACrB,WAAW,IAAIqB,OAAO,CAACrB,WAAW,CAAC5Q,MAAM,GAAG,CAAC,EAAE;MACzD,MAAM6X,UAAU,GAAG5F,OAAO,CAACrB,WAAW,CAAC,CAAC,CAAC;MACzC,IAAIiH,UAAU,CAACpH,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIqE,UAAU,CAACpH,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIqE,UAAU,CAACpH,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,OAAO,MAAM;;IAGf;IACA,IAAIvB,OAAO,CAAC6F,QAAQ,IAAI7F,OAAO,CAAC8F,QAAQ,IAAI9F,OAAO,CAAC+F,KAAK,EAAE,OAAO,OAAO;IAEzE,OAAO,MAAM;EACf;EAEAnY,QAAQA,CAACoS,OAAY;IACnB;IACA,IAAIA,OAAO,CAACxB,IAAI,KAAK,OAAO,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,EAAE;MACxD,OAAO,IAAI;;IAGb;IACA,MAAMwH,kBAAkB,GACtBhG,OAAO,CAACrB,WAAW,EAAEK,IAAI,CAAEH,GAAQ,IAAI;MACrC,OAAOA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,IAAI1C,GAAG,CAACL,IAAI,KAAK,OAAO;IAC/D,CAAC,CAAC,IAAI,KAAK;IAEb;IACA,MAAMyH,WAAW,GAAG,CAAC,EAAEjG,OAAO,CAACkG,QAAQ,IAAIlG,OAAO,CAAClZ,KAAK,CAAC;IAEzD,OAAOkf,kBAAkB,IAAIC,WAAW;EAC1C;EAEApY,OAAOA,CAACmS,OAAY;IAClB;IACA,IAAIA,OAAO,CAACxB,IAAI,KAAK,MAAM,IAAIwB,OAAO,CAACxB,IAAI,KAAK,MAAM,EAAE;MACtD,OAAO,IAAI;;IAGb;IACA,MAAM2H,iBAAiB,GACrBnG,OAAO,CAACrB,WAAW,EAAEK,IAAI,CAAEH,GAAQ,IAAI;MACrC,OAAO,CAACA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,IAAI1C,GAAG,CAACL,IAAI,KAAK,OAAO;IAChE,CAAC,CAAC,IAAI,KAAK;IAEb,OAAO2H,iBAAiB;EAC1B;EAEA5d,WAAWA,CAACyX,OAAY;IACtB;IACA,IAAIA,OAAO,CAACkG,QAAQ,EAAE;MACpB,OAAOlG,OAAO,CAACkG,QAAQ;;IAEzB,IAAIlG,OAAO,CAAClZ,KAAK,EAAE;MACjB,OAAOkZ,OAAO,CAAClZ,KAAK;;IAGtB;IACA,MAAMsf,eAAe,GAAGpG,OAAO,CAACrB,WAAW,EAAE3B,IAAI,CAC9C6B,GAAQ,IAAKA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,IAAI1C,GAAG,CAACL,IAAI,KAAK,OAAO,CACrE;IAED,IAAI4H,eAAe,EAAE;MACnB,OAAOA,eAAe,CAACvP,GAAG,IAAIuP,eAAe,CAACtH,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEAjU,WAAWA,CAACmV,OAAY;IACtB,MAAMsB,cAAc,GAAGtB,OAAO,CAACrB,WAAW,EAAE3B,IAAI,CAC7C6B,GAAQ,IAAK,CAACA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,OAAOD,cAAc,EAAE7O,IAAI,IAAI,SAAS;EAC1C;EAEA3H,WAAWA,CAACkV,OAAY;IACtB,MAAMsB,cAAc,GAAGtB,OAAO,CAACrB,WAAW,EAAE3B,IAAI,CAC7C6B,GAAQ,IAAK,CAACA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAExK,IAAI,EAAE,OAAO,EAAE;IAEpC,MAAMsK,KAAK,GAAGE,cAAc,CAACxK,IAAI;IACjC,IAAIsK,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOZ,IAAI,CAACa,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOZ,IAAI,CAACa,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAxW,WAAWA,CAACoV,OAAY;IACtB,MAAMsB,cAAc,GAAGtB,OAAO,CAACrB,WAAW,EAAE3B,IAAI,CAC7C6B,GAAQ,IAAK,CAACA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAE9C,IAAI,EAAE,OAAO,aAAa;IAE/C,IAAI8C,cAAc,CAAC9C,IAAI,CAAC+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAAC9C,IAAI,CAAC+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAAC9C,IAAI,CAAC6H,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IACjE,IAAI/E,cAAc,CAAC9C,IAAI,CAAC6H,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,kBAAkB;IACnE,IAAI/E,cAAc,CAAC9C,IAAI,CAAC6H,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IACrE,OAAO,aAAa;EACtB;EAEAnf,YAAYA,CAAC8U,MAAc;IACzB;IACA,MAAMsK,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAM1d,KAAK,GAAGoT,MAAM,CAACuK,UAAU,CAAC,CAAC,CAAC,GAAGD,MAAM,CAACvY,MAAM;IAClD,OAAOuY,MAAM,CAAC1d,KAAK,CAAC;EACtB;EAEA;EACAyD,cAAcA,CAAC2T,OAAY,EAAE0C,KAAU;IACrCjI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEsF,OAAO,CAAC;EAC1C;EAEAwG,aAAaA,CAAC9D,KAAU;IACtB;IACA,IAAI,CAAC+D,qBAAqB,EAAE;EAC9B;EAEAC,cAAcA,CAAChE,KAAoB;IACjC,IAAIA,KAAK,CAACiE,GAAG,KAAK,OAAO,IAAI,CAACjE,KAAK,CAACkE,QAAQ,EAAE;MAC5ClE,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAACnS,WAAW,EAAE;;EAEtB;EAEAqW,YAAYA,CAAA;IACV;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFC,QAAQA,CAACrE,KAAU;IACjB;IACA,MAAMvC,OAAO,GAAGuC,KAAK,CAACb,MAAM;IAC5B,IACE1B,OAAO,CAACE,SAAS,KAAK,CAAC,IACvB,IAAI,CAAC3I,eAAe,IACpB,CAAC,IAAI,CAACD,aAAa,EACnB;MACA,IAAI,CAACwG,gBAAgB,EAAE;;EAE3B;EAEAvX,eAAeA,CAACsV,MAAc;IAC5BvB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEsB,MAAM,CAAC;EAClD;EAEAhU,WAAWA,CAAC0a,KAAU,EAAE1C,OAAY;IAClCvF,OAAO,CAACC,GAAG,CACT,oDAAoD,EACpDsF,OAAO,CAACpZ,EAAE,EACV8b,KAAK,CAACb,MAAM,CAACmF,GAAG,CACjB;EACH;EAEA7e,YAAYA,CAACua,KAAU,EAAE1C,OAAY;IACnCvF,OAAO,CAACY,KAAK,CAAC,+CAA+C,EAAE2E,OAAO,CAACpZ,EAAE,EAAE;MACzEogB,GAAG,EAAEtE,KAAK,CAACb,MAAM,CAACmF,GAAG;MACrB3L,KAAK,EAAEqH;KACR,CAAC;IACF;IACAA,KAAK,CAACb,MAAM,CAACmF,GAAG,GACd,4WAA4W;EAChX;EAEApf,eAAeA,CAACoY,OAAY;IAC1B,MAAMoG,eAAe,GAAGpG,OAAO,CAACrB,WAAW,EAAE3B,IAAI,CAAE6B,GAAQ,IACzDA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,CAC/B;IACD,IAAI6E,eAAe,EAAEvP,GAAG,EAAE;MACxB,IAAI,CAACD,aAAa,GAAG;QACnBC,GAAG,EAAEuP,eAAe,CAACvP,GAAG;QACxBpE,IAAI,EAAE2T,eAAe,CAAC3T,IAAI,IAAI,OAAO;QACrCqE,IAAI,EAAE,IAAI,CAACqK,cAAc,CAACiF,eAAe,CAACtP,IAAI,IAAI,CAAC,CAAC;QACpDkJ,OAAO,EAAEA;OACV;MACD,IAAI,CAAC5H,eAAe,GAAG,IAAI;MAC3BqC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC9D,aAAa,CAAC;;EAEvE;EAEAjB,gBAAgBA,CAAA;IACd,IAAI,CAACyC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACxB,aAAa,GAAG,IAAI;IACzB6D,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC;EAEA1E,aAAaA,CAAA;IACX,IAAI,IAAI,CAACY,aAAa,EAAEC,GAAG,EAAE;MAC3B,MAAM2K,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAG,IAAI,CAAC/K,aAAa,CAACC,GAAG;MAClC2K,IAAI,CAACI,QAAQ,GAAG,IAAI,CAAChL,aAAa,CAACnE,IAAI,IAAI,OAAO;MAClD+O,IAAI,CAACK,MAAM,GAAG,QAAQ;MACtBJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,EAAE;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/B,IAAI,CAACpK,YAAY,CAAC8K,WAAW,CAAC,wBAAwB,CAAC;MACvDzH,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAC9D,aAAa,CAACnE,IAAI,CACxB;;EAEL;EAEA;EACA;EACA;EAEAwU,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACnP,WAAW,CAACgI,IAAI,EAAE,EAAE;MAC5B,IAAI,CAAC/H,aAAa,GAAG,EAAE;MACvB;;IAGF,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC3J,QAAQ,CAAC8Y,MAAM,CACtClH,OAAO,IACNA,OAAO,CAAC3Y,OAAO,EACX8f,WAAW,EAAE,CACdd,QAAQ,CAAC,IAAI,CAACvO,WAAW,CAACqP,WAAW,EAAE,CAAC,IAC3CnH,OAAO,CAACrZ,MAAM,EAAEV,QAAQ,EACpBkhB,WAAW,EAAE,CACdd,QAAQ,CAAC,IAAI,CAACvO,WAAW,CAACqP,WAAW,EAAE,CAAC,CAC9C;EACH;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACH,cAAc,EAAE;EACvB;EAEAI,WAAWA,CAAA;IACT,IAAI,CAACvP,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,aAAa,GAAG,EAAE;EACzB;EAEAuP,aAAaA,CAAC3H,SAAiB;IAC7B,MAAM4H,cAAc,GAAG9F,QAAQ,CAAC+F,cAAc,CAAC,WAAW7H,SAAS,EAAE,CAAC;IACtE,IAAI4H,cAAc,EAAE;MAClBA,cAAc,CAACE,cAAc,CAAC;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAQ,CAAE,CAAC;MACtE;MACAJ,cAAc,CAAChD,SAAS,CAACxJ,GAAG,CAAC,WAAW,CAAC;MACzCqE,UAAU,CAAC,MAAK;QACdmI,cAAc,CAAChD,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;MAC9C,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;EACA;EACA;EAEAoD,gBAAgBA,CAAA;IACd,IAAI,CAAC3P,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACvD,eAAe,GAAG,IAAI;EAC7B;EAEA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EAEQmT,YAAYA,CAAC/O,QAAkB;IACrC,IAAI,CAAC,IAAI,CAAC3T,gBAAgB,EAAE;MAC1B,IAAI,CAACiS,YAAY,CAACkF,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,MAAMwL,WAAW,GAAG,IAAI,CAAC3iB,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAAC8W,GAAG;IACzE,IAAI,CAAC6L,WAAW,EAAE;MAChB,IAAI,CAAC1Q,YAAY,CAACkF,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF7B,OAAO,CAACC,GAAG,CAAC,iBAAiB5B,QAAQ,gBAAgB,EAAEgP,WAAW,CAAC;IAEnE,IAAI,CAACjP,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,KAAKpU,QAAQ,CAACqjB,KAAK,GAAG,OAAO,GAAG,OAAO;IAC/D,IAAI,CAAChP,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,CAACiP,cAAc,EAAE;IAErB;IACA,IAAI,CAAC7Q,cAAc,CAAC0Q,YAAY,CAC9BC,WAAW,EACXhP,QAAQ,EACR,IAAI,CAACxB,YAAY,EAAE1Q,EAAE,CACtB,CAACqU,SAAS,CAAC;MACVC,IAAI,EAAGK,IAAU,IAAI;QACnBd,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEa,IAAI,CAAC;QACnD,IAAI,CAACtC,UAAU,GAAGsC,IAAI;QACtB,IAAI,CAACrC,eAAe,GAAG,KAAK;QAC5B,IAAI,CAAC9B,YAAY,CAAC8K,WAAW,CAC3B,SAASpJ,QAAQ,KAAKpU,QAAQ,CAACqjB,KAAK,GAAG,OAAO,GAAG,OAAO,SAAS,CAClE;MACH,CAAC;MACD1M,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAAC6F,OAAO,EAAE;QACd,IAAI,CAAC9J,YAAY,CAACkF,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACJ;EAEA2L,UAAUA,CAAC9M,YAA0B;IACnCV,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAES,YAAY,CAAC;IAExD,IAAI,CAAChE,cAAc,CAAC8Q,UAAU,CAAC9M,YAAY,CAAC,CAACF,SAAS,CAAC;MACrDC,IAAI,EAAGK,IAAU,IAAI;QACnBd,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEa,IAAI,CAAC;QAClD,IAAI,CAACtC,UAAU,GAAGsC,IAAI;QACtB,IAAI,CAAC1C,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACK,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACJ,QAAQ,GAAGyC,IAAI,CAACiD,IAAI,KAAK9Z,QAAQ,CAACqjB,KAAK,GAAG,OAAO,GAAG,OAAO;QAChE,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAAC5Q,YAAY,CAAC8K,WAAW,CAAC,eAAe,CAAC;MAChD,CAAC;MACD7G,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACjE,YAAY,CAACkF,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;EACJ;EAEA4L,UAAUA,CAAC/M,YAA0B;IACnCV,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAES,YAAY,CAAC;IAExD,IAAI,CAAChE,cAAc,CAAC+Q,UAAU,CAAC/M,YAAY,CAACvU,EAAE,EAAE,eAAe,CAAC,CAACqU,SAAS,CAAC;MACzEC,IAAI,EAAEA,CAAA,KAAK;QACTT,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3C,IAAI,CAACtD,YAAY,CAAC8K,WAAW,CAAC,cAAc,CAAC;MAC/C,CAAC;MACD7G,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACjE,YAAY,CAACkF,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;EACJ;EAEA;EACA;EAEQ0L,cAAcA,CAAA;IACpB,IAAI,CAACjP,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,SAAS,GAAGmP,WAAW,CAAC,MAAK;MAChC,IAAI,CAACpP,YAAY,EAAE;MACnB,IAAI,CAAC1B,GAAG,CAAC8H,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV;EAEQiJ,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACpP,SAAS,EAAE;MAClBqP,aAAa,CAAC,IAAI,CAACrP,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;EAEA;EACAkP,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACrP,UAAU,EAAE;IAEtB,IAAI,CAACE,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,IAAI,CAAChC,cAAc,CAACoR,WAAW,CAC7B,IAAI,CAACtP,UAAU,CAACrS,EAAE,EAClBmZ,SAAS;IAAE;IACX,CAAC,IAAI,CAAC5G,OAAO,CAAC;KACf,CAAC8B,SAAS,CAAC;MACVC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC9D,YAAY,CAAC8K,WAAW,CAC3B,IAAI,CAAC/I,OAAO,GAAG,aAAa,GAAG,cAAc,CAC9C;MACH,CAAC;MACDkC,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACA,IAAI,CAAClC,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;QAC5B,IAAI,CAAC/B,YAAY,CAACkF,SAAS,CAAC,oCAAoC,CAAC;MACnE;KACD,CAAC;EACJ;EAEAkM,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACvP,UAAU,EAAE;IAEtB,IAAI,CAACG,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C;IACA,IAAI,CAACjC,cAAc,CAACoR,WAAW,CAC7B,IAAI,CAACtP,UAAU,CAACrS,EAAE,EAClB,IAAI,CAACwS,cAAc;IAAE;IACrB2G,SAAS,CAAC;KACX,CAAC9E,SAAS,CAAC;MACVC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC9D,YAAY,CAAC8K,WAAW,CAC3B,IAAI,CAAC9I,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACDiC,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;QACA,IAAI,CAACjC,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,CAAChC,YAAY,CAACkF,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACJ;EAEAmM,kBAAkBA,CAAC7H,QAAgB;IACjC,MAAM8H,KAAK,GAAGlI,IAAI,CAACC,KAAK,CAACG,QAAQ,GAAG,IAAI,CAAC;IACzC,MAAM+H,OAAO,GAAGnI,IAAI,CAACC,KAAK,CAAEG,QAAQ,GAAG,IAAI,GAAI,EAAE,CAAC;IAClD,MAAMgI,OAAO,GAAGhI,QAAQ,GAAG,EAAE;IAE7B,IAAI8H,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIC,OAAO,CAACtF,QAAQ,EAAE,CAACwF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CAC9DvF,QAAQ,EAAE,CACVwF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAEvB,OAAO,GAAGF,OAAO,IAAIC,OAAO,CAACvF,QAAQ,EAAE,CAACwF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EACA;EAEMC,mBAAmBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACvBvO,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MAErD,IAAI;QACF;QACA,IAAI,CAACuO,SAAS,CAACC,YAAY,IAAI,CAACD,SAAS,CAACC,YAAY,CAACC,YAAY,EAAE;UACnE,MAAM,IAAIC,KAAK,CACb,yDAAyD,CAC1D;;QAGH;QACA,IAAI,CAACC,MAAM,CAACC,aAAa,EAAE;UACzB,MAAM,IAAIF,KAAK,CACb,uDAAuD,CACxD;;QAGH3O,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QAEzD;QACA,MAAM6O,MAAM,SAASN,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDK,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE,IAAI;YACrBC,UAAU,EAAE,KAAK;YACjBC,YAAY,EAAE;;SAEjB,CAAC;QAEFpP,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QAEnD;QACA,IAAIoP,QAAQ,GAAG,wBAAwB;QACvC,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;UAC5CA,QAAQ,GAAG,YAAY;UACvB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;YAC5CA,QAAQ,GAAG,WAAW;YACtB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;cAC5CA,QAAQ,GAAG,EAAE,CAAC,CAAC;;;;;QAKrBrP,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEoP,QAAQ,CAAC;QAEpD;QACAf,KAAI,CAACxQ,aAAa,GAAG,IAAI+Q,aAAa,CAACC,MAAM,EAAE;UAC7CO,QAAQ,EAAEA,QAAQ,IAAI/J;SACvB,CAAC;QAEF;QACAgJ,KAAI,CAACvQ,WAAW,GAAG,EAAE;QACrBuQ,KAAI,CAAC5Y,gBAAgB,GAAG,IAAI;QAC5B4Y,KAAI,CAACrX,sBAAsB,GAAG,CAAC;QAC/BqX,KAAI,CAAC3Y,mBAAmB,GAAG,WAAW;QAEtCqK,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;QAElE;QACAqO,KAAI,CAACtQ,cAAc,GAAG0P,WAAW,CAAC,MAAK;UACrCY,KAAI,CAACrX,sBAAsB,EAAE;UAC7B;UACAqX,KAAI,CAACiB,iBAAiB,EAAE;UACxBjB,KAAI,CAAC1R,GAAG,CAAC8H,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAER;QACA4J,KAAI,CAACxQ,aAAa,CAAC0R,eAAe,GAAIvH,KAAK,IAAI;UAC7CjI,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgI,KAAK,CAAC3B,IAAI,CAACjK,IAAI,EAAE,OAAO,CAAC;UACnE,IAAI4L,KAAK,CAAC3B,IAAI,CAACjK,IAAI,GAAG,CAAC,EAAE;YACvBiS,KAAI,CAACvQ,WAAW,CAAC0G,IAAI,CAACwD,KAAK,CAAC3B,IAAI,CAAC;;QAErC,CAAC;QAEDgI,KAAI,CAACxQ,aAAa,CAAC2R,MAAM,GAAG,MAAK;UAC/BzP,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;UACpEqO,KAAI,CAACoB,oBAAoB,EAAE;QAC7B,CAAC;QAEDpB,KAAI,CAACxQ,aAAa,CAAC6R,OAAO,GAAI1H,KAAU,IAAI;UAC1CjI,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEqH,KAAK,CAACrH,KAAK,CAAC;UAC7D0N,KAAI,CAAC3R,YAAY,CAACkF,SAAS,CAAC,iCAAiC,CAAC;UAC9DyM,KAAI,CAAC7X,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACA6X,KAAI,CAACxQ,aAAa,CAAC8R,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/B5P,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QAExDqO,KAAI,CAAC3R,YAAY,CAAC8K,WAAW,CAAC,iCAAiC,CAAC;OACjE,CAAC,OAAO7G,KAAU,EAAE;QACnBZ,OAAO,CAACY,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAE5D,IAAIiP,YAAY,GAAG,+CAA+C;QAElE,IAAIjP,KAAK,CAAC5I,IAAI,KAAK,iBAAiB,EAAE;UACpC6X,YAAY,GACV,iGAAiG;SACpG,MAAM,IAAIjP,KAAK,CAAC5I,IAAI,KAAK,eAAe,EAAE;UACzC6X,YAAY,GACV,6DAA6D;SAChE,MAAM,IAAIjP,KAAK,CAAC5I,IAAI,KAAK,mBAAmB,EAAE;UAC7C6X,YAAY,GACV,0DAA0D;SAC7D,MAAM,IAAIjP,KAAK,CAAC2E,OAAO,EAAE;UACxBsK,YAAY,GAAGjP,KAAK,CAAC2E,OAAO;;QAG9B+I,KAAI,CAAC3R,YAAY,CAACkF,SAAS,CAACgO,YAAY,CAAC;QACzCvB,KAAI,CAAC7X,oBAAoB,EAAE;;IAC5B;EACH;EAEAI,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACiH,aAAa,IAAI,IAAI,CAACA,aAAa,CAACgS,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAAChS,aAAa,CAACiS,IAAI,EAAE;MACzB,IAAI,CAACjS,aAAa,CAACgR,MAAM,CAACkB,SAAS,EAAE,CAAC7L,OAAO,CAAE8L,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAAC/R,cAAc,EAAE;MACvB4P,aAAa,CAAC,IAAI,CAAC5P,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACtI,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,YAAY;EACzC;EAEAc,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACqH,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACgS,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAAChS,aAAa,CAACiS,IAAI,EAAE;;MAE3B,IAAI,CAACjS,aAAa,CAACgR,MAAM,CAACkB,SAAS,EAAE,CAAC7L,OAAO,CAAE8L,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;MACtE,IAAI,CAACjS,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvB4P,aAAa,CAAC,IAAI,CAAC5P,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACtI,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACuB,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACtB,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACoI,WAAW,GAAG,EAAE;EACvB;EAEc2R,oBAAoBA,CAAA;IAAA,IAAAQ,MAAA;IAAA,OAAA3B,iBAAA;MAChCvO,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MAEtD,IAAI;QACF;QACA,IAAIiQ,MAAI,CAACnS,WAAW,CAACzK,MAAM,KAAK,CAAC,EAAE;UACjC0M,OAAO,CAACY,KAAK,CAAC,sCAAsC,CAAC;UACrDsP,MAAI,CAACvT,YAAY,CAACkF,SAAS,CAAC,wBAAwB,CAAC;UACrDqO,MAAI,CAACzZ,oBAAoB,EAAE;UAC3B;;QAGFuJ,OAAO,CAACC,GAAG,CACT,0BAA0B,EAC1BiQ,MAAI,CAACnS,WAAW,CAACzK,MAAM,EACvB,WAAW,EACX4c,MAAI,CAACjZ,sBAAsB,CAC5B;QAED;QACA,IAAIiZ,MAAI,CAACjZ,sBAAsB,GAAG,CAAC,EAAE;UACnC+I,OAAO,CAACY,KAAK,CACX,iCAAiC,EACjCsP,MAAI,CAACjZ,sBAAsB,CAC5B;UACDiZ,MAAI,CAACvT,YAAY,CAACkF,SAAS,CACzB,+CAA+C,CAChD;UACDqO,MAAI,CAACzZ,oBAAoB,EAAE;UAC3B;;QAGF;QACA,IAAI4Y,QAAQ,GAAG,wBAAwB;QACvC,IAAIa,MAAI,CAACpS,aAAa,EAAEuR,QAAQ,EAAE;UAChCA,QAAQ,GAAGa,MAAI,CAACpS,aAAa,CAACuR,QAAQ;;QAGxCrP,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEoP,QAAQ,CAAC;QAEvE;QACA,MAAMc,SAAS,GAAG,IAAIC,IAAI,CAACF,MAAI,CAACnS,WAAW,EAAE;UAC3CgG,IAAI,EAAEsL;SACP,CAAC;QAEFrP,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5C5D,IAAI,EAAE8T,SAAS,CAAC9T,IAAI;UACpB0H,IAAI,EAAEoM,SAAS,CAACpM;SACjB,CAAC;QAEF;QACA,IAAIsM,SAAS,GAAG,OAAO;QACvB,IAAIhB,QAAQ,CAACzD,QAAQ,CAAC,KAAK,CAAC,EAAE;UAC5ByE,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIhB,QAAQ,CAACzD,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnCyE,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIhB,QAAQ,CAACzD,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnCyE,SAAS,GAAG,MAAM;;QAGpB;QACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CACxB,CAACJ,SAAS,CAAC,EACX,SAASnN,IAAI,CAACiD,GAAG,EAAE,GAAGoK,SAAS,EAAE,EACjC;UACEtM,IAAI,EAAEsL;SACP,CACF;QAEDrP,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CjI,IAAI,EAAEsY,SAAS,CAACtY,IAAI;UACpBqE,IAAI,EAAEiU,SAAS,CAACjU,IAAI;UACpB0H,IAAI,EAAEuM,SAAS,CAACvM;SACjB,CAAC;QAEF;QACAmM,MAAI,CAACva,mBAAmB,GAAG,YAAY;QACvC,MAAMua,MAAI,CAACM,gBAAgB,CAACF,SAAS,CAAC;QAEtCtQ,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzDiQ,MAAI,CAACvT,YAAY,CAAC8K,WAAW,CAAC,yBAAyB,CAAC;OACzD,CAAC,OAAO7G,KAAU,EAAE;QACnBZ,OAAO,CAACY,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DsP,MAAI,CAACvT,YAAY,CAACkF,SAAS,CACzB,2CAA2C,IACxCjB,KAAK,CAAC2E,OAAO,IAAI,iBAAiB,CAAC,CACvC;OACF,SAAS;QACR;QACA2K,MAAI,CAACva,mBAAmB,GAAG,MAAM;QACjCua,MAAI,CAACjZ,sBAAsB,GAAG,CAAC;QAC/BiZ,MAAI,CAACnS,WAAW,GAAG,EAAE;QACrBmS,MAAI,CAACxa,gBAAgB,GAAG,KAAK;QAE7BsK,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;;IAClE;EACH;EAEcuQ,gBAAgBA,CAACF,SAAe;IAAA,IAAAG,MAAA;IAAA,OAAAlC,iBAAA;MAC5C,MAAMtK,UAAU,GAAGwM,MAAI,CAAC/lB,gBAAgB,EAAEyB,EAAE,IAAIskB,MAAI,CAAC/lB,gBAAgB,EAAE8W,GAAG;MAE1E,IAAI,CAACyC,UAAU,EAAE;QACf,MAAM,IAAI0K,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAI+B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCH,MAAI,CAAC/T,cAAc,CAAC3G,WAAW,CAC7BkO,UAAU,EACV,EAAE,EACFqM,SAAS,EACT,OAAc,EACdG,MAAI,CAAC5T,YAAY,CAAC1Q,EAAE,CACrB,CAACqU,SAAS,CAAC;UACVC,IAAI,EAAG8E,OAAY,IAAI;YACrBkL,MAAI,CAAC9c,QAAQ,CAAC8Q,IAAI,CAACc,OAAO,CAAC;YAC3BkL,MAAI,CAAClN,cAAc,EAAE;YACrBoN,OAAO,EAAE;UACX,CAAC;UACD/P,KAAK,EAAGA,KAAU,IAAI;YACpBZ,OAAO,CAACY,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChEgQ,MAAM,CAAChQ,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA5J,uBAAuBA,CAACmP,QAAgB;IACtC,MAAM+H,OAAO,GAAGnI,IAAI,CAACC,KAAK,CAACG,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMgI,OAAO,GAAGhI,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAG+H,OAAO,IAAIC,OAAO,CAACvF,QAAQ,EAAE,CAACwF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EAEA5Z,aAAaA,CAACyT,KAAY;IACxBA,KAAK,CAACC,cAAc,EAAE;IACtBlI,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvCvK,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CsB,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;MACnD6G,aAAa,EAAE,CAAC,CAAC,IAAI,CAACA;KACvB,CAAC;IAEF;IACA,IAAI,IAAI,CAACnI,mBAAmB,KAAK,YAAY,EAAE;MAC7CqK,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,IAAI,CAACtD,YAAY,CAACkU,WAAW,CAAC,wBAAwB,CAAC;MACvD;;IAGF,IAAI,IAAI,CAACnb,gBAAgB,EAAE;MACzBsK,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D,IAAI,CAACtD,YAAY,CAACkU,WAAW,CAAC,iCAAiC,CAAC;MAChE;;IAGF;IACA,IAAI,CAAClU,YAAY,CAACmU,QAAQ,CAAC,2CAA2C,CAAC;IAEvE;IACA,IAAI,CAACzC,mBAAmB,EAAE,CAAC0C,KAAK,CAAEnQ,KAAK,IAAI;MACzCZ,OAAO,CAACY,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAACjE,YAAY,CAACkF,SAAS,CACzB,iDAAiD,IAC9CjB,KAAK,CAAC2E,OAAO,IAAI,iBAAiB,CAAC,CACvC;IACH,CAAC,CAAC;EACJ;EAEA5Q,WAAWA,CAACsT,KAAY;IACtBA,KAAK,CAACC,cAAc,EAAE;IACtBlI,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,IAAI,CAAC,IAAI,CAACvK,gBAAgB,EAAE;MAC1BsK,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD;;IAGF;IACA,IAAI,CAACpJ,kBAAkB,EAAE;EAC3B;EAEA/B,cAAcA,CAACmT,KAAY;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtBlI,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD,IAAI,CAAC,IAAI,CAACvK,gBAAgB,EAAE;MAC1BsK,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD;;IAGF;IACA,IAAI,CAACxJ,oBAAoB,EAAE;EAC7B;EAEAS,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC4G,aAAa,EAAEuR,QAAQ,EAAE;MAChC,IAAI,IAAI,CAACvR,aAAa,CAACuR,QAAQ,CAACzD,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,MAAM;MAC/D,IAAI,IAAI,CAAC9N,aAAa,CAACuR,QAAQ,CAACzD,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAAC9N,aAAa,CAACuR,QAAQ,CAACzD,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAAC9N,aAAa,CAACuR,QAAQ,CAACzD,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;;IAE/D,OAAO,MAAM;EACf;EAEA;EAEQ2D,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAACxY,UAAU,GAAG,IAAI,CAACA,UAAU,CAACia,GAAG,CAAC,MAAK;MACzC,OAAOjL,IAAI,CAACC,KAAK,CAACD,IAAI,CAACkL,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;EACJ;;EAEAC,cAAcA,CAACjJ,KAAU;IACvBjI,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD,MAAMkR,KAAK,GAAGlJ,KAAK,CAACb,MAAM,CAAC+J,KAAK;IAEhC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAC7d,MAAM,KAAK,CAAC,EAAE;MAChC0M,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C;;IAGFD,OAAO,CAACC,GAAG,CAAC,eAAekR,KAAK,CAAC7d,MAAM,oBAAoB,EAAE6d,KAAK,CAAC;IAEnE,KAAK,IAAIC,IAAI,IAAID,KAAK,EAAE;MACtBnR,OAAO,CAACC,GAAG,CACT,gCAAgCmR,IAAI,CAACpZ,IAAI,WAAWoZ,IAAI,CAAC/U,IAAI,WAAW+U,IAAI,CAACrN,IAAI,EAAE,CACpF;MACD,IAAI,CAACsN,UAAU,CAACD,IAAI,CAAC;;EAEzB;EAEQC,UAAUA,CAACD,IAAU;IAC3BpR,OAAO,CAACC,GAAG,CAAC,yCAAyCmR,IAAI,CAACpZ,IAAI,EAAE,CAAC;IAEjE,MAAMiM,UAAU,GAAG,IAAI,CAACvZ,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAE8W,GAAG;IAE1E,IAAI,CAACyC,UAAU,EAAE;MACfjE,OAAO,CAACY,KAAK,CAAC,kCAAkC,CAAC;MACjD,IAAI,CAACjE,YAAY,CAACkF,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF7B,OAAO,CAACC,GAAG,CAAC,4BAA4BgE,UAAU,EAAE,CAAC;IAErD;IACA,MAAMqN,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,IAAIF,IAAI,CAAC/U,IAAI,GAAGiV,OAAO,EAAE;MACvBtR,OAAO,CAACY,KAAK,CAAC,+BAA+BwQ,IAAI,CAAC/U,IAAI,QAAQ,CAAC;MAC/D,IAAI,CAACM,YAAY,CAACkF,SAAS,CAAC,oCAAoC,CAAC;MACjE;;IAGF;IACA,IAAIuP,IAAI,CAACrN,IAAI,CAAC+C,UAAU,CAAC,QAAQ,CAAC,IAAIsK,IAAI,CAAC/U,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;MAC7D;MACA2D,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtCmR,IAAI,CAACpZ,IAAI,EACT,gBAAgB,EAChBoZ,IAAI,CAAC/U,IAAI,CACV;MACD,IAAI,CAACkV,aAAa,CAACH,IAAI,CAAC,CACrBI,IAAI,CAAEC,cAAc,IAAI;QACvBzR,OAAO,CAACC,GAAG,CACT,8DAA8D,EAC9DwR,cAAc,CAACpV,IAAI,CACpB;QACD,IAAI,CAACqV,gBAAgB,CAACD,cAAc,EAAExN,UAAU,CAAC;MACnD,CAAC,CAAC,CACD8M,KAAK,CAAEnQ,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE;QACA,IAAI,CAAC8Q,gBAAgB,CAACN,IAAI,EAAEnN,UAAU,CAAC;MACzC,CAAC,CAAC;MACJ;;IAGF;IACA,IAAI,CAACyN,gBAAgB,CAACN,IAAI,EAAEnN,UAAU,CAAC;EACzC;EAEQyN,gBAAgBA,CAACN,IAAU,EAAEnN,UAAkB;IACrD,MAAM0N,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACR,IAAI,CAAC;IACjDpR,OAAO,CAACC,GAAG,CAAC,wCAAwC0R,WAAW,EAAE,CAAC;IAClE3R,OAAO,CAACC,GAAG,CAAC,gCAAgC,IAAI,CAACpD,YAAY,CAAC1Q,EAAE,EAAE,CAAC;IAEnE,IAAI,CAACgK,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACyH,WAAW,GAAG,IAAI;IACvB,IAAI,CAACzJ,cAAc,GAAG,CAAC;IACvB6L,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhE;IACA,MAAM4R,gBAAgB,GAAGnE,WAAW,CAAC,MAAK;MACxC,IAAI,CAACvZ,cAAc,IAAI4R,IAAI,CAACkL,MAAM,EAAE,GAAG,EAAE;MACzC,IAAI,IAAI,CAAC9c,cAAc,IAAI,EAAE,EAAE;QAC7ByZ,aAAa,CAACiE,gBAAgB,CAAC;;MAEjC,IAAI,CAACjV,GAAG,CAAC8H,aAAa,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,CAAChI,cAAc,CAAC3G,WAAW,CAC7BkO,UAAU,EACV,EAAE,EACFmN,IAAI,EACJO,WAAW,EACX,IAAI,CAAC9U,YAAY,CAAC1Q,EAAE,CACrB,CAACqU,SAAS,CAAC;MACVC,IAAI,EAAG8E,OAAY,IAAI;QACrBvF,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEsF,OAAO,CAAC;QAC7DvF,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;UAChD9T,EAAE,EAAEoZ,OAAO,CAACpZ,EAAE;UACd4X,IAAI,EAAEwB,OAAO,CAACxB,IAAI;UAClBG,WAAW,EAAEqB,OAAO,CAACrB,WAAW;UAChC/Q,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAACoS,OAAO,CAAC;UAChCnS,OAAO,EAAE,IAAI,CAACA,OAAO,CAACmS,OAAO,CAAC;UAC9BkG,QAAQ,EAAE,IAAI,CAAC3d,WAAW,CAACyX,OAAO;SACnC,CAAC;QAEFqI,aAAa,CAACiE,gBAAgB,CAAC;QAC/B,IAAI,CAAC1d,cAAc,GAAG,GAAG;QAEzBwQ,UAAU,CAAC,MAAK;UACd,IAAI,CAAChR,QAAQ,CAAC8Q,IAAI,CAACc,OAAO,CAAC;UAC3B,IAAI,CAAChC,cAAc,EAAE;UACrB,IAAI,CAAC5G,YAAY,CAAC8K,WAAW,CAAC,4BAA4B,CAAC;UAC3D,IAAI,CAACxT,gBAAgB,EAAE;QACzB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD2M,KAAK,EAAGA,KAAU,IAAI;QACpBZ,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDgN,aAAa,CAACiE,gBAAgB,CAAC;QAC/B,IAAI,CAAClV,YAAY,CAACkF,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAAC5N,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEQ2d,kBAAkBA,CAACR,IAAU;IACnC,IAAIA,IAAI,CAACrN,IAAI,CAAC+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAIsK,IAAI,CAACrN,IAAI,CAAC+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAIsK,IAAI,CAACrN,IAAI,CAAC+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,OAAO,MAAa;EACtB;EAEAgL,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEA7d,gBAAgBA,CAAA;IACd,IAAI,CAACkC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACyH,WAAW,GAAG,KAAK;IACxB,IAAI,CAACzJ,cAAc,GAAG,CAAC;EACzB;EAEA;EAEA4d,UAAUA,CAAC9J,KAAgB;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAAC5Z,eAAe,EAAE;IACvB,IAAI,CAACwP,UAAU,GAAG,IAAI;EACxB;EAEAmU,WAAWA,CAAC/J,KAAgB;IAC1BA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAAC5Z,eAAe,EAAE;IACvB;IACA,MAAM4jB,IAAI,GAAIhK,KAAK,CAACiK,aAA6B,CAACC,qBAAqB,EAAE;IACzE,MAAM1Y,CAAC,GAAGwO,KAAK,CAACE,OAAO;IACvB,MAAMzO,CAAC,GAAGuO,KAAK,CAACG,OAAO;IAEvB,IAAI3O,CAAC,GAAGwY,IAAI,CAACG,IAAI,IAAI3Y,CAAC,GAAGwY,IAAI,CAACI,KAAK,IAAI3Y,CAAC,GAAGuY,IAAI,CAACK,GAAG,IAAI5Y,CAAC,GAAGuY,IAAI,CAACM,MAAM,EAAE;MACtE,IAAI,CAAC1U,UAAU,GAAG,KAAK;;EAE3B;EAEA2U,MAAMA,CAACvK,KAAgB;IACrBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAAC5Z,eAAe,EAAE;IACvB,IAAI,CAACwP,UAAU,GAAG,KAAK;IAEvB,MAAMsT,KAAK,GAAGlJ,KAAK,CAACwK,YAAY,EAAEtB,KAAK;IACvC,IAAIA,KAAK,IAAIA,KAAK,CAAC7d,MAAM,GAAG,CAAC,EAAE;MAC7B0M,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEkR,KAAK,CAAC7d,MAAM,CAAC;MAE1D;MACAof,KAAK,CAACC,IAAI,CAACxB,KAAK,CAAC,CAAChN,OAAO,CAAEiN,IAAI,IAAI;QACjCpR,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCmR,IAAI,CAACpZ,IAAI,EACToZ,IAAI,CAACrN,IAAI,EACTqN,IAAI,CAAC/U,IAAI,CACV;QACD,IAAI,CAACgV,UAAU,CAACD,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,IAAI,CAACzU,YAAY,CAAC8K,WAAW,CAC3B,GAAG0J,KAAK,CAAC7d,MAAM,8BAA8B,CAC9C;;EAEL;EAEA;EAEQie,aAAaA,CAACH,IAAU,EAAEwB,OAAA,GAAkB,GAAG;IACrD,OAAO,IAAIlC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMiC,MAAM,GAAG7L,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAM6L,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;MAEvBD,GAAG,CAACE,MAAM,GAAG,MAAK;QAChB;QACA,MAAMC,QAAQ,GAAG,IAAI;QACrB,MAAMC,SAAS,GAAG,IAAI;QACtB,IAAI;UAAEC,KAAK;UAAEC;QAAM,CAAE,GAAGN,GAAG;QAE3B,IAAIK,KAAK,GAAGF,QAAQ,IAAIG,MAAM,GAAGF,SAAS,EAAE;UAC1C,MAAMG,KAAK,GAAGxN,IAAI,CAAC8D,GAAG,CAACsJ,QAAQ,GAAGE,KAAK,EAAED,SAAS,GAAGE,MAAM,CAAC;UAC5DD,KAAK,IAAIE,KAAK;UACdD,MAAM,IAAIC,KAAK;;QAGjBV,MAAM,CAACQ,KAAK,GAAGA,KAAK;QACpBR,MAAM,CAACS,MAAM,GAAGA,MAAM;QAEtB;QACAR,GAAG,EAAEU,SAAS,CAACR,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEK,KAAK,EAAEC,MAAM,CAAC;QAExC;QACAT,MAAM,CAACY,MAAM,CACVC,IAAI,IAAI;UACP,IAAIA,IAAI,EAAE;YACR,MAAMjC,cAAc,GAAG,IAAIlB,IAAI,CAAC,CAACmD,IAAI,CAAC,EAAEtC,IAAI,CAACpZ,IAAI,EAAE;cACjD+L,IAAI,EAAEqN,IAAI,CAACrN,IAAI;cACf4P,YAAY,EAAE3Q,IAAI,CAACiD,GAAG;aACvB,CAAC;YACF0K,OAAO,CAACc,cAAc,CAAC;WACxB,MAAM;YACLb,MAAM,CAAC,IAAIjC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;QAEjD,CAAC,EACDyC,IAAI,CAACrN,IAAI,EACT6O,OAAO,CACR;MACH,CAAC;MAEDI,GAAG,CAACrD,OAAO,GAAG,MAAMiB,MAAM,CAAC,IAAIjC,KAAK,CAAC,sBAAsB,CAAC,CAAC;MAC7DqE,GAAG,CAACzG,GAAG,GAAGqH,GAAG,CAACC,eAAe,CAACzC,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA;EAEQpF,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAAC/M,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB;MACA,IAAI,CAAC6U,mBAAmB,CAAC,IAAI,CAAC;;IAGhC;IACA,IAAI,IAAI,CAAC3U,aAAa,EAAE;MACtB4U,YAAY,CAAC,IAAI,CAAC5U,aAAa,CAAC;;IAGlC,IAAI,CAACA,aAAa,GAAGwF,UAAU,CAAC,MAAK;MACnC,IAAI,CAAC1F,QAAQ,GAAG,KAAK;MACrB;MACA,IAAI,CAAC6U,mBAAmB,CAAC,KAAK,CAAC;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;EAEQA,mBAAmBA,CAAC7U,QAAiB;IAC3C;IACA,MAAMgF,UAAU,GAAG,IAAI,CAACvZ,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAE8W,GAAG;IAC1E,IAAIyC,UAAU,IAAI,IAAI,CAACpH,YAAY,EAAE1Q,EAAE,EAAE;MACvC6T,OAAO,CAACC,GAAG,CACT,gCAAgChB,QAAQ,YAAYgF,UAAU,EAAE,CACjE;MACD;MACA;;EAEJ;EAEA;EAEA+P,cAAcA,CAAClT,IAAU;IACvBd,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEa,IAAI,CAAC;IACrD,IAAI,CAACtC,UAAU,GAAGsC,IAAI;IACtB,IAAI,CAAC1C,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACK,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC8O,cAAc,EAAE;IACrB,IAAI,CAAC5Q,YAAY,CAAC8K,WAAW,CAAC,eAAe,CAAC;EAChD;EAEAwM,cAAcA,CAAA;IACZjU,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAACwG,OAAO,EAAE;IACd,IAAI,CAAC9J,YAAY,CAACmU,QAAQ,CAAC,cAAc,CAAC;EAC5C;EAEA;EAEAoD,gBAAgBA,CAAC3O,OAAY;IAC3BvF,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEsF,OAAO,CAACpZ,EAAE,CAAC;IAC5D,IAAI,CAAC+C,mBAAmB,CAACqW,OAAO,CAAC;EACnC;EAEA9W,cAAcA,CAACyW,SAAiB;IAC9B,OAAO,IAAI,CAAChH,gBAAgB,KAAKgH,SAAS;EAC5C;EAEAhW,mBAAmBA,CAACqW,OAAY;IAC9B,MAAML,SAAS,GAAGK,OAAO,CAACpZ,EAAE;IAC5B,MAAMkf,QAAQ,GAAG,IAAI,CAAC8I,WAAW,CAAC5O,OAAO,CAAC;IAE1C,IAAI,CAAC8F,QAAQ,EAAE;MACbrL,OAAO,CAACY,KAAK,CAAC,4CAA4C,EAAEsE,SAAS,CAAC;MACtE,IAAI,CAACvI,YAAY,CAACkF,SAAS,CAAC,2BAA2B,CAAC;MACxD;;IAGF;IACA,IAAI,IAAI,CAACpT,cAAc,CAACyW,SAAS,CAAC,EAAE;MAClC,IAAI,CAACkP,iBAAiB,EAAE;MACxB;;IAGF;IACA,IAAI,CAACA,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,kBAAkB,CAAC9O,OAAO,EAAE8F,QAAQ,CAAC;EAC5C;EAEQgJ,kBAAkBA,CAAC9O,OAAY,EAAE8F,QAAgB;IACvD,MAAMnG,SAAS,GAAGK,OAAO,CAACpZ,EAAE;IAE5B,IAAI;MACF6T,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnCiF,SAAS,EACT,MAAM,EACNmG,QAAQ,CACT;MAED,IAAI,CAACpN,YAAY,GAAG,IAAIqW,KAAK,CAACjJ,QAAQ,CAAC;MACvC,IAAI,CAACnN,gBAAgB,GAAGgH,SAAS;MAEjC;MACA,MAAMqP,WAAW,GAAG,IAAI,CAAC1lB,oBAAoB,CAACqW,SAAS,CAAC;MACxD,IAAI,CAACmB,oBAAoB,CAACnB,SAAS,EAAE;QACnCgB,QAAQ,EAAE,CAAC;QACXE,WAAW,EAAE,CAAC;QACdtX,KAAK,EAAEylB,WAAW,CAACzlB,KAAK,IAAI,CAAC;QAC7BqX,QAAQ,EAAEoO,WAAW,CAACpO,QAAQ,IAAI;OACnC,CAAC;MAEF;MACA,IAAI,CAAClI,YAAY,CAACuW,YAAY,GAAGD,WAAW,CAACzlB,KAAK,IAAI,CAAC;MAEvD;MACA,IAAI,CAACmP,YAAY,CAACwW,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QACxD,IAAI,IAAI,CAACxW,YAAY,EAAE;UACrB,IAAI,CAACoI,oBAAoB,CAACnB,SAAS,EAAE;YACnCiB,QAAQ,EAAE,IAAI,CAAClI,YAAY,CAACkI;WAC7B,CAAC;UACFnG,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAAChC,YAAY,CAACkI,QAAQ,CAC3B;;MAEL,CAAC,CAAC;MAEF,IAAI,CAAClI,YAAY,CAACwW,gBAAgB,CAAC,YAAY,EAAE,MAAK;QACpD,IAAI,IAAI,CAACxW,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKgH,SAAS,EAAE;UAC5D,MAAMkB,WAAW,GAAG,IAAI,CAACnI,YAAY,CAACmI,WAAW;UACjD,MAAMF,QAAQ,GAAIE,WAAW,GAAG,IAAI,CAACnI,YAAY,CAACkI,QAAQ,GAAI,GAAG;UACjE,IAAI,CAACE,oBAAoB,CAACnB,SAAS,EAAE;YAAEkB,WAAW;YAAEF;UAAQ,CAAE,CAAC;UAC/D,IAAI,CAACtJ,GAAG,CAAC8H,aAAa,EAAE;;MAE5B,CAAC,CAAC;MAEF,IAAI,CAACzG,YAAY,CAACwW,gBAAgB,CAAC,OAAO,EAAE,MAAK;QAC/CzU,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEiF,SAAS,CAAC;QACxD,IAAI,CAACkP,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF,IAAI,CAACnW,YAAY,CAACwW,gBAAgB,CAAC,OAAO,EAAG7T,KAAK,IAAI;QACpDZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACjE,YAAY,CAACkF,SAAS,CAAC,iCAAiC,CAAC;QAC9D,IAAI,CAACuS,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF;MACA,IAAI,CAACnW,YAAY,CACd+C,IAAI,EAAE,CACNwQ,IAAI,CAAC,MAAK;QACTxR,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,IAAI,CAACtD,YAAY,CAAC8K,WAAW,CAAC,6BAA6B,CAAC;MAC9D,CAAC,CAAC,CACDsJ,KAAK,CAAEnQ,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAACjE,YAAY,CAACkF,SAAS,CAAC,qCAAqC,CAAC;QAClE,IAAI,CAACuS,iBAAiB,EAAE;MAC1B,CAAC,CAAC;KACL,CAAC,OAAOxT,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,IAAI,CAACjE,YAAY,CAACkF,SAAS,CAAC,iCAAiC,CAAC;MAC9D,IAAI,CAACuS,iBAAiB,EAAE;;EAE5B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACnW,YAAY,EAAE;MACrB+B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC/B,gBAAgB,CAAC;MACvE,IAAI,CAACD,YAAY,CAACyW,KAAK,EAAE;MACzB,IAAI,CAACzW,YAAY,CAACmI,WAAW,GAAG,CAAC;MACjC,IAAI,CAACnI,YAAY,GAAG,IAAI;;IAE1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACtB,GAAG,CAAC8H,aAAa,EAAE;EAC1B;EAEAyP,WAAWA,CAAC5O,OAAY;IACtB;IACA,IAAIA,OAAO,CAAC6F,QAAQ,EAAE,OAAO7F,OAAO,CAAC6F,QAAQ;IAC7C,IAAI7F,OAAO,CAAC8F,QAAQ,EAAE,OAAO9F,OAAO,CAAC8F,QAAQ;IAC7C,IAAI9F,OAAO,CAAC+F,KAAK,EAAE,OAAO/F,OAAO,CAAC+F,KAAK;IAEvC;IACA,MAAMqJ,eAAe,GAAGpP,OAAO,CAACrB,WAAW,EAAE3B,IAAI,CAC9C6B,GAAQ,IAAKA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,IAAI1C,GAAG,CAACL,IAAI,KAAK,OAAO,CACrE;IAED,IAAI4Q,eAAe,EAAE;MACnB,OAAOA,eAAe,CAACvY,GAAG,IAAIuY,eAAe,CAACtQ,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEA3U,aAAaA,CAAC6V,OAAY;IACxB;IACA,MAAML,SAAS,GAAGK,OAAO,CAACpZ,EAAE,IAAI,EAAE;IAClC,MAAMyoB,IAAI,GAAG1P,SAAS,CACnB2P,KAAK,CAAC,EAAE,CAAC,CACTC,MAAM,CAAC,CAACC,GAAW,EAAEC,IAAY,KAAKD,GAAG,GAAGC,IAAI,CAAClJ,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,MAAMmJ,KAAK,GAAa,EAAE;IAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAM5B,MAAM,GAAG,CAAC,GAAI,CAACsB,IAAI,GAAGM,CAAC,GAAG,CAAC,IAAI,EAAG;MACxCD,KAAK,CAACxQ,IAAI,CAAC6O,MAAM,CAAC;;IAGpB,OAAO2B,KAAK;EACd;EAEAvmB,gBAAgBA,CAAC6W,OAAY;IAC3B,MAAMe,IAAI,GAAG,IAAI,CAACzX,oBAAoB,CAAC0W,OAAO,CAACpZ,EAAE,CAAC;IAClD,MAAMgpB,UAAU,GAAG,EAAE;IACrB,OAAOpP,IAAI,CAACC,KAAK,CAAEM,IAAI,CAACJ,QAAQ,GAAG,GAAG,GAAIiP,UAAU,CAAC;EACvD;EAEAxlB,mBAAmBA,CAAC4V,OAAY;IAC9B,MAAMe,IAAI,GAAG,IAAI,CAACzX,oBAAoB,CAAC0W,OAAO,CAACpZ,EAAE,CAAC;IAClD,OAAO,IAAI,CAACipB,eAAe,CAAC9O,IAAI,CAACF,WAAW,CAAC;EAC/C;EAEAxW,gBAAgBA,CAAC2V,OAAY;IAC3B,MAAMe,IAAI,GAAG,IAAI,CAACzX,oBAAoB,CAAC0W,OAAO,CAACpZ,EAAE,CAAC;IAClD,MAAMga,QAAQ,GAAGG,IAAI,CAACH,QAAQ,IAAIZ,OAAO,CAAC8P,QAAQ,EAAElP,QAAQ,IAAI,CAAC;IAEjE,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAOA,QAAQ,CAAC,CAAC;;;IAGnB,OAAO,IAAI,CAACiP,eAAe,CAACjP,QAAQ,CAAC;EACvC;EAEQiP,eAAeA,CAACjH,OAAe;IACrC,MAAMD,OAAO,GAAGnI,IAAI,CAACC,KAAK,CAACmI,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMmH,gBAAgB,GAAGvP,IAAI,CAACC,KAAK,CAACmI,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGD,OAAO,IAAIoH,gBAAgB,CAAC1M,QAAQ,EAAE,CAACwF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEA9f,gBAAgBA,CAACiX,OAAY,EAAEgQ,SAAiB;IAC9C,MAAMrQ,SAAS,GAAGK,OAAO,CAACpZ,EAAE;IAE5B,IAAI,CAAC,IAAI,CAAC8R,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKgH,SAAS,EAAE;MAC7D;;IAGF,MAAMiQ,UAAU,GAAG,EAAE;IACrB,MAAMK,cAAc,GAAID,SAAS,GAAGJ,UAAU,GAAI,GAAG;IACrD,MAAMM,QAAQ,GAAID,cAAc,GAAG,GAAG,GAAI,IAAI,CAACvX,YAAY,CAACkI,QAAQ;IAEpE,IAAI,CAAClI,YAAY,CAACmI,WAAW,GAAGqP,QAAQ;IACxCzV,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEwV,QAAQ,EAAE,SAAS,CAAC;EAC5D;EAEAjmB,gBAAgBA,CAAC+V,OAAY;IAC3B,MAAML,SAAS,GAAGK,OAAO,CAACpZ,EAAE;IAC5B,MAAMma,IAAI,GAAG,IAAI,CAACzX,oBAAoB,CAACqW,SAAS,CAAC;IAEjD;IACA,MAAMwQ,QAAQ,GAAGpP,IAAI,CAACxX,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGwX,IAAI,CAACxX,KAAK,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;IAEpE,IAAI,CAACuX,oBAAoB,CAACnB,SAAS,EAAE;MAAEpW,KAAK,EAAE4mB;IAAQ,CAAE,CAAC;IAEzD,IAAI,IAAI,CAACzX,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKgH,SAAS,EAAE;MAC5D,IAAI,CAACjH,YAAY,CAACuW,YAAY,GAAGkB,QAAQ;;IAG3C,IAAI,CAAC/Y,YAAY,CAAC8K,WAAW,CAAC,YAAYiO,QAAQ,GAAG,CAAC;EACxD;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACvW,aAAa,CAACwW,WAAW,EAAE;IAEhC;IACA,IAAI,IAAI,CAACrX,SAAS,EAAE;MAClBqP,aAAa,CAAC,IAAI,CAACrP,SAAS,CAAC;;IAE/B,IAAI,IAAI,CAACP,cAAc,EAAE;MACvB4P,aAAa,CAAC,IAAI,CAAC5P,cAAc,CAAC;;IAEpC,IAAI,IAAI,CAACmB,aAAa,EAAE;MACtB4U,YAAY,CAAC,IAAI,CAAC5U,aAAa,CAAC;;IAGlC;IACA,IAAI,IAAI,CAACrB,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACgS,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAAChS,aAAa,CAACiS,IAAI,EAAE;;MAE3B,IAAI,CAACjS,aAAa,CAACgR,MAAM,EAAEkB,SAAS,EAAE,CAAC7L,OAAO,CAAE8L,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGzE;IACA,IAAI,CAACqE,iBAAiB,EAAE;EAC1B;;;uBAz2EW9X,oBAAoB,EAAApS,EAAA,CAAA2rB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7rB,EAAA,CAAA2rB,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA/rB,EAAA,CAAA2rB,iBAAA,CAAAK,EAAA,CAAAxZ,cAAA,GAAAxS,EAAA,CAAA2rB,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAAlsB,EAAA,CAAA2rB,iBAAA,CAAA3rB,EAAA,CAAAmsB,iBAAA;IAAA;EAAA;;;YAApB/Z,oBAAoB;MAAAga,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAA3D,GAAA;QAAA,IAAA2D,EAAA;;;;;;;;;;;;;;;UClBjCvsB,EAAA,CAAAE,cAAA,aAQC;UAyBKF,EAAA,CAAAY,UAAA,mBAAA4rB,sDAAA;YAAA,OAAS5D,GAAA,CAAA/K,qBAAA,EAAuB;UAAA,EAAC;UAajC7d,EAAA,CAAAC,SAAA,WAAwD;UAC1DD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,aAAuE;UAcjEF,EAAA,CAAAY,UAAA,mBAAA6rB,mDAAA;YAAA,OAAS7D,GAAA,CAAA7mB,eAAA,CAAA6mB,GAAA,CAAApoB,gBAAA,kBAAAooB,GAAA,CAAApoB,gBAAA,CAAAyB,EAAA,CAAsC;UAAA,EAAC;UAZlDjC,EAAA,CAAAI,YAAA,EAeE;UACFJ,EAAA,CAAAyD,UAAA,IAAAipB,mCAAA,iBAaO;UACT1sB,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAE,cAAA,aAAmC;UAW/BF,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,eAA6C;UAC3CF,EAAA,CAAAyD,UAAA,KAAAkpB,oCAAA,kBAkCM;UACN3sB,EAAA,CAAAyD,UAAA,KAAAmpB,qCAAA,mBAMO;UACT5sB,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAE,cAAA,eAA0D;UAEtDF,EAAA,CAAAY,UAAA,mBAAAisB,uDAAA;YAAA,OAASjE,GAAA,CAAAvM,cAAA,EAAgB;UAAA,EAAC;UAc1Brc,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAcC;UAbCF,EAAA,CAAAY,UAAA,mBAAAksB,uDAAA;YAAA,OAASlE,GAAA,CAAAtM,cAAA,EAAgB;UAAA,EAAC;UAc1Btc,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAgBC;UAfCF,EAAA,CAAAY,UAAA,mBAAAmsB,uDAAA;YAAA,OAASnE,GAAA,CAAA1nB,YAAA,EAAc;UAAA,EAAC;UAgBxBlB,EAAA,CAAAC,SAAA,aAA6B;UAC/BD,EAAA,CAAAI,YAAA,EAAS;UAETJ,EAAA,CAAAE,cAAA,kBAiBC;UAhBCF,EAAA,CAAAY,UAAA,mBAAAosB,uDAAA;YAAA,OAASpE,GAAA,CAAAhL,cAAA,EAAgB;UAAA,EAAC;UAiB1B5d,EAAA,CAAAC,SAAA,aAAiC;UACnCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAyD,UAAA,KAAAwpB,oCAAA,mBAiCM;UACRjtB,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,oBAQC;UALCF,EAAA,CAAAY,UAAA,oBAAAssB,sDAAA/pB,MAAA;YAAA,OAAUylB,GAAA,CAAAxG,QAAA,CAAAjf,MAAA,CAAgB;UAAA,EAAC,sBAAAgqB,wDAAAhqB,MAAA;YAAA,OACfylB,GAAA,CAAAf,UAAA,CAAA1kB,MAAA,CAAkB;UAAA,EADH,uBAAAiqB,yDAAAjqB,MAAA;YAAA,OAEdylB,GAAA,CAAAd,WAAA,CAAA3kB,MAAA,CAAmB;UAAA,EAFL,kBAAAkqB,oDAAAlqB,MAAA;YAAA,OAGnBylB,GAAA,CAAAN,MAAA,CAAAnlB,MAAA,CAAc;UAAA,EAHK;UAO3BnD,EAAA,CAAAyD,UAAA,KAAA6pB,oCAAA,mBAsCM;UAINttB,EAAA,CAAAyD,UAAA,KAAA8pB,oCAAA,kBAUM;UAGNvtB,EAAA,CAAAyD,UAAA,KAAA+pB,oCAAA,kBAaM;UAGNxtB,EAAA,CAAAyD,UAAA,KAAAgqB,oCAAA,kBAqXM;UACRztB,EAAA,CAAAI,YAAA,EAAO;UAGPJ,EAAA,CAAAyD,UAAA,KAAAiqB,oCAAA,mBA2BM;UAGN1tB,EAAA,CAAAE,cAAA,kBAEC;UAGGF,EAAA,CAAAY,UAAA,sBAAA+sB,wDAAA;YAAA,OAAY/E,GAAA,CAAA/c,WAAA,EAAa;UAAA,EAAC;UAI1B7L,EAAA,CAAAE,cAAA,eAAwB;UAGpBF,EAAA,CAAAY,UAAA,mBAAAgtB,uDAAA;YAAA,OAAShF,GAAA,CAAAxK,iBAAA,EAAmB;UAAA,EAAC;UAM7Bpe,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAOC;UALCF,EAAA,CAAAY,UAAA,mBAAAitB,uDAAA;YAAA,OAASjF,GAAA,CAAAnK,oBAAA,EAAsB;UAAA,EAAC;UAMhCze,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAE,cAAA,eAAoB;UAQhBF,EAAA,CAAAY,UAAA,mBAAAktB,yDAAA3qB,MAAA;YAAA,OAASylB,GAAA,CAAA/G,aAAA,CAAA1e,MAAA,CAAqB;UAAA,EAAC,qBAAA4qB,2DAAA5qB,MAAA;YAAA,OACpBylB,GAAA,CAAA7G,cAAA,CAAA5e,MAAA,CAAsB;UAAA,EADF,mBAAA6qB,yDAAA;YAAA,OAEtBpF,GAAA,CAAA1G,YAAA,EAAc;UAAA,EAFQ,kBAAA+L,wDAAA;YAAA,OAGvBrF,GAAA,CAAAzG,WAAA,EAAa;UAAA,EAHU;UASjCniB,EAAA,CAAAG,MAAA;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAIbJ,EAAA,CAAAE,cAAA,eAAwB;UAEtBF,EAAA,CAAAyD,UAAA,KAAAyqB,uCAAA,qBAyBS;UAGTluB,EAAA,CAAAyD,UAAA,KAAA0qB,uCAAA,qBAUS;UACXnuB,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAyD,UAAA,KAAA2qB,oCAAA,mBAqEM;UAGNpuB,EAAA,CAAAyD,UAAA,KAAA4qB,oCAAA,kBA6BM;UAGNruB,EAAA,CAAAyD,UAAA,KAAA6qB,oCAAA,mBA4DM;UAGNtuB,EAAA,CAAAE,cAAA,qBAOE;UAHAF,EAAA,CAAAY,UAAA,oBAAA2tB,uDAAAprB,MAAA;YAAA,OAAUylB,GAAA,CAAA5B,cAAA,CAAA7jB,MAAA,CAAsB;UAAA,EAAC;UAJnCnD,EAAA,CAAAI,YAAA,EAOE;UAGFJ,EAAA,CAAAyD,UAAA,KAAA+qB,oCAAA,kBAeM;UAGNxuB,EAAA,CAAAyD,UAAA,KAAAgrB,oCAAA,mBAqCM;UAGNzuB,EAAA,CAAAyD,UAAA,KAAAirB,oCAAA,kBAUO;UACT1uB,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAE,cAAA,8BAQC;UAHCF,EAAA,CAAAY,UAAA,uBAAA+tB,uEAAA;YAAA,OAAa/F,GAAA,CAAArM,OAAA,EAAS;UAAA,EAAC,0BAAAqS,0EAAAzrB,MAAA;YAAA,OACPylB,GAAA,CAAAkB,cAAA,CAAA3mB,MAAA,CAAsB;UAAA,EADf,0BAAA0rB,0EAAA;YAAA,OAEPjG,GAAA,CAAAmB,cAAA,EAAgB;UAAA,EAFT;UAGxB/pB,EAAA,CAAAI,YAAA,EAAqB;UAGtBJ,EAAA,CAAAyD,UAAA,KAAAqrB,oCAAA,mBAyEM;;;;;UAxmCI9uB,EAAA,CAAAK,SAAA,GAAqE;UAArEL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAApoB,gBAAA,kBAAAooB,GAAA,CAAApoB,gBAAA,CAAA2B,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAAqE,QAAAwmB,GAAA,CAAApoB,gBAAA,kBAAAooB,GAAA,CAAApoB,gBAAA,CAAAc,QAAA;UAgBpEtB,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAApoB,gBAAA,kBAAAooB,GAAA,CAAApoB,gBAAA,CAAAC,QAAA,CAAgC;UA0BjCT,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,OAAAsoB,GAAA,CAAApoB,gBAAA,kBAAAooB,GAAA,CAAApoB,gBAAA,CAAAc,QAAA,wBACF;UAGKtB,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAA5T,YAAA,CAAkB;UAkCdhV,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAkC,UAAA,UAAA0mB,GAAA,CAAA5T,YAAA,CAAmB;UA0D5BhV,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAqC,WAAA,eAAAumB,GAAA,CAAAvV,UAAA,6BAA2D,UAAAuV,GAAA,CAAAvV,UAAA;UAqB3DrT,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAqC,WAAA,eAAAumB,GAAA,CAAAxnB,YAAA,6BAA6D,UAAAwnB,GAAA,CAAAxnB,YAAA;UAY9DpB,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAAxnB,YAAA,CAAkB;UA2CrBpB,EAAA,CAAAK,SAAA,GAA0E;UAA1EL,EAAA,CAAAqC,WAAA,eAAAumB,GAAA,CAAAjV,UAAA,4CAA0E;UAIvE3T,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAAjV,UAAA,CAAgB;UA0ChB3T,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAA/V,SAAA,CAAe;UAaf7S,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAkC,UAAA,UAAA0mB,GAAA,CAAA/V,SAAA,IAAA+V,GAAA,CAAAnf,QAAA,CAAAL,MAAA,OAAyC;UAetCpJ,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAAkC,UAAA,UAAA0mB,GAAA,CAAA/V,SAAA,IAAA+V,GAAA,CAAAnf,QAAA,CAAAL,MAAA,KAAuC;UA0X5CpJ,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAAlV,WAAA,CAAiB;UAiChB1T,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAkC,UAAA,cAAA0mB,GAAA,CAAAzT,WAAA,CAAyB;UAUrBnV,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAyE,WAAA,iBAAAmkB,GAAA,CAAA5V,eAAA,CAAsC,mBAAA4V,GAAA,CAAA5V,eAAA;UAUtChT,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAyE,WAAA,iBAAAmkB,GAAA,CAAA3V,kBAAA,CAAyC,mBAAA2V,GAAA,CAAA3V,kBAAA;UAezCjT,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAyE,WAAA,eAAAmkB,GAAA,CAAArT,eAAA,GAAsC,uBAAAqT,GAAA,CAAArT,eAAA;UAkBrCvV,EAAA,CAAAK,SAAA,GAAgD;UAAhDL,EAAA,CAAAkC,UAAA,YAAA6sB,QAAA,GAAAnG,GAAA,CAAAzT,WAAA,CAAAO,GAAA,8BAAAqZ,QAAA,CAAA7T,KAAA,kBAAA6T,QAAA,CAAA7T,KAAA,CAAAC,IAAA,IAAgD;UA4BhDnb,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAAkC,UAAA,UAAA8sB,QAAA,GAAApG,GAAA,CAAAzT,WAAA,CAAAO,GAAA,8BAAAsZ,QAAA,CAAA9T,KAAA,kBAAA8T,QAAA,CAAA9T,KAAA,CAAAC,IAAA,GAA+C;UAgBrDnb,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAApd,gBAAA,CAAsB;UAwEtBxL,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAA5V,eAAA,CAAqB;UAgCrBhT,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAA3V,kBAAA,CAAwB;UAmEzBjT,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAAkC,UAAA,WAAA0mB,GAAA,CAAAhB,kBAAA,GAA+B;UAM9B5nB,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAArV,kBAAA,CAAwB;UAkBxBvT,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAAtV,sBAAA,CAA4B;UAwC5BtT,EAAA,CAAAK,SAAA,GAOL;UAPKL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAA5V,eAAA,IAAA4V,GAAA,CAAA3V,kBAAA,IAAA2V,GAAA,CAAAxnB,YAAA,IAAAwnB,GAAA,CAAAtV,sBAAA,IAAAsV,GAAA,CAAArV,kBAAA,CAOL;UAOEvT,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,cAAA0mB,GAAA,CAAA1U,QAAA,CAAsB,eAAA0U,GAAA,CAAAtU,UAAA,cAAAsU,GAAA,CAAAzU,QAAA,sBAAAyU,GAAA,CAAApoB,gBAAA;UAWrBR,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAkC,UAAA,SAAA0mB,GAAA,CAAAnV,eAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}