{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:18:36","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:18:36"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:18:36"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:18:36"}
{"level":"http","message":"GraphQL anonymous completed in 271ms","timestamp":"2025-05-30 21:18:36"}
{"level":"http","message":"POST / 200 - 276ms","timestamp":"2025-05-30 21:18:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:18:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:18:53"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:18:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:18:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:18:53"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:18:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-30 21:18:54","variables":{"content":"perfect all clear","conversationId":"6839c3a5b3d00b25cf26afd1","receiverId":"6839c1fcb3d00b25cf26afa7","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, type=TEXT, hasMetadata=false","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a12aee780b958457257c4","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a12aee780b958457257c4","timestamp":"2025-05-30 21:18:54"}
{"level":"http","message":"GraphQL anonymous completed in 623ms","timestamp":"2025-05-30 21:18:54"}
{"level":"http","message":"POST / 200 - 638ms","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:18:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-30 21:18:54","variables":{"messageId":"683a12aee780b958457257c4"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:18:54","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:18:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:18:54","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 30","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 30","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:18:54"}
{"level":"http","message":"GraphQL anonymous completed in 260ms","timestamp":"2025-05-30 21:18:55"}
{"level":"http","message":"POST / 200 - 266ms","timestamp":"2025-05-30 21:18:55"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:18:55"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:18:55"}
{"level":"http","message":"GraphQL anonymous completed in 432ms","timestamp":"2025-05-30 21:18:55"}
{"level":"http","message":"POST / 200 - 437ms","timestamp":"2025-05-30 21:18:55"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:18:55"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:18:55"}
{"level":"http","message":"GraphQL anonymous completed in 445ms","timestamp":"2025-05-30 21:18:55"}
{"level":"http","message":"POST / 200 - 449ms","timestamp":"2025-05-30 21:18:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-30 21:19:16","variables":{"content":"but write insite espace tchat not clear","conversationId":"6839c3a5b3d00b25cf26afd1","receiverId":"6839c1fcb3d00b25cf26afa7","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, type=TEXT, hasMetadata=false","timestamp":"2025-05-30 21:19:16"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a12c4e780b958457257ea","timestamp":"2025-05-30 21:19:16"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a12c4e780b958457257ea","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-30 21:19:17","variables":{"messageId":"683a12c4e780b958457257ea"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:19:17","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:19:17","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous completed in 564ms","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"POST / 200 - 567ms","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous completed in 253ms","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"POST / 200 - 257ms","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous completed in 505ms","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"POST / 200 - 509ms","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous completed in 507ms","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"POST / 200 - 510ms","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:19:22","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":3,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=3, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:19:22"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:19:22"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:19:22"}
{"level":"http","message":"GraphQL anonymous completed in 484ms","timestamp":"2025-05-30 21:19:22"}
{"level":"http","message":"POST / 200 - 489ms","timestamp":"2025-05-30 21:19:22"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:19:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:19:23"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:19:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:19:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:19:23"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:19:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:19:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:19:53"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:19:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:19:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:19:53"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:19:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:20:02","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":4,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=4, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:20:02"}
{"level":"info","message":"[MessageService] Retrieved 1 messages","timestamp":"2025-05-30 21:20:03"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:20:03"}
{"level":"http","message":"GraphQL anonymous completed in 586ms","timestamp":"2025-05-30 21:20:03"}
{"level":"http","message":"POST / 200 - 590ms","timestamp":"2025-05-30 21:20:03"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:20:12"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:20:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:20:13","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:20:13"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 21:20:13"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:20:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:20:14","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:20:14","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:20:14","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:20:14","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:20:14","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 405ms","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 377ms","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 518ms","timestamp":"2025-05-30 21:20:15"}
{"level":"http","message":"GraphQL anonymous completed in 922ms","timestamp":"2025-05-30 21:20:15"}
{"level":"http","message":"POST / 200 - 927ms","timestamp":"2025-05-30 21:20:15"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 1077ms","timestamp":"2025-05-30 21:20:15"}
{"level":"http","message":"GraphQL anonymous completed in 1504ms","timestamp":"2025-05-30 21:20:15"}
{"level":"http","message":"POST / 200 - 1512ms","timestamp":"2025-05-30 21:20:15"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:20:16"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:20:16"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:20:16"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:20:16"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:20:17"}
{"level":"http","message":"GraphQL anonymous completed in 3536ms","timestamp":"2025-05-30 21:20:17"}
{"level":"http","message":"POST / 200 - 3554ms","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:20:17"}
{"level":"http","message":"GraphQL anonymous completed in 3393ms","timestamp":"2025-05-30 21:20:17"}
{"level":"http","message":"POST / 200 - 3400ms","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:20:23","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:20:23"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:20:24"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:20:24"}
{"level":"http","message":"GraphQL anonymous completed in 377ms","timestamp":"2025-05-30 21:20:24"}
{"level":"http","message":"POST / 200 - 380ms","timestamp":"2025-05-30 21:20:24"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:20:28"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:20:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:20:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"POST / 200 - 53ms","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:20:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:20:29","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:20:29","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:20:29","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:20:29","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 3308ms","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 3324ms","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"GraphQL anonymous completed in 3582ms","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"POST / 200 - 3597ms","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 301ms","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 300ms","timestamp":"2025-05-30 21:20:33"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:20:33"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"GraphQL anonymous completed in 3753ms","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"POST / 200 - 3757ms","timestamp":"2025-05-30 21:20:33"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:20:33"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:20:33"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:20:34"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:20:34"}
{"level":"http","message":"GraphQL anonymous completed in 4785ms","timestamp":"2025-05-30 21:20:34"}
{"level":"http","message":"POST / 200 - 4794ms","timestamp":"2025-05-30 21:20:34"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:34"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:34"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:34"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:20:35"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:20:35"}
{"level":"http","message":"GraphQL anonymous completed in 5322ms","timestamp":"2025-05-30 21:20:35"}
{"level":"http","message":"POST / 200 - 5338ms","timestamp":"2025-05-30 21:20:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:20:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:20:59"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:20:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:20:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:20:59"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:20:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:21:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:21:29"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:21:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:21:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:21:29"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:21:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:21:43","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:21:43"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:21:44"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:21:44"}
{"level":"http","message":"GraphQL anonymous completed in 287ms","timestamp":"2025-05-30 21:21:44"}
{"level":"http","message":"POST / 200 - 290ms","timestamp":"2025-05-30 21:21:44"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:21:59"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:21:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:22:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:22:00"}
{"level":"http","message":"POST / 200 - 24ms","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:22:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:22:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:22:00"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:22:01","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:22:01","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:22:01","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:22:01","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 358ms","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 361ms","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GraphQL anonymous completed in 435ms","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"POST / 200 - 440ms","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 75ms","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GraphQL anonymous completed in 437ms","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"POST / 200 - 442ms","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 323ms","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:22:08"}
{"level":"http","message":"GraphQL anonymous completed in 7452ms","timestamp":"2025-05-30 21:22:08"}
{"level":"http","message":"POST / 200 - 7457ms","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:22:08"}
{"level":"http","message":"GraphQL anonymous completed in 7445ms","timestamp":"2025-05-30 21:22:08"}
{"level":"http","message":"POST / 200 - 7456ms","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:22:12","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:22:12"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:22:13"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:22:13"}
{"level":"http","message":"GraphQL anonymous completed in 994ms","timestamp":"2025-05-30 21:22:13"}
{"level":"http","message":"POST / 200 - 998ms","timestamp":"2025-05-30 21:22:13"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:22:17"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:22:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:22:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"POST / 200 - 28ms","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:22:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"POST / 200 - 20ms","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:22:18","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:22:18","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:22:18","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 214ms","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 104ms","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:22:18","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 255ms","timestamp":"2025-05-30 21:22:19"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 239ms","timestamp":"2025-05-30 21:22:19"}
{"level":"http","message":"GraphQL anonymous completed in 513ms","timestamp":"2025-05-30 21:22:19"}
{"level":"http","message":"POST / 200 - 517ms","timestamp":"2025-05-30 21:22:19"}
{"level":"http","message":"GraphQL anonymous completed in 682ms","timestamp":"2025-05-30 21:22:19"}
{"level":"http","message":"POST / 200 - 695ms","timestamp":"2025-05-30 21:22:19"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:22:19"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:22:19"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:22:19"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:22:19"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:22:20"}
{"level":"http","message":"GraphQL anonymous completed in 2186ms","timestamp":"2025-05-30 21:22:20"}
{"level":"http","message":"POST / 200 - 2208ms","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:22:20"}
{"level":"http","message":"GraphQL anonymous completed in 2116ms","timestamp":"2025-05-30 21:22:20"}
{"level":"http","message":"POST / 200 - 2126ms","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:22:24","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:22:24"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:22:24"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:22:24"}
{"level":"http","message":"GraphQL anonymous completed in 239ms","timestamp":"2025-05-30 21:22:24"}
{"level":"http","message":"POST / 200 - 242ms","timestamp":"2025-05-30 21:22:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:22:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:22:48"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:22:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:22:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:22:48"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 21:22:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:22:59","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":3,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=3, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:22:59"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:23:00"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:23:00"}
{"level":"http","message":"GraphQL anonymous completed in 258ms","timestamp":"2025-05-30 21:23:00"}
{"level":"http","message":"POST / 200 - 261ms","timestamp":"2025-05-30 21:23:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:23:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:23:18"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 21:23:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:23:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:23:18"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:23:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:23:24","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":4,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=4, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:23:24"}
{"level":"info","message":"[MessageService] Retrieved 1 messages","timestamp":"2025-05-30 21:23:25"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:23:25"}
{"level":"http","message":"GraphQL anonymous completed in 273ms","timestamp":"2025-05-30 21:23:25"}
{"level":"http","message":"POST / 200 - 276ms","timestamp":"2025-05-30 21:23:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:23:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:23:48"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:23:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:23:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:23:48"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:23:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:24:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:24:18"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 21:24:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:24:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:24:18"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:24:18"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:24:18"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:24:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:24:20","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:24:20","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:24:20","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:24:20","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:24:20","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:24:20","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2142ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2139ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 191ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"GraphQL anonymous completed in 2351ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"POST / 200 - 2356ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 237ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"GraphQL anonymous completed in 2336ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"POST / 200 - 2345ms","timestamp":"2025-05-30 21:24:22"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:24:23"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:24:23"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:24:23"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:24:23"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:24:24"}
{"level":"http","message":"GraphQL anonymous completed in 3610ms","timestamp":"2025-05-30 21:24:24"}
{"level":"http","message":"POST / 200 - 3616ms","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:24:24"}
{"level":"http","message":"GraphQL anonymous completed in 3717ms","timestamp":"2025-05-30 21:24:24"}
{"level":"http","message":"POST / 200 - 3722ms","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:24:36","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:24:36"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:24:37"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:24:37"}
{"level":"http","message":"GraphQL anonymous completed in 1324ms","timestamp":"2025-05-30 21:24:37"}
{"level":"http","message":"POST / 200 - 1326ms","timestamp":"2025-05-30 21:24:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:24:50","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:24:50"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:24:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:24:50","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:24:50"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 21:24:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:24:57","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":3,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=3, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:24:57"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:24:57"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:24:57"}
{"level":"http","message":"GraphQL anonymous completed in 266ms","timestamp":"2025-05-30 21:24:57"}
{"level":"http","message":"POST / 200 - 269ms","timestamp":"2025-05-30 21:24:57"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:25:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:25:08","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:25:08"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:25:08","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:25:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:25:08","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 110ms","timestamp":"2025-05-30 21:25:09"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 274ms","timestamp":"2025-05-30 21:25:09"}
{"level":"http","message":"GraphQL anonymous completed in 390ms","timestamp":"2025-05-30 21:25:09"}
{"level":"http","message":"POST / 200 - 394ms","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:25:09"}
{"level":"http","message":"GraphQL anonymous completed in 1004ms","timestamp":"2025-05-30 21:25:09"}
{"level":"http","message":"POST / 200 - 1007ms","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:25:13","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:25:13"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:25:13"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:25:13"}
{"level":"http","message":"GraphQL anonymous completed in 255ms","timestamp":"2025-05-30 21:25:13"}
{"level":"http","message":"POST / 200 - 260ms","timestamp":"2025-05-30 21:25:13"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:25:20","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:25:20"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:25:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:25:38","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:25:38"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 21:25:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:25:50","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:25:50"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:25:50"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:25:53"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:25:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:25:55","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:25:55","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"POST / 200 - 21ms","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:25:55","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:25:55","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:25:55","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:25:55","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2010ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2088ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"GraphQL anonymous completed in 2266ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"POST / 200 - 2276ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 287ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 288ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"GraphQL anonymous completed in 2554ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"POST / 200 - 2561ms","timestamp":"2025-05-30 21:25:57"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:25:57"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:25:57"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:25:57"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:25:57"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:26:00"}
{"level":"http","message":"GraphQL anonymous completed in 5004ms","timestamp":"2025-05-30 21:26:00"}
{"level":"http","message":"POST / 200 - 5010ms","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:26:00"}
{"level":"http","message":"GraphQL anonymous completed in 5057ms","timestamp":"2025-05-30 21:26:00"}
{"level":"http","message":"POST / 200 - 5078ms","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:26:07","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:26:07"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:26:07"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:26:07"}
{"level":"http","message":"GraphQL anonymous completed in 256ms","timestamp":"2025-05-30 21:26:07"}
{"level":"http","message":"POST / 200 - 259ms","timestamp":"2025-05-30 21:26:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:26:25","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:26:25"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:26:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:26:25","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:26:25"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 21:26:25"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:26:40"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:26:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:26:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:26:41"}
{"level":"http","message":"POST / 200 - 51ms","timestamp":"2025-05-30 21:26:41"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:26:41"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:41"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:41"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:41"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:26:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:26:41"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:26:42","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:26:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:26:42","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:26:42","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:26:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:26:42","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 3018ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2990ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 233ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 253ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"GraphQL anonymous completed in 3260ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"POST / 200 - 3274ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"GraphQL anonymous completed in 3322ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"POST / 200 - 3331ms","timestamp":"2025-05-30 21:26:45"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:26:45"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:26:45"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:26:45"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:26:45"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:26:47"}
{"level":"http","message":"GraphQL anonymous completed in 5179ms","timestamp":"2025-05-30 21:26:47"}
{"level":"http","message":"POST / 200 - 5184ms","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:26:47"}
{"level":"http","message":"GraphQL anonymous completed in 5159ms","timestamp":"2025-05-30 21:26:47"}
{"level":"http","message":"POST / 200 - 5164ms","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:26:58","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:26:58"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:26:58"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:26:58"}
{"level":"http","message":"GraphQL anonymous completed in 490ms","timestamp":"2025-05-30 21:26:58"}
{"level":"http","message":"POST / 200 - 495ms","timestamp":"2025-05-30 21:26:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:27:00","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":3,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=3, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:27:00"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:27:00"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:27:00"}
{"level":"http","message":"GraphQL anonymous completed in 466ms","timestamp":"2025-05-30 21:27:00"}
{"level":"http","message":"POST / 200 - 468ms","timestamp":"2025-05-30 21:27:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:27:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:27:11"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:27:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:27:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:27:11"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:27:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:27:26","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":4,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=4, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:27:26"}
{"level":"info","message":"[MessageService] Retrieved 1 messages","timestamp":"2025-05-30 21:27:26"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:27:26"}
{"level":"http","message":"GraphQL anonymous completed in 270ms","timestamp":"2025-05-30 21:27:26"}
{"level":"http","message":"POST / 200 - 274ms","timestamp":"2025-05-30 21:27:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:27:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:27:41"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 21:27:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:27:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:27:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:27:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:28:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:28:11"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:28:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:28:12","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:28:12"}
{"level":"http","message":"POST / 200 - 47ms","timestamp":"2025-05-30 21:28:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:28:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:28:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:28:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:28:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:28:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:28:41"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:28:52"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:28:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:28:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:28:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"POST / 200 - 20ms","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:28:53","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:28:53","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:28:53","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 195ms","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:28:53","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 183ms","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 133ms","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GraphQL anonymous completed in 2162ms","timestamp":"2025-05-30 21:28:55"}
{"level":"http","message":"POST / 200 - 2169ms","timestamp":"2025-05-30 21:28:55"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 1967ms","timestamp":"2025-05-30 21:28:55"}
{"level":"http","message":"GraphQL anonymous completed in 2113ms","timestamp":"2025-05-30 21:28:55"}
{"level":"http","message":"POST / 200 - 2121ms","timestamp":"2025-05-30 21:28:55"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:28:56"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:28:56"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:28:56"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:28:56"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:28:57"}
{"level":"http","message":"GraphQL anonymous completed in 3599ms","timestamp":"2025-05-30 21:28:57"}
{"level":"http","message":"POST / 200 - 3605ms","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:28:57"}
{"level":"http","message":"GraphQL anonymous completed in 3547ms","timestamp":"2025-05-30 21:28:57"}
{"level":"http","message":"POST / 200 - 3550ms","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:29:09","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:29:09"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:29:10"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:29:10"}
{"level":"http","message":"GraphQL anonymous completed in 1032ms","timestamp":"2025-05-30 21:29:10"}
{"level":"http","message":"POST / 200 - 1035ms","timestamp":"2025-05-30 21:29:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:29:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:29:23"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:29:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:29:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:29:23"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:29:23"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 21:35:41"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 21:35:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:36:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 26ms","timestamp":"2025-05-30 21:36:36"}
{"level":"http","message":"POST / 200 - 73ms","timestamp":"2025-05-30 21:36:36"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:36:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:36:36","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:36:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:36:36","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:36:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:36:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:36:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:36:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:36:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:36:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:36:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:36:36"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 274ms","timestamp":"2025-05-30 21:36:36"}
{"level":"http","message":"GraphQL anonymous completed in 375ms","timestamp":"2025-05-30 21:36:36"}
{"level":"http","message":"POST / 200 - 386ms","timestamp":"2025-05-30 21:36:36"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 133ms","timestamp":"2025-05-30 21:36:36"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:36:36"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:36:36"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:36:37"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:36:37"}
{"level":"http","message":"GraphQL anonymous completed in 1240ms","timestamp":"2025-05-30 21:36:37"}
{"level":"http","message":"POST / 200 - 1249ms","timestamp":"2025-05-30 21:36:37"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:36:37"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:36:37"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:36:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:36:44","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:36:44"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:36:44"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:36:44"}
{"level":"http","message":"GraphQL anonymous completed in 316ms","timestamp":"2025-05-30 21:36:44"}
{"level":"http","message":"POST / 200 - 320ms","timestamp":"2025-05-30 21:36:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:37:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:37:05"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:37:05"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:37:27"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:37:27","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:37:27"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 21:37:27"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:37:27"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:37:27"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:37:27"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:37:27"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:37:27"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:37:27","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:37:27"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:37:27","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:37:27"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:37:27"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:37:27"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:37:27"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 218ms","timestamp":"2025-05-30 21:37:28"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 79ms","timestamp":"2025-05-30 21:37:28"}
{"level":"http","message":"GraphQL anonymous completed in 326ms","timestamp":"2025-05-30 21:37:28"}
{"level":"http","message":"POST / 200 - 333ms","timestamp":"2025-05-30 21:37:28"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:37:28"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:37:28"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:37:28"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:37:28"}
{"level":"http","message":"GraphQL anonymous completed in 633ms","timestamp":"2025-05-30 21:37:28"}
{"level":"http","message":"POST / 200 - 637ms","timestamp":"2025-05-30 21:37:28"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:37:28"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:37:28"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:37:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:37:35","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:37:35"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:37:36"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:37:36"}
{"level":"http","message":"GraphQL anonymous completed in 286ms","timestamp":"2025-05-30 21:37:36"}
{"level":"http","message":"POST / 200 - 290ms","timestamp":"2025-05-30 21:37:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-30 21:37:45","variables":{"content":"salut","conversationId":"6839c3a5b3d00b25cf26afd1","receiverId":"6839c1fcb3d00b25cf26afa7","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, type=TEXT, hasMetadata=false","timestamp":"2025-05-30 21:37:45"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a17193e1f79a3208736c1","timestamp":"2025-05-30 21:37:45"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a17193e1f79a3208736c1","timestamp":"2025-05-30 21:37:46"}
{"level":"http","message":"GraphQL anonymous completed in 610ms","timestamp":"2025-05-30 21:37:46"}
{"level":"http","message":"POST / 200 - 614ms","timestamp":"2025-05-30 21:37:46"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:37:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:37:46","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:37:46"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 32","timestamp":"2025-05-30 21:37:46"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:37:46"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:37:46"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:37:46"}
{"level":"http","message":"GraphQL anonymous completed in 449ms","timestamp":"2025-05-30 21:37:46"}
{"level":"http","message":"POST / 200 - 453ms","timestamp":"2025-05-30 21:37:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:37:52","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:37:52"}
{"level":"http","message":"POST / 200 - 15ms","timestamp":"2025-05-30 21:37:52"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:37:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:37:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:37:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:37:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:37:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:37:52","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:37:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:37:52","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:37:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:37:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:37:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:37:52"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 270ms","timestamp":"2025-05-30 21:37:52"}
{"level":"http","message":"GraphQL anonymous completed in 534ms","timestamp":"2025-05-30 21:37:53"}
{"level":"http","message":"POST / 200 - 542ms","timestamp":"2025-05-30 21:37:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 331ms","timestamp":"2025-05-30 21:37:53"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 32","timestamp":"2025-05-30 21:37:53"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:37:53"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:37:53"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:37:53"}
{"level":"http","message":"GraphQL anonymous completed in 844ms","timestamp":"2025-05-30 21:37:53"}
{"level":"http","message":"POST / 200 - 849ms","timestamp":"2025-05-30 21:37:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:37:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:37:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:37:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:37:57","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:37:57"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:37:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:38:22","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:38:22"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 21:38:22"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:38:27","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:38:27"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 21:38:27"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:38:52","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":3,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=3, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:38:52"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:38:52"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:38:52"}
{"level":"http","message":"GraphQL anonymous completed in 283ms","timestamp":"2025-05-30 21:38:52"}
{"level":"http","message":"POST / 200 - 289ms","timestamp":"2025-05-30 21:38:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:38:52","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:38:52"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:38:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:38:57","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:38:57"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:38:57"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:39:08"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:39:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:39:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:39:09"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 21:39:09"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:39:09"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:39:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:39:10"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 21:39:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:39:10","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:39:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:39:10","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:39:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:39:10","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:39:10","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:39:10"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 152ms","timestamp":"2025-05-30 21:39:10"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 145ms","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:10"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 245ms","timestamp":"2025-05-30 21:39:10"}
{"level":"http","message":"GraphQL anonymous completed in 427ms","timestamp":"2025-05-30 21:39:10"}
{"level":"http","message":"POST / 200 - 435ms","timestamp":"2025-05-30 21:39:10"}
{"level":"http","message":"GraphQL anonymous completed in 464ms","timestamp":"2025-05-30 21:39:10"}
{"level":"http","message":"POST / 200 - 469ms","timestamp":"2025-05-30 21:39:10"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 356ms","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 32","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:39:10"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 32","timestamp":"2025-05-30 21:39:11"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:39:11"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:39:12"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:39:12"}
{"level":"http","message":"GraphQL anonymous completed in 1838ms","timestamp":"2025-05-30 21:39:12"}
{"level":"http","message":"POST / 200 - 1847ms","timestamp":"2025-05-30 21:39:12"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:12"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:12"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:12"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:39:12"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:39:12"}
{"level":"http","message":"GraphQL anonymous completed in 1850ms","timestamp":"2025-05-30 21:39:12"}
{"level":"http","message":"POST / 200 - 1859ms","timestamp":"2025-05-30 21:39:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:39:14","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:39:14"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:39:15"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:39:15"}
{"level":"http","message":"GraphQL anonymous completed in 297ms","timestamp":"2025-05-30 21:39:15"}
{"level":"http","message":"POST / 200 - 302ms","timestamp":"2025-05-30 21:39:15"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:39:27"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:39:27"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:39:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:39:28"}
{"level":"http","message":"POST / 200 - 42ms","timestamp":"2025-05-30 21:39:28"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:39:28"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:28"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:28"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:28"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:39:29","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:39:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:39:29","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:39:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 5ms","timestamp":"2025-05-30 21:39:29"}
{"level":"http","message":"POST / 200 - 22ms","timestamp":"2025-05-30 21:39:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:39:29","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:39:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:39:29","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:39:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:29"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 1884ms","timestamp":"2025-05-30 21:39:30"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 1287ms","timestamp":"2025-05-30 21:39:30"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 269ms","timestamp":"2025-05-30 21:39:31"}
{"level":"http","message":"GraphQL anonymous completed in 1743ms","timestamp":"2025-05-30 21:39:31"}
{"level":"http","message":"POST / 200 - 1750ms","timestamp":"2025-05-30 21:39:31"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 309ms","timestamp":"2025-05-30 21:39:31"}
{"level":"http","message":"GraphQL anonymous completed in 1802ms","timestamp":"2025-05-30 21:39:31"}
{"level":"http","message":"POST / 200 - 1814ms","timestamp":"2025-05-30 21:39:31"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 32","timestamp":"2025-05-30 21:39:31"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:39:31"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 32","timestamp":"2025-05-30 21:39:32"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:39:32"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:39:37"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:39:37"}
{"level":"http","message":"GraphQL anonymous completed in 8895ms","timestamp":"2025-05-30 21:39:37"}
{"level":"http","message":"POST / 200 - 8953ms","timestamp":"2025-05-30 21:39:37"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:39:37"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:39:37"}
{"level":"http","message":"GraphQL anonymous completed in 8429ms","timestamp":"2025-05-30 21:39:37"}
{"level":"http","message":"POST / 200 - 8441ms","timestamp":"2025-05-30 21:39:37"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:37"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:37"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:39:37"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:37"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:37"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:39:45","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:39:45"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:39:46"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:39:46"}
{"level":"http","message":"GraphQL anonymous completed in 1101ms","timestamp":"2025-05-30 21:39:46"}
{"level":"http","message":"POST / 200 - 1104ms","timestamp":"2025-05-30 21:39:46"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:39:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:39:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:39:53"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:39:53"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:39:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:39:53","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:39:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:39:53","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 72ms","timestamp":"2025-05-30 21:39:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 53ms","timestamp":"2025-05-30 21:39:53"}
{"level":"http","message":"GraphQL anonymous completed in 162ms","timestamp":"2025-05-30 21:39:53"}
{"level":"http","message":"POST / 200 - 167ms","timestamp":"2025-05-30 21:39:53"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 32","timestamp":"2025-05-30 21:39:53"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:39:53"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:39:54"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:39:54"}
{"level":"http","message":"GraphQL anonymous completed in 464ms","timestamp":"2025-05-30 21:39:54"}
{"level":"http","message":"POST / 200 - 467ms","timestamp":"2025-05-30 21:39:54"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:54"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:54"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:39:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:39:58","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:39:58"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 21:39:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:40:06","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839bf2db3d00b25cf26af7e","senderId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839c1fcb3d00b25cf26afa7, receiverId=6839bf2db3d00b25cf26af7e, page=2, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=undefined","timestamp":"2025-05-30 21:40:06"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:40:06"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:40:06"}
{"level":"http","message":"GraphQL anonymous completed in 470ms","timestamp":"2025-05-30 21:40:06"}
{"level":"http","message":"POST / 200 - 475ms","timestamp":"2025-05-30 21:40:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-30 21:40:19","variables":{"content":"dd","conversationId":"6839c3a5b3d00b25cf26afd1","receiverId":"6839bf2db3d00b25cf26af7e","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839c1fcb3d00b25cf26afa7, receiverId=6839bf2db3d00b25cf26af7e, type=TEXT, hasMetadata=false","timestamp":"2025-05-30 21:40:19"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a17b43e1f79a32087373b","timestamp":"2025-05-30 21:40:20"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a17b43e1f79a32087373b","timestamp":"2025-05-30 21:40:20"}
{"level":"http","message":"GraphQL anonymous completed in 558ms","timestamp":"2025-05-30 21:40:20"}
{"level":"http","message":"POST / 200 - 563ms","timestamp":"2025-05-30 21:40:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:40:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-30 21:40:20","variables":{"messageId":"683a17b43e1f79a32087373b"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:40:20","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:40:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:40:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:40:20","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:40:20"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 33","timestamp":"2025-05-30 21:40:20"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:40:20"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 33","timestamp":"2025-05-30 21:40:20"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:40:20"}
{"level":"http","message":"GraphQL anonymous completed in 253ms","timestamp":"2025-05-30 21:40:20"}
{"level":"http","message":"POST / 200 - 257ms","timestamp":"2025-05-30 21:40:20"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:40:20"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:40:20"}
{"level":"http","message":"GraphQL anonymous completed in 433ms","timestamp":"2025-05-30 21:40:20"}
{"level":"http","message":"POST / 200 - 438ms","timestamp":"2025-05-30 21:40:20"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:40:21"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:40:21"}
{"level":"http","message":"GraphQL anonymous completed in 453ms","timestamp":"2025-05-30 21:40:21"}
{"level":"http","message":"POST / 200 - 458ms","timestamp":"2025-05-30 21:40:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:40:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:40:23"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:40:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:40:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:40:28"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:40:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:40:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:40:53"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:40:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:40:58","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:40:58"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 21:40:58"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:41:07"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:41:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:41:08","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:41:08"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 21:41:08"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:41:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:41:08","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:41:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:41:08","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:41:08","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:41:08"}
{"level":"http","message":"POST / 200 - 20ms","timestamp":"2025-05-30 21:41:08"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:41:08"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:08"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:08"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:08"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:41:09","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:41:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:41:09","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:09"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:09"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:09"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:09"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 687ms","timestamp":"2025-05-30 21:41:09"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 951ms","timestamp":"2025-05-30 21:41:09"}
{"level":"http","message":"GraphQL anonymous completed in 1199ms","timestamp":"2025-05-30 21:41:09"}
{"level":"http","message":"POST / 200 - 1208ms","timestamp":"2025-05-30 21:41:09"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 321ms","timestamp":"2025-05-30 21:41:10"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 297ms","timestamp":"2025-05-30 21:41:10"}
{"level":"http","message":"GraphQL anonymous completed in 967ms","timestamp":"2025-05-30 21:41:10"}
{"level":"http","message":"POST / 200 - 975ms","timestamp":"2025-05-30 21:41:10"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 33","timestamp":"2025-05-30 21:41:10"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:41:10"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 33","timestamp":"2025-05-30 21:41:10"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:41:10"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:41:11"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:41:11"}
{"level":"http","message":"GraphQL anonymous completed in 2346ms","timestamp":"2025-05-30 21:41:11"}
{"level":"http","message":"POST / 200 - 2351ms","timestamp":"2025-05-30 21:41:11"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:41:11"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:41:11"}
{"level":"http","message":"GraphQL anonymous completed in 2638ms","timestamp":"2025-05-30 21:41:11"}
{"level":"http","message":"POST / 200 - 2643ms","timestamp":"2025-05-30 21:41:11"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:11"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:11"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:41:18","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:41:18"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:41:19"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:41:19"}
{"level":"http","message":"GraphQL anonymous completed in 268ms","timestamp":"2025-05-30 21:41:19"}
{"level":"http","message":"POST / 200 - 270ms","timestamp":"2025-05-30 21:41:19"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:41:19"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:41:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:41:21","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:41:21"}
{"level":"http","message":"POST / 200 - 78ms","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:41:21","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:41:21"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:41:21","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 159ms","timestamp":"2025-05-30 21:41:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:41:21","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:41:21","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:41:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:41:21","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:21"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 175ms","timestamp":"2025-05-30 21:41:21"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 200ms","timestamp":"2025-05-30 21:41:21"}
{"level":"http","message":"GraphQL anonymous completed in 401ms","timestamp":"2025-05-30 21:41:22"}
{"level":"http","message":"POST / 200 - 406ms","timestamp":"2025-05-30 21:41:22"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 163ms","timestamp":"2025-05-30 21:41:22"}
{"level":"http","message":"GraphQL anonymous completed in 415ms","timestamp":"2025-05-30 21:41:22"}
{"level":"http","message":"POST / 200 - 425ms","timestamp":"2025-05-30 21:41:22"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 33","timestamp":"2025-05-30 21:41:22"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:41:22"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 33","timestamp":"2025-05-30 21:41:22"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:41:22"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:41:24"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:41:24"}
{"level":"http","message":"GraphQL anonymous completed in 3268ms","timestamp":"2025-05-30 21:41:24"}
{"level":"http","message":"POST / 200 - 3296ms","timestamp":"2025-05-30 21:41:24"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:41:24"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:41:24"}
{"level":"http","message":"GraphQL anonymous completed in 3200ms","timestamp":"2025-05-30 21:41:24"}
{"level":"http","message":"POST / 200 - 3216ms","timestamp":"2025-05-30 21:41:24"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:24"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:24"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:24"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:24"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:24"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:24"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:41:36"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:41:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:41:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:41:37"}
{"level":"http","message":"POST / 200 - 30ms","timestamp":"2025-05-30 21:41:37"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:41:37"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:41:38","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:41:38","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 156ms","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:41:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:41:38","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:41:38"}
{"level":"http","message":"POST / 200 - 33ms","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 250ms","timestamp":"2025-05-30 21:41:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:41:38","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:41:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:41:38","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:38"}
{"level":"http","message":"GraphQL anonymous completed in 485ms","timestamp":"2025-05-30 21:41:38"}
{"level":"http","message":"POST / 200 - 492ms","timestamp":"2025-05-30 21:41:38"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 203ms","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 33","timestamp":"2025-05-30 21:41:38"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:41:38"}
{"level":"http","message":"GraphQL anonymous completed in 388ms","timestamp":"2025-05-30 21:41:39"}
{"level":"http","message":"POST / 200 - 395ms","timestamp":"2025-05-30 21:41:39"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 204ms","timestamp":"2025-05-30 21:41:39"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 33","timestamp":"2025-05-30 21:41:41"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:41:41"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:41:42"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:41:42"}
{"level":"http","message":"GraphQL anonymous completed in 4034ms","timestamp":"2025-05-30 21:41:42"}
{"level":"http","message":"POST / 200 - 4050ms","timestamp":"2025-05-30 21:41:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:41:42"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:41:42"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:41:42"}
{"level":"http","message":"GraphQL anonymous completed in 3991ms","timestamp":"2025-05-30 21:41:42"}
{"level":"http","message":"POST / 200 - 3995ms","timestamp":"2025-05-30 21:41:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:41:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:42:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:42:07"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 21:42:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:42:08","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:42:08"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 21:42:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:42:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:42:37"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 21:42:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:42:38","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:42:38"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 21:42:38"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:42:44"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:42:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:42:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:42:46"}
{"level":"http","message":"POST / 200 - 42ms","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:42:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:42:46"}
{"level":"http","message":"POST / 200 - 17ms","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:42:46","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:42:46","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:42:46","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:42:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:42:46","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:42:46"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2698ms","timestamp":"2025-05-30 21:42:48"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2475ms","timestamp":"2025-05-30 21:42:48"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 228ms","timestamp":"2025-05-30 21:42:49"}
{"level":"http","message":"GraphQL anonymous completed in 2762ms","timestamp":"2025-05-30 21:42:49"}
{"level":"http","message":"POST / 200 - 2790ms","timestamp":"2025-05-30 21:42:49"}
{"level":"http","message":"GraphQL anonymous completed in 2733ms","timestamp":"2025-05-30 21:42:49"}
{"level":"http","message":"POST / 200 - 2740ms","timestamp":"2025-05-30 21:42:49"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 305ms","timestamp":"2025-05-30 21:42:49"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 33","timestamp":"2025-05-30 21:42:49"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:42:49"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 33","timestamp":"2025-05-30 21:42:49"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:42:49"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:42:50"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:42:50"}
{"level":"http","message":"GraphQL anonymous completed in 4633ms","timestamp":"2025-05-30 21:42:50"}
{"level":"http","message":"POST / 200 - 4645ms","timestamp":"2025-05-30 21:42:50"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:42:50"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:42:50"}
{"level":"http","message":"GraphQL anonymous completed in 4457ms","timestamp":"2025-05-30 21:42:50"}
{"level":"http","message":"POST / 200 - 4463ms","timestamp":"2025-05-30 21:42:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:42:51"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:42:51"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:42:51"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:42:51"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:42:51"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:42:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:42:57","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:42:57"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:42:58"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:42:58"}
{"level":"http","message":"GraphQL anonymous completed in 445ms","timestamp":"2025-05-30 21:42:58"}
{"level":"http","message":"POST / 200 - 449ms","timestamp":"2025-05-30 21:42:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:42:59","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":3,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=3, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:42:59"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:42:59"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:42:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:43:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:43:01"}
{"level":"http","message":"POST / 200 - 16ms","timestamp":"2025-05-30 21:43:01"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:43:01"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:01"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:43:02","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:43:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:43:02","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:43:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:43:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:43:02"}
{"level":"http","message":"POST / 200 - 89ms","timestamp":"2025-05-30 21:43:02"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:43:02","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:43:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:43:02","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:02"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 1143ms","timestamp":"2025-05-30 21:43:03"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 1302ms","timestamp":"2025-05-30 21:43:03"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 305ms","timestamp":"2025-05-30 21:43:03"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:43:03"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:43:03"}
{"level":"http","message":"GraphQL anonymous completed in 4191ms","timestamp":"2025-05-30 21:43:03"}
{"level":"http","message":"GraphQL anonymous completed in 1440ms","timestamp":"2025-05-30 21:43:03"}
{"level":"http","message":"POST / 200 - 1446ms","timestamp":"2025-05-30 21:43:03"}
{"level":"http","message":"GraphQL anonymous completed in 1708ms","timestamp":"2025-05-30 21:43:03"}
{"level":"http","message":"POST / 200 - 1713ms","timestamp":"2025-05-30 21:43:03"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 33","timestamp":"2025-05-30 21:43:03"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:43:03"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 3639ms","timestamp":"2025-05-30 21:43:07"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 33","timestamp":"2025-05-30 21:43:07"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:43:07"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:43:07"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:43:07"}
{"level":"http","message":"GraphQL anonymous completed in 5428ms","timestamp":"2025-05-30 21:43:07"}
{"level":"http","message":"POST / 200 - 5437ms","timestamp":"2025-05-30 21:43:07"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:43:07"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:43:07"}
{"level":"http","message":"GraphQL anonymous completed in 5223ms","timestamp":"2025-05-30 21:43:07"}
{"level":"http","message":"POST / 200 - 5228ms","timestamp":"2025-05-30 21:43:07"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:07"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:07"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:07"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:07"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:07"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:43:31","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:43:31"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:43:31"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:43:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:43:32"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:43:32"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:43:39"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:43:39"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:43:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:43:40"}
{"level":"http","message":"POST / 200 - 52ms","timestamp":"2025-05-30 21:43:40"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:43:40"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:40"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:40"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:40"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:43:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:43:40"}
{"level":"http","message":"POST / 200 - 27ms","timestamp":"2025-05-30 21:43:40"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:43:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:43:40","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:43:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:43:40","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:40"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:40"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:40"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:40"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:40"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:40"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:40"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:40"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 245ms","timestamp":"2025-05-30 21:43:41"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:41"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:41"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:41"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:43:41","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:43:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:43:41","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 313ms","timestamp":"2025-05-30 21:43:41"}
{"level":"http","message":"POST / 200 - 321ms","timestamp":"2025-05-30 21:43:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 174ms","timestamp":"2025-05-30 21:43:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 260ms","timestamp":"2025-05-30 21:43:41"}
{"level":"http","message":"GraphQL anonymous completed in 1771ms","timestamp":"2025-05-30 21:43:42"}
{"level":"http","message":"POST / 200 - 1776ms","timestamp":"2025-05-30 21:43:42"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 1538ms","timestamp":"2025-05-30 21:43:42"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 33","timestamp":"2025-05-30 21:43:42"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:43:42"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 33","timestamp":"2025-05-30 21:43:43"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:43:43"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:43:51"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:43:51"}
{"level":"http","message":"GraphQL anonymous completed in 10174ms","timestamp":"2025-05-30 21:43:51"}
{"level":"http","message":"POST / 200 - 10220ms","timestamp":"2025-05-30 21:43:51"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:51"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:51"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:51"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:43:51"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:43:51"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:43:51"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:43:51"}
{"level":"http","message":"GraphQL anonymous completed in 10151ms","timestamp":"2025-05-30 21:43:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:43:52","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:43:52"}
{"level":"http","message":"POST / 200 - 15ms","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:43:52","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:43:52"}
{"level":"http","message":"POST / 200 - 16ms","timestamp":"2025-05-30 21:43:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:43:52","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:43:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:43:52","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 149ms","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 56ms","timestamp":"2025-05-30 21:43:52"}
{"level":"http","message":"GraphQL anonymous completed in 203ms","timestamp":"2025-05-30 21:43:52"}
{"level":"http","message":"POST / 200 - 216ms","timestamp":"2025-05-30 21:43:52"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 129ms","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 33","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:43:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:43:52","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:43:52","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 281ms","timestamp":"2025-05-30 21:43:53"}
{"level":"http","message":"GraphQL anonymous completed in 1723ms","timestamp":"2025-05-30 21:43:54"}
{"level":"http","message":"POST / 200 - 1728ms","timestamp":"2025-05-30 21:43:54"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 33","timestamp":"2025-05-30 21:43:54"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:43:54"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:43:54"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:43:54"}
{"level":"http","message":"GraphQL anonymous completed in 2380ms","timestamp":"2025-05-30 21:43:54"}
{"level":"http","message":"POST / 200 - 2395ms","timestamp":"2025-05-30 21:43:54"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:54"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:54"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:43:54"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:43:55"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:43:55"}
{"level":"http","message":"GraphQL anonymous completed in 2669ms","timestamp":"2025-05-30 21:43:55"}
{"level":"http","message":"POST / 200 - 2674ms","timestamp":"2025-05-30 21:43:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:43:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:44:22","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:44:22"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:44:22"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:44:22","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:44:22"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:44:22"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:44:47"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:44:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:44:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:44:48"}
{"level":"http","message":"POST / 200 - 25ms","timestamp":"2025-05-30 21:44:48"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:44:48"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:44:48"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:44:48"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:44:48"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:44:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:44:49","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:44:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:44:49","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:44:49"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:44:49"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:44:49"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:44:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:44:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:44:49"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 21:44:49"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:44:49"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:44:49"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:44:49"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:44:49"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:44:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:44:49","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:44:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:44:49","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 234ms","timestamp":"2025-05-30 21:44:49"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:44:49"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:44:49"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:44:49"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:44:49"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 159ms","timestamp":"2025-05-30 21:44:49"}
{"level":"http","message":"GraphQL anonymous completed in 1821ms","timestamp":"2025-05-30 21:44:51"}
{"level":"http","message":"POST / 200 - 1827ms","timestamp":"2025-05-30 21:44:51"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2867ms","timestamp":"2025-05-30 21:44:52"}
{"level":"http","message":"GraphQL anonymous completed in 3094ms","timestamp":"2025-05-30 21:44:52"}
{"level":"http","message":"POST / 200 - 3106ms","timestamp":"2025-05-30 21:44:52"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2819ms","timestamp":"2025-05-30 21:44:52"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 33","timestamp":"2025-05-30 21:44:52"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:44:52"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 33","timestamp":"2025-05-30 21:44:52"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:44:52"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:44:53"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:44:53"}
{"level":"http","message":"GraphQL anonymous completed in 4465ms","timestamp":"2025-05-30 21:44:53"}
{"level":"http","message":"POST / 200 - 4468ms","timestamp":"2025-05-30 21:44:53"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:44:53"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:44:53"}
{"level":"http","message":"GraphQL anonymous completed in 4290ms","timestamp":"2025-05-30 21:44:53"}
{"level":"http","message":"POST / 200 - 4294ms","timestamp":"2025-05-30 21:44:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:44:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:44:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:44:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:44:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:44:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:44:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:44:57","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:44:57"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:44:58"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:44:58"}
{"level":"http","message":"GraphQL anonymous completed in 871ms","timestamp":"2025-05-30 21:44:58"}
{"level":"http","message":"POST / 200 - 875ms","timestamp":"2025-05-30 21:44:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:45:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:45:18"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:45:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:45:19","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:45:19"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:45:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:45:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:45:48"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:45:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:45:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:45:49"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:45:49"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:46:11"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:46:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:46:12","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:46:12"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:46:12","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:46:12"}
{"level":"http","message":"POST / 200 - 51ms","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:46:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:46:12","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:46:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:46:12","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:46:12","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:46:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:46:12","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:12"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 198ms","timestamp":"2025-05-30 21:46:12"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 200ms","timestamp":"2025-05-30 21:46:12"}
{"level":"http","message":"GraphQL anonymous completed in 348ms","timestamp":"2025-05-30 21:46:12"}
{"level":"http","message":"POST / 200 - 358ms","timestamp":"2025-05-30 21:46:12"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 258ms","timestamp":"2025-05-30 21:46:13"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 265ms","timestamp":"2025-05-30 21:46:13"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 33","timestamp":"2025-05-30 21:46:13"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:46:13"}
{"level":"http","message":"GraphQL anonymous completed in 475ms","timestamp":"2025-05-30 21:46:13"}
{"level":"http","message":"POST / 200 - 480ms","timestamp":"2025-05-30 21:46:13"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 33","timestamp":"2025-05-30 21:46:13"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:46:13"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:46:17"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:46:17"}
{"level":"http","message":"GraphQL anonymous completed in 4737ms","timestamp":"2025-05-30 21:46:17"}
{"level":"http","message":"POST / 200 - 4755ms","timestamp":"2025-05-30 21:46:17"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:46:17"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:46:17"}
{"level":"http","message":"GraphQL anonymous completed in 4886ms","timestamp":"2025-05-30 21:46:17"}
{"level":"http","message":"POST / 200 - 4895ms","timestamp":"2025-05-30 21:46:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:46:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:46:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:46:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:46:22","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:46:22"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:46:22"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:46:22"}
{"level":"http","message":"GraphQL anonymous completed in 346ms","timestamp":"2025-05-30 21:46:22"}
{"level":"http","message":"POST / 200 - 349ms","timestamp":"2025-05-30 21:46:22"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:46:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:46:38","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:46:38"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:46:38","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:46:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:46:38","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:38"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 284ms","timestamp":"2025-05-30 21:46:38"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 57ms","timestamp":"2025-05-30 21:46:38"}
{"level":"http","message":"GraphQL anonymous completed in 330ms","timestamp":"2025-05-30 21:46:38"}
{"level":"http","message":"POST / 200 - 336ms","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 33","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:46:38"}
{"level":"http","message":"GraphQL anonymous completed in 680ms","timestamp":"2025-05-30 21:46:38"}
{"level":"http","message":"POST / 200 - 685ms","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:38"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:46:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:46:42","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:46:42"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:46:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:46:44","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:46:44"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:46:44"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:46:44"}
{"level":"http","message":"GraphQL anonymous completed in 258ms","timestamp":"2025-05-30 21:46:44"}
{"level":"http","message":"POST / 200 - 262ms","timestamp":"2025-05-30 21:46:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-30 21:47:01","variables":{"content":"😇","conversationId":"6839c3a5b3d00b25cf26afd1","receiverId":"6839c1fcb3d00b25cf26afa7","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, type=TEXT, hasMetadata=false","timestamp":"2025-05-30 21:47:01"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a19463e1f79a320873876","timestamp":"2025-05-30 21:47:02"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a19463e1f79a320873876","timestamp":"2025-05-30 21:47:02"}
{"level":"http","message":"GraphQL anonymous completed in 607ms","timestamp":"2025-05-30 21:47:02"}
{"level":"http","message":"POST / 200 - 611ms","timestamp":"2025-05-30 21:47:02"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:47:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-30 21:47:02","variables":{"messageId":"683a19463e1f79a320873876"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:47:02","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:47:02"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:47:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:47:02","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:47:02"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 3, messages: 34","timestamp":"2025-05-30 21:47:02"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:47:02"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 34","timestamp":"2025-05-30 21:47:02"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:47:02"}
{"level":"http","message":"GraphQL anonymous completed in 250ms","timestamp":"2025-05-30 21:47:02"}
{"level":"http","message":"POST / 200 - 255ms","timestamp":"2025-05-30 21:47:02"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:47:03"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:47:03"}
{"level":"http","message":"GraphQL anonymous completed in 857ms","timestamp":"2025-05-30 21:47:03"}
{"level":"http","message":"POST / 200 - 860ms","timestamp":"2025-05-30 21:47:03"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:47:03"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:47:03"}
{"level":"http","message":"GraphQL anonymous completed in 876ms","timestamp":"2025-05-30 21:47:03"}
{"level":"http","message":"POST / 200 - 880ms","timestamp":"2025-05-30 21:47:03"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:47:08","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:47:08"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:47:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:47:08","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":3,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=3, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:47:08"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:47:08"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:47:08"}
{"level":"http","message":"GraphQL anonymous completed in 280ms","timestamp":"2025-05-30 21:47:08"}
{"level":"http","message":"POST / 200 - 284ms","timestamp":"2025-05-30 21:47:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:47:12","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:47:12"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:47:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:47:19","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":4,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=4, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:47:19"}
{"level":"info","message":"[MessageService] Retrieved 4 messages","timestamp":"2025-05-30 21:47:19"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:47:19"}
{"level":"http","message":"GraphQL anonymous completed in 235ms","timestamp":"2025-05-30 21:47:19"}
{"level":"http","message":"POST / 200 - 238ms","timestamp":"2025-05-30 21:47:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:47:38","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:47:38"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:47:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:47:42","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:47:42"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:47:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:48:08","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:48:08"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:48:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:48:12","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:48:12"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:48:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:48:38","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:48:38"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 21:48:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:48:42","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:48:42"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:48:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:49:08","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:49:08"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:49:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:49:12","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:49:12"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:49:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-30 21:49:18","variables":{"content":"😇","conversationId":"6839c3a5b3d00b25cf26afd1","receiverId":"6839c1fcb3d00b25cf26afa7","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, type=TEXT, hasMetadata=false","timestamp":"2025-05-30 21:49:18"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a19ce3e1f79a3208738a6","timestamp":"2025-05-30 21:49:18"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a19ce3e1f79a3208738a6","timestamp":"2025-05-30 21:49:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-30 21:49:19","variables":{"messageId":"683a19ce3e1f79a3208738a6"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:49:19","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:49:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:49:19","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:49:19"}
{"level":"http","message":"GraphQL anonymous completed in 867ms","timestamp":"2025-05-30 21:49:19"}
{"level":"http","message":"POST / 200 - 869ms","timestamp":"2025-05-30 21:49:19"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 35","timestamp":"2025-05-30 21:49:19"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:49:19"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 35","timestamp":"2025-05-30 21:49:19"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:49:19"}
{"level":"http","message":"GraphQL anonymous completed in 465ms","timestamp":"2025-05-30 21:49:19"}
{"level":"http","message":"POST / 200 - 470ms","timestamp":"2025-05-30 21:49:19"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:49:19"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:49:19"}
{"level":"http","message":"GraphQL anonymous completed in 672ms","timestamp":"2025-05-30 21:49:19"}
{"level":"http","message":"POST / 200 - 677ms","timestamp":"2025-05-30 21:49:19"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:49:19"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:49:19"}
{"level":"http","message":"GraphQL anonymous completed in 670ms","timestamp":"2025-05-30 21:49:19"}
{"level":"http","message":"POST / 200 - 674ms","timestamp":"2025-05-30 21:49:19"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:49:19"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:49:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:49:21","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":5,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=5, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:49:21"}
{"level":"info","message":"[MessageService] Retrieved 0 messages","timestamp":"2025-05-30 21:49:21"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:49:21"}
{"level":"http","message":"GraphQL anonymous completed in 196ms","timestamp":"2025-05-30 21:49:21"}
{"level":"http","message":"POST / 200 - 199ms","timestamp":"2025-05-30 21:49:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:49:38","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:49:38"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:49:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:49:42","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:49:42"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 21:49:42"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:49:42"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:49:43"}
