{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:18:36","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:18:36"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:18:36"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:18:36"}
{"level":"http","message":"GraphQL anonymous completed in 271ms","timestamp":"2025-05-30 21:18:36"}
{"level":"http","message":"POST / 200 - 276ms","timestamp":"2025-05-30 21:18:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:18:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:18:53"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:18:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:18:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:18:53"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:18:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-30 21:18:54","variables":{"content":"perfect all clear","conversationId":"6839c3a5b3d00b25cf26afd1","receiverId":"6839c1fcb3d00b25cf26afa7","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, type=TEXT, hasMetadata=false","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a12aee780b958457257c4","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a12aee780b958457257c4","timestamp":"2025-05-30 21:18:54"}
{"level":"http","message":"GraphQL anonymous completed in 623ms","timestamp":"2025-05-30 21:18:54"}
{"level":"http","message":"POST / 200 - 638ms","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:18:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-30 21:18:54","variables":{"messageId":"683a12aee780b958457257c4"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:18:54","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:18:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:18:54","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 2, messages: 30","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 30","timestamp":"2025-05-30 21:18:54"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:18:54"}
{"level":"http","message":"GraphQL anonymous completed in 260ms","timestamp":"2025-05-30 21:18:55"}
{"level":"http","message":"POST / 200 - 266ms","timestamp":"2025-05-30 21:18:55"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:18:55"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:18:55"}
{"level":"http","message":"GraphQL anonymous completed in 432ms","timestamp":"2025-05-30 21:18:55"}
{"level":"http","message":"POST / 200 - 437ms","timestamp":"2025-05-30 21:18:55"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:18:55"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:18:55"}
{"level":"http","message":"GraphQL anonymous completed in 445ms","timestamp":"2025-05-30 21:18:55"}
{"level":"http","message":"POST / 200 - 449ms","timestamp":"2025-05-30 21:18:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-30 21:19:16","variables":{"content":"but write insite espace tchat not clear","conversationId":"6839c3a5b3d00b25cf26afd1","receiverId":"6839c1fcb3d00b25cf26afa7","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, type=TEXT, hasMetadata=false","timestamp":"2025-05-30 21:19:16"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a12c4e780b958457257ea","timestamp":"2025-05-30 21:19:16"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a12c4e780b958457257ea","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-30 21:19:17","variables":{"messageId":"683a12c4e780b958457257ea"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:19:17","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:19:17","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous completed in 564ms","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"POST / 200 - 567ms","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous completed in 253ms","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"POST / 200 - 257ms","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous completed in 505ms","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"POST / 200 - 509ms","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous completed in 507ms","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"POST / 200 - 510ms","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:19:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:19:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:19:22","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":3,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=3, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:19:22"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:19:22"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:19:22"}
{"level":"http","message":"GraphQL anonymous completed in 484ms","timestamp":"2025-05-30 21:19:22"}
{"level":"http","message":"POST / 200 - 489ms","timestamp":"2025-05-30 21:19:22"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:19:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:19:23"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:19:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:19:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:19:23"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:19:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:19:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:19:53"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:19:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:19:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:19:53"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:19:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:20:02","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":4,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=4, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:20:02"}
{"level":"info","message":"[MessageService] Retrieved 1 messages","timestamp":"2025-05-30 21:20:03"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:20:03"}
{"level":"http","message":"GraphQL anonymous completed in 586ms","timestamp":"2025-05-30 21:20:03"}
{"level":"http","message":"POST / 200 - 590ms","timestamp":"2025-05-30 21:20:03"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:20:12"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:20:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:20:13","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:20:13"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 21:20:13"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:20:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:20:14","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:20:14","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:20:14","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:20:14","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:20:14","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 405ms","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 377ms","timestamp":"2025-05-30 21:20:14"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 518ms","timestamp":"2025-05-30 21:20:15"}
{"level":"http","message":"GraphQL anonymous completed in 922ms","timestamp":"2025-05-30 21:20:15"}
{"level":"http","message":"POST / 200 - 927ms","timestamp":"2025-05-30 21:20:15"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 1077ms","timestamp":"2025-05-30 21:20:15"}
{"level":"http","message":"GraphQL anonymous completed in 1504ms","timestamp":"2025-05-30 21:20:15"}
{"level":"http","message":"POST / 200 - 1512ms","timestamp":"2025-05-30 21:20:15"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:20:16"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:20:16"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:20:16"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:20:16"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:20:17"}
{"level":"http","message":"GraphQL anonymous completed in 3536ms","timestamp":"2025-05-30 21:20:17"}
{"level":"http","message":"POST / 200 - 3554ms","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:20:17"}
{"level":"http","message":"GraphQL anonymous completed in 3393ms","timestamp":"2025-05-30 21:20:17"}
{"level":"http","message":"POST / 200 - 3400ms","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:20:23","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:20:23"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:20:24"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:20:24"}
{"level":"http","message":"GraphQL anonymous completed in 377ms","timestamp":"2025-05-30 21:20:24"}
{"level":"http","message":"POST / 200 - 380ms","timestamp":"2025-05-30 21:20:24"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:20:28"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:20:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:20:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"POST / 200 - 53ms","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:20:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:20:29","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:20:29","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:20:29","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:20:29","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 3308ms","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 3324ms","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"GraphQL anonymous completed in 3582ms","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"POST / 200 - 3597ms","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 301ms","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 300ms","timestamp":"2025-05-30 21:20:33"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:20:33"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"GraphQL anonymous completed in 3753ms","timestamp":"2025-05-30 21:20:33"}
{"level":"http","message":"POST / 200 - 3757ms","timestamp":"2025-05-30 21:20:33"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:20:33"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:20:33"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:20:34"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:20:34"}
{"level":"http","message":"GraphQL anonymous completed in 4785ms","timestamp":"2025-05-30 21:20:34"}
{"level":"http","message":"POST / 200 - 4794ms","timestamp":"2025-05-30 21:20:34"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:34"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:34"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:20:34"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:20:35"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:20:35"}
{"level":"http","message":"GraphQL anonymous completed in 5322ms","timestamp":"2025-05-30 21:20:35"}
{"level":"http","message":"POST / 200 - 5338ms","timestamp":"2025-05-30 21:20:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:20:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:20:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:20:59"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:20:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:20:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:20:59"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:20:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:21:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:21:29"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:21:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:21:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:21:29"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:21:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:21:43","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:21:43"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:21:44"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:21:44"}
{"level":"http","message":"GraphQL anonymous completed in 287ms","timestamp":"2025-05-30 21:21:44"}
{"level":"http","message":"POST / 200 - 290ms","timestamp":"2025-05-30 21:21:44"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:21:59"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:21:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:22:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:22:00"}
{"level":"http","message":"POST / 200 - 24ms","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:22:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:22:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:22:00"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:22:01","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:22:01","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:22:01","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:22:01","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 358ms","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 361ms","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GraphQL anonymous completed in 435ms","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"POST / 200 - 440ms","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 75ms","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GraphQL anonymous completed in 437ms","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"POST / 200 - 442ms","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:22:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 323ms","timestamp":"2025-05-30 21:22:01"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:22:08"}
{"level":"http","message":"GraphQL anonymous completed in 7452ms","timestamp":"2025-05-30 21:22:08"}
{"level":"http","message":"POST / 200 - 7457ms","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:22:08"}
{"level":"http","message":"GraphQL anonymous completed in 7445ms","timestamp":"2025-05-30 21:22:08"}
{"level":"http","message":"POST / 200 - 7456ms","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:08"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:22:12","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:22:12"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:22:13"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:22:13"}
{"level":"http","message":"GraphQL anonymous completed in 994ms","timestamp":"2025-05-30 21:22:13"}
{"level":"http","message":"POST / 200 - 998ms","timestamp":"2025-05-30 21:22:13"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:22:17"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:22:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:22:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"POST / 200 - 28ms","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:22:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"POST / 200 - 20ms","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:22:18","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:22:18","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:22:18","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 214ms","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 104ms","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:22:18","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:18"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 255ms","timestamp":"2025-05-30 21:22:19"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 239ms","timestamp":"2025-05-30 21:22:19"}
{"level":"http","message":"GraphQL anonymous completed in 513ms","timestamp":"2025-05-30 21:22:19"}
{"level":"http","message":"POST / 200 - 517ms","timestamp":"2025-05-30 21:22:19"}
{"level":"http","message":"GraphQL anonymous completed in 682ms","timestamp":"2025-05-30 21:22:19"}
{"level":"http","message":"POST / 200 - 695ms","timestamp":"2025-05-30 21:22:19"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:22:19"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:22:19"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:22:19"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:22:19"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:22:20"}
{"level":"http","message":"GraphQL anonymous completed in 2186ms","timestamp":"2025-05-30 21:22:20"}
{"level":"http","message":"POST / 200 - 2208ms","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:22:20"}
{"level":"http","message":"GraphQL anonymous completed in 2116ms","timestamp":"2025-05-30 21:22:20"}
{"level":"http","message":"POST / 200 - 2126ms","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:22:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:22:24","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:22:24"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:22:24"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:22:24"}
{"level":"http","message":"GraphQL anonymous completed in 239ms","timestamp":"2025-05-30 21:22:24"}
{"level":"http","message":"POST / 200 - 242ms","timestamp":"2025-05-30 21:22:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:22:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:22:48"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:22:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:22:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:22:48"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 21:22:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:22:59","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":3,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=3, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:22:59"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:23:00"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:23:00"}
{"level":"http","message":"GraphQL anonymous completed in 258ms","timestamp":"2025-05-30 21:23:00"}
{"level":"http","message":"POST / 200 - 261ms","timestamp":"2025-05-30 21:23:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:23:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:23:18"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 21:23:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:23:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:23:18"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:23:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:23:24","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":4,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=4, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:23:24"}
{"level":"info","message":"[MessageService] Retrieved 1 messages","timestamp":"2025-05-30 21:23:25"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:23:25"}
{"level":"http","message":"GraphQL anonymous completed in 273ms","timestamp":"2025-05-30 21:23:25"}
{"level":"http","message":"POST / 200 - 276ms","timestamp":"2025-05-30 21:23:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:23:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:23:48"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:23:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:23:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:23:48"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:23:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:24:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:24:18"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 21:24:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:24:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:24:18"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:24:18"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:24:18"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:24:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:24:20","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:24:20","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:24:20","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:24:20","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:24:20","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:24:20","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:20"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2142ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2139ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 191ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"GraphQL anonymous completed in 2351ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"POST / 200 - 2356ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 237ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"GraphQL anonymous completed in 2336ms","timestamp":"2025-05-30 21:24:22"}
{"level":"http","message":"POST / 200 - 2345ms","timestamp":"2025-05-30 21:24:22"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:24:23"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:24:23"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:24:23"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:24:23"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:24:24"}
{"level":"http","message":"GraphQL anonymous completed in 3610ms","timestamp":"2025-05-30 21:24:24"}
{"level":"http","message":"POST / 200 - 3616ms","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:24:24"}
{"level":"http","message":"GraphQL anonymous completed in 3717ms","timestamp":"2025-05-30 21:24:24"}
{"level":"http","message":"POST / 200 - 3722ms","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:24"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:24:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:24:36","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:24:36"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:24:37"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:24:37"}
{"level":"http","message":"GraphQL anonymous completed in 1324ms","timestamp":"2025-05-30 21:24:37"}
{"level":"http","message":"POST / 200 - 1326ms","timestamp":"2025-05-30 21:24:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:24:50","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:24:50"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:24:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:24:50","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:24:50"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 21:24:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:24:57","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":3,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=3, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:24:57"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:24:57"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:24:57"}
{"level":"http","message":"GraphQL anonymous completed in 266ms","timestamp":"2025-05-30 21:24:57"}
{"level":"http","message":"POST / 200 - 269ms","timestamp":"2025-05-30 21:24:57"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:25:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:25:08","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:25:08"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:25:08","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:25:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:25:08","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:08"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 110ms","timestamp":"2025-05-30 21:25:09"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 274ms","timestamp":"2025-05-30 21:25:09"}
{"level":"http","message":"GraphQL anonymous completed in 390ms","timestamp":"2025-05-30 21:25:09"}
{"level":"http","message":"POST / 200 - 394ms","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:25:09"}
{"level":"http","message":"GraphQL anonymous completed in 1004ms","timestamp":"2025-05-30 21:25:09"}
{"level":"http","message":"POST / 200 - 1007ms","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:09"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:25:13","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:25:13"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:25:13"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:25:13"}
{"level":"http","message":"GraphQL anonymous completed in 255ms","timestamp":"2025-05-30 21:25:13"}
{"level":"http","message":"POST / 200 - 260ms","timestamp":"2025-05-30 21:25:13"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:25:20","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:25:20"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:25:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:25:38","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:25:38"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 21:25:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:25:50","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:25:50"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:25:50"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:25:53"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:25:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:25:55","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:25:55","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"POST / 200 - 21ms","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:25:55","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:25:55","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:25:55","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:25:55","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:25:55"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2010ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2088ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"GraphQL anonymous completed in 2266ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"POST / 200 - 2276ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 287ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 288ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"GraphQL anonymous completed in 2554ms","timestamp":"2025-05-30 21:25:57"}
{"level":"http","message":"POST / 200 - 2561ms","timestamp":"2025-05-30 21:25:57"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:25:57"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:25:57"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:25:57"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:25:57"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:26:00"}
{"level":"http","message":"GraphQL anonymous completed in 5004ms","timestamp":"2025-05-30 21:26:00"}
{"level":"http","message":"POST / 200 - 5010ms","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:26:00"}
{"level":"http","message":"GraphQL anonymous completed in 5057ms","timestamp":"2025-05-30 21:26:00"}
{"level":"http","message":"POST / 200 - 5078ms","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:26:07","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:26:07"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:26:07"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:26:07"}
{"level":"http","message":"GraphQL anonymous completed in 256ms","timestamp":"2025-05-30 21:26:07"}
{"level":"http","message":"POST / 200 - 259ms","timestamp":"2025-05-30 21:26:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:26:25","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:26:25"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:26:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:26:25","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:26:25"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 21:26:25"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:26:40"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:26:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:26:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:26:41"}
{"level":"http","message":"POST / 200 - 51ms","timestamp":"2025-05-30 21:26:41"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:26:41"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:41"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:41"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:41"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:26:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:26:41"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:26:42","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:26:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:26:42","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:26:42","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:26:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:26:42","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:42"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 3018ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 2990ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 233ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 253ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"GraphQL anonymous completed in 3260ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"POST / 200 - 3274ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"GraphQL anonymous completed in 3322ms","timestamp":"2025-05-30 21:26:45"}
{"level":"http","message":"POST / 200 - 3331ms","timestamp":"2025-05-30 21:26:45"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:26:45"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:26:45"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:26:45"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:26:45"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:26:47"}
{"level":"http","message":"GraphQL anonymous completed in 5179ms","timestamp":"2025-05-30 21:26:47"}
{"level":"http","message":"POST / 200 - 5184ms","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:26:47"}
{"level":"http","message":"GraphQL anonymous completed in 5159ms","timestamp":"2025-05-30 21:26:47"}
{"level":"http","message":"POST / 200 - 5164ms","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:47"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:26:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:26:58","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:26:58"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:26:58"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:26:58"}
{"level":"http","message":"GraphQL anonymous completed in 490ms","timestamp":"2025-05-30 21:26:58"}
{"level":"http","message":"POST / 200 - 495ms","timestamp":"2025-05-30 21:26:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:27:00","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":3,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=3, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:27:00"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:27:00"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:27:00"}
{"level":"http","message":"GraphQL anonymous completed in 466ms","timestamp":"2025-05-30 21:27:00"}
{"level":"http","message":"POST / 200 - 468ms","timestamp":"2025-05-30 21:27:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:27:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:27:11"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:27:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:27:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:27:11"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:27:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:27:26","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":4,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=4, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:27:26"}
{"level":"info","message":"[MessageService] Retrieved 1 messages","timestamp":"2025-05-30 21:27:26"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:27:26"}
{"level":"http","message":"GraphQL anonymous completed in 270ms","timestamp":"2025-05-30 21:27:26"}
{"level":"http","message":"POST / 200 - 274ms","timestamp":"2025-05-30 21:27:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:27:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:27:41"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 21:27:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:27:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:27:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:27:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:28:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:28:11"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:28:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:28:12","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:28:12"}
{"level":"http","message":"POST / 200 - 47ms","timestamp":"2025-05-30 21:28:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:28:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:28:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:28:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:28:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:28:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:28:41"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:28:52"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 21:28:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:28:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:28:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"POST / 200 - 20ms","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:28:53","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:28:53","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 21:28:53","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 195ms","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 21:28:53","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 183ms","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 133ms","timestamp":"2025-05-30 21:28:53"}
{"level":"http","message":"GraphQL anonymous completed in 2162ms","timestamp":"2025-05-30 21:28:55"}
{"level":"http","message":"POST / 200 - 2169ms","timestamp":"2025-05-30 21:28:55"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 1967ms","timestamp":"2025-05-30 21:28:55"}
{"level":"http","message":"GraphQL anonymous completed in 2113ms","timestamp":"2025-05-30 21:28:55"}
{"level":"http","message":"POST / 200 - 2121ms","timestamp":"2025-05-30 21:28:55"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:28:56"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-30 21:28:56"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 31","timestamp":"2025-05-30 21:28:56"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 21:28:56"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:28:57"}
{"level":"http","message":"GraphQL anonymous completed in 3599ms","timestamp":"2025-05-30 21:28:57"}
{"level":"http","message":"POST / 200 - 3605ms","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:28:57"}
{"level":"http","message":"GraphQL anonymous completed in 3547ms","timestamp":"2025-05-30 21:28:57"}
{"level":"http","message":"POST / 200 - 3550ms","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:57"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-30 21:28:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 21:29:09","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 21:29:09"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 21:29:10"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 21:29:10"}
{"level":"http","message":"GraphQL anonymous completed in 1032ms","timestamp":"2025-05-30 21:29:10"}
{"level":"http","message":"POST / 200 - 1035ms","timestamp":"2025-05-30 21:29:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:29:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 21:29:23"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:29:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 21:29:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 21:29:23"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 21:29:23"}
