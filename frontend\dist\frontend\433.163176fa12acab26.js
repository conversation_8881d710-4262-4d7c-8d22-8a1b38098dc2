"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[433],{9433:(N,M,s)=>{s.r(M),s.d(M,{NotificationsModule:()=>bt});var u=s(177),h=s(6647),k=s(1413),v=s(4412),b=s(7673),g=s(5293),d=s(6697),f=s(6977),l=s(152),m=s(3294),O=s(5964),_=s(6354),P=s(9437),t=s(7705),C=s(7455),w=s(487);const y=["notificationContainer"];function I(e,c){if(1&e){const n=t.RV6();t.j41(0,"div",33)(1,"label",34)(2,"input",35),t.bIt("click",function(i){t.eBV(n);const a=t.XpG(2);return t.Njj(a.toggleSelectAll(i))}),t.k0s(),t.nrm(3,"span",36),t.k0s()()}if(2&e){const n=t.XpG(2);t.R7$(2),t.Y8G("checked",n.allSelected)}}function j(e,c){if(1&e){const n=t.RV6();t.j41(0,"button",37),t.bIt("click",function(){t.eBV(n);const i=t.XpG(2);return t.Njj(i.markAllAsRead())}),t.nrm(1,"i",38),t.EFF(2," Tout marquer comme lu "),t.k0s()}}function E(e,c){if(1&e){const n=t.RV6();t.j41(0,"button",39),t.bIt("click",function(){t.eBV(n);const i=t.XpG(2);return t.Njj(i.deleteAllNotifications())}),t.nrm(1,"i",40),t.EFF(2," Tout supprimer "),t.k0s()}}function R(e,c){if(1&e){const n=t.RV6();t.j41(0,"div",23)(1,"button",24),t.bIt("click",function(){t.eBV(n);const i=t.XpG();return t.Njj(i.loadNotifications())}),t.nrm(2,"i",25),t.k0s(),t.DNE(3,I,4,1,"div",26),t.nI1(4,"async"),t.j41(5,"button",27),t.bIt("click",function(){t.eBV(n);const i=t.XpG();return t.Njj(i.toggleUnreadFilter())}),t.nrm(6,"i",28),t.k0s(),t.j41(7,"button",29),t.bIt("click",function(){t.eBV(n);const i=t.XpG();return t.Njj(i.toggleSound())}),t.nrm(8,"i",30),t.k0s(),t.DNE(9,j,3,0,"button",31),t.nI1(10,"async"),t.DNE(11,E,3,0,"button",32),t.nI1(12,"async"),t.k0s()}if(2&e){const n=t.XpG();t.R7$(3),t.Y8G("ngIf",t.bMT(4,9,n.hasNotifications())),t.R7$(2),t.AVh("active",n.showOnlyUnread),t.R7$(2),t.AVh("active",!n.isSoundMuted),t.FS9("title",n.isSoundMuted?"Activer le son":"D\xe9sactiver le son"),t.R7$(1),t.Y8G("ngClass",n.isSoundMuted?"fa-volume-mute":"fa-volume-up"),t.R7$(1),t.Y8G("ngIf",t.bMT(10,11,n.unreadCount$)||0),t.R7$(2),t.Y8G("ngIf",t.bMT(12,13,n.hasNotifications()))}}function S(e,c){if(1&e){const n=t.RV6();t.j41(0,"div",41)(1,"span",42),t.EFF(2),t.k0s(),t.j41(3,"button",37),t.bIt("click",function(){t.eBV(n);const i=t.XpG();return t.Njj(i.markSelectedAsRead())}),t.nrm(4,"i",43),t.EFF(5," Marquer comme lu "),t.k0s(),t.j41(6,"button",44),t.bIt("click",function(){t.eBV(n);const i=t.XpG();return t.Njj(i.deleteSelectedNotifications())}),t.nrm(7,"i",40),t.EFF(8," Supprimer "),t.k0s(),t.j41(9,"button",45),t.bIt("click",function(){t.eBV(n);const i=t.XpG();return i.selectedNotifications.clear(),i.showSelectionBar=!1,t.Njj(i.allSelected=!1)}),t.nrm(10,"i",46),t.EFF(11," Annuler "),t.k0s()()}if(2&e){const n=t.XpG();t.R7$(2),t.SpI("",n.selectedNotifications.size," s\xe9lectionn\xe9(s)")}}function F(e,c){1&e&&(t.j41(0,"div",47),t.nrm(1,"div",48),t.j41(2,"p",49),t.EFF(3,"Chargement des notifications..."),t.k0s()())}function $(e,c){if(1&e){const n=t.RV6();t.j41(0,"div",50)(1,"div",51),t.nrm(2,"i",52),t.j41(3,"div")(4,"h3",53),t.EFF(5,"Erreur de chargement"),t.k0s(),t.j41(6,"p",54),t.EFF(7),t.k0s()(),t.j41(8,"button",55),t.bIt("click",function(){t.eBV(n);const i=t.XpG();return t.Njj(i.loadNotifications())}),t.EFF(9," R\xe9essayer "),t.k0s()()()}if(2&e){const n=t.XpG();t.R7$(7),t.JRh(n.getErrorMessage())}}function G(e,c){if(1&e){const n=t.RV6();t.j41(0,"div",56)(1,"div",57),t.nrm(2,"i",58),t.k0s(),t.j41(3,"h3",59),t.EFF(4,"Aucune notification"),t.k0s(),t.j41(5,"p",60),t.EFF(6,"Vous \xeates \xe0 jour !"),t.k0s(),t.j41(7,"button",61),t.bIt("click",function(){t.eBV(n);const i=t.XpG();return t.Njj(i.loadNotifications())}),t.EFF(8," V\xe9rifier les nouvelles notifications "),t.k0s()()}}function T(e,c){if(1&e&&(t.j41(0,"div",89),t.EFF(1),t.k0s()),2&e){const n=t.XpG().$implicit;t.R7$(1),t.SpI(" ",null==n.message?null:n.message.content," ")}}function A(e,c){if(1&e&&(t.j41(0,"div",90),t.nrm(1,"i",91),t.EFF(2),t.k0s()),2&e){const n=t.XpG().$implicit;t.R7$(2),t.SpI(" ",null==n.message||null==n.message.attachments?null:n.message.attachments.length," pi\xe8ce(s) jointe(s) ")}}function z(e,c){1&e&&t.nrm(0,"div",92)}function L(e,c){if(1&e){const n=t.RV6();t.j41(0,"button",93),t.bIt("click",function(i){t.eBV(n);const a=t.XpG().$implicit;return t.XpG(2).getNotificationAttachments(a.id),t.Njj(i.stopPropagation())}),t.nrm(1,"i",91),t.k0s()}}function D(e,c){1&e&&t.nrm(0,"i",97)}function Y(e,c){1&e&&t.nrm(0,"i",98)}function V(e,c){if(1&e){const n=t.RV6();t.j41(0,"button",94),t.bIt("click",function(i){t.eBV(n);const a=t.XpG().$implicit;return t.XpG(2).joinConversation(a),t.Njj(i.stopPropagation())}),t.DNE(1,D,1,0,"i",95),t.DNE(2,Y,1,0,"i",96),t.k0s()}if(2&e){const n=t.XpG(3);t.Y8G("disabled",n.loading),t.R7$(1),t.Y8G("ngIf",!n.loading),t.R7$(1),t.Y8G("ngIf",n.loading)}}function X(e,c){if(1&e){const n=t.RV6();t.j41(0,"button",99),t.bIt("click",function(i){t.eBV(n);const a=t.XpG().$implicit;return t.XpG(2).markAsRead(a.id),t.Njj(i.stopPropagation())}),t.nrm(1,"i",100),t.k0s()}}function B(e,c){if(1&e){const n=t.RV6();t.qex(0),t.j41(1,"div",66)(2,"div",67)(3,"label",34)(4,"input",35),t.bIt("click",function(i){const r=t.eBV(n).$implicit,p=t.XpG(2);return t.Njj(p.toggleSelection(r.id,i))}),t.k0s(),t.nrm(5,"span",36),t.k0s()(),t.j41(6,"div",68),t.nrm(7,"img",69),t.k0s(),t.j41(8,"div",70)(9,"div",71)(10,"div",72)(11,"div",73)(12,"span",74),t.EFF(13),t.k0s(),t.j41(14,"div",75),t.EFF(15),t.nI1(16,"date"),t.k0s()()(),t.j41(17,"div",76)(18,"span",77),t.EFF(19),t.k0s()(),t.DNE(20,T,2,1,"div",78),t.DNE(21,A,3,1,"div",79),t.k0s(),t.DNE(22,z,1,0,"div",80),t.k0s(),t.j41(23,"div",81),t.DNE(24,L,2,0,"button",82),t.DNE(25,V,3,3,"button",83),t.j41(26,"button",84),t.bIt("click",function(i){const r=t.eBV(n).$implicit;return t.XpG(2).openNotificationDetails(r),t.Njj(i.stopPropagation())}),t.nrm(27,"i",85),t.k0s(),t.DNE(28,X,2,0,"button",86),t.j41(29,"button",87),t.bIt("click",function(i){const r=t.eBV(n).$implicit;return t.XpG(2).deleteNotification(r.id),t.Njj(i.stopPropagation())}),t.nrm(30,"i",88),t.k0s()()(),t.bVm()}if(2&e){const n=c.$implicit,o=t.XpG(2);t.R7$(1),t.AVh("futuristic-notification-unread",!n.isRead)("futuristic-notification-read",n.isRead)("futuristic-notification-selected",o.isSelected(n.id)),t.R7$(3),t.Y8G("checked",o.isSelected(n.id)),t.R7$(3),t.Y8G("src",(null==n.senderId?null:n.senderId.image)||"assets/images/default-avatar.png",t.B4B),t.R7$(6),t.JRh((null==n.senderId?null:n.senderId.username)||"Syst\xe8me"),t.R7$(2),t.SpI(" ",t.i5U(16,17,n.timestamp,"shortTime")," "),t.R7$(4),t.JRh(n.content),t.R7$(1),t.Y8G("ngIf",null==n.message?null:n.message.content),t.R7$(1),t.Y8G("ngIf",null==n.message||null==n.message.attachments?null:n.message.attachments.length),t.R7$(1),t.Y8G("ngIf",!n.isRead),t.R7$(2),t.Y8G("ngIf",null==n.message||null==n.message.attachments?null:n.message.attachments.length),t.R7$(1),t.Y8G("ngIf","NEW_MESSAGE"===n.type||"GROUP_INVITE"===n.type||"MESSAGE_REACTION"===n.type),t.R7$(3),t.Y8G("ngIf",!n.isRead)}}function U(e,c){1&e&&(t.j41(0,"div",101),t.nrm(1,"div",102),t.j41(2,"p",103),t.EFF(3," Chargement des notifications plus anciennes... "),t.k0s()())}function W(e,c){if(1&e){const n=t.RV6();t.j41(0,"div",62,63),t.bIt("scroll",function(){t.eBV(n);const i=t.sdS(1),a=t.XpG();return t.Njj(a.onScroll(i))}),t.DNE(2,B,31,20,"ng-container",64),t.nI1(3,"async"),t.DNE(4,U,4,0,"div",65),t.k0s()}if(2&e){const n=t.XpG();t.R7$(2),t.Y8G("ngForOf",t.bMT(3,2,n.filteredNotifications$)),t.R7$(2),t.Y8G("ngIf",n.loadingMore)}}function Q(e,c){1&e&&(t.j41(0,"div",47),t.nrm(1,"div",48),t.j41(2,"p",49),t.EFF(3,"Chargement des pi\xe8ces jointes..."),t.k0s()())}function J(e,c){1&e&&(t.j41(0,"div",56)(1,"div",57),t.nrm(2,"i",104),t.k0s(),t.j41(3,"h3",59),t.EFF(4,"Aucune pi\xe8ce jointe"),t.k0s(),t.j41(5,"p",60),t.EFF(6," Aucune pi\xe8ce jointe n'a \xe9t\xe9 trouv\xe9e pour cette notification. "),t.k0s()())}function H(e,c){if(1&e){const n=t.RV6();t.j41(0,"div",120)(1,"img",121),t.bIt("click",function(){t.eBV(n);const i=t.XpG().$implicit,a=t.XpG(2);return t.Njj(a.openAttachment(i.url))}),t.k0s()()}if(2&e){const n=t.XpG().$implicit;t.R7$(1),t.Y8G("src",n.url,t.B4B)}}function K(e,c){if(1&e&&(t.j41(0,"div",122),t.nrm(1,"i"),t.k0s()),2&e){const n=t.XpG().$implicit,o=t.XpG(2);t.R7$(1),t.HbH(o.getFileIcon(n.type))}}function Z(e,c){if(1&e&&(t.j41(0,"span",123),t.EFF(1),t.k0s()),2&e){const n=t.XpG().$implicit,o=t.XpG(2);t.R7$(1),t.JRh(o.formatFileSize(n.size))}}function q(e,c){if(1&e){const n=t.RV6();t.j41(0,"div",107),t.DNE(1,H,2,1,"div",108),t.DNE(2,K,2,2,"div",109),t.j41(3,"div",110)(4,"div",111),t.EFF(5),t.k0s(),t.j41(6,"div",112)(7,"span",113),t.EFF(8),t.k0s(),t.DNE(9,Z,2,1,"span",114),t.k0s()(),t.j41(10,"div",115)(11,"button",116),t.bIt("click",function(){const a=t.eBV(n).$implicit,r=t.XpG(2);return t.Njj(r.openAttachment(a.url))}),t.nrm(12,"i",117),t.k0s(),t.j41(13,"button",118),t.bIt("click",function(){const a=t.eBV(n).$implicit,r=t.XpG(2);return t.Njj(r.downloadAttachment(a))}),t.nrm(14,"i",119),t.k0s()()()}if(2&e){const n=c.$implicit,o=t.XpG(2);t.R7$(1),t.Y8G("ngIf",o.isImage(n.type)),t.R7$(1),t.Y8G("ngIf",!o.isImage(n.type)),t.R7$(3),t.SpI(" ",n.name||"Pi\xe8ce jointe"," "),t.R7$(3),t.JRh(o.getFileTypeLabel(n.type)),t.R7$(1),t.Y8G("ngIf",n.size)}}function tt(e,c){if(1&e&&(t.j41(0,"div",105),t.DNE(1,q,15,5,"div",106),t.k0s()),2&e){const n=t.XpG();t.R7$(1),t.Y8G("ngForOf",n.currentAttachments)}}function nt(e,c){if(1&e&&(t.j41(0,"div",147)(1,"strong"),t.EFF(2,"Message original :"),t.k0s(),t.EFF(3),t.k0s()),2&e){const n=t.XpG(2);t.R7$(3),t.SpI(" ",null==n.currentNotification.message?null:n.currentNotification.message.content," ")}}function it(e,c){if(1&e&&(t.j41(0,"div",137)(1,"span",138),t.EFF(2,"Lu le :"),t.k0s(),t.j41(3,"span",139),t.EFF(4),t.nI1(5,"date"),t.k0s()()),2&e){const n=t.XpG(2);t.R7$(4),t.JRh(t.i5U(5,1,n.currentNotification.readAt,"medium"))}}function ot(e,c){1&e&&(t.j41(0,"div",148)(1,"span",138),t.nrm(2,"i",149),t.EFF(3," Note : "),t.k0s(),t.j41(4,"span",150),t.EFF(5," Ouvrir les d\xe9tails ne marque pas automatiquement comme lu "),t.k0s()())}function et(e,c){if(1&e){const n=t.RV6();t.j41(0,"div",164)(1,"img",121),t.bIt("click",function(){t.eBV(n);const i=t.XpG().$implicit,a=t.XpG(3);return t.Njj(a.openAttachment(i.url))}),t.k0s()()}if(2&e){const n=t.XpG().$implicit;t.R7$(1),t.Y8G("src",n.url,t.B4B)}}function at(e,c){if(1&e&&(t.j41(0,"div",165),t.nrm(1,"i"),t.k0s()),2&e){const n=t.XpG().$implicit,o=t.XpG(3);t.R7$(1),t.HbH(o.getFileIcon(n.type))}}function rt(e,c){if(1&e&&(t.j41(0,"span",166),t.EFF(1),t.k0s()),2&e){const n=t.XpG().$implicit,o=t.XpG(3);t.R7$(1),t.JRh(o.formatFileSize(n.size))}}function ct(e,c){if(1&e){const n=t.RV6();t.j41(0,"div",153),t.DNE(1,et,2,1,"div",154),t.DNE(2,at,2,2,"div",155),t.j41(3,"div",156)(4,"div",157),t.EFF(5),t.k0s(),t.j41(6,"div",158)(7,"span",159),t.EFF(8),t.k0s(),t.DNE(9,rt,2,1,"span",160),t.k0s()(),t.j41(10,"div",161)(11,"button",162),t.bIt("click",function(){const a=t.eBV(n).$implicit,r=t.XpG(3);return t.Njj(r.openAttachment(a.url))}),t.nrm(12,"i",117),t.k0s(),t.j41(13,"button",163),t.bIt("click",function(){const a=t.eBV(n).$implicit,r=t.XpG(3);return t.Njj(r.downloadAttachment(a))}),t.nrm(14,"i",119),t.k0s()()()}if(2&e){const n=c.$implicit,o=t.XpG(3);t.R7$(1),t.Y8G("ngIf",o.isImage(n.type)),t.R7$(1),t.Y8G("ngIf",!o.isImage(n.type)),t.R7$(3),t.SpI(" ",n.name||"Pi\xe8ce jointe"," "),t.R7$(3),t.JRh(o.getFileTypeLabel(n.type)),t.R7$(1),t.Y8G("ngIf",n.size)}}function st(e,c){if(1&e&&(t.j41(0,"div",124)(1,"h4",125),t.nrm(2,"i",16),t.EFF(3),t.k0s(),t.j41(4,"div",151),t.DNE(5,ct,15,5,"div",152),t.k0s()()),2&e){const n=t.XpG(2);t.R7$(3),t.SpI(" Pi\xe8ces jointes (",n.currentAttachments.length,") "),t.R7$(2),t.Y8G("ngForOf",n.currentAttachments)}}function ft(e,c){1&e&&t.nrm(0,"i",170)}function dt(e,c){1&e&&t.nrm(0,"i",171)}function lt(e,c){if(1&e){const n=t.RV6();t.j41(0,"button",167),t.bIt("click",function(){t.eBV(n);const i=t.XpG(2);return i.joinConversation(i.currentNotification),t.Njj(i.closeNotificationDetailsModal())}),t.DNE(1,ft,1,0,"i",168),t.DNE(2,dt,1,0,"i",169),t.EFF(3," Rejoindre la conversation "),t.k0s()}if(2&e){const n=t.XpG(2);t.Y8G("disabled",n.loading),t.R7$(1),t.Y8G("ngIf",!n.loading),t.R7$(1),t.Y8G("ngIf",n.loading)}}function gt(e,c){if(1&e){const n=t.RV6();t.j41(0,"button",172),t.bIt("click",function(){t.eBV(n);const i=t.XpG(2);return t.Njj(i.markAsRead(i.currentNotification.id))}),t.nrm(1,"i",173),t.EFF(2," Marquer comme lu "),t.k0s()}}function ut(e,c){if(1&e){const n=t.RV6();t.j41(0,"div",19)(1,"div",124)(2,"h4",125),t.nrm(3,"i",126),t.EFF(4," Exp\xe9diteur "),t.k0s(),t.j41(5,"div",127),t.nrm(6,"img",128),t.j41(7,"div",129)(8,"span",130),t.EFF(9),t.k0s(),t.j41(10,"span",131),t.EFF(11),t.nI1(12,"date"),t.k0s()()()(),t.j41(13,"div",124)(14,"h4",125),t.nrm(15,"i",132),t.EFF(16," Message "),t.k0s(),t.j41(17,"div",133),t.EFF(18),t.k0s(),t.DNE(19,nt,4,1,"div",134),t.k0s(),t.j41(20,"div",124)(21,"h4",125),t.nrm(22,"i",135),t.EFF(23," Informations "),t.k0s(),t.j41(24,"div",136)(25,"div",137)(26,"span",138),t.EFF(27,"Type :"),t.k0s(),t.j41(28,"span",139),t.EFF(29),t.k0s()(),t.j41(30,"div",137)(31,"span",138),t.EFF(32,"Statut :"),t.k0s(),t.j41(33,"span",139),t.EFF(34),t.k0s()(),t.DNE(35,it,6,4,"div",140),t.DNE(36,ot,6,0,"div",141),t.k0s()(),t.DNE(37,st,6,2,"div",142),t.j41(38,"div",143),t.DNE(39,lt,4,3,"button",144),t.DNE(40,gt,3,0,"button",145),t.j41(41,"button",44),t.bIt("click",function(){t.eBV(n);const i=t.XpG();return i.deleteNotification(i.currentNotification.id),t.Njj(i.closeNotificationDetailsModal())}),t.nrm(42,"i",146),t.EFF(43," Supprimer "),t.k0s()()()}if(2&e){const n=t.XpG();t.R7$(6),t.Y8G("src",(null==n.currentNotification.senderId?null:n.currentNotification.senderId.image)||"assets/images/default-avatar.png",t.B4B),t.R7$(3),t.SpI(" ",(null==n.currentNotification.senderId?null:n.currentNotification.senderId.username)||"Syst\xe8me"," "),t.R7$(2),t.SpI(" ",t.i5U(12,16,n.currentNotification.timestamp,"medium")," "),t.R7$(7),t.SpI(" ",n.currentNotification.content," "),t.R7$(1),t.Y8G("ngIf",null==n.currentNotification.message?null:n.currentNotification.message.content),t.R7$(10),t.JRh(n.currentNotification.type),t.R7$(4),t.AVh("text-green-500",n.currentNotification.isRead)("text-orange-500",!n.currentNotification.isRead),t.R7$(1),t.SpI(" ",n.currentNotification.isRead?"Lu":"Non lu"," "),t.R7$(1),t.Y8G("ngIf",n.currentNotification.readAt),t.R7$(1),t.Y8G("ngIf",!n.currentNotification.isRead),t.R7$(1),t.Y8G("ngIf",n.currentAttachments.length>0),t.R7$(2),t.Y8G("ngIf","NEW_MESSAGE"===n.currentNotification.type||"GROUP_INVITE"===n.currentNotification.type||"MESSAGE_REACTION"===n.currentNotification.type),t.R7$(1),t.Y8G("ngIf",!n.currentNotification.isRead)}}let pt=(()=>{class e{constructor(n,o,i){this.messageService=n,this.themeService=o,this.router=i,this.loading=!0,this.loadingMore=!1,this.hasMoreNotifications=!0,this.error=null,this.showOnlyUnread=!1,this.isSoundMuted=!1,this.selectedNotifications=new Set,this.allSelected=!1,this.showSelectionBar=!1,this.showAttachmentsModal=!1,this.loadingAttachments=!1,this.currentAttachments=[],this.showNotificationDetailsModal=!1,this.currentNotification=null,this.destroy$=new k.B,this.scrollPosition$=new v.t(0),this.notifications$=this.messageService.notifications$,this.filteredNotifications$=this.notifications$,this.unreadCount$=this.messageService.notificationCount$,this.isDarkMode$=this.themeService.darkMode$,this.isSoundMuted=this.messageService.isMuted()}joinConversation(n){this.markAsRead(n.id);const o=n.conversationId||n.metadata&&n.metadata.conversationId||(n.relatedEntity&&n.relatedEntity.includes("conversation")?n.relatedEntity:null),i=n.groupId||n.metadata&&n.metadata.groupId||(n.relatedEntity&&n.relatedEntity.includes("group")?n.relatedEntity:null);o?this.router.navigate(["/messages/conversations/chat",o]):i?this.router.navigate(["/messages/group",i]):n.senderId&&n.senderId.id?(this.loading=!0,this.messageService.getOrCreateConversation(n.senderId.id).subscribe({next:a=>{this.loading=!1,this.router.navigate(a&&a.id?["/messages/conversations/chat",a.id]:["/messages"])},error:a=>{this.loading=!1,this.error=a,this.router.navigate(["/messages"])}})):this.router.navigate(["/messages"])}onScroll(n){if(!n)return;const o=n.scrollTop;n.scrollHeight-o-n.clientHeight<200&&this.scrollPosition$.next(o)}ngOnInit(){const n=localStorage.getItem("notificationSoundMuted");null!==n&&(this.isSoundMuted="true"===n,this.messageService.setMuted(this.isSoundMuted)),this.loadNotifications(),this.setupSubscriptions(),this.setupInfiniteScroll(),this.filterDeletedNotifications()}filterDeletedNotifications(){const n=this.getDeletedNotificationIds();n.size>0&&this.notifications$.pipe((0,d.s)(1)).subscribe(o=>{const i=o.filter(r=>!n.has(r.id));this.messageService.notifications.next(i);const a=i.filter(r=>!r.isRead).length;this.messageService.notificationCount.next(a),this.updateNotificationCache(i)})}setupInfiniteScroll(){this.scrollPosition$.pipe((0,f.Q)(this.destroy$),(0,l.B)(200),(0,m.F)(),(0,O.p)(()=>!this.loadingMore&&this.hasMoreNotifications)).subscribe(()=>{this.loadMoreNotifications()})}loadNotifications(){this.loading=!0,this.loadingMore=!1,this.error=null,this.hasMoreNotifications=!0;const n=this.getDeletedNotificationIds();this.messageService.getNotifications(!0).pipe((0,f.Q)(this.destroy$),(0,_.T)(o=>n.size>0?o.filter(i=>!n.has(i.id)):o)).subscribe({next:o=>{this.messageService.notifications.next(o);const i=o.filter(a=>!a.isRead).length;this.messageService.notificationCount.next(i),this.loading=!1,this.hasMoreNotifications=this.messageService.hasMoreNotifications()},error:o=>{this.error=o,this.loading=!1,this.hasMoreNotifications=!1}})}loadMoreNotifications(){if(this.loadingMore||!this.hasMoreNotifications)return;this.loadingMore=!0;const n=this.getDeletedNotificationIds();this.messageService.loadMoreNotifications().pipe((0,f.Q)(this.destroy$),(0,_.T)(o=>n.size>0?o.filter(i=>!n.has(i.id)):o)).subscribe({next:o=>{this.notifications$.pipe((0,d.s)(1)).subscribe(i=>{const a=[...i,...o];this.messageService.notifications.next(a);const r=a.filter(p=>!p.isRead).length;this.messageService.notificationCount.next(r),this.updateNotificationCache(a)}),this.loadingMore=!1,this.hasMoreNotifications=this.messageService.hasMoreNotifications()},error:o=>{this.loadingMore=!1,this.hasMoreNotifications=!1}})}setupSubscriptions(){this.messageService.subscribeToNewNotifications().pipe((0,f.Q)(this.destroy$),(0,P.W)(n=>(console.log("Notification stream error:",n),(0,b.of)(null)))).subscribe(),this.messageService.subscribeToNotificationsRead().pipe((0,f.Q)(this.destroy$),(0,P.W)(n=>(console.log("Notifications read stream error:",n),(0,b.of)(null)))).subscribe()}markAsRead(n){n?this.notifications$.pipe((0,d.s)(1)).subscribe(o=>{const i=o.find(a=>a.id===n);if(i){if(i.isRead)return;const a=o.map(r=>r.id===n?{...r,isRead:!0,readAt:(new Date).toISOString()}:r);this.updateUIWithNotifications(a),this.messageService.markAsRead([n]).pipe((0,f.Q)(this.destroy$)).subscribe({next:r=>{r&&r.success&&this.error&&this.error.message.includes("mark")&&(this.error=null)},error:r=>{const p=o.map(x=>x.id===n?{...x,isRead:!1,readAt:void 0}:x);this.messageService.notifications.next(p);const Ct=p.filter(x=>!x.isRead).length;this.messageService.notificationCount.next(Ct)}})}else this.loadNotifications()}):this.error=new Error("ID de notification invalide")}updateUIWithNotifications(n){this.messageService.notifications.next(n);const o=n.filter(i=>!i.isRead).length;this.messageService.notificationCount.next(o),this.updateNotificationCache(n)}updateNotificationCache(n){n.forEach(o=>{this.messageService.updateNotificationCache?.(o)})}resetSelection(){this.selectedNotifications.clear(),this.allSelected=!1,this.showSelectionBar=!1}markAllAsRead(){this.notifications$.pipe((0,d.s)(1)).subscribe(n=>{const o=n.filter(r=>!r.isRead).map(r=>r.id);if(0===o.length)return;const i=o.filter(r=>r&&"string"==typeof r&&""!==r.trim());if(i.length!==o.length)return void(this.error=new Error("Invalid notification IDs"));const a=n.map(r=>i.includes(r.id)?{...r,isRead:!0,readAt:(new Date).toISOString()}:r);this.updateUIWithNotifications(a),this.messageService.markAsRead(i).pipe((0,f.Q)(this.destroy$)).subscribe({next:r=>{r&&r.success&&this.error&&this.error.message.includes("mark")&&(this.error=null)},error:r=>{}})})}hasNotifications(){return this.notifications$.pipe((0,_.T)(n=>n?.length>0))}hasUnreadNotifications(){return this.unreadCount$.pipe((0,_.T)(n=>n>0))}toggleUnreadFilter(){this.showOnlyUnread=!this.showOnlyUnread,this.filteredNotifications$=this.showOnlyUnread?this.messageService.getUnreadNotifications():this.notifications$}toggleSound(){this.isSoundMuted=!this.isSoundMuted,this.messageService.setMuted(this.isSoundMuted),this.isSoundMuted||setTimeout(()=>{this.messageService.playNotificationSound(),setTimeout(()=>{this.messageService.playNotificationSound()},1e3)},100),localStorage.setItem("notificationSoundMuted",this.isSoundMuted.toString())}getNotificationAttachments(n){if(!n)return;let o;if(this.currentAttachments=[],this.loadingAttachments=!0,this.showAttachmentsModal=!0,this.notifications$.pipe((0,d.s)(1)).subscribe(i=>{o=i.find(a=>a.id===n)}),o&&o.message&&o.message.attachments&&o.message.attachments.length>0)return this.loadingAttachments=!1,void(this.currentAttachments=o.message.attachments.map(i=>({id:"",url:i.url||"",type:this.convertAttachmentTypeToMessageType(i.type),name:i.name||"",size:i.size||0,duration:0})));this.messageService.getNotificationAttachments(n).pipe((0,f.Q)(this.destroy$)).subscribe({next:i=>{this.loadingAttachments=!1,this.currentAttachments=i},error:i=>{this.loadingAttachments=!1}})}closeAttachmentsModal(){this.showAttachmentsModal=!1}openNotificationDetails(n){this.currentNotification=n,this.showNotificationDetailsModal=!0,n.message?.attachments?.length&&this.getNotificationAttachmentsForModal(n.id)}closeNotificationDetailsModal(){this.showNotificationDetailsModal=!1,this.currentNotification=null,this.currentAttachments=[]}getNotificationAttachmentsForModal(n){this.currentAttachments=[],this.currentNotification?.message?.attachments?.length&&(this.currentAttachments=this.currentNotification.message.attachments.map(o=>({id:"",url:o.url||"",type:this.convertAttachmentTypeToMessageType(o.type),name:o.name||"",size:o.size||0,duration:0})))}convertAttachmentTypeToMessageType(n){switch(n){case"IMAGE":return g.Go.IMAGE;case"VIDEO":return g.Go.VIDEO;case"AUDIO":return g.Go.AUDIO;default:return g.Go.FILE}}isImage(n){return n?.startsWith("image/")||!1}getFileIcon(n){return n?n.startsWith("image/")?"fas fa-file-image":n.startsWith("video/")?"fas fa-file-video":n.startsWith("audio/")?"fas fa-file-audio":n.startsWith("text/")?"fas fa-file-alt":n.includes("pdf")?"fas fa-file-pdf":n.includes("word")||n.includes("document")?"fas fa-file-word":n.includes("excel")||n.includes("sheet")?"fas fa-file-excel":n.includes("powerpoint")||n.includes("presentation")?"fas fa-file-powerpoint":n.includes("zip")||n.includes("compressed")?"fas fa-file-archive":"fas fa-file":"fas fa-file"}getFileTypeLabel(n){return n?n.startsWith("image/")?"Image":n.startsWith("video/")?"Vid\xe9o":n.startsWith("audio/")?"Audio":n.startsWith("text/")?"Texte":n.includes("pdf")?"PDF":n.includes("word")||n.includes("document")?"Document":n.includes("excel")||n.includes("sheet")?"Feuille de calcul":n.includes("powerpoint")||n.includes("presentation")?"Pr\xe9sentation":n.includes("zip")||n.includes("compressed")?"Archive":"Fichier":"Fichier"}formatFileSize(n){if(!n)return"";const o=["B","KB","MB","GB","TB"];let i=0,a=n;for(;a>=1024&&i<o.length-1;)a/=1024,i++;return`${a.toFixed(1)} ${o[i]}`}openAttachment(n){n&&window.open(n,"_blank")}downloadAttachment(n){if(!n?.url)return;const o=document.createElement("a");o.href=n.url,o.download=n.name||"attachment",o.target="_blank",document.body.appendChild(o),o.click(),document.body.removeChild(o)}acceptFriendRequest(n){this.markAsRead(n.id)}deleteNotification(n){if(!n)return void(this.error=new Error("ID de notification invalide"));const o=this.getDeletedNotificationIds();o.add(n),this.saveDeletedNotificationIds(o),this.messageService.deleteNotification(n).pipe((0,f.Q)(this.destroy$)).subscribe({next:i=>{i&&i.success&&this.error&&this.error.message.includes("suppression")&&(this.error=null)},error:i=>{this.error=i}})}deleteAllNotifications(){this.notifications$.pipe((0,d.s)(1)).subscribe(n=>{const o=this.getDeletedNotificationIds();n.forEach(i=>{o.add(i.id)}),this.saveDeletedNotificationIds(o),this.messageService.deleteAllNotifications().pipe((0,f.Q)(this.destroy$)).subscribe({next:i=>{i&&i.success&&this.error&&this.error.message.includes("suppression")&&(this.error=null)},error:i=>{this.error=i}})})}getErrorMessage(){return this.error?.message||"Unknown error occurred"}getDeletedNotificationIds(){try{const n=localStorage.getItem("deletedNotificationIds");return n?new Set(JSON.parse(n)):new Set}catch{return new Set}}saveDeletedNotificationIds(n){try{localStorage.setItem("deletedNotificationIds",JSON.stringify(Array.from(n)))}catch{}}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}toggleSelection(n,o){o.stopPropagation(),this.selectedNotifications.has(n)?this.selectedNotifications.delete(n):this.selectedNotifications.add(n),this.updateSelectionState(),this.showSelectionBar=this.selectedNotifications.size>0}toggleSelectAll(n){n.stopPropagation(),this.allSelected=!this.allSelected,this.filteredNotifications$.pipe((0,d.s)(1)).subscribe(o=>{this.allSelected?o.forEach(i=>{this.selectedNotifications.add(i.id)}):this.selectedNotifications.clear(),this.showSelectionBar=this.selectedNotifications.size>0})}updateSelectionState(){this.filteredNotifications$.pipe((0,d.s)(1)).subscribe(n=>{this.allSelected=n.length>0&&this.selectedNotifications.size===n.length})}deleteSelectedNotifications(){if(0===this.selectedNotifications.size)return;const n=Array.from(this.selectedNotifications);this.notifications$.pipe((0,d.s)(1)).subscribe(o=>{const i=o.filter(a=>!this.selectedNotifications.has(a.id));this.updateUIWithNotifications(i),this.resetSelection()}),this.messageService.deleteMultipleNotifications(n).pipe((0,f.Q)(this.destroy$)).subscribe({next:o=>{},error:o=>{}})}markSelectedAsRead(){if(0===this.selectedNotifications.size)return;const n=Array.from(this.selectedNotifications);this.notifications$.pipe((0,d.s)(1)).subscribe(o=>{const i=o.map(a=>this.selectedNotifications.has(a.id)?{...a,isRead:!0,readAt:(new Date).toISOString()}:a);this.updateUIWithNotifications(i),this.resetSelection()}),this.messageService.markAsRead(n).pipe((0,f.Q)(this.destroy$)).subscribe({next:o=>{},error:o=>{}})}isSelected(n){return this.selectedNotifications.has(n)}static{this.\u0275fac=function(o){return new(o||e)(t.rXU(C.b),t.rXU(w.F),t.rXU(h.Ix))}}static{this.\u0275cmp=t.VBU({type:e,selectors:[["app-notification-list"]],viewQuery:function(o,i){if(1&o&&t.GBs(y,5),2&o){let a;t.mGM(a=t.lsd())&&(i.notificationContainer=a.first)}},hostBindings:function(o,i){1&o&&t.bIt("scroll",function(r){return i.onScroll(r.target)})},decls:37,vars:22,consts:[[1,"futuristic-notifications-container","main-grid-container"],[1,"background-elements","background-grid"],[1,"futuristic-notifications-card","content-card","relative","z-10"],[1,"futuristic-notifications-header"],[1,"futuristic-title"],[1,"fas","fa-bell","mr-2"],["class","flex space-x-2",4,"ngIf"],["class","flex space-x-2 selection-actions",4,"ngIf"],["class","futuristic-loading-container",4,"ngIf"],["class","futuristic-error-message",4,"ngIf"],["class","futuristic-empty-state",4,"ngIf"],["class","futuristic-notifications-list",3,"scroll",4,"ngIf"],[1,"futuristic-modal-overlay",3,"click"],[1,"futuristic-modal-container",3,"click"],[1,"futuristic-modal-header"],[1,"futuristic-modal-title"],[1,"fas","fa-paperclip","mr-2"],[1,"futuristic-modal-close",3,"click"],[1,"fas","fa-times"],[1,"futuristic-modal-body"],["class","futuristic-attachments-list",4,"ngIf"],[1,"fas","fa-info-circle","mr-2"],["class","futuristic-modal-body",4,"ngIf"],[1,"flex","space-x-2"],["title","Rafra\xeechir",1,"futuristic-action-button",3,"click"],[1,"fas","fa-sync-alt"],["class","select-all-checkbox",4,"ngIf"],["title","Filtrer les non lues",1,"futuristic-action-button",3,"click"],[1,"fas","fa-filter"],[1,"futuristic-action-button",3,"title","click"],[1,"fas",3,"ngClass"],["class","futuristic-primary-button",3,"click",4,"ngIf"],["class","futuristic-danger-button","title","Supprimer toutes les notifications",3,"click",4,"ngIf"],[1,"select-all-checkbox"],[1,"futuristic-checkbox"],["type","checkbox",3,"checked","click"],[1,"checkmark"],[1,"futuristic-primary-button",3,"click"],[1,"fas","fa-check-double","mr-1"],["title","Supprimer toutes les notifications",1,"futuristic-danger-button",3,"click"],[1,"fas","fa-trash-alt","mr-1"],[1,"flex","space-x-2","selection-actions"],[1,"selection-count"],[1,"fas","fa-check","mr-1"],[1,"futuristic-danger-button",3,"click"],[1,"futuristic-cancel-button",3,"click"],[1,"fas","fa-times","mr-1"],[1,"futuristic-loading-container"],[1,"futuristic-loading-circle"],[1,"futuristic-loading-text"],[1,"futuristic-error-message"],[1,"flex","items-center"],[1,"fas","fa-exclamation-triangle","futuristic-error-icon"],[1,"futuristic-error-title"],[1,"futuristic-error-text"],[1,"futuristic-retry-button","ml-auto",3,"click"],[1,"futuristic-empty-state"],[1,"futuristic-empty-icon"],[1,"fas","fa-bell-slash"],[1,"futuristic-empty-title"],[1,"futuristic-empty-text"],[1,"futuristic-check-button",3,"click"],[1,"futuristic-notifications-list",3,"scroll"],["notificationContainer",""],[4,"ngFor","ngForOf"],["class","futuristic-loading-more",4,"ngIf"],[1,"futuristic-notification-card"],[1,"notification-checkbox"],[1,"notification-avatar"],["alt","Avatar","onerror","this.src='assets/images/default-avatar.png'",3,"src"],[1,"notification-main-content"],[1,"notification-content"],[1,"notification-header"],[1,"notification-header-top"],[1,"notification-sender"],[1,"notification-time"],[1,"notification-text-container"],[1,"notification-text"],["class","notification-message-preview",4,"ngIf"],["class","notification-attachments-indicator",4,"ngIf"],["class","unread-indicator",4,"ngIf"],[1,"notification-actions"],["class","notification-action-button notification-attachment-button","title","Voir les pi\xe8ces jointes",3,"click",4,"ngIf"],["class","notification-action-button notification-join-button","title","Rejoindre la conversation",3,"disabled","click",4,"ngIf"],["title","Voir les d\xe9tails (ne marque PAS comme lu automatiquement)",1,"notification-action-button","notification-details-button",3,"click"],[1,"fas","fa-info-circle"],["class","notification-action-button notification-read-button","title","Marquer cette notification comme lue",3,"click",4,"ngIf"],["title","Supprimer cette notification",1,"notification-action-button","notification-delete-button",3,"click"],[1,"fas","fa-trash-alt"],[1,"notification-message-preview"],[1,"notification-attachments-indicator"],[1,"fas","fa-paperclip"],[1,"unread-indicator"],["title","Voir les pi\xe8ces jointes",1,"notification-action-button","notification-attachment-button",3,"click"],["title","Rejoindre la conversation",1,"notification-action-button","notification-join-button",3,"disabled","click"],["class","fas fa-comments",4,"ngIf"],["class","fas fa-spinner fa-spin",4,"ngIf"],[1,"fas","fa-comments"],[1,"fas","fa-spinner","fa-spin"],["title","Marquer cette notification comme lue",1,"notification-action-button","notification-read-button",3,"click"],[1,"fas","fa-check"],[1,"futuristic-loading-more"],[1,"futuristic-loading-circle-small"],[1,"futuristic-loading-text-small"],[1,"fas","fa-file-alt"],[1,"futuristic-attachments-list"],["class","futuristic-attachment-item",4,"ngFor","ngForOf"],[1,"futuristic-attachment-item"],["class","futuristic-attachment-preview",4,"ngIf"],["class","futuristic-attachment-icon",4,"ngIf"],[1,"futuristic-attachment-info"],[1,"futuristic-attachment-name"],[1,"futuristic-attachment-meta"],[1,"futuristic-attachment-type"],["class","futuristic-attachment-size",4,"ngIf"],[1,"futuristic-attachment-actions"],["title","Ouvrir",1,"futuristic-attachment-button",3,"click"],[1,"fas","fa-external-link-alt"],["title","T\xe9l\xe9charger",1,"futuristic-attachment-button",3,"click"],[1,"fas","fa-download"],[1,"futuristic-attachment-preview"],["alt","Image",3,"src","click"],[1,"futuristic-attachment-icon"],[1,"futuristic-attachment-size"],[1,"notification-detail-section"],[1,"notification-detail-title"],[1,"fas","fa-user","mr-2"],[1,"notification-sender-info"],["alt","Avatar","onerror","this.src='assets/images/default-avatar.png'",1,"notification-sender-avatar",3,"src"],[1,"notification-sender-details"],[1,"notification-sender-name"],[1,"notification-timestamp"],[1,"fas","fa-message","mr-2"],[1,"notification-content-detail"],["class","notification-message-detail",4,"ngIf"],[1,"fas","fa-tag","mr-2"],[1,"notification-info-grid"],[1,"notification-info-item"],[1,"notification-info-label"],[1,"notification-info-value"],["class","notification-info-item",4,"ngIf"],["class","notification-info-item","style","\n              background: rgba(255, 140, 0, 0.1);\n              border: 1px solid rgba(255, 140, 0, 0.3);\n            ",4,"ngIf"],["class","notification-detail-section",4,"ngIf"],[1,"notification-detail-actions"],["class","futuristic-primary-button",3,"disabled","click",4,"ngIf"],["class","futuristic-secondary-button",3,"click",4,"ngIf"],[1,"fas","fa-trash-alt","mr-2"],[1,"notification-message-detail"],[1,"notification-info-item",2,"background","rgba(255, 140, 0, 0.1)","border","1px solid rgba(255, 140, 0, 0.3)"],[1,"fas","fa-info-circle","mr-1"],[1,"notification-info-value",2,"color","#ff8c00","font-style","italic"],[1,"notification-attachments-grid"],["class","notification-attachment-item",4,"ngFor","ngForOf"],[1,"notification-attachment-item"],["class","notification-attachment-preview",4,"ngIf"],["class","notification-attachment-icon",4,"ngIf"],[1,"notification-attachment-info"],[1,"notification-attachment-name"],[1,"notification-attachment-meta"],[1,"notification-attachment-type"],["class","notification-attachment-size",4,"ngIf"],[1,"notification-attachment-actions"],["title","Ouvrir",1,"notification-attachment-button",3,"click"],["title","T\xe9l\xe9charger",1,"notification-attachment-button",3,"click"],[1,"notification-attachment-preview"],[1,"notification-attachment-icon"],[1,"notification-attachment-size"],[1,"futuristic-primary-button",3,"disabled","click"],["class","fas fa-comments mr-2",4,"ngIf"],["class","fas fa-spinner fa-spin mr-2",4,"ngIf"],[1,"fas","fa-comments","mr-2"],[1,"fas","fa-spinner","fa-spin","mr-2"],[1,"futuristic-secondary-button",3,"click"],[1,"fas","fa-check","mr-2"]],template:function(o,i){1&o&&(t.j41(0,"div",0),t.nI1(1,"async"),t.nrm(2,"div",1),t.j41(3,"div",2)(4,"div",3)(5,"h2",4),t.nrm(6,"i",5),t.EFF(7," Notifications "),t.k0s(),t.DNE(8,R,13,15,"div",6),t.DNE(9,S,12,1,"div",7),t.k0s(),t.DNE(10,F,4,0,"div",8),t.DNE(11,$,10,1,"div",9),t.DNE(12,G,9,0,"div",10),t.nI1(13,"async"),t.DNE(14,W,5,4,"div",11),t.nI1(15,"async"),t.k0s()(),t.j41(16,"div",12),t.bIt("click",function(){return i.closeAttachmentsModal()}),t.j41(17,"div",13),t.bIt("click",function(r){return r.stopPropagation()}),t.j41(18,"div",14)(19,"h3",15),t.nrm(20,"i",16),t.EFF(21," Pi\xe8ces jointes "),t.k0s(),t.j41(22,"button",17),t.bIt("click",function(){return i.closeAttachmentsModal()}),t.nrm(23,"i",18),t.k0s()(),t.j41(24,"div",19),t.DNE(25,Q,4,0,"div",8),t.DNE(26,J,7,0,"div",10),t.DNE(27,tt,2,1,"div",20),t.k0s()()(),t.j41(28,"div",12),t.bIt("click",function(){return i.closeNotificationDetailsModal()}),t.j41(29,"div",13),t.bIt("click",function(r){return r.stopPropagation()}),t.j41(30,"div",14)(31,"h3",15),t.nrm(32,"i",21),t.EFF(33," D\xe9tails de la notification "),t.k0s(),t.j41(34,"button",17),t.bIt("click",function(){return i.closeNotificationDetailsModal()}),t.nrm(35,"i",18),t.k0s()(),t.DNE(36,ut,44,19,"div",22),t.k0s()()),2&o&&(t.AVh("dark",t.bMT(1,16,i.isDarkMode$)),t.R7$(8),t.Y8G("ngIf",!i.showSelectionBar),t.R7$(1),t.Y8G("ngIf",i.showSelectionBar),t.R7$(1),t.Y8G("ngIf",i.loading),t.R7$(1),t.Y8G("ngIf",i.error),t.R7$(1),t.Y8G("ngIf",!i.loading&&!t.bMT(13,18,i.hasNotifications())),t.R7$(2),t.Y8G("ngIf",!i.loading&&t.bMT(15,20,i.hasNotifications())),t.R7$(2),t.xc7("display",i.showAttachmentsModal?"flex":"none"),t.R7$(9),t.Y8G("ngIf",i.loadingAttachments),t.R7$(1),t.Y8G("ngIf",!i.loadingAttachments&&0===i.currentAttachments.length),t.R7$(1),t.Y8G("ngIf",!i.loadingAttachments&&i.currentAttachments.length>0),t.R7$(1),t.xc7("display",i.showNotificationDetailsModal?"flex":"none"),t.R7$(8),t.Y8G("ngIf",i.currentNotification))},dependencies:[u.YU,u.Sq,u.bT,u.Jj,u.vh],styles:['@charset "UTF-8";.futuristic-notifications-container[_ngcontent-%COMP%]{padding:0;min-height:calc(100vh - 4rem);position:relative;overflow:hidden;width:100%;display:flex;justify-content:center;padding-top:1rem;margin-bottom:0;height:100vh}:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%]{background-color:#edf1f4;color:#6d6870;position:relative;overflow:hidden}.dark[_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%]{background-color:#121212;color:#a0a0a0;position:relative;overflow:hidden}.futuristic-notifications-container[_ngcontent-%COMP%]   .background-elements[_ngcontent-%COMP%]{position:absolute;inset:0;overflow:hidden;pointer-events:none;z-index:0}:not(.dark)[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;opacity:.05;background-image:linear-gradient(to right,#4f5fad 1px,transparent 1px),linear-gradient(to bottom,#4f5fad 1px,transparent 1px);background-size:calc(100% / 12) 100%,100% calc(100% / 12);z-index:0}.dark[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;opacity:.05;background-image:linear-gradient(to right,rgba(255,140,0,.3) 1px,transparent 1px),linear-gradient(to bottom,rgba(255,140,0,.3) 1px,transparent 1px);background-size:5% 100%,100% 5%;z-index:0;animation:_ngcontent-%COMP%_grid-pulse 4s ease-in-out infinite}.dark[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]:after{content:"";position:absolute;left:0;right:0;height:2px;background:linear-gradient(to right,transparent 0%,rgba(255,140,0,.5) 50%,transparent 100%);box-shadow:0 0 10px #ff8c0080;z-index:1;animation:_ngcontent-%COMP%_scan 8s linear infinite}@keyframes _ngcontent-%COMP%_grid-pulse{0%{opacity:.03}50%{opacity:.07}to{opacity:.03}}@keyframes _ngcontent-%COMP%_scan{0%{top:-10%;opacity:.5}50%{opacity:.8}to{top:110%;opacity:.5}}.futuristic-notifications-card[_ngcontent-%COMP%]{border-radius:.5rem;overflow:hidden;position:relative;z-index:1;margin:.5rem auto;display:flex;flex-direction:column;width:100%;max-width:1200px;height:calc(100vh - 1rem)}:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%]{background-color:#fff;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(79,95,173,.1)}.dark[_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%]{background-color:#1e1e1e;box-shadow:0 8px 32px #0000004d;border:1px solid rgba(109,120,201,.1);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.futuristic-notifications-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem;position:sticky;top:0;z-index:10;margin-bottom:1.5rem}:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%]{border-bottom:1px solid rgba(79,95,173,.1);background-color:#f0f4f880;box-shadow:0 4px 20px #0000001a}.dark[_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%]{border-bottom:1px solid rgba(0,247,255,.1);background-color:#0003;box-shadow:0 4px 20px #0000004d}:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#4f5fad;display:flex;align-items:center}.dark[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:var(--accent-color);display:flex;align-items:center}.futuristic-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.5rem;position:relative;top:0;width:22px;height:22px;display:flex;align-items:center;justify-content:center;border-radius:50%;background:rgba(0,247,255,.1);border:1px solid rgba(0,247,255,.3);box-shadow:0 0 8px #00f7ff4d;transition:all .3s ease}:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{background:rgba(79,95,173,.1);border:1px solid rgba(79,95,173,.3);box-shadow:0 0 8px #4f5fad4d;color:#4f5fad}.futuristic-title[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:scale(1.05);box-shadow:0 0 12px #00f7ff80}:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{box-shadow:0 0 12px #4f5fad80}.futuristic-loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:4rem 2rem}.futuristic-loading-circle[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;border:2px solid transparent;border-top-color:var(--accent-color);border-bottom-color:var(--secondary-color);animation:_ngcontent-%COMP%_futuristic-spin 1.2s linear infinite;box-shadow:0 0 15px #00f7ff4d}@keyframes _ngcontent-%COMP%_futuristic-spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.futuristic-loading-text[_ngcontent-%COMP%]{margin-top:1rem;color:var(--text-dim);font-size:.875rem;text-align:center}.futuristic-error-message[_ngcontent-%COMP%]{margin:1rem;padding:1rem;background-color:#ff004c1a;border-left:4px solid var(--error-color);border-radius:var(--border-radius-md)}.futuristic-error-icon[_ngcontent-%COMP%]{color:var(--error-color);font-size:1.25rem;margin-right:.75rem}.futuristic-error-title[_ngcontent-%COMP%]{color:var(--error-color);font-weight:600;font-size:.875rem}.futuristic-error-text[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.75rem;margin-top:.25rem}.futuristic-retry-button[_ngcontent-%COMP%]{padding:.25rem .75rem;background-color:#ff004c1a;color:var(--error-color);border:1px solid var(--error-color);border-radius:var(--border-radius-sm);font-size:.75rem;cursor:pointer;transition:all var(--transition-fast)}.futuristic-retry-button[_ngcontent-%COMP%]:hover{background-color:#ff004c33;box-shadow:0 0 10px #ff004c4d}.futuristic-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:4rem 2rem;text-align:center}.futuristic-empty-icon[_ngcontent-%COMP%]{font-size:3rem;color:var(--accent-color);margin-bottom:1rem;opacity:.5}.futuristic-empty-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:var(--text-light);margin-bottom:.5rem}.futuristic-empty-text[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.875rem;margin-bottom:1rem}.futuristic-check-button[_ngcontent-%COMP%]{padding:.5rem 1rem;background-color:#00f7ff1a;color:var(--accent-color);border:1px solid rgba(0,247,255,.3);border-radius:var(--border-radius-md);font-size:.875rem;cursor:pointer;transition:all var(--transition-fast)}.futuristic-check-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:translateY(-2px);box-shadow:var(--glow-effect)}.futuristic-notifications-list[_ngcontent-%COMP%]{padding:0;display:flex;flex-direction:column;flex:1;overflow-y:auto;position:relative;scrollbar-width:thin;z-index:1;width:100%}.futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]{scrollbar-color:#4f5fad transparent;background-color:#fff}:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:#4f5fad;border-radius:10px}.dark[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]{scrollbar-color:var(--accent-color) transparent;background-color:transparent}.dark[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .dark   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:var(--accent-color);border-radius:10px}.futuristic-notification-card[_ngcontent-%COMP%]{display:flex;align-items:center;padding:30px 20px 16px;position:relative;transition:all .2s ease;margin:.5rem 1rem;border-radius:8px;flex-wrap:nowrap;justify-content:space-between}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]{border-bottom:1px solid rgba(79,95,173,.1);background-color:#fff;box-shadow:0 1px 3px #0000000d;border-radius:15px;transition:all .3s ease}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover{background-color:#4f5fad0d;transform:translateY(-1px);box-shadow:0 3px 6px #0000001a}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(79,95,173,.1),rgba(61,74,133,.2));border:1px solid rgba(79,95,173,.3);box-shadow:0 2px 10px #0000001a;border-bottom-right-radius:0;position:relative;overflow:hidden}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:after{content:"";position:absolute;inset:0;border-radius:inherit;pointer-events:none;z-index:-1;box-shadow:inset 0 0 0 1px #fff3}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover{transform:translateY(-2px);background:linear-gradient(135deg,rgba(79,95,173,.15),rgba(61,74,133,.25));box-shadow:0 4px 12px #4f5fad33}.dark[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]{border-bottom:none;background-color:var( --dark-medium-bg, #252740 );border:1px solid rgba(255,255,255,.1);border-radius:15px;box-shadow:0 2px 10px #0003;margin-bottom:15px;margin-left:15px;margin-right:15px;transition:all .3s ease;color:var( --text-light, #ffffff )}.dark[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 15px #0000004d}.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]{position:relative;overflow:hidden;background:linear-gradient(135deg,#00f7ff20,#00c3ff30);border:1px solid rgba(0,247,255,.3);box-shadow:0 2px 10px #0003;border-bottom-right-radius:0;transition:all .3s ease}.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:after{content:"";position:absolute;inset:0;border-radius:inherit;pointer-events:none;z-index:-1;box-shadow:inset 0 0 0 1px #00f7ff4d}.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 15px #00f7ff33;background:linear-gradient(135deg,#00f7ff30,#00c3ff40)}.futuristic-notification-unread[_ngcontent-%COMP%]{position:relative;overflow:hidden}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_scaleIn{0%{transform:scale(.9);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_borderFlow{0%{background-position:0% 0%}to{background-position:200% 0%}}.futuristic-modal-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#000000b3;-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);display:none;align-items:center;justify-content:center;z-index:1000;animation:_ngcontent-%COMP%_fadeIn .3s ease;opacity:0;transition:opacity .3s ease}.futuristic-modal-overlay[style*="display: flex"][_ngcontent-%COMP%]{opacity:1}.dark[_nghost-%COMP%]   .futuristic-modal-overlay[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-overlay[_ngcontent-%COMP%]{background-color:#000c;-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px);animation:_ngcontent-%COMP%_modalBackdropFadeIn .3s ease-out}@keyframes _ngcontent-%COMP%_modalBackdropFadeIn{0%{background-color:#0000;-webkit-backdrop-filter:blur(0px);backdrop-filter:blur(0px)}to{background-color:#000c;-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}}:not(.dark)[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]{width:90%;max-width:600px;max-height:80vh;background-color:#fff;border-radius:12px;overflow:hidden;box-shadow:0 10px 30px #0003;display:flex;flex-direction:column;animation:_ngcontent-%COMP%_scaleIn .3s ease;border:1px solid rgba(79,95,173,.2);position:relative}:not(.dark)[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:0;right:0;height:1px;background:linear-gradient(90deg,transparent 0%,rgba(79,95,173,.2) 20%,rgba(79,95,173,.8) 50%,rgba(79,95,173,.2) 80%,transparent 100%);background-size:200% 100%;animation:_ngcontent-%COMP%_borderFlow 3s infinite linear;box-shadow:0 0 10px #4f5fad66;z-index:1}.dark[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]{width:90%;max-width:600px;max-height:80vh;background-color:#121212f2;border-radius:16px;overflow:hidden;box-shadow:0 0 40px #00f7ff66,inset 0 0 20px #00f7ff1a;display:flex;flex-direction:column;animation:_ngcontent-%COMP%_modalFadeIn .4s cubic-bezier(.19,1,.22,1);border:1px solid rgba(0,247,255,.3);position:relative}@keyframes _ngcontent-%COMP%_modalFadeIn{0%{opacity:0;transform:scale(.9) translateY(20px)}to{opacity:1;transform:scale(1) translateY(0)}}.dark[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]:after{content:"";position:absolute;top:0;left:0;right:0;height:1px;background:linear-gradient(90deg,transparent 0%,rgba(0,247,255,.2) 20%,rgba(0,247,255,.8) 50%,rgba(0,247,255,.2) 80%,transparent 100%);background-size:200% 100%;animation:_ngcontent-%COMP%_borderFlow 3s infinite linear;box-shadow:0 0 15px #00f7ffb3;z-index:1}:not(.dark)[_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%]{padding:16px;display:flex;align-items:center;justify-content:space-between;border-bottom:1px solid rgba(79,95,173,.1);background-color:#4f5fad0d}:not(.dark)[_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#4f5fad;margin:0;display:flex;align-items:center}:not(.dark)[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background-color:#4f5fad1a;color:#4f5fad;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}:not(.dark)[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover{background-color:#4f5fad33;transform:scale(1.1);box-shadow:0 0 10px #4f5fad4d}.dark[_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%]{padding:16px;display:flex;align-items:center;justify-content:space-between;border-bottom:1px solid rgba(0,247,255,.1);background-color:#0003}.dark[_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#00f7ff;margin:0;display:flex;align-items:center}.dark[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background-color:#00f7ff1a;color:#00f7ff;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}.dark[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:scale(1.1);box-shadow:0 0 15px #00f7ff80}.futuristic-modal-body[_ngcontent-%COMP%]{padding:16px;overflow-y:auto;max-height:calc(80vh - 70px)}.futuristic-attachments-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px;border-radius:8px;background-color:#4f5fad0d;border:1px solid rgba(79,95,173,.1);transition:all .2s ease}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover{background-color:#4f5fad1a;transform:translateY(-2px);box-shadow:0 5px 15px #0000001a}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:4px;overflow:hidden;margin-right:12px;flex-shrink:0;border:1px solid rgba(79,95,173,.2)}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:4px;background-color:#4f5fad1a;display:flex;align-items:center;justify-content:center;margin-right:12px;flex-shrink:0}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px;color:#4f5fad}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background-color:#4f5fad1a;color:#4f5fad;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover{background-color:#4f5fad33;transform:scale(1.1);box-shadow:0 0 10px #4f5fad4d}.dark[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px;border-radius:8px;background-color:#00f7ff0d;border:1px solid rgba(0,247,255,.1);transition:all .2s ease}.dark[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover{background-color:#00f7ff1a;transform:translateY(-2px);box-shadow:0 5px 15px #0003}.dark[_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:4px;overflow:hidden;margin-right:12px;flex-shrink:0;border:1px solid rgba(0,247,255,.2);box-shadow:0 0 10px #00f7ff33}.dark[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:4px;background-color:#00f7ff1a;display:flex;align-items:center;justify-content:center;margin-right:12px;flex-shrink:0}.dark[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px;color:#00f7ff}.dark[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background-color:#00f7ff1a;color:#00f7ff;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}.dark[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:scale(1.1);box-shadow:0 0 15px #00f7ff80}.futuristic-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;cursor:pointer;transition:transform .2s ease}.futuristic-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover{transform:scale(1.1)}.futuristic-attachment-info[_ngcontent-%COMP%]{flex:1;min-width:0}.futuristic-attachment-name[_ngcontent-%COMP%]{font-weight:500;margin-bottom:4px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.futuristic-attachment-meta[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:.8rem;color:var(--text-dim)}.futuristic-attachment-actions[_ngcontent-%COMP%]{display:flex;gap:8px;margin-left:12px}.futuristic-loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 0}.futuristic-loading-circle[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:3px solid transparent;margin-bottom:16px;animation:_ngcontent-%COMP%_spin 1.2s linear infinite}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%]{border-top-color:#4f5fad;box-shadow:0 0 15px #4f5fad4d}.dark[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%]{border-top-color:#00f7ff;box-shadow:0 0 15px #00f7ff80}.futuristic-loading-text[_ngcontent-%COMP%]{font-size:.9rem;color:var(--text-dim)}.futuristic-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 0;text-align:center}.futuristic-empty-icon[_ngcontent-%COMP%]{font-size:48px;margin-bottom:16px;opacity:.5}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%]{color:#4f5fad}.dark[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%]{color:#00f7ff}.futuristic-empty-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:500;margin-bottom:8px}.futuristic-empty-text[_ngcontent-%COMP%]{font-size:.9rem;color:var(--text-dim);max-width:300px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.notification-avatar[_ngcontent-%COMP%]{width:40px;height:40px;flex-shrink:0;margin-right:12px;margin-left:10px}.notification-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;border-radius:50%;object-fit:cover}.notification-content[_ngcontent-%COMP%]{flex:1;min-width:0;padding-right:16px}.notification-header[_ngcontent-%COMP%]{margin-bottom:6px}.notification-header-top[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%}.notification-sender[_ngcontent-%COMP%]{font-weight:600;font-size:.95rem;color:#4f5fad;padding:2px 0;transition:all .3s ease}.notification-sender[_ngcontent-%COMP%]:hover{color:#3d4a85;text-shadow:0 0 1px rgba(79,95,173,.3)}.dark[_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%]{color:#ff8c00;text-shadow:0 0 5px rgba(255,140,0,.3)}.dark[_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%]:hover{color:#ffa040;text-shadow:0 0 8px rgba(255,140,0,.5)}.notification-text[_ngcontent-%COMP%]{color:#333;font-size:.9rem;position:relative;z-index:1}.dark[_nghost-%COMP%]   .notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-text[_ngcontent-%COMP%]{color:#fff}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%]{color:var( --light-text, #333333 );font-weight:400}.dark[_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%]{color:var( --dark-text, #ffffff );font-weight:400}.notification-message-preview[_ngcontent-%COMP%]{font-size:.85rem;color:#666;margin-top:4px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:100%}.dark[_nghost-%COMP%]   .notification-message-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-message-preview[_ngcontent-%COMP%]{color:#ccc}.notification-attachments-indicator[_ngcontent-%COMP%]{font-size:.75rem;color:#ff8c00;margin-top:.25rem;display:flex;align-items:center}.dark[_nghost-%COMP%]   .notification-attachments-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachments-indicator[_ngcontent-%COMP%]{color:#00f7ffe6}.notification-attachments-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.25rem}.notification-action-button[_ngcontent-%COMP%]{cursor:pointer;display:flex;align-items:center;justify-content:center;font-size:.9rem;transition:all .3s ease;margin:6px;border:none;position:relative;overflow:hidden}.notification-time[_ngcontent-%COMP%]{position:absolute;top:10px;right:10px;font-size:.75rem;color:#00f7ffe6;font-weight:600;padding:5px 10px;border-radius:8px;background:linear-gradient(135deg,rgba(0,247,255,.15),rgba(0,200,255,.1));border:1px solid rgba(0,247,255,.3);box-shadow:0 2px 8px #00f7ff33;z-index:15;transition:all .3s ease;white-space:nowrap;letter-spacing:.5px;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased}.dark[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]{color:#0ff!important;background:linear-gradient(135deg,rgba(0,255,255,.4),rgba(0,255,200,.35),rgba(0,200,255,.3));border:1px solid rgba(0,255,255,.8);text-shadow:0 0 5px rgba(0,255,255,.8),0 0 10px rgba(0,255,255,.6),0 0 15px rgba(0,255,255,.4);box-shadow:0 2px 10px #0ff9,0 0 20px #00ffff4d,inset 0 0 10px #0ff3;-webkit-backdrop-filter:none;backdrop-filter:none;animation:_ngcontent-%COMP%_fluoro-pulse 2s ease-in-out infinite alternate}:not(.dark)[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]{color:#4f5fad;background:linear-gradient(135deg,rgba(79,95,173,.1),rgba(79,95,173,.05));border:1px solid rgba(79,95,173,.2);box-shadow:0 2px 8px #4f5fad26}.notification-time[_ngcontent-%COMP%]:hover{transform:translateY(-2px) scale(1.05);box-shadow:0 4px 15px #00f7ff66}.dark[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover{color:#fff!important;background:linear-gradient(135deg,rgba(0,255,255,.6),rgba(0,255,200,.5),rgba(0,200,255,.45));border-color:#0ff;text-shadow:0 0 8px rgba(255,255,255,1),0 0 15px rgba(0,255,255,.9),0 0 25px rgba(0,255,255,.7);box-shadow:0 4px 20px #0ffc,0 0 30px #00ffff80,inset 0 0 15px #00ffff4d;animation:_ngcontent-%COMP%_fluoro-pulse-intense 1s ease-in-out infinite alternate}:not(.dark)[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(79,95,173,.15),rgba(79,95,173,.1));box-shadow:0 4px 15px #4f5fad40}.notification-join-button[_ngcontent-%COMP%]{width:40px;height:40px;background:linear-gradient(135deg,#00c853,#00a843);color:#fff;border:2px solid #00c853;clip-path:polygon(25% 0%,75% 0%,100% 50%,75% 100%,25% 100%,0% 50%);position:relative}.notification-join-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#00e676,#00c853);transform:scale(1.15) rotate(5deg);box-shadow:0 0 15px #00c85399;border-color:#00e676}.notification-join-button[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:-2px;background:linear-gradient(45deg,#00c853,#00e676);clip-path:polygon(25% 0%,75% 0%,100% 50%,75% 100%,25% 100%,0% 50%);z-index:-1;opacity:0;transition:opacity .3s ease}.notification-join-button[_ngcontent-%COMP%]:hover:before{opacity:1}.notification-details-button[_ngcontent-%COMP%]{width:38px;height:38px;background:linear-gradient(135deg,#2196f3,#1976d2);color:#fff;border:2px solid #2196f3;border-radius:8px;position:relative}.notification-details-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#42a5f5,#2196f3);transform:scale(1.1) rotateY(15deg);box-shadow:0 0 15px #2196f399;border-color:#42a5f5}.notification-details-button[_ngcontent-%COMP%]:after{content:"";position:absolute;inset:2px;background:rgba(255,255,255,.1);border-radius:6px;transition:all .3s ease}.notification-details-button[_ngcontent-%COMP%]:hover:after{background:rgba(255,255,255,.2)}.notification-read-button[_ngcontent-%COMP%]{width:36px;height:36px;background:linear-gradient(135deg,#ffc107,#ff9800);color:#fff;border:2px solid #ffc107;border-radius:50%;transform:rotate(45deg);position:relative}.notification-read-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transform:rotate(-45deg)}.notification-read-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#ffca28,#ffc107);transform:rotate(45deg) scale(1.15);box-shadow:0 0 15px #ffc10799;border-color:#ffca28}.notification-read-button[_ngcontent-%COMP%]:before{content:"";position:absolute;top:50%;left:50%;width:8px;height:8px;background:rgba(255,255,255,.3);border-radius:50%;transform:translate(-50%,-50%) rotate(-45deg);animation:_ngcontent-%COMP%_pulse 2s infinite}.notification-delete-button[_ngcontent-%COMP%]{width:38px;height:38px;background:linear-gradient(135deg,#f44336,#d32f2f);color:#fff;border:2px solid #f44336;clip-path:polygon(50% 0%,0% 100%,100% 100%);position:relative}.notification-delete-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#ef5350,#f44336);transform:scale(1.15) rotate(-5deg);box-shadow:0 0 15px #f4433699;border-color:#ef5350}.notification-delete-button[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:-2px;background:linear-gradient(45deg,#f44336,#ef5350);clip-path:polygon(50% 0%,0% 100%,100% 100%);z-index:-1;opacity:0;transition:opacity .3s ease}.notification-delete-button[_ngcontent-%COMP%]:hover:before{opacity:1}.notification-attachment-button[_ngcontent-%COMP%]{width:40px;height:40px;background:linear-gradient(135deg,#9c27b0,#7b1fa2);color:#fff;border:2px solid #9c27b0;clip-path:polygon(30% 0%,70% 0%,100% 30%,100% 70%,70% 100%,30% 100%,0% 70%,0% 30%);position:relative}.notification-attachment-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#ab47bc,#9c27b0);transform:scale(1.1) rotate(10deg);box-shadow:0 0 15px #9c27b099;border-color:#ab47bc}.notification-attachment-button[_ngcontent-%COMP%]:after{content:"";position:absolute;top:50%;left:50%;width:6px;height:6px;background:rgba(255,255,255,.4);border-radius:50%;transform:translate(-50%,-50%);animation:_ngcontent-%COMP%_bounce 1.5s infinite}.dark[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00c853,#00a843);border-color:#00e676;box-shadow:0 0 10px #00c85366}.dark[_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#2196f3,#1976d2);border-color:#42a5f5;box-shadow:0 0 10px #2196f366}.dark[_nghost-%COMP%]   .notification-read-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-read-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffc107,#ff9800);border-color:#ffca28;box-shadow:0 0 10px #ffc10766}.dark[_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f44336,#d32f2f);border-color:#ef5350;box-shadow:0 0 10px #f4433666}.dark[_nghost-%COMP%]   .notification-attachment-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#9c27b0,#7b1fa2);border-color:#ab47bc;box-shadow:0 0 10px #9c27b066}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1;transform:translate(-50%,-50%) rotate(-45deg) scale(1)}50%{opacity:.5;transform:translate(-50%,-50%) rotate(-45deg) scale(1.2)}}@keyframes _ngcontent-%COMP%_bounce{0%,to{transform:translate(-50%,-50%) scale(1)}50%{transform:translate(-50%,-50%) scale(1.3)}}.notification-join-button[_ngcontent-%COMP%]:hover:after{content:"\\1f4ac";position:absolute;top:-8px;right:-8px;font-size:10px;background:rgba(255,255,255,.9);border-radius:50%;width:16px;height:16px;display:flex;align-items:center;justify-content:center;opacity:1;transition:opacity .3s ease;z-index:10}.notification-details-button[_ngcontent-%COMP%]:hover:after{content:"\\2139\\fe0f";position:absolute;top:-8px;right:-8px;font-size:10px;background:rgba(255,255,255,.9);border-radius:50%;width:16px;height:16px;display:flex;align-items:center;justify-content:center;opacity:1;transition:opacity .3s ease;z-index:10}.notification-delete-button[_ngcontent-%COMP%]:hover:after{content:"\\1f5d1\\fe0f";position:absolute;top:-8px;right:-8px;font-size:10px;background:rgba(255,255,255,.9);border-radius:50%;width:16px;height:16px;display:flex;align-items:center;justify-content:center;opacity:1;transition:opacity .3s ease;z-index:10}.futuristic-checkbox[_ngcontent-%COMP%]{position:relative;display:inline-block;width:22px;height:22px;cursor:pointer;transition:all .2s ease}.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{position:absolute;opacity:0;cursor:pointer;height:0;width:0}.checkmark[_ngcontent-%COMP%]{position:absolute;top:0;left:0;height:22px;width:22px;background:linear-gradient(135deg,rgba(0,255,200,.1),rgba(0,200,255,.1));border:2px solid transparent;border-radius:50%;transition:all .3s ease;box-shadow:0 0 10px #00ffc866;display:flex;align-items:center;justify-content:center;animation:_ngcontent-%COMP%_glow-pulse 2s infinite alternate;position:relative;z-index:1;overflow:hidden}@keyframes _ngcontent-%COMP%_glow-pulse{0%{box-shadow:0 0 8px #00ffc84d}to{box-shadow:0 0 15px #00c8ff99}}.dark[_nghost-%COMP%]   .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .checkmark[_ngcontent-%COMP%]{background:rgba(0,247,255,.1);border:1px solid rgba(0,247,255,.3);box-shadow:0 0 12px #00f7ff66;animation:_ngcontent-%COMP%_glow-pulse 2s infinite alternate}.futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%] ~ .checkmark[_ngcontent-%COMP%]{background:rgba(0,247,255,.2);border:1px solid rgba(0,247,255,.3);box-shadow:var( --glow-effect );transform:scale(1.05)}.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%] ~ .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%] ~ .checkmark[_ngcontent-%COMP%]{background:rgba(0,247,255,.2);border:1px solid rgba(0,247,255,.3);box-shadow:var( --glow-effect );transform:scale(1.05)}.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked ~ .checkmark[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(0,255,200,.8),rgba(0,200,255,.8));border:2px solid transparent;box-shadow:0 0 20px #00ffc8cc;animation:_ngcontent-%COMP%_checkbox-glow 1.5s infinite alternate}@keyframes _ngcontent-%COMP%_checkbox-glow{0%{box-shadow:0 0 15px #00ffc899;transform:scale(1)}to{box-shadow:0 0 25px #00c8ffe6;transform:scale(1.15)}}@keyframes _ngcontent-%COMP%_checkbox-pulse{0%{transform:scale(1);box-shadow:0 0 15px #00f7ff66}to{transform:scale(1.15);box-shadow:0 0 20px #00f7ffcc}}.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked ~ .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked ~ .checkmark[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(0,255,200,.8),rgba(0,200,255,.8));border:2px solid transparent;box-shadow:0 0 20px #00ffc8cc;animation:_ngcontent-%COMP%_checkbox-glow 1.5s infinite alternate}.checkmark[_ngcontent-%COMP%]:after{content:"";position:absolute;display:none}.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked ~ .checkmark[_ngcontent-%COMP%]:after{display:block}.futuristic-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after{left:7px;top:3px;width:6px;height:12px;border:solid white;border-width:0 2px 2px 0;transform:rotate(45deg);box-shadow:0 0 5px #fffc;animation:_ngcontent-%COMP%_pulse-check 1.5s infinite alternate}@keyframes _ngcontent-%COMP%_pulse-check{0%{opacity:.8;box-shadow:0 0 5px #fffc}to{opacity:1;box-shadow:0 0 10px #fff}}.select-all-checkbox[_ngcontent-%COMP%]{display:inline-flex;align-items:center;justify-content:center;margin:0 5px}.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]{width:36px;height:36px}.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]{width:36px;height:36px;border-radius:50%;background:rgba(0,247,255,.1);border:1px solid rgba(0,247,255,.3);box-shadow:0 0 10px #00f7ff66}.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%] ~ .checkmark[_ngcontent-%COMP%]{background:rgba(0,247,255,.2);box-shadow:var(--glow-effect);transform:scale(1.05)}.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after{left:13px;top:7px;width:8px;height:16px}.selection-actions[_ngcontent-%COMP%]{display:flex;align-items:center;background-color:#ff8c001a;border-radius:8px;padding:8px 12px;box-shadow:0 0 15px #ff8c0033;animation:_ngcontent-%COMP%_fadeIn .3s ease}.dark[_nghost-%COMP%]   .selection-actions[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .selection-actions[_ngcontent-%COMP%]{background-color:#ff8c001a;box-shadow:0 0 15px #ff8c0033}.selection-count[_ngcontent-%COMP%]{font-weight:500;margin-right:15px;color:#ff8c00}.dark[_nghost-%COMP%]   .selection-count[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .selection-count[_ngcontent-%COMP%]{color:#ff8c00e6}.futuristic-notification-selected[_ngcontent-%COMP%]{border:1px solid rgba(255,140,0,.5)!important;background-color:#ff8c000d!important;box-shadow:0 5px 15px #ff8c001a!important;transform:translateY(-2px)}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]{border:1px solid rgba(255,140,0,.3)!important;background:linear-gradient(135deg,rgba(255,140,0,.15),rgba(255,0,128,.15),rgba(128,0,255,.15))!important;box-shadow:0 5px 15px #ff8c0033,inset 0 0 20px #ff00801a!important;transform:translateY(-2px);padding:18px 22px!important;margin-bottom:18px!important;position:relative;overflow:hidden}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]:before{content:"";position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:radial-gradient(circle,rgba(255,140,0,.1) 0%,transparent 70%);animation:_ngcontent-%COMP%_rotate-gradient 8s linear infinite;pointer-events:none}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]:after{content:"";position:absolute;inset:0;background-image:radial-gradient(circle at 10% 10%,rgba(255,255,255,.8) 0%,rgba(255,255,255,0) 2%),radial-gradient(circle at 20% 30%,rgba(255,140,0,.8) 0%,rgba(255,140,0,0) 2%),radial-gradient(circle at 30% 70%,rgba(255,0,128,.8) 0%,rgba(255,0,128,0) 2%),radial-gradient(circle at 70% 40%,rgba(128,0,255,.8) 0%,rgba(128,0,255,0) 2%),radial-gradient(circle at 80% 80%,rgba(255,255,255,.8) 0%,rgba(255,255,255,0) 2%),radial-gradient(circle at 90% 10%,rgba(255,140,0,.8) 0%,rgba(255,140,0,0) 2%),radial-gradient(circle at 50% 50%,rgba(255,0,128,.8) 0%,rgba(255,0,128,0) 2%);opacity:0;animation:_ngcontent-%COMP%_sparkle-effect 4s ease-in-out infinite;pointer-events:none}@keyframes _ngcontent-%COMP%_sparkle-effect{0%{opacity:0}50%{opacity:1}to{opacity:0}}@keyframes _ngcontent-%COMP%_rotate-gradient{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%], .dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%]{color:#ffffffe6!important}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%]{background-color:#00000080!important;color:#ffffffe6!important;border-left:2px solid rgba(255,140,0,.5)!important}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%]{color:#ff8c00!important;font-weight:600}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(255,140,0,.2),rgba(255,94,98,.2));color:#ff8c00;border:1px solid rgba(255,140,0,.4)}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(255,140,0,.3),rgba(255,94,98,.3));box-shadow:0 0 15px #ff8c0080}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%]{color:#ffffffb3;background-color:#ff8c001a;border:1px solid rgba(255,140,0,.3)}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover{background-color:#ff8c0033;color:#ff8c00;box-shadow:0 0 15px #ff8c0066}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]{background-color:#ff8c001a;color:#ff8c00;border:1px solid rgba(255,140,0,.3)}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover{background-color:#ff8c0033;color:#ff8c00;box-shadow:0 0 8px #ff8c0066}.notification-separator-dot[_ngcontent-%COMP%]{width:4px;height:4px;border-radius:50%;background-color:#00f7ff99;margin:0 8px;box-shadow:0 0 5px #00f7ff66;animation:_ngcontent-%COMP%_dot-pulse 2s infinite alternate;transition:all .5s ease}.notification-separator-dot.fade-out[_ngcontent-%COMP%]{opacity:0;transform:scale(0);width:0;margin:0}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-separator-dot[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-separator-dot[_ngcontent-%COMP%]{background-color:#00f7ffcc;box-shadow:0 0 8px #00f7ff99;animation:_ngcontent-%COMP%_dot-pulse-selected 1.5s infinite alternate}@keyframes _ngcontent-%COMP%_dot-pulse-selected{0%{opacity:.6;transform:scale(1)}to{opacity:1;transform:scale(1.5)}}@keyframes _ngcontent-%COMP%_dot-pulse{0%{opacity:.4;transform:scale(.8)}to{opacity:1;transform:scale(1.2)}}.notification-checkbox[_ngcontent-%COMP%]{position:absolute;top:10px;left:10px;z-index:10}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1)}50%{transform:scale(1.1)}to{transform:scale(1)}}.futuristic-cancel-button[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;background-color:#96969633;color:#6d6870;border:none;border-radius:var(--border-radius-md);font-size:.875rem;font-weight:500;cursor:pointer;transition:all .3s ease;box-shadow:0 0 8px #96969633}.futuristic-cancel-button[_ngcontent-%COMP%]:hover{background-color:#9696964d;box-shadow:0 0 12px #9696964d;transform:translateY(-2px)}.dark[_nghost-%COMP%]   .futuristic-cancel-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-cancel-button[_ngcontent-%COMP%]{background-color:#64646433;color:#e0e0e0;box-shadow:0 0 8px #64646433}.notification-detail-section[_ngcontent-%COMP%]{margin-bottom:1.5rem;padding:1rem;background:rgba(255,255,255,.05);border-radius:12px;border:1px solid rgba(255,255,255,.1)}.dark[_nghost-%COMP%]   .notification-detail-section[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-detail-section[_ngcontent-%COMP%]{background:rgba(0,247,255,.05);border:1px solid rgba(0,247,255,.1)}.notification-detail-title[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:1rem;font-weight:600;color:#4f5fad;margin-bottom:.75rem}.dark[_nghost-%COMP%]   .notification-detail-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-detail-title[_ngcontent-%COMP%]{color:#00f7ff;text-shadow:0 0 6px rgba(0,247,255,.3)}.notification-sender-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem}.notification-sender-avatar[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;object-fit:cover;border:2px solid rgba(79,95,173,.3)}.dark[_nghost-%COMP%]   .notification-sender-avatar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-sender-avatar[_ngcontent-%COMP%]{border:2px solid rgba(0,247,255,.3)}.notification-sender-details[_ngcontent-%COMP%]{display:flex;flex-direction:column}.notification-sender-name[_ngcontent-%COMP%]{font-weight:600;color:#4f5fad;font-size:1rem}.dark[_nghost-%COMP%]   .notification-sender-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-sender-name[_ngcontent-%COMP%]{color:#00f7ff}.notification-timestamp[_ngcontent-%COMP%]{font-size:.8rem;color:#a0a0a0}.notification-content-detail[_ngcontent-%COMP%]{background:rgba(79,95,173,.1);padding:.75rem;border-radius:8px;color:#333;line-height:1.5;border-left:3px solid #4f5fad}.dark[_nghost-%COMP%]   .notification-content-detail[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-content-detail[_ngcontent-%COMP%]{background:rgba(0,0,0,.2);color:#e0e0e0;border-left:3px solid #00f7ff}.notification-message-detail[_ngcontent-%COMP%]{margin-top:.75rem;padding:.75rem;background:rgba(79,95,173,.05);border-radius:8px;color:#333;font-size:.9rem}.dark[_nghost-%COMP%]   .notification-message-detail[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-message-detail[_ngcontent-%COMP%]{background:rgba(0,247,255,.1);color:#e0e0e0}.notification-info-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr;gap:.5rem}.notification-info-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:.5rem;background:rgba(79,95,173,.05);border-radius:6px}.dark[_nghost-%COMP%]   .notification-info-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-info-item[_ngcontent-%COMP%]{background:rgba(0,0,0,.2)}.notification-info-label[_ngcontent-%COMP%]{font-weight:500;color:#666}.dark[_nghost-%COMP%]   .notification-info-label[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-info-label[_ngcontent-%COMP%]{color:#a0a0a0}.notification-info-value[_ngcontent-%COMP%]{font-weight:600;color:#333}.dark[_nghost-%COMP%]   .notification-info-value[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-info-value[_ngcontent-%COMP%]{color:#e0e0e0}.notification-attachments-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr;gap:.75rem}.notification-attachment-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;padding:.75rem;background:rgba(79,95,173,.05);border-radius:8px;border:1px solid rgba(79,95,173,.1);transition:all .3s ease}.dark[_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%]{background:rgba(0,0,0,.2);border:1px solid rgba(255,255,255,.1)}.notification-attachment-item[_ngcontent-%COMP%]:hover{background:rgba(79,95,173,.1);border-color:#4f5fad4d}.dark[_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%]:hover{background:rgba(0,247,255,.1);border-color:#00f7ff4d}.notification-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:48px;height:48px;object-fit:cover;border-radius:6px;cursor:pointer;transition:transform .3s ease}.notification-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover{transform:scale(1.1)}.notification-attachment-icon[_ngcontent-%COMP%]{width:48px;height:48px;display:flex;align-items:center;justify-content:center;background:rgba(79,95,173,.2);border-radius:6px;font-size:1.5rem;color:#4f5fad}.dark[_nghost-%COMP%]   .notification-attachment-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-icon[_ngcontent-%COMP%]{background:rgba(0,247,255,.2);color:#00f7ff}.notification-attachment-info[_ngcontent-%COMP%]{flex:1;min-width:0}.notification-attachment-name[_ngcontent-%COMP%]{font-weight:600;color:#333;margin-bottom:.25rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.dark[_nghost-%COMP%]   .notification-attachment-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-name[_ngcontent-%COMP%]{color:#e0e0e0}.notification-attachment-meta[_ngcontent-%COMP%]{display:flex;gap:.5rem;font-size:.8rem;color:#666}.dark[_nghost-%COMP%]   .notification-attachment-meta[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-meta[_ngcontent-%COMP%]{color:#a0a0a0}.notification-attachment-actions[_ngcontent-%COMP%]{display:flex;gap:.25rem}@keyframes _ngcontent-%COMP%_fluoro-pulse{0%{text-shadow:0 0 5px rgba(0,255,255,.8),0 0 10px rgba(0,255,255,.6),0 0 15px rgba(0,255,255,.4);box-shadow:0 2px 10px #0ff9,0 0 20px #00ffff4d,inset 0 0 10px #0ff3;border-color:#0ffc}to{text-shadow:0 0 8px rgba(0,255,255,1),0 0 15px rgba(0,255,255,.8),0 0 25px rgba(0,255,255,.6);box-shadow:0 2px 15px #0ffc,0 0 30px #00ffff80,inset 0 0 15px #00ffff4d;border-color:#0ff}}@keyframes _ngcontent-%COMP%_fluoro-pulse-intense{0%{text-shadow:0 0 8px rgba(255,255,255,1),0 0 15px rgba(0,255,255,.9),0 0 25px rgba(0,255,255,.7);box-shadow:0 4px 20px #0ffc,0 0 30px #00ffff80,inset 0 0 15px #00ffff4d;transform:translateY(-2px) scale(1.05)}to{text-shadow:0 0 12px rgba(255,255,255,1),0 0 20px rgba(0,255,255,1),0 0 35px rgba(0,255,255,.9);box-shadow:0 6px 25px #0ff,0 0 40px #00ffffb3,inset 0 0 20px #0ff6;transform:translateY(-3px) scale(1.08)}}']})}}return e})();const ht=[{path:"",component:s(8076).Z,data:{context:"notifications"},children:[{path:"",component:pt,data:{title:"Notifications"}}]}];let mt=(()=>{class e{static{this.\u0275fac=function(o){return new(o||e)}}static{this.\u0275mod=t.$C({type:e})}static{this.\u0275inj=t.G2t({imports:[h.iI.forChild(ht),h.iI]})}}return e})(),bt=(()=>{class e{static{this.\u0275fac=function(o){return new(o||e)}}static{this.\u0275mod=t.$C({type:e})}static{this.\u0275inj=t.G2t({providers:[C.b],imports:[u.MD,mt,h.iI]})}}return e})()},152:(N,M,s)=>{s.d(M,{B:()=>v});var u=s(3236),h=s(9974),k=s(4360);function v(b,g=u.E){return(0,h.N)((d,f)=>{let l=null,m=null,O=null;const _=()=>{if(l){l.unsubscribe(),l=null;const t=m;m=null,f.next(t)}};function P(){const t=O+b,C=g.now();if(C<t)return l=this.schedule(void 0,t-C),void f.add(l);_()}d.subscribe((0,k._)(f,t=>{m=t,O=g.now(),l||(l=g.schedule(P,b),f.add(l))},()=>{_(),f.complete()},void 0,()=>{m=l=null}))})}}}]);