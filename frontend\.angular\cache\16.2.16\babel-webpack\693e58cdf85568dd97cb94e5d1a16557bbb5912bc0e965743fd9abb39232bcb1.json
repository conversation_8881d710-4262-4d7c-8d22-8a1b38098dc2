{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { CallType } from '../../../../models/message.model';\nexport let MessageChatComponent = class MessageChatComponent {\n  constructor(fb, route, MessageService, toastService, cdr) {\n    this.fb = fb;\n    this.route = route;\n    this.MessageService = MessageService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    // === DONNÉES PRINCIPALES ===\n    this.conversation = null;\n    this.messages = [];\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    // === ÉTATS DE L'INTERFACE ===\n    this.isLoading = false;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.searchMode = false;\n    this.isSendingMessage = false;\n    this.otherUserIsTyping = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n    this.contextMenuPosition = {\n      x: 0,\n      y: 0\n    };\n    this.showReactionPicker = false;\n    this.reactionPickerMessage = null;\n    this.isDarkMode = false;\n    this.currentTheme = 'light'; // 'light', 'dark', 'blue', 'pink'\n    this.themes = [{\n      id: 'light',\n      name: 'Clair',\n      icon: 'fas fa-sun',\n      color: '#f59e0b'\n    }, {\n      id: 'dark',\n      name: 'Sombre',\n      icon: 'fas fa-moon',\n      color: '#6b7280'\n    }, {\n      id: 'blue',\n      name: 'Bleu',\n      icon: 'fas fa-water',\n      color: '#3b82f6'\n    }, {\n      id: 'pink',\n      name: 'Rose',\n      icon: 'fas fa-heart',\n      color: '#ec4899'\n    }];\n    this.showThemeSelector = false;\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    this.uploadProgress = 0;\n    this.isUploading = false;\n    this.isDragOver = false;\n    // === GESTION VOCALE OPTIMISÉE ===\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.recordingTimer = null;\n    this.voiceWaves = [4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8];\n    // Lecture des messages vocaux\n    this.currentAudio = null;\n    this.playingMessageId = null;\n    this.voicePlayback = {};\n    // === APPELS WEBRTC ===\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // État de l'appel WebRTC\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    // === ÉMOJIS ===\n    this.emojiCategories = [{\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [{\n        emoji: '😀',\n        name: 'grinning face'\n      }, {\n        emoji: '😃',\n        name: 'grinning face with big eyes'\n      }, {\n        emoji: '😄',\n        name: 'grinning face with smiling eyes'\n      }, {\n        emoji: '😁',\n        name: 'beaming face with smiling eyes'\n      }, {\n        emoji: '😆',\n        name: 'grinning squinting face'\n      }, {\n        emoji: '😅',\n        name: 'grinning face with sweat'\n      }, {\n        emoji: '😂',\n        name: 'face with tears of joy'\n      }, {\n        emoji: '🤣',\n        name: 'rolling on the floor laughing'\n      }, {\n        emoji: '😊',\n        name: 'smiling face with smiling eyes'\n      }, {\n        emoji: '😇',\n        name: 'smiling face with halo'\n      }]\n    }, {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [{\n        emoji: '👶',\n        name: 'baby'\n      }, {\n        emoji: '🧒',\n        name: 'child'\n      }, {\n        emoji: '👦',\n        name: 'boy'\n      }, {\n        emoji: '👧',\n        name: 'girl'\n      }, {\n        emoji: '🧑',\n        name: 'person'\n      }, {\n        emoji: '👨',\n        name: 'man'\n      }, {\n        emoji: '👩',\n        name: 'woman'\n      }, {\n        emoji: '👴',\n        name: 'old man'\n      }, {\n        emoji: '👵',\n        name: 'old woman'\n      }]\n    }, {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [{\n        emoji: '🐶',\n        name: 'dog face'\n      }, {\n        emoji: '🐱',\n        name: 'cat face'\n      }, {\n        emoji: '🐭',\n        name: 'mouse face'\n      }, {\n        emoji: '🐹',\n        name: 'hamster'\n      }, {\n        emoji: '🐰',\n        name: 'rabbit face'\n      }, {\n        emoji: '🦊',\n        name: 'fox'\n      }, {\n        emoji: '🐻',\n        name: 'bear'\n      }, {\n        emoji: '🐼',\n        name: 'panda'\n      }]\n    }];\n    this.selectedEmojiCategory = this.emojiCategories[0];\n    // === PAGINATION ===\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.currentPage = 1;\n    // === AUTRES ÉTATS ===\n    this.isTyping = false;\n    this.isUserTyping = false;\n    this.typingTimeout = null;\n    this.subscriptions = new Subscription();\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\n  isInputDisabled() {\n    return !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage;\n  }\n  // Méthode pour gérer l'état du contrôle de saisie\n  updateInputState() {\n    const contentControl = this.messageForm.get('content');\n    if (this.isInputDisabled()) {\n      contentControl?.disable();\n    } else {\n      contentControl?.enable();\n    }\n  }\n  ngOnInit() {\n    console.log('🚀 MessageChatComponent initialized');\n    this.initializeTheme();\n    this.initializeComponent();\n  }\n  initializeTheme() {\n    // Charger le thème sauvegardé ou utiliser 'light' par défaut\n    const savedTheme = localStorage.getItem('currentTheme') || 'light';\n    this.currentTheme = savedTheme;\n    this.isDarkMode = savedTheme === 'dark';\n    this.applyTheme(savedTheme);\n  }\n  // === GESTION DES THÈMES OPTIMISÉE ===\n  applyTheme(themeId) {\n    const html = document.documentElement;\n    // Supprimer toutes les classes de thème existantes\n    html.classList.remove('dark', 'theme-light', 'theme-dark', 'theme-blue', 'theme-pink');\n    // Appliquer le nouveau thème\n    html.classList.add(`theme-${themeId}`);\n    if (themeId === 'dark') {\n      html.classList.add('dark');\n    }\n  }\n  getCurrentTheme() {\n    return this.themes.find(t => t.id === this.currentTheme);\n  }\n  toggleThemeSelector() {\n    this.showThemeSelector = !this.showThemeSelector;\n  }\n  selectTheme(themeId) {\n    this.currentTheme = themeId;\n    this.isDarkMode = themeId === 'dark';\n    this.applyTheme(themeId);\n    localStorage.setItem('currentTheme', themeId);\n    this.showThemeSelector = false;\n    this.showMainMenu = false;\n    const themeName = this.themes.find(t => t.id === themeId)?.name || themeId;\n    this.toastService.showSuccess(`Thème ${themeName} appliqué`);\n  }\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupCallSubscriptions();\n  }\n  setupCallSubscriptions() {\n    // S'abonner aux appels entrants\n    this.subscriptions.add(this.MessageService.incomingCall$.subscribe({\n      next: incomingCall => {\n        if (incomingCall) {\n          console.log('📞 Incoming call received:', incomingCall);\n          this.handleIncomingCall(incomingCall);\n        }\n      },\n      error: error => {\n        console.error('❌ Error in incoming call subscription:', error);\n      }\n    }));\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(this.MessageService.activeCall$.subscribe({\n      next: call => {\n        if (call) {\n          console.log('📞 Active call updated:', call);\n          this.activeCall = call;\n        }\n      },\n      error: error => {\n        console.error('❌ Error in active call subscription:', error);\n      }\n    }));\n  }\n  handleIncomingCall(incomingCall) {\n    // Afficher une notification ou modal d'appel entrant\n    // Pour l'instant, on log juste\n    console.log('🔔 Handling incoming call from:', incomingCall.caller.username);\n    // Jouer la sonnerie\n    this.MessageService.play('ringtone');\n    // Ici on pourrait afficher une modal ou notification\n    // Pour l'instant, on accepte automatiquement pour tester\n    // this.acceptCall(incomingCall);\n  }\n\n  loadCurrentUser() {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('🔍 Raw user from localStorage:', userString);\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n      const user = JSON.parse(userString);\n      console.log('🔍 Parsed user object:', user);\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      console.log('🔍 Trying to extract user ID:', {\n        _id: user._id,\n        id: user.id,\n        userId: user.userId,\n        extracted: userId\n      });\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n        console.log('✅ Current user loaded successfully:', {\n          id: this.currentUserId,\n          username: this.currentUsername\n        });\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n  loadConversation() {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: conversation => {\n        console.log('🔍 Conversation loaded successfully:', conversation);\n        console.log('🔍 Conversation structure:', {\n          id: conversation?.id,\n          participants: conversation?.participants,\n          participantsCount: conversation?.participants?.length,\n          isGroup: conversation?.isGroup,\n          messages: conversation?.messages,\n          messagesCount: conversation?.messages?.length\n        });\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n        // Configurer les subscriptions temps réel après le chargement de la conversation\n        this.setupSubscriptions();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError('Erreur lors du chargement de la conversation');\n        this.isLoading = false;\n      }\n    });\n  }\n  setOtherParticipant() {\n    if (!this.conversation?.participants || this.conversation.participants.length === 0) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n    console.log('Setting other participant...');\n    console.log('Current user ID:', this.currentUserId);\n    console.log('All participants:', this.conversation.participants);\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        console.log('Comparing participant ID:', participantId, 'with current user ID:', this.currentUserId);\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      console.log('Fallback: using first participant');\n      this.otherParticipant = this.conversation.participants[0];\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId = this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log('First participant is current user, using second participant');\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      console.log('✅ Other participant set successfully:', {\n        id: this.otherParticipant.id || this.otherParticipant._id,\n        username: this.otherParticipant.username,\n        image: this.otherParticipant.image,\n        isOnline: this.otherParticipant.isOnline\n      });\n      // Log très visible pour debug\n      console.log('🎯 FINAL RESULT: otherParticipant =', this.otherParticipant.username);\n      console.log('🎯 Should display in sidebar:', this.otherParticipant.username);\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      console.log('Conversation participants:', this.conversation.participants);\n      console.log('Current user ID:', this.currentUserId);\n      // Log très visible pour debug\n      console.log('🚨 ERROR: No otherParticipant found!');\n    }\n    // Mettre à jour l'état du champ de saisie\n    this.updateInputState();\n  }\n  loadMessages() {\n    if (!this.conversation?.id) return;\n    // Les messages sont déjà chargés avec la conversation\n    let messages = this.conversation.messages || [];\n    // Trier les messages par timestamp (plus anciens en premier)\n    this.messages = messages.sort((a, b) => {\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\n    });\n\n    console.log('📋 Messages loaded and sorted:', {\n      total: this.messages.length,\n      first: this.messages[0]?.content,\n      last: this.messages[this.messages.length - 1]?.content\n    });\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id) return;\n    this.isLoadingMore = true;\n    this.currentPage++;\n    // Calculer l'offset basé sur les messages déjà chargés\n    const offset = this.messages.length;\n    this.MessageService.getMessages(this.currentUserId,\n    // senderId\n    this.otherParticipant?.id || this.otherParticipant?._id,\n    // receiverId\n    this.conversation.id, this.currentPage, this.MAX_MESSAGES_TO_LOAD).subscribe({\n      next: newMessages => {\n        if (newMessages && newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste\n          this.messages = [...newMessages.reverse(), ...this.messages];\n          this.hasMoreMessages = newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      }\n    });\n  }\n\n  setupSubscriptions() {\n    if (!this.conversation?.id) {\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\n      return;\n    }\n    console.log('🔄 Setting up real-time subscriptions for conversation:', this.conversation.id);\n    // Subscription pour les nouveaux messages\n    console.log('📨 Setting up message subscription...');\n    this.subscriptions.add(this.MessageService.subscribeToNewMessages(this.conversation.id).subscribe({\n      next: newMessage => {\n        console.log('📨 New message received via subscription:', newMessage);\n        console.log('📨 Message structure:', {\n          id: newMessage.id,\n          type: newMessage.type,\n          content: newMessage.content,\n          sender: newMessage.sender,\n          senderId: newMessage.senderId,\n          receiverId: newMessage.receiverId,\n          attachments: newMessage.attachments\n        });\n        // Debug des attachments\n        console.log('📨 [Debug] Message type detected:', this.getMessageType(newMessage));\n        console.log('📨 [Debug] Has image:', this.hasImage(newMessage));\n        console.log('📨 [Debug] Has file:', this.hasFile(newMessage));\n        console.log('📨 [Debug] Image URL:', this.getImageUrl(newMessage));\n        if (newMessage.attachments) {\n          newMessage.attachments.forEach((att, index) => {\n            console.log(`📨 [Debug] Attachment ${index}:`, {\n              type: att.type,\n              url: att.url,\n              path: att.path,\n              name: att.name,\n              size: att.size\n            });\n          });\n        }\n        // Ajouter le message à la liste s'il n'existe pas déjà\n        const messageExists = this.messages.some(msg => msg.id === newMessage.id);\n        if (!messageExists) {\n          // Ajouter le nouveau message à la fin (en bas)\n          this.messages.push(newMessage);\n          console.log('✅ Message added to list, total messages:', this.messages.length);\n          // Forcer la détection de changements\n          this.cdr.detectChanges();\n          // Scroll vers le bas après un court délai\n          setTimeout(() => {\n            this.scrollToBottom();\n          }, 50);\n          // Marquer comme lu si ce n'est pas notre message\n          const senderId = newMessage.sender?.id || newMessage.senderId;\n          console.log('📨 Checking if message should be marked as read:', {\n            senderId,\n            currentUserId: this.currentUserId,\n            shouldMarkAsRead: senderId !== this.currentUserId\n          });\n          if (senderId && senderId !== this.currentUserId) {\n            this.markMessageAsRead(newMessage.id);\n          }\n        }\n      },\n      error: error => {\n        console.error('❌ Error in message subscription:', error);\n      }\n    }));\n    // Subscription pour les indicateurs de frappe\n    console.log('📝 Setting up typing indicator subscription...');\n    this.subscriptions.add(this.MessageService.subscribeToTypingIndicator(this.conversation.id).subscribe({\n      next: typingData => {\n        console.log('📝 Typing indicator received:', typingData);\n        // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n        if (typingData.userId !== this.currentUserId) {\n          this.otherUserIsTyping = typingData.isTyping;\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in typing subscription:', error);\n      }\n    }));\n    // Subscription pour les mises à jour de conversation\n    this.subscriptions.add(this.MessageService.subscribeToConversationUpdates(this.conversation.id).subscribe({\n      next: conversationUpdate => {\n        console.log('📋 Conversation update:', conversationUpdate);\n        // Mettre à jour la conversation si nécessaire\n        if (conversationUpdate.id === this.conversation.id) {\n          this.conversation = {\n            ...this.conversation,\n            ...conversationUpdate\n          };\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in conversation subscription:', error);\n      }\n    }));\n  }\n  markMessageAsRead(messageId) {\n    this.MessageService.markMessageAsRead(messageId).subscribe({\n      next: () => {\n        console.log('✅ Message marked as read:', messageId);\n      },\n      error: error => {\n        console.error('❌ Error marking message as read:', error);\n      }\n    });\n  }\n  // === ENVOI DE MESSAGES ===\n  sendMessage() {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    // Désactiver le bouton d'envoi\n    this.isSendingMessage = true;\n    this.updateInputState();\n    console.log('📤 Sending message:', {\n      content,\n      receiverId,\n      conversationId: this.conversation.id\n    });\n    this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({\n      next: message => {\n        console.log('✅ Message sent successfully:', message);\n        // Ajouter le message à la liste s'il n'y est pas déjà\n        const messageExists = this.messages.some(msg => msg.id === message.id);\n        if (!messageExists) {\n          this.messages.push(message);\n          console.log('📋 Message added to local list, total:', this.messages.length);\n        }\n        // Réinitialiser le formulaire\n        this.messageForm.reset();\n        this.isSendingMessage = false;\n        this.updateInputState();\n        // Forcer la détection de changements et scroll\n        this.cdr.detectChanges();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 50);\n      },\n      error: error => {\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        this.isSendingMessage = false;\n        this.updateInputState();\n      }\n    });\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const diffMins = Math.floor((Date.now() - new Date(lastActive).getTime()) / 60000);\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\n  }\n  // Méthodes utilitaires pour les messages vocaux\n  getVoicePlaybackData(messageId) {\n    return this.voicePlayback[messageId] || {\n      progress: 0,\n      duration: 0,\n      currentTime: 0,\n      speed: 1\n    };\n  }\n  setVoicePlaybackData(messageId, data) {\n    this.voicePlayback[messageId] = {\n      ...this.getVoicePlaybackData(messageId),\n      ...data\n    };\n  }\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // isVoicePlaying, playVoiceMessage, toggleVoicePlayback - définies plus loin\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n  startVideoCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    this.callType = 'VIDEO';\n    this.isInCall = true;\n    console.log('📹 Starting video call with:', this.otherParticipant.username);\n  }\n  startVoiceCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    this.callType = 'AUDIO';\n    this.isInCall = true;\n    console.log('📞 Starting voice call with:', this.otherParticipant.username);\n  }\n  endCall() {\n    this.isInCall = false;\n    this.callType = null;\n    this.activeCall = null;\n    console.log('📞 Call ended');\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // onCallAccepted, onCallRejected - définies plus loin\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n  formatFileSize(bytes) {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  downloadFile(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (fileAttachment?.url) {\n      const link = document.createElement('a');\n      link.href = fileAttachment.url;\n      link.download = fileAttachment.name || 'file';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n  toggleTheme() {\n    const newTheme = this.isDarkMode ? 'light' : 'dark';\n    this.selectTheme(newTheme);\n  }\n  toggleSearch() {\n    this.searchMode = !this.searchMode;\n    this.showSearch = this.searchMode;\n  }\n  toggleMainMenu() {\n    this.showMainMenu = !this.showMainMenu;\n  }\n  goBackToConversations() {\n    // Navigation vers la liste des conversations\n    console.log('🔙 Going back to conversations');\n  }\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.showReactionPicker = false;\n    this.showThemeSelector = false;\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    this.selectedMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showMessageContextMenu = true;\n  }\n  showQuickReactions(message, event) {\n    event.stopPropagation();\n    this.reactionPickerMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showReactionPicker = true;\n  }\n  quickReact(emoji) {\n    if (this.reactionPickerMessage) {\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\n    }\n    this.showReactionPicker = false;\n  }\n  toggleReaction(messageId, emoji) {\n    console.log('👍 Toggling reaction:', emoji, 'for message:', messageId);\n    // Implémentation de la réaction\n  }\n\n  hasUserReacted(reaction, userId) {\n    return reaction.userId === userId;\n  }\n  replyToMessage(message) {\n    console.log('↩️ Replying to message:', message.id);\n    this.closeAllMenus();\n  }\n  forwardMessage(message) {\n    console.log('➡️ Forwarding message:', message.id);\n    this.closeAllMenus();\n  }\n  deleteMessage(message) {\n    console.log('🗑️ Deleting message:', message.id);\n    this.closeAllMenus();\n  }\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n  selectEmojiCategory(category) {\n    this.selectedEmojiCategory = category;\n  }\n  getEmojisForCategory(category) {\n    return category?.emojis || [];\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.showEmojiPicker = false;\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODES POUR LA GESTION DE LA FRAPPE ===\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // handleTypingIndicator - définie plus loin\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n  trackByMessageId(index, message) {\n    return message.id || message._id || index.toString();\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  testAddMessage() {\n    console.log('🧪 Test: Adding message');\n    const testMessage = {\n      id: `test-${Date.now()}`,\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\n      timestamp: new Date().toISOString(),\n      sender: {\n        id: this.otherParticipant?.id || 'test-user',\n        username: this.otherParticipant?.username || 'Test User',\n        image: this.otherParticipant?.image || 'assets/images/default-avatar.png'\n      },\n      type: 'TEXT',\n      isRead: false\n    };\n    this.messages.push(testMessage);\n    this.cdr.detectChanges();\n    setTimeout(() => this.scrollToBottom(), 50);\n  }\n  isGroupConversation() {\n    return this.conversation?.isGroup || this.conversation?.participants?.length > 2 || false;\n  }\n  openCamera() {\n    console.log('📷 Opening camera');\n    this.showAttachmentMenu = false;\n    // TODO: Implémenter l'ouverture de la caméra\n  }\n\n  zoomImage(factor) {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      const currentTransform = imageElement.style.transform || 'scale(1)';\n      const currentScale = parseFloat(currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1');\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n      imageElement.style.transform = `scale(${newScale})`;\n      if (newScale > 1) {\n        imageElement.classList.add('zoomed');\n      } else {\n        imageElement.classList.remove('zoomed');\n      }\n    }\n  }\n  resetZoom() {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      imageElement.style.transform = 'scale(1)';\n      imageElement.classList.remove('zoomed');\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  triggerFileInput(type) {\n    const input = this.fileInput?.nativeElement;\n    if (!input) {\n      console.error('File input element not found');\n      return;\n    }\n    // Configurer le type de fichier accepté\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    } else {\n      input.accept = '*/*';\n    }\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\n    input.value = '';\n    // Déclencher la sélection de fichier\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n  formatMessageContent(content) {\n    if (!content) return '';\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(urlRegex, '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>');\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  shouldShowAvatar(index) {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    if (!nextMessage) return true;\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n  shouldShowSenderName(index) {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!previousMessage) return true;\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n  getMessageType(message) {\n    // Vérifier d'abord le type de message explicite\n    if (message.type) {\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\n    }\n    // Ensuite vérifier les attachments\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    // Vérifier si c'est un message vocal basé sur les propriétés\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n    return 'text';\n  }\n  hasImage(message) {\n    console.log('🖼️ [Debug] hasImage called for message:', message.id, {\n      type: message.type,\n      attachments: message.attachments,\n      imageUrl: message.imageUrl,\n      image: message.image\n    });\n    // Vérifier le type de message\n    if (message.type === 'IMAGE' || message.type === 'image') {\n      console.log('🖼️ [Debug] Message type is IMAGE');\n      return true;\n    }\n    // Vérifier les attachments\n    const hasImageAttachment = message.attachments?.some(att => {\n      const isImage = att.type?.startsWith('image/') || att.type === 'IMAGE';\n      console.log('🖼️ [Debug] Checking attachment:', att, 'isImage:', isImage);\n      return isImage;\n    }) || false;\n    // Vérifier les propriétés directes d'image\n    const hasImageUrl = !!(message.imageUrl || message.image);\n    const result = hasImageAttachment || hasImageUrl;\n    console.log('🖼️ [Debug] hasImage result:', result, {\n      hasImageAttachment,\n      hasImageUrl\n    });\n    return result;\n  }\n  hasFile(message) {\n    console.log('📁 [Debug] hasFile called for message:', message.id, {\n      type: message.type,\n      attachments: message.attachments\n    });\n    // Vérifier le type de message\n    if (message.type === 'FILE' || message.type === 'file') {\n      console.log('📁 [Debug] Message type is FILE');\n      return true;\n    }\n    // Vérifier les attachments non-image\n    const hasFileAttachment = message.attachments?.some(att => {\n      const isFile = !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n      console.log('📁 [Debug] Checking attachment:', att, 'isFile:', isFile);\n      return isFile;\n    }) || false;\n    console.log('📁 [Debug] hasFile result:', hasFileAttachment);\n    return hasFileAttachment;\n  }\n  getImageUrl(message) {\n    console.log('🖼️ [Debug] getImageUrl called for message:', message.id);\n    // Vérifier les propriétés directes d'image\n    if (message.imageUrl) {\n      console.log('🖼️ [Debug] Found imageUrl:', message.imageUrl);\n      return message.imageUrl;\n    }\n    if (message.image) {\n      console.log('🖼️ [Debug] Found image:', message.image);\n      return message.image;\n    }\n    // Vérifier les attachments\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/') || att.type === 'IMAGE');\n    console.log('🖼️ [Debug] Found image attachment:', imageAttachment);\n    if (imageAttachment) {\n      const url = imageAttachment.url || imageAttachment.path || '';\n      console.log('🖼️ [Debug] Image attachment URL (full):', url);\n      console.log('🖼️ [Debug] Image attachment object:', JSON.stringify(imageAttachment, null, 2));\n      return url;\n    }\n    console.log('🖼️ [Debug] No image URL found, returning empty string');\n    return '';\n  }\n  getFileName(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    return fileAttachment?.name || 'Fichier';\n  }\n  getFileSize(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.size) return '';\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  getFileIcon(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.type) return 'fas fa-file';\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n  getUserColor(userId) {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message, event) {\n    console.log('Message clicked:', message);\n  }\n  onInputChange(event) {\n    // Gérer les changements dans le champ de saisie\n    this.handleTypingIndicator();\n  }\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  onInputFocus() {\n    // Gérer le focus sur le champ de saisie\n  }\n  onInputBlur() {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n  onScroll(event) {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {\n      this.loadMoreMessages();\n    }\n  }\n  openUserProfile(userId) {\n    console.log('Opening user profile for:', userId);\n  }\n  onImageLoad(event, message) {\n    console.log('🖼️ [Debug] Image loaded successfully for message:', message.id, event.target.src);\n  }\n  onImageError(event, message) {\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n      src: event.target.src,\n      error: event\n    });\n    // Optionnel : afficher une image de remplacement\n    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n  }\n  openImageViewer(message) {\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));\n    if (imageAttachment?.url) {\n      this.selectedImage = {\n        url: imageAttachment.url,\n        name: imageAttachment.name || 'Image',\n        size: this.formatFileSize(imageAttachment.size || 0),\n        message: message\n      };\n      this.showImageViewer = true;\n      console.log('🖼️ [ImageViewer] Opening image:', this.selectedImage);\n    }\n  }\n  closeImageViewer() {\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    console.log('🖼️ [ImageViewer] Closed');\n  }\n  downloadImage() {\n    if (this.selectedImage?.url) {\n      const link = document.createElement('a');\n      link.href = this.selectedImage.url;\n      link.download = this.selectedImage.name || 'image';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n      console.log('🖼️ [ImageViewer] Download started:', this.selectedImage.name);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  searchMessages() {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n    this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()) || message.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n  onSearchQueryChange() {\n    this.searchMessages();\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n  }\n  jumpToMessage(messageId) {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      // Highlight temporairement le message\n      messageElement.classList.add('highlight');\n      setTimeout(() => {\n        messageElement.classList.remove('highlight');\n      }, 2000);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  closeContextMenu() {\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // triggerFileInput - définie plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n  // goBackToConversations, startVideoCall, startVoiceCall\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  initiateCall(callType) {\n    if (!this.otherParticipant) {\n      this.toastService.showError('Aucun destinataire sélectionné');\n      return;\n    }\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n    if (!recipientId) {\n      this.toastService.showError('ID du destinataire introuvable');\n      return;\n    }\n    console.log(`🔄 Initiating ${callType} call to user:`, recipientId);\n    this.isInCall = true;\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n    this.callDuration = 0;\n    // Démarrer le timer d'appel\n    this.startCallTimer();\n    // Utiliser le vrai service WebRTC\n    this.MessageService.initiateCall(recipientId, callType, this.conversation?.id).subscribe({\n      next: call => {\n        console.log('✅ Call initiated successfully:', call);\n        this.activeCall = call;\n        this.isCallConnected = false;\n        this.toastService.showSuccess(`Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`);\n      },\n      error: error => {\n        console.error('❌ Error initiating call:', error);\n        this.endCall();\n        this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n      }\n    });\n  }\n  acceptCall(incomingCall) {\n    console.log('🔄 Accepting incoming call:', incomingCall);\n    this.MessageService.acceptCall(incomingCall).subscribe({\n      next: call => {\n        console.log('✅ Call accepted successfully:', call);\n        this.activeCall = call;\n        this.isInCall = true;\n        this.isCallConnected = true;\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n        this.startCallTimer();\n        this.toastService.showSuccess('Appel accepté');\n      },\n      error: error => {\n        console.error('❌ Error accepting call:', error);\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\n      }\n    });\n  }\n  rejectCall(incomingCall) {\n    console.log('🔄 Rejecting incoming call:', incomingCall);\n    this.MessageService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n      next: () => {\n        console.log('✅ Call rejected successfully');\n        this.toastService.showSuccess('Appel rejeté');\n      },\n      error: error => {\n        console.error('❌ Error rejecting call:', error);\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n      }\n    });\n  }\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // endCall - Cette méthode est déjà définie plus loin dans le fichier\n  startCallTimer() {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n  resetCallState() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n  }\n  // === CONTRÔLES D'APPEL ===\n  toggleMute() {\n    if (!this.activeCall) return;\n    this.isMuted = !this.isMuted;\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(this.activeCall.id, undefined,\n    // video unchanged\n    !this.isMuted // audio state\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isMuted ? 'Micro coupé' : 'Micro activé');\n      },\n      error: error => {\n        console.error('❌ Error toggling mute:', error);\n        // Revert state on error\n        this.isMuted = !this.isMuted;\n        this.toastService.showError('Erreur lors du changement du micro');\n      }\n    });\n  }\n  toggleVideo() {\n    if (!this.activeCall) return;\n    this.isVideoEnabled = !this.isVideoEnabled;\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(this.activeCall.id, this.isVideoEnabled,\n    // video state\n    undefined // audio unchanged\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      error: error => {\n        console.error('❌ Error toggling video:', error);\n        // Revert state on error\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.toastService.showError('Erreur lors du changement de la caméra');\n      }\n    });\n  }\n  formatCallDuration(duration) {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor(duration % 3600 / 60);\n    const seconds = duration % 60;\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎤 [Voice] Starting voice recording...');\n      try {\n        // Vérifier le support du navigateur\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n          throw new Error(\"Votre navigateur ne supporte pas l'enregistrement audio\");\n        }\n        // Vérifier si MediaRecorder est supporté\n        if (!window.MediaRecorder) {\n          throw new Error(\"MediaRecorder n'est pas supporté par votre navigateur\");\n        }\n        console.log('🎤 [Voice] Requesting microphone access...');\n        // Demander l'accès au microphone avec des contraintes optimisées\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true,\n            sampleRate: 44100,\n            channelCount: 1\n          }\n        });\n        console.log('🎤 [Voice] Microphone access granted');\n        // Vérifier les types MIME supportés\n        let mimeType = 'audio/webm;codecs=opus';\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\n          mimeType = 'audio/webm';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = 'audio/mp4';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n              mimeType = ''; // Laisser le navigateur choisir\n            }\n          }\n        }\n\n        console.log('🎤 [Voice] Using MIME type:', mimeType);\n        // Créer le MediaRecorder\n        _this.mediaRecorder = new MediaRecorder(stream, {\n          mimeType: mimeType || undefined\n        });\n        // Initialiser les variables\n        _this.audioChunks = [];\n        _this.isRecordingVoice = true;\n        _this.voiceRecordingDuration = 0;\n        _this.voiceRecordingState = 'recording';\n        console.log('🎤 [Voice] MediaRecorder created, starting timer...');\n        // Démarrer le timer\n        _this.recordingTimer = setInterval(() => {\n          _this.voiceRecordingDuration++;\n          // Animer les waves\n          _this.animateVoiceWaves();\n          _this.cdr.detectChanges();\n        }, 1000);\n        // Gérer les événements du MediaRecorder\n        _this.mediaRecorder.ondataavailable = event => {\n          console.log('🎤 [Voice] Data available:', event.data.size, 'bytes');\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        _this.mediaRecorder.onstop = () => {\n          console.log('🎤 [Voice] MediaRecorder stopped, processing audio...');\n          _this.processRecordedAudio();\n        };\n        _this.mediaRecorder.onerror = event => {\n          console.error('🎤 [Voice] MediaRecorder error:', event.error);\n          _this.toastService.showError(\"Erreur lors de l'enregistrement\");\n          _this.cancelVoiceRecording();\n        };\n        // Démarrer l'enregistrement\n        _this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n        console.log('🎤 [Voice] Recording started successfully');\n        _this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n      } catch (error) {\n        console.error('🎤 [Voice] Error starting recording:', error);\n        let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n        if (error.name === 'NotAllowedError') {\n          errorMessage = \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n        } else if (error.name === 'NotFoundError') {\n          errorMessage = 'Aucun microphone détecté. Veuillez connecter un microphone.';\n        } else if (error.name === 'NotSupportedError') {\n          errorMessage = \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n        _this.toastService.showError(errorMessage);\n        _this.cancelVoiceRecording();\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n  cancelVoiceRecording() {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      this.mediaRecorder = null;\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n  processRecordedAudio() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎤 [Voice] Processing recorded audio...');\n      try {\n        // Vérifier qu'on a des données audio\n        if (_this2.audioChunks.length === 0) {\n          console.error('🎤 [Voice] No audio chunks available');\n          _this2.toastService.showError('Aucun audio enregistré');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        console.log('🎤 [Voice] Audio chunks:', _this2.audioChunks.length, 'Duration:', _this2.voiceRecordingDuration);\n        // Vérifier la durée minimale\n        if (_this2.voiceRecordingDuration < 1) {\n          console.error('🎤 [Voice] Recording too short:', _this2.voiceRecordingDuration);\n          _this2.toastService.showError('Enregistrement trop court (minimum 1 seconde)');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        // Déterminer le type MIME du blob\n        let mimeType = 'audio/webm;codecs=opus';\n        if (_this2.mediaRecorder?.mimeType) {\n          mimeType = _this2.mediaRecorder.mimeType;\n        }\n        console.log('🎤 [Voice] Creating audio blob with MIME type:', mimeType);\n        // Créer le blob audio\n        const audioBlob = new Blob(_this2.audioChunks, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio blob created:', {\n          size: audioBlob.size,\n          type: audioBlob.type\n        });\n        // Déterminer l'extension du fichier\n        let extension = '.webm';\n        if (mimeType.includes('mp4')) {\n          extension = '.mp4';\n        } else if (mimeType.includes('wav')) {\n          extension = '.wav';\n        } else if (mimeType.includes('ogg')) {\n          extension = '.ogg';\n        }\n        // Créer le fichier\n        const audioFile = new File([audioBlob], `voice_${Date.now()}${extension}`, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio file created:', {\n          name: audioFile.name,\n          size: audioFile.size,\n          type: audioFile.type\n        });\n        // Envoyer le message vocal\n        _this2.voiceRecordingState = 'processing';\n        yield _this2.sendVoiceMessage(audioFile);\n        console.log('🎤 [Voice] Voice message sent successfully');\n        _this2.toastService.showSuccess('🎤 Message vocal envoyé');\n      } catch (error) {\n        console.error('🎤 [Voice] Error processing audio:', error);\n        _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal: \" + (error.message || 'Erreur inconnue'));\n      } finally {\n        // Nettoyer l'état\n        _this2.voiceRecordingState = 'idle';\n        _this2.voiceRecordingDuration = 0;\n        _this2.audioChunks = [];\n        _this2.isRecordingVoice = false;\n        console.log('🎤 [Voice] Audio processing completed, state reset');\n      }\n    })();\n  }\n  sendVoiceMessage(audioFile) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const receiverId = _this3.otherParticipant?.id || _this3.otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this3.MessageService.sendMessage(receiverId, '', audioFile, 'AUDIO', _this3.conversation.id).subscribe({\n          next: message => {\n            _this3.messages.push(message);\n            _this3.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n  onRecordStart(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record start triggered');\n    console.log('🎤 [Voice] Current state:', {\n      isRecordingVoice: this.isRecordingVoice,\n      voiceRecordingState: this.voiceRecordingState,\n      voiceRecordingDuration: this.voiceRecordingDuration,\n      mediaRecorder: !!this.mediaRecorder\n    });\n    // Vérifier si on peut enregistrer\n    if (this.voiceRecordingState === 'processing') {\n      console.log('🎤 [Voice] Already processing, ignoring start');\n      this.toastService.showWarning('Traitement en cours...');\n      return;\n    }\n    if (this.isRecordingVoice) {\n      console.log('🎤 [Voice] Already recording, ignoring start');\n      this.toastService.showWarning('Enregistrement déjà en cours...');\n      return;\n    }\n    // Afficher un message de début\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n    // Démarrer l'enregistrement\n    this.startVoiceRecording().catch(error => {\n      console.error('🎤 [Voice] Failed to start recording:', error);\n      this.toastService.showError(\"Impossible de démarrer l'enregistrement vocal: \" + (error.message || 'Erreur inconnue'));\n    });\n  }\n  onRecordEnd(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record end triggered');\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring end');\n      return;\n    }\n    // Arrêter l'enregistrement et envoyer\n    this.stopVoiceRecording();\n  }\n  onRecordCancel(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record cancel triggered');\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring cancel');\n      return;\n    }\n    // Annuler l'enregistrement\n    this.cancelVoiceRecording();\n  }\n  getRecordingFormat() {\n    if (this.mediaRecorder?.mimeType) {\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n    }\n    return 'Auto';\n  }\n  // === ANIMATION DES WAVES VOCALES ===\n  animateVoiceWaves() {\n    // Animer les waves pendant l'enregistrement\n    this.voiceWaves = this.voiceWaves.map(() => {\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n    });\n  }\n\n  onFileSelected(event) {\n    console.log('📁 [Upload] File selection triggered');\n    const files = event.target.files;\n    if (!files || files.length === 0) {\n      console.log('📁 [Upload] No files selected');\n      return;\n    }\n    console.log(`📁 [Upload] ${files.length} file(s) selected:`, files);\n    for (let file of files) {\n      console.log(`📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`);\n      this.uploadFile(file);\n    }\n  }\n  uploadFile(file) {\n    console.log(`📁 [Upload] Starting upload for file: ${file.name}`);\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      console.error('📁 [Upload] No receiver ID found');\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    console.log(`📁 [Upload] Receiver ID: ${receiverId}`);\n    // Vérifier la taille du fichier (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\n      return;\n    }\n    // 🖼️ Compression d'image si nécessaire\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n      // > 1MB\n      console.log('🖼️ [Compression] Compressing image:', file.name, 'Original size:', file.size);\n      this.compressImage(file).then(compressedFile => {\n        console.log('🖼️ [Compression] ✅ Image compressed successfully. New size:', compressedFile.size);\n        this.sendFileToServer(compressedFile, receiverId);\n      }).catch(error => {\n        console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n        // Envoyer le fichier original en cas d'erreur\n        this.sendFileToServer(file, receiverId);\n      });\n      return;\n    }\n    // Envoyer le fichier sans compression\n    this.sendFileToServer(file, receiverId);\n  }\n  sendFileToServer(file, receiverId) {\n    const messageType = this.getFileMessageType(file);\n    console.log(`📁 [Upload] Message type determined: ${messageType}`);\n    console.log(`📁 [Upload] Conversation ID: ${this.conversation.id}`);\n    this.isSendingMessage = true;\n    this.isUploading = true;\n    this.uploadProgress = 0;\n    console.log('📁 [Upload] Calling MessageService.sendMessage...');\n    // Simuler la progression d'upload\n    const progressInterval = setInterval(() => {\n      this.uploadProgress += Math.random() * 15;\n      if (this.uploadProgress >= 90) {\n        clearInterval(progressInterval);\n      }\n      this.cdr.detectChanges();\n    }, 300);\n    this.MessageService.sendMessage(receiverId, '', file, messageType, this.conversation.id).subscribe({\n      next: message => {\n        console.log('📁 [Upload] ✅ File sent successfully:', message);\n        console.log('📁 [Debug] Sent message structure:', {\n          id: message.id,\n          type: message.type,\n          attachments: message.attachments,\n          hasImage: this.hasImage(message),\n          hasFile: this.hasFile(message),\n          imageUrl: this.getImageUrl(message)\n        });\n        clearInterval(progressInterval);\n        this.uploadProgress = 100;\n        setTimeout(() => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Fichier envoyé avec succès');\n          this.resetUploadState();\n        }, 500);\n      },\n      error: error => {\n        console.error('📁 [Upload] ❌ Error sending file:', error);\n        clearInterval(progressInterval);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n        this.resetUploadState();\n      }\n    });\n  }\n  getFileMessageType(file) {\n    if (file.type.startsWith('image/')) return 'IMAGE';\n    if (file.type.startsWith('video/')) return 'VIDEO';\n    if (file.type.startsWith('audio/')) return 'AUDIO';\n    return 'FILE';\n  }\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  resetUploadState() {\n    this.isSendingMessage = false;\n    this.isUploading = false;\n    this.uploadProgress = 0;\n  }\n  // === DRAG & DROP ===\n  onDragOver(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n  onDragLeave(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\n    const rect = event.currentTarget.getBoundingClientRect();\n    const x = event.clientX;\n    const y = event.clientY;\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      this.isDragOver = false;\n    }\n  }\n  onDrop(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      console.log('📁 [Drag&Drop] Files dropped:', files.length);\n      // Traiter chaque fichier\n      Array.from(files).forEach(file => {\n        console.log('📁 [Drag&Drop] Processing file:', file.name, file.type, file.size);\n        this.uploadFile(file);\n      });\n      this.toastService.showSuccess(`${files.length} fichier(s) en cours d'envoi`);\n    }\n  }\n  // === COMPRESSION D'IMAGES ===\n  compressImage(file, quality = 0.8) {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n      img.onload = () => {\n        // Calculer les nouvelles dimensions (max 1920x1080)\n        const maxWidth = 1920;\n        const maxHeight = 1080;\n        let {\n          width,\n          height\n        } = img;\n        if (width > maxWidth || height > maxHeight) {\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\n          width *= ratio;\n          height *= ratio;\n        }\n        canvas.width = width;\n        canvas.height = height;\n        // Dessiner l'image redimensionnée\n        ctx?.drawImage(img, 0, 0, width, height);\n        // Convertir en blob avec compression\n        canvas.toBlob(blob => {\n          if (blob) {\n            const compressedFile = new File([blob], file.name, {\n              type: file.type,\n              lastModified: Date.now()\n            });\n            resolve(compressedFile);\n          } else {\n            reject(new Error('Failed to compress image'));\n          }\n        }, file.type, quality);\n      };\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  }\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n  handleTypingIndicator() {\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\n      this.sendTypingIndicator(true);\n    }\n    // Reset le timer\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Arrêter l'indicateur de frappe\n      this.sendTypingIndicator(false);\n    }, 2000);\n  }\n  sendTypingIndicator(isTyping) {\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (receiverId && this.conversation?.id) {\n      console.log(`📝 Sending typing indicator: ${isTyping} to user ${receiverId}`);\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n  onCallAccepted(call) {\n    console.log('🔄 Call accepted from interface:', call);\n    this.activeCall = call;\n    this.isInCall = true;\n    this.isCallConnected = true;\n    this.startCallTimer();\n    this.toastService.showSuccess('Appel accepté');\n  }\n  onCallRejected() {\n    console.log('🔄 Call rejected from interface');\n    this.endCall();\n    this.toastService.showInfo('Appel rejeté');\n  }\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n  playVoiceMessage(message) {\n    console.log('🎵 [Voice] Playing voice message:', message.id);\n    this.toggleVoicePlayback(message);\n  }\n  isVoicePlaying(messageId) {\n    return this.playingMessageId === messageId;\n  }\n  toggleVoicePlayback(message) {\n    const messageId = message.id;\n    const audioUrl = this.getVoiceUrl(message);\n    if (!audioUrl) {\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\n      this.toastService.showError('Fichier audio introuvable');\n      return;\n    }\n    // Si c'est déjà en cours de lecture, arrêter\n    if (this.isVoicePlaying(messageId)) {\n      this.stopVoicePlayback();\n      return;\n    }\n    // Arrêter toute autre lecture en cours\n    this.stopVoicePlayback();\n    // Démarrer la nouvelle lecture\n    this.startVoicePlayback(message, audioUrl);\n  }\n  startVoicePlayback(message, audioUrl) {\n    const messageId = message.id;\n    try {\n      console.log('🎵 [Voice] Starting playback for:', messageId, 'URL:', audioUrl);\n      this.currentAudio = new Audio(audioUrl);\n      this.playingMessageId = messageId;\n      // Initialiser les valeurs par défaut avec la nouvelle structure\n      const currentData = this.getVoicePlaybackData(messageId);\n      this.setVoicePlaybackData(messageId, {\n        progress: 0,\n        currentTime: 0,\n        speed: currentData.speed || 1,\n        duration: currentData.duration || 0\n      });\n      // Configurer la vitesse de lecture\n      this.currentAudio.playbackRate = currentData.speed || 1;\n      // Événements audio\n      this.currentAudio.addEventListener('loadedmetadata', () => {\n        if (this.currentAudio) {\n          this.setVoicePlaybackData(messageId, {\n            duration: this.currentAudio.duration\n          });\n          console.log('🎵 [Voice] Audio loaded, duration:', this.currentAudio.duration);\n        }\n      });\n      this.currentAudio.addEventListener('timeupdate', () => {\n        if (this.currentAudio && this.playingMessageId === messageId) {\n          const currentTime = this.currentAudio.currentTime;\n          const progress = currentTime / this.currentAudio.duration * 100;\n          this.setVoicePlaybackData(messageId, {\n            currentTime,\n            progress\n          });\n          this.cdr.detectChanges();\n        }\n      });\n      this.currentAudio.addEventListener('ended', () => {\n        console.log('🎵 [Voice] Playback ended for:', messageId);\n        this.stopVoicePlayback();\n      });\n      this.currentAudio.addEventListener('error', error => {\n        console.error('🎵 [Voice] Audio error:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      });\n      // Démarrer la lecture\n      this.currentAudio.play().then(() => {\n        console.log('🎵 [Voice] Playback started successfully');\n        this.toastService.showSuccess('🎵 Lecture du message vocal');\n      }).catch(error => {\n        console.error('🎵 [Voice] Error starting playback:', error);\n        this.toastService.showError('Impossible de lire le message vocal');\n        this.stopVoicePlayback();\n      });\n    } catch (error) {\n      console.error('🎵 [Voice] Error creating audio:', error);\n      this.toastService.showError('Erreur lors de la lecture audio');\n      this.stopVoicePlayback();\n    }\n  }\n  stopVoicePlayback() {\n    if (this.currentAudio) {\n      console.log('🎵 [Voice] Stopping playback for:', this.playingMessageId);\n      this.currentAudio.pause();\n      this.currentAudio.currentTime = 0;\n      this.currentAudio = null;\n    }\n    this.playingMessageId = null;\n    this.cdr.detectChanges();\n  }\n  getVoiceUrl(message) {\n    // Vérifier les propriétés directes d'audio\n    if (message.voiceUrl) return message.voiceUrl;\n    if (message.audioUrl) return message.audioUrl;\n    if (message.voice) return message.voice;\n    // Vérifier les attachments audio\n    const audioAttachment = message.attachments?.find(att => att.type?.startsWith('audio/') || att.type === 'AUDIO');\n    if (audioAttachment) {\n      return audioAttachment.url || audioAttachment.path || '';\n    }\n    return '';\n  }\n  getVoiceWaves(message) {\n    // Générer des waves basées sur l'ID du message pour la cohérence\n    const messageId = message.id || '';\n    const seed = messageId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);\n    const waves = [];\n    for (let i = 0; i < 16; i++) {\n      const height = 4 + (seed + i * 7) % 20;\n      waves.push(height);\n    }\n    return waves;\n  }\n  getVoiceProgress(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const totalWaves = 16;\n    return Math.floor(data.progress / 100 * totalWaves);\n  }\n  getVoiceCurrentTime(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    return this.formatAudioTime(data.currentTime);\n  }\n  getVoiceDuration(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const duration = data.duration || message.metadata?.duration || 0;\n    if (typeof duration === 'string') {\n      return duration; // Déjà formaté\n    }\n\n    return this.formatAudioTime(duration);\n  }\n  formatAudioTime(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  seekVoiceMessage(message, waveIndex) {\n    const messageId = message.id;\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\n      return;\n    }\n    const totalWaves = 16;\n    const seekPercentage = waveIndex / totalWaves * 100;\n    const seekTime = seekPercentage / 100 * this.currentAudio.duration;\n    this.currentAudio.currentTime = seekTime;\n    console.log('🎵 [Voice] Seeking to:', seekTime, 'seconds');\n  }\n  toggleVoiceSpeed(message) {\n    const messageId = message.id;\n    const data = this.getVoicePlaybackData(messageId);\n    // Cycle entre 1x, 1.5x, 2x\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n    this.setVoicePlaybackData(messageId, {\n      speed: newSpeed\n    });\n    if (this.currentAudio && this.playingMessageId === messageId) {\n      this.currentAudio.playbackRate = newSpeed;\n    }\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n    // Nettoyer les timers\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    // Nettoyer les ressources audio\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream?.getTracks().forEach(track => track.stop());\n    }\n    // Nettoyer la lecture audio\n    this.stopVoicePlayback();\n  }\n};\n__decorate([ViewChild('messagesContainer')], MessageChatComponent.prototype, \"messagesContainer\", void 0);\n__decorate([ViewChild('fileInput', {\n  static: false\n})], MessageChatComponent.prototype, \"fileInput\", void 0);\nMessageChatComponent = __decorate([Component({\n  selector: 'app-message-chat',\n  templateUrl: './message-chat.component.html'\n})], MessageChatComponent);", "map": {"version": 3, "names": ["Component", "ViewChild", "Validators", "Subscription", "CallType", "MessageChatComponent", "constructor", "fb", "route", "MessageService", "toastService", "cdr", "conversation", "messages", "currentUserId", "currentUsername", "otherParticipant", "isLoading", "isLoadingMore", "hasMoreMessages", "showEmojiPicker", "showAttachmentMenu", "showSearch", "searchQuery", "searchResults", "searchMode", "isSendingMessage", "otherUserIsTyping", "showMainMenu", "showMessageContextMenu", "selectedMessage", "contextMenuPosition", "x", "y", "showReactionPicker", "reactionPickerMessage", "isDarkMode", "currentTheme", "themes", "id", "name", "icon", "color", "showThemeSelector", "showImageViewer", "selectedImage", "uploadProgress", "isUploading", "isDragOver", "isRecordingVoice", "voiceRecordingDuration", "voiceRecordingState", "mediaRecorder", "audioChunks", "recordingTimer", "voiceWaves", "currentAudio", "playingMessageId", "voicePlayback", "isInCall", "callType", "callDuration", "callTimer", "activeCall", "isCallConnected", "isMuted", "isVideoEnabled", "localVideoElement", "remoteVideoElement", "emojiCategories", "emojis", "emoji", "selectedEmojiCategory", "MAX_MESSAGES_TO_LOAD", "currentPage", "isTyping", "isUserTyping", "typingTimeout", "subscriptions", "messageForm", "group", "content", "required", "<PERSON><PERSON><PERSON><PERSON>", "isInputDisabled", "updateInputState", "contentControl", "get", "disable", "enable", "ngOnInit", "console", "log", "initializeTheme", "initializeComponent", "savedTheme", "localStorage", "getItem", "applyTheme", "themeId", "html", "document", "documentElement", "classList", "remove", "add", "getCurrentTheme", "find", "t", "toggleThemeSelector", "selectTheme", "setItem", "themeName", "showSuccess", "loadCurrentUser", "loadConversation", "setupCallSubscriptions", "incomingCall$", "subscribe", "next", "incomingCall", "handleIncomingCall", "error", "activeCall$", "call", "caller", "username", "play", "userString", "user", "JSON", "parse", "userId", "_id", "extracted", "conversationId", "snapshot", "paramMap", "showError", "getConversation", "participants", "participantsCount", "length", "isGroup", "messagesCount", "setOtherParticipant", "loadMessages", "setupSubscriptions", "warn", "p", "participantId", "String", "firstParticipantId", "image", "isOnline", "sort", "a", "b", "dateA", "Date", "timestamp", "createdAt", "getTime", "dateB", "total", "first", "last", "scrollToBottom", "loadMoreMessages", "offset", "getMessages", "newMessages", "reverse", "subscribeToNewMessages", "newMessage", "type", "sender", "senderId", "receiverId", "attachments", "getMessageType", "hasImage", "hasFile", "getImageUrl", "for<PERSON>ach", "att", "index", "url", "path", "size", "messageExists", "some", "msg", "push", "detectChanges", "setTimeout", "shouldMarkAsRead", "markMessageAsRead", "subscribeToTypingIndicator", "typingData", "subscribeToConversationUpdates", "conversationUpdate", "messageId", "sendMessage", "valid", "value", "trim", "undefined", "message", "reset", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "formatLastActive", "lastActive", "diffMins", "Math", "floor", "now", "getVoicePlaybackData", "progress", "duration", "currentTime", "speed", "setVoicePlaybackData", "data", "startVideoCall", "startVoiceCall", "endCall", "formatFileSize", "bytes", "round", "downloadFile", "fileAttachment", "startsWith", "link", "createElement", "href", "download", "target", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "toggleTheme", "newTheme", "toggleSearch", "toggleMainMenu", "goBackToConversations", "closeAllMenus", "onMessageContextMenu", "event", "preventDefault", "clientX", "clientY", "showQuickReactions", "stopPropagation", "quickReact", "toggleReaction", "hasUserReacted", "reaction", "replyToMessage", "forwardMessage", "deleteMessage", "toggleEmojiPicker", "selectEmojiCategory", "category", "getEmojisForCategory", "insert<PERSON><PERSON><PERSON>", "currentC<PERSON>nt", "newContent", "patchValue", "toggleAttachmentMenu", "trackByMessageId", "toString", "testAddMessage", "testMessage", "toLocaleTimeString", "toISOString", "isRead", "isGroupConversation", "openCamera", "zoomImage", "factor", "imageElement", "querySelector", "currentTransform", "style", "transform", "currentScale", "parseFloat", "match", "newScale", "max", "min", "resetZoom", "triggerFileInput", "input", "fileInput", "accept", "formatMessageTime", "date", "hour", "minute", "formatDateSeparator", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "formatMessageContent", "urlRegex", "replace", "shouldShowDateSeparator", "currentMessage", "previousMessage", "currentDate", "previousDate", "shouldShowAvatar", "nextMessage", "shouldShowSenderName", "attachment", "voiceUrl", "audioUrl", "voice", "imageUrl", "hasImageAttachment", "isImage", "hasImageUrl", "result", "hasFileAttachment", "isFile", "imageAttachment", "stringify", "getFileName", "getFileSize", "getFileIcon", "includes", "getUserColor", "colors", "charCodeAt", "onMessageClick", "onInputChange", "handleTypingIndicator", "onInputKeyDown", "key", "shift<PERSON>ey", "onInputFocus", "onInputBlur", "onScroll", "openUserProfile", "onImageLoad", "src", "onImageError", "openImageViewer", "closeImageViewer", "downloadImage", "searchMessages", "filter", "toLowerCase", "onSearchQueryChange", "clearSearch", "jumpToMessage", "messageElement", "getElementById", "scrollIntoView", "behavior", "block", "closeContextMenu", "initiateCall", "recipientId", "VIDEO", "startCallTimer", "acceptCall", "rejectCall", "setInterval", "resetCallState", "clearInterval", "toggleMute", "toggleMedia", "toggleVideo", "formatCallDuration", "hours", "minutes", "seconds", "padStart", "startVoiceRecording", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "Error", "window", "MediaRecorder", "stream", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "channelCount", "mimeType", "isTypeSupported", "animateVoice<PERSON>aves", "ondataavailable", "onstop", "processRecordedAudio", "onerror", "cancelVoiceRecording", "start", "errorMessage", "stopVoiceRecording", "state", "stop", "getTracks", "track", "_this2", "audioBlob", "Blob", "extension", "audioFile", "File", "sendVoiceMessage", "_this3", "Promise", "resolve", "reject", "formatRecordingDuration", "onRecordStart", "showWarning", "showInfo", "catch", "onRecordEnd", "onRecordCancel", "getRecordingFormat", "map", "random", "onFileSelected", "files", "file", "uploadFile", "maxSize", "compressImage", "then", "compressedFile", "sendFileToServer", "messageType", "getFileMessageType", "progressInterval", "resetUploadState", "getFileAcceptTypes", "onDragOver", "onDragLeave", "rect", "currentTarget", "getBoundingClientRect", "left", "right", "top", "bottom", "onDrop", "dataTransfer", "Array", "from", "quality", "canvas", "ctx", "getContext", "img", "Image", "onload", "max<PERSON><PERSON><PERSON>", "maxHeight", "width", "height", "ratio", "drawImage", "toBlob", "blob", "lastModified", "URL", "createObjectURL", "sendTypingIndicator", "clearTimeout", "onCallAccepted", "onCallRejected", "playVoiceMessage", "toggleVoicePlayback", "isVoicePlaying", "getVoiceUrl", "stopVoicePlayback", "startVoicePlayback", "Audio", "currentData", "playbackRate", "addEventListener", "pause", "audioAttachment", "getVoiceWaves", "seed", "split", "reduce", "acc", "char", "waves", "i", "getVoiceProgress", "totalWaves", "getVoiceCurrentTime", "formatAudioTime", "getVoiceDuration", "metadata", "remainingSeconds", "seekVoiceMessage", "waveIndex", "seekPercentage", "seekTime", "toggleVoiceSpeed", "newSpeed", "ngOnDestroy", "unsubscribe", "__decorate", "static", "selector", "templateUrl"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON>Child,\n  ElementRef,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { MessageService } from '../../../../services/message.service';\nimport { ToastService } from '../../../../services/toast.service';\nimport { CallType, Call, IncomingCall } from '../../../../models/message.model';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: './message-chat.component.html',\n})\nexport class MessageChatComponent implements OnInit, OnDestroy {\n  // === RÉFÉRENCES DOM ===\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  // === DONNÉES PRINCIPALES ===\n  conversation: any = null;\n  messages: any[] = [];\n  currentUserId: string | null = null;\n  currentUsername = 'You';\n  messageForm: FormGroup;\n  otherParticipant: any = null;\n\n  // === ÉTATS DE L'INTERFACE ===\n  isLoading = false;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  showEmojiPicker = false;\n  showAttachmentMenu = false;\n  showSearch = false;\n  searchQuery = '';\n  searchResults: any[] = [];\n  searchMode = false;\n  isSendingMessage = false;\n  otherUserIsTyping = false;\n  showMainMenu = false;\n  showMessageContextMenu = false;\n  selectedMessage: any = null;\n  contextMenuPosition = { x: 0, y: 0 };\n  showReactionPicker = false;\n  reactionPickerMessage: any = null;\n  isDarkMode = false;\n  currentTheme = 'light'; // 'light', 'dark', 'blue', 'pink'\n  themes = [\n    { id: 'light', name: 'Clair', icon: 'fas fa-sun', color: '#f59e0b' },\n    { id: 'dark', name: 'Sombre', icon: 'fas fa-moon', color: '#6b7280' },\n    { id: 'blue', name: 'Bleu', icon: 'fas fa-water', color: '#3b82f6' },\n    { id: 'pink', name: 'Rose', icon: 'fas fa-heart', color: '#ec4899' },\n  ];\n  showThemeSelector = false;\n  showImageViewer = false;\n  selectedImage: any = null;\n  uploadProgress = 0;\n  isUploading = false;\n  isDragOver = false;\n\n  // === GESTION VOCALE OPTIMISÉE ===\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';\n  private mediaRecorder: MediaRecorder | null = null;\n  private audioChunks: Blob[] = [];\n  private recordingTimer: any = null;\n  voiceWaves: number[] = [\n    4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8,\n  ];\n\n  // Lecture des messages vocaux\n  private currentAudio: HTMLAudioElement | null = null;\n  private playingMessageId: string | null = null;\n  private voicePlayback: {\n    [messageId: string]: {\n      progress: number;\n      duration: number;\n      currentTime: number;\n      speed: number;\n    };\n  } = {};\n\n  // === APPELS WEBRTC ===\n  isInCall = false;\n  callType: 'VIDEO' | 'AUDIO' | null = null;\n  callDuration = 0;\n  private callTimer: any = null;\n\n  // État de l'appel WebRTC\n  activeCall: any = null;\n  isCallConnected = false;\n  isMuted = false;\n  isVideoEnabled = true;\n  localVideoElement: HTMLVideoElement | null = null;\n  remoteVideoElement: HTMLVideoElement | null = null;\n\n  // === ÉMOJIS ===\n  emojiCategories: any[] = [\n    {\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [\n        { emoji: '😀', name: 'grinning face' },\n        { emoji: '😃', name: 'grinning face with big eyes' },\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\n        { emoji: '😆', name: 'grinning squinting face' },\n        { emoji: '😅', name: 'grinning face with sweat' },\n        { emoji: '😂', name: 'face with tears of joy' },\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\n        { emoji: '😇', name: 'smiling face with halo' },\n      ],\n    },\n    {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [\n        { emoji: '👶', name: 'baby' },\n        { emoji: '🧒', name: 'child' },\n        { emoji: '👦', name: 'boy' },\n        { emoji: '👧', name: 'girl' },\n        { emoji: '🧑', name: 'person' },\n        { emoji: '👨', name: 'man' },\n        { emoji: '👩', name: 'woman' },\n        { emoji: '👴', name: 'old man' },\n        { emoji: '👵', name: 'old woman' },\n      ],\n    },\n    {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [\n        { emoji: '🐶', name: 'dog face' },\n        { emoji: '🐱', name: 'cat face' },\n        { emoji: '🐭', name: 'mouse face' },\n        { emoji: '🐹', name: 'hamster' },\n        { emoji: '🐰', name: 'rabbit face' },\n        { emoji: '🦊', name: 'fox' },\n        { emoji: '🐻', name: 'bear' },\n        { emoji: '🐼', name: 'panda' },\n      ],\n    },\n  ];\n  selectedEmojiCategory = this.emojiCategories[0];\n\n  // === PAGINATION ===\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private currentPage = 1;\n\n  // === AUTRES ÉTATS ===\n  isTyping = false;\n  isUserTyping = false;\n  private typingTimeout: any = null;\n  private subscriptions = new Subscription();\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private MessageService: MessageService,\n    private toastService: ToastService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]],\n    });\n  }\n\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\n  isInputDisabled(): boolean {\n    return (\n      !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage\n    );\n  }\n\n  // Méthode pour gérer l'état du contrôle de saisie\n  private updateInputState(): void {\n    const contentControl = this.messageForm.get('content');\n    if (this.isInputDisabled()) {\n      contentControl?.disable();\n    } else {\n      contentControl?.enable();\n    }\n  }\n\n  ngOnInit(): void {\n    console.log('🚀 MessageChatComponent initialized');\n    this.initializeTheme();\n    this.initializeComponent();\n  }\n\n  private initializeTheme(): void {\n    // Charger le thème sauvegardé ou utiliser 'light' par défaut\n    const savedTheme = localStorage.getItem('currentTheme') || 'light';\n    this.currentTheme = savedTheme;\n    this.isDarkMode = savedTheme === 'dark';\n    this.applyTheme(savedTheme);\n  }\n\n  // === GESTION DES THÈMES OPTIMISÉE ===\n\n  private applyTheme(themeId: string): void {\n    const html = document.documentElement;\n\n    // Supprimer toutes les classes de thème existantes\n    html.classList.remove(\n      'dark',\n      'theme-light',\n      'theme-dark',\n      'theme-blue',\n      'theme-pink'\n    );\n\n    // Appliquer le nouveau thème\n    html.classList.add(`theme-${themeId}`);\n    if (themeId === 'dark') {\n      html.classList.add('dark');\n    }\n  }\n\n  getCurrentTheme() {\n    return this.themes.find((t) => t.id === this.currentTheme);\n  }\n\n  toggleThemeSelector(): void {\n    this.showThemeSelector = !this.showThemeSelector;\n  }\n\n  selectTheme(themeId: string): void {\n    this.currentTheme = themeId;\n    this.isDarkMode = themeId === 'dark';\n    this.applyTheme(themeId);\n    localStorage.setItem('currentTheme', themeId);\n    this.showThemeSelector = false;\n    this.showMainMenu = false;\n\n    const themeName =\n      this.themes.find((t) => t.id === themeId)?.name || themeId;\n    this.toastService.showSuccess(`Thème ${themeName} appliqué`);\n  }\n\n  private initializeComponent(): void {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupCallSubscriptions();\n  }\n\n  private setupCallSubscriptions(): void {\n    // S'abonner aux appels entrants\n    this.subscriptions.add(\n      this.MessageService.incomingCall$.subscribe({\n        next: (incomingCall) => {\n          if (incomingCall) {\n            console.log('📞 Incoming call received:', incomingCall);\n            this.handleIncomingCall(incomingCall);\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in incoming call subscription:', error);\n        },\n      })\n    );\n\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(\n      this.MessageService.activeCall$.subscribe({\n        next: (call) => {\n          if (call) {\n            console.log('📞 Active call updated:', call);\n            this.activeCall = call;\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in active call subscription:', error);\n        },\n      })\n    );\n  }\n\n  private handleIncomingCall(incomingCall: IncomingCall): void {\n    // Afficher une notification ou modal d'appel entrant\n    // Pour l'instant, on log juste\n    console.log(\n      '🔔 Handling incoming call from:',\n      incomingCall.caller.username\n    );\n\n    // Jouer la sonnerie\n    this.MessageService.play('ringtone');\n\n    // Ici on pourrait afficher une modal ou notification\n    // Pour l'instant, on accepte automatiquement pour tester\n    // this.acceptCall(incomingCall);\n  }\n\n  private loadCurrentUser(): void {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('🔍 Raw user from localStorage:', userString);\n\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n\n      const user = JSON.parse(userString);\n      console.log('🔍 Parsed user object:', user);\n\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      console.log('🔍 Trying to extract user ID:', {\n        _id: user._id,\n        id: user.id,\n        userId: user.userId,\n        extracted: userId,\n      });\n\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n        console.log('✅ Current user loaded successfully:', {\n          id: this.currentUserId,\n          username: this.currentUsername,\n        });\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n\n  private loadConversation(): void {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: (conversation) => {\n        console.log('🔍 Conversation loaded successfully:', conversation);\n        console.log('🔍 Conversation structure:', {\n          id: conversation?.id,\n          participants: conversation?.participants,\n          participantsCount: conversation?.participants?.length,\n          isGroup: conversation?.isGroup,\n          messages: conversation?.messages,\n          messagesCount: conversation?.messages?.length,\n        });\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n\n        // Configurer les subscriptions temps réel après le chargement de la conversation\n        this.setupSubscriptions();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError(\n          'Erreur lors du chargement de la conversation'\n        );\n        this.isLoading = false;\n      },\n    });\n  }\n\n  private setOtherParticipant(): void {\n    if (\n      !this.conversation?.participants ||\n      this.conversation.participants.length === 0\n    ) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n\n    console.log('Setting other participant...');\n    console.log('Current user ID:', this.currentUserId);\n    console.log('All participants:', this.conversation.participants);\n\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\n        const participantId = p.id || p._id;\n        console.log(\n          'Comparing participant ID:',\n          participantId,\n          'with current user ID:',\n          this.currentUserId\n        );\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      console.log('Fallback: using first participant');\n      this.otherParticipant = this.conversation.participants[0];\n\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId =\n          this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log(\n            'First participant is current user, using second participant'\n          );\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      console.log('✅ Other participant set successfully:', {\n        id: this.otherParticipant.id || this.otherParticipant._id,\n        username: this.otherParticipant.username,\n        image: this.otherParticipant.image,\n        isOnline: this.otherParticipant.isOnline,\n      });\n\n      // Log très visible pour debug\n      console.log(\n        '🎯 FINAL RESULT: otherParticipant =',\n        this.otherParticipant.username\n      );\n      console.log(\n        '🎯 Should display in sidebar:',\n        this.otherParticipant.username\n      );\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      console.log('Conversation participants:', this.conversation.participants);\n      console.log('Current user ID:', this.currentUserId);\n\n      // Log très visible pour debug\n      console.log('🚨 ERROR: No otherParticipant found!');\n    }\n\n    // Mettre à jour l'état du champ de saisie\n    this.updateInputState();\n  }\n\n  private loadMessages(): void {\n    if (!this.conversation?.id) return;\n\n    // Les messages sont déjà chargés avec la conversation\n    let messages = this.conversation.messages || [];\n\n    // Trier les messages par timestamp (plus anciens en premier)\n    this.messages = messages.sort((a: any, b: any) => {\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\n    });\n\n    console.log('📋 Messages loaded and sorted:', {\n      total: this.messages.length,\n      first: this.messages[0]?.content,\n      last: this.messages[this.messages.length - 1]?.content,\n    });\n\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id)\n      return;\n\n    this.isLoadingMore = true;\n    this.currentPage++;\n\n    // Calculer l'offset basé sur les messages déjà chargés\n    const offset = this.messages.length;\n\n    this.MessageService.getMessages(\n      this.currentUserId!, // senderId\n      this.otherParticipant?.id || this.otherParticipant?._id!, // receiverId\n      this.conversation.id,\n      this.currentPage,\n      this.MAX_MESSAGES_TO_LOAD\n    ).subscribe({\n      next: (newMessages: any[]) => {\n        if (newMessages && newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste\n          this.messages = [...newMessages.reverse(), ...this.messages];\n          this.hasMoreMessages =\n            newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      },\n    });\n  }\n\n  private setupSubscriptions(): void {\n    if (!this.conversation?.id) {\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\n      return;\n    }\n\n    console.log(\n      '🔄 Setting up real-time subscriptions for conversation:',\n      this.conversation.id\n    );\n\n    // Subscription pour les nouveaux messages\n    console.log('📨 Setting up message subscription...');\n    this.subscriptions.add(\n      this.MessageService.subscribeToNewMessages(\n        this.conversation.id\n      ).subscribe({\n        next: (newMessage: any) => {\n          console.log('📨 New message received via subscription:', newMessage);\n          console.log('📨 Message structure:', {\n            id: newMessage.id,\n            type: newMessage.type,\n            content: newMessage.content,\n            sender: newMessage.sender,\n            senderId: newMessage.senderId,\n            receiverId: newMessage.receiverId,\n            attachments: newMessage.attachments,\n          });\n\n          // Debug des attachments\n          console.log(\n            '📨 [Debug] Message type detected:',\n            this.getMessageType(newMessage)\n          );\n          console.log('📨 [Debug] Has image:', this.hasImage(newMessage));\n          console.log('📨 [Debug] Has file:', this.hasFile(newMessage));\n          console.log('📨 [Debug] Image URL:', this.getImageUrl(newMessage));\n          if (newMessage.attachments) {\n            newMessage.attachments.forEach((att: any, index: number) => {\n              console.log(`📨 [Debug] Attachment ${index}:`, {\n                type: att.type,\n                url: att.url,\n                path: att.path,\n                name: att.name,\n                size: att.size,\n              });\n            });\n          }\n\n          // Ajouter le message à la liste s'il n'existe pas déjà\n          const messageExists = this.messages.some(\n            (msg) => msg.id === newMessage.id\n          );\n          if (!messageExists) {\n            // Ajouter le nouveau message à la fin (en bas)\n            this.messages.push(newMessage);\n            console.log(\n              '✅ Message added to list, total messages:',\n              this.messages.length\n            );\n\n            // Forcer la détection de changements\n            this.cdr.detectChanges();\n\n            // Scroll vers le bas après un court délai\n            setTimeout(() => {\n              this.scrollToBottom();\n            }, 50);\n\n            // Marquer comme lu si ce n'est pas notre message\n            const senderId = newMessage.sender?.id || newMessage.senderId;\n            console.log('📨 Checking if message should be marked as read:', {\n              senderId,\n              currentUserId: this.currentUserId,\n              shouldMarkAsRead: senderId !== this.currentUserId,\n            });\n\n            if (senderId && senderId !== this.currentUserId) {\n              this.markMessageAsRead(newMessage.id);\n            }\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in message subscription:', error);\n        },\n      })\n    );\n\n    // Subscription pour les indicateurs de frappe\n    console.log('📝 Setting up typing indicator subscription...');\n    this.subscriptions.add(\n      this.MessageService.subscribeToTypingIndicator(\n        this.conversation.id\n      ).subscribe({\n        next: (typingData: any) => {\n          console.log('📝 Typing indicator received:', typingData);\n\n          // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n          if (typingData.userId !== this.currentUserId) {\n            this.otherUserIsTyping = typingData.isTyping;\n            this.cdr.detectChanges();\n          }\n        },\n        error: (error: any) => {\n          console.error('❌ Error in typing subscription:', error);\n        },\n      })\n    );\n\n    // Subscription pour les mises à jour de conversation\n    this.subscriptions.add(\n      this.MessageService.subscribeToConversationUpdates(\n        this.conversation.id\n      ).subscribe({\n        next: (conversationUpdate: any) => {\n          console.log('📋 Conversation update:', conversationUpdate);\n\n          // Mettre à jour la conversation si nécessaire\n          if (conversationUpdate.id === this.conversation.id) {\n            this.conversation = { ...this.conversation, ...conversationUpdate };\n            this.cdr.detectChanges();\n          }\n        },\n        error: (error: any) => {\n          console.error('❌ Error in conversation subscription:', error);\n        },\n      })\n    );\n  }\n\n  private markMessageAsRead(messageId: string): void {\n    this.MessageService.markMessageAsRead(messageId).subscribe({\n      next: () => {\n        console.log('✅ Message marked as read:', messageId);\n      },\n      error: (error) => {\n        console.error('❌ Error marking message as read:', error);\n      },\n    });\n  }\n\n  // === ENVOI DE MESSAGES ===\n  sendMessage(): void {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    // Désactiver le bouton d'envoi\n    this.isSendingMessage = true;\n    this.updateInputState();\n\n    console.log('📤 Sending message:', {\n      content,\n      receiverId,\n      conversationId: this.conversation.id,\n    });\n\n    this.MessageService.sendMessage(\n      receiverId,\n      content,\n      undefined,\n      'TEXT' as any,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        console.log('✅ Message sent successfully:', message);\n\n        // Ajouter le message à la liste s'il n'y est pas déjà\n        const messageExists = this.messages.some(\n          (msg) => msg.id === message.id\n        );\n        if (!messageExists) {\n          this.messages.push(message);\n          console.log(\n            '📋 Message added to local list, total:',\n            this.messages.length\n          );\n        }\n\n        // Réinitialiser le formulaire\n        this.messageForm.reset();\n        this.isSendingMessage = false;\n        this.updateInputState();\n\n        // Forcer la détection de changements et scroll\n        this.cdr.detectChanges();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 50);\n      },\n      error: (error: any) => {\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        this.isSendingMessage = false;\n        this.updateInputState();\n      },\n    });\n  }\n\n  scrollToBottom(): void {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n  formatLastActive(lastActive: string | Date | null): string {\n    if (!lastActive) return 'Hors ligne';\n\n    const diffMins = Math.floor(\n      (Date.now() - new Date(lastActive).getTime()) / 60000\n    );\n\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\n  }\n\n  // Méthodes utilitaires pour les messages vocaux\n  getVoicePlaybackData(messageId: string) {\n    return (\n      this.voicePlayback[messageId] || {\n        progress: 0,\n        duration: 0,\n        currentTime: 0,\n        speed: 1,\n      }\n    );\n  }\n\n  private setVoicePlaybackData(\n    messageId: string,\n    data: Partial<(typeof this.voicePlayback)[string]>\n  ) {\n    this.voicePlayback[messageId] = {\n      ...this.getVoicePlaybackData(messageId),\n      ...data,\n    };\n  }\n\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // isVoicePlaying, playVoiceMessage, toggleVoicePlayback - définies plus loin\n\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n\n  startVideoCall(): void {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n\n    this.callType = 'VIDEO';\n    this.isInCall = true;\n    console.log('📹 Starting video call with:', this.otherParticipant.username);\n  }\n\n  startVoiceCall(): void {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n\n    this.callType = 'AUDIO';\n    this.isInCall = true;\n    console.log('📞 Starting voice call with:', this.otherParticipant.username);\n  }\n\n  endCall(): void {\n    this.isInCall = false;\n    this.callType = null;\n    this.activeCall = null;\n    console.log('📞 Call ended');\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // onCallAccepted, onCallRejected - définies plus loin\n\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n\n  formatFileSize(bytes: number): string {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n\n  downloadFile(message: any): void {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (fileAttachment?.url) {\n      const link = document.createElement('a');\n      link.href = fileAttachment.url;\n      link.download = fileAttachment.name || 'file';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n    }\n  }\n\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n\n  toggleTheme(): void {\n    const newTheme = this.isDarkMode ? 'light' : 'dark';\n    this.selectTheme(newTheme);\n  }\n\n  toggleSearch(): void {\n    this.searchMode = !this.searchMode;\n    this.showSearch = this.searchMode;\n  }\n\n  toggleMainMenu(): void {\n    this.showMainMenu = !this.showMainMenu;\n  }\n\n  goBackToConversations(): void {\n    // Navigation vers la liste des conversations\n    console.log('🔙 Going back to conversations');\n  }\n\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n\n  closeAllMenus(): void {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.showReactionPicker = false;\n    this.showThemeSelector = false;\n  }\n\n  onMessageContextMenu(message: any, event: MouseEvent): void {\n    event.preventDefault();\n    this.selectedMessage = message;\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\n    this.showMessageContextMenu = true;\n  }\n\n  showQuickReactions(message: any, event: MouseEvent): void {\n    event.stopPropagation();\n    this.reactionPickerMessage = message;\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\n    this.showReactionPicker = true;\n  }\n\n  quickReact(emoji: string): void {\n    if (this.reactionPickerMessage) {\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\n    }\n    this.showReactionPicker = false;\n  }\n\n  toggleReaction(messageId: string, emoji: string): void {\n    console.log('👍 Toggling reaction:', emoji, 'for message:', messageId);\n    // Implémentation de la réaction\n  }\n\n  hasUserReacted(reaction: any, userId: string): boolean {\n    return reaction.userId === userId;\n  }\n\n  replyToMessage(message: any): void {\n    console.log('↩️ Replying to message:', message.id);\n    this.closeAllMenus();\n  }\n\n  forwardMessage(message: any): void {\n    console.log('➡️ Forwarding message:', message.id);\n    this.closeAllMenus();\n  }\n\n  deleteMessage(message: any): void {\n    console.log('🗑️ Deleting message:', message.id);\n    this.closeAllMenus();\n  }\n\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n\n  toggleEmojiPicker(): void {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n\n  selectEmojiCategory(category: any): void {\n    this.selectedEmojiCategory = category;\n  }\n\n  getEmojisForCategory(category: any): any[] {\n    return category?.emojis || [];\n  }\n\n  insertEmoji(emoji: any): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({ content: newContent });\n    this.showEmojiPicker = false;\n  }\n\n  toggleAttachmentMenu(): void {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  // === MÉTHODES POUR LA GESTION DE LA FRAPPE ===\n\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // handleTypingIndicator - définie plus loin\n\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n\n  trackByMessageId(index: number, message: any): string {\n    return message.id || message._id || index.toString();\n  }\n\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n\n  testAddMessage(): void {\n    console.log('🧪 Test: Adding message');\n    const testMessage = {\n      id: `test-${Date.now()}`,\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\n      timestamp: new Date().toISOString(),\n      sender: {\n        id: this.otherParticipant?.id || 'test-user',\n        username: this.otherParticipant?.username || 'Test User',\n        image:\n          this.otherParticipant?.image || 'assets/images/default-avatar.png',\n      },\n      type: 'TEXT',\n      isRead: false,\n    };\n    this.messages.push(testMessage);\n    this.cdr.detectChanges();\n    setTimeout(() => this.scrollToBottom(), 50);\n  }\n\n  isGroupConversation(): boolean {\n    return (\n      this.conversation?.isGroup ||\n      this.conversation?.participants?.length > 2 ||\n      false\n    );\n  }\n\n  openCamera(): void {\n    console.log('📷 Opening camera');\n    this.showAttachmentMenu = false;\n    // TODO: Implémenter l'ouverture de la caméra\n  }\n\n  zoomImage(factor: number): void {\n    const imageElement = document.querySelector(\n      '.image-viewer-zoom'\n    ) as HTMLElement;\n    if (imageElement) {\n      const currentTransform = imageElement.style.transform || 'scale(1)';\n      const currentScale = parseFloat(\n        currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1'\n      );\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n      imageElement.style.transform = `scale(${newScale})`;\n      if (newScale > 1) {\n        imageElement.classList.add('zoomed');\n      } else {\n        imageElement.classList.remove('zoomed');\n      }\n    }\n  }\n\n  resetZoom(): void {\n    const imageElement = document.querySelector(\n      '.image-viewer-zoom'\n    ) as HTMLElement;\n    if (imageElement) {\n      imageElement.style.transform = 'scale(1)';\n      imageElement.classList.remove('zoomed');\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  triggerFileInput(type?: string): void {\n    const input = this.fileInput?.nativeElement;\n    if (!input) {\n      console.error('File input element not found');\n      return;\n    }\n\n    // Configurer le type de fichier accepté\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    } else {\n      input.accept = '*/*';\n    }\n\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\n    input.value = '';\n\n    // Déclencher la sélection de fichier\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n\n  formatMessageTime(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  }\n\n  formatDateSeparator(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n\n  formatMessageContent(content: string): string {\n    if (!content) return '';\n\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(\n      urlRegex,\n      '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>'\n    );\n  }\n\n  shouldShowDateSeparator(index: number): boolean {\n    if (index === 0) return true;\n\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n\n    return currentDate !== previousDate;\n  }\n\n  shouldShowAvatar(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n\n    if (!nextMessage) return true;\n\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n\n  shouldShowSenderName(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!previousMessage) return true;\n\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n\n  getMessageType(message: any): string {\n    // Vérifier d'abord le type de message explicite\n    if (message.type) {\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\n    }\n\n    // Ensuite vérifier les attachments\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n\n    // Vérifier si c'est un message vocal basé sur les propriétés\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n\n    return 'text';\n  }\n\n  hasImage(message: any): boolean {\n    console.log('🖼️ [Debug] hasImage called for message:', message.id, {\n      type: message.type,\n      attachments: message.attachments,\n      imageUrl: message.imageUrl,\n      image: message.image,\n    });\n\n    // Vérifier le type de message\n    if (message.type === 'IMAGE' || message.type === 'image') {\n      console.log('🖼️ [Debug] Message type is IMAGE');\n      return true;\n    }\n\n    // Vérifier les attachments\n    const hasImageAttachment =\n      message.attachments?.some((att: any) => {\n        const isImage = att.type?.startsWith('image/') || att.type === 'IMAGE';\n        console.log(\n          '🖼️ [Debug] Checking attachment:',\n          att,\n          'isImage:',\n          isImage\n        );\n        return isImage;\n      }) || false;\n\n    // Vérifier les propriétés directes d'image\n    const hasImageUrl = !!(message.imageUrl || message.image);\n\n    const result = hasImageAttachment || hasImageUrl;\n    console.log('🖼️ [Debug] hasImage result:', result, {\n      hasImageAttachment,\n      hasImageUrl,\n    });\n\n    return result;\n  }\n\n  hasFile(message: any): boolean {\n    console.log('📁 [Debug] hasFile called for message:', message.id, {\n      type: message.type,\n      attachments: message.attachments,\n    });\n\n    // Vérifier le type de message\n    if (message.type === 'FILE' || message.type === 'file') {\n      console.log('📁 [Debug] Message type is FILE');\n      return true;\n    }\n\n    // Vérifier les attachments non-image\n    const hasFileAttachment =\n      message.attachments?.some((att: any) => {\n        const isFile = !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n        console.log('📁 [Debug] Checking attachment:', att, 'isFile:', isFile);\n        return isFile;\n      }) || false;\n\n    console.log('📁 [Debug] hasFile result:', hasFileAttachment);\n    return hasFileAttachment;\n  }\n\n  getImageUrl(message: any): string {\n    console.log('🖼️ [Debug] getImageUrl called for message:', message.id);\n\n    // Vérifier les propriétés directes d'image\n    if (message.imageUrl) {\n      console.log('🖼️ [Debug] Found imageUrl:', message.imageUrl);\n      return message.imageUrl;\n    }\n    if (message.image) {\n      console.log('🖼️ [Debug] Found image:', message.image);\n      return message.image;\n    }\n\n    // Vérifier les attachments\n    const imageAttachment = message.attachments?.find(\n      (att: any) => att.type?.startsWith('image/') || att.type === 'IMAGE'\n    );\n\n    console.log('🖼️ [Debug] Found image attachment:', imageAttachment);\n\n    if (imageAttachment) {\n      const url = imageAttachment.url || imageAttachment.path || '';\n      console.log('🖼️ [Debug] Image attachment URL (full):', url);\n      console.log(\n        '🖼️ [Debug] Image attachment object:',\n        JSON.stringify(imageAttachment, null, 2)\n      );\n      return url;\n    }\n\n    console.log('🖼️ [Debug] No image URL found, returning empty string');\n    return '';\n  }\n\n  getFileName(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    return fileAttachment?.name || 'Fichier';\n  }\n\n  getFileSize(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.size) return '';\n\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n\n  getFileIcon(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.type) return 'fas fa-file';\n\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n\n  getUserColor(userId: string): string {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = [\n      '#FF6B6B',\n      '#4ECDC4',\n      '#45B7D1',\n      '#96CEB4',\n      '#FFEAA7',\n      '#DDA0DD',\n      '#98D8C8',\n    ];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message: any, event: any): void {\n    console.log('Message clicked:', message);\n  }\n\n  onInputChange(event: any): void {\n    // Gérer les changements dans le champ de saisie\n    this.handleTypingIndicator();\n  }\n\n  onInputKeyDown(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  onInputFocus(): void {\n    // Gérer le focus sur le champ de saisie\n  }\n\n  onInputBlur(): void {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n\n  onScroll(event: any): void {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (\n      element.scrollTop === 0 &&\n      this.hasMoreMessages &&\n      !this.isLoadingMore\n    ) {\n      this.loadMoreMessages();\n    }\n  }\n\n  openUserProfile(userId: string): void {\n    console.log('Opening user profile for:', userId);\n  }\n\n  onImageLoad(event: any, message: any): void {\n    console.log(\n      '🖼️ [Debug] Image loaded successfully for message:',\n      message.id,\n      event.target.src\n    );\n  }\n\n  onImageError(event: any, message: any): void {\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n      src: event.target.src,\n      error: event,\n    });\n    // Optionnel : afficher une image de remplacement\n    event.target.src =\n      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n  }\n\n  openImageViewer(message: any): void {\n    const imageAttachment = message.attachments?.find((att: any) =>\n      att.type?.startsWith('image/')\n    );\n    if (imageAttachment?.url) {\n      this.selectedImage = {\n        url: imageAttachment.url,\n        name: imageAttachment.name || 'Image',\n        size: this.formatFileSize(imageAttachment.size || 0),\n        message: message,\n      };\n      this.showImageViewer = true;\n      console.log('🖼️ [ImageViewer] Opening image:', this.selectedImage);\n    }\n  }\n\n  closeImageViewer(): void {\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    console.log('🖼️ [ImageViewer] Closed');\n  }\n\n  downloadImage(): void {\n    if (this.selectedImage?.url) {\n      const link = document.createElement('a');\n      link.href = this.selectedImage.url;\n      link.download = this.selectedImage.name || 'image';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n      console.log(\n        '🖼️ [ImageViewer] Download started:',\n        this.selectedImage.name\n      );\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  searchMessages(): void {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n\n    this.searchResults = this.messages.filter(\n      (message) =>\n        message.content\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase()) ||\n        message.sender?.username\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase())\n    );\n  }\n\n  onSearchQueryChange(): void {\n    this.searchMessages();\n  }\n\n  clearSearch(): void {\n    this.searchQuery = '';\n    this.searchResults = [];\n  }\n\n  jumpToMessage(messageId: string): void {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\n      // Highlight temporairement le message\n      messageElement.classList.add('highlight');\n      setTimeout(() => {\n        messageElement.classList.remove('highlight');\n      }, 2000);\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  closeContextMenu(): void {\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // triggerFileInput - définie plus loin\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n  // goBackToConversations, startVideoCall, startVoiceCall\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  private initiateCall(callType: CallType): void {\n    if (!this.otherParticipant) {\n      this.toastService.showError('Aucun destinataire sélectionné');\n      return;\n    }\n\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n    if (!recipientId) {\n      this.toastService.showError('ID du destinataire introuvable');\n      return;\n    }\n\n    console.log(`🔄 Initiating ${callType} call to user:`, recipientId);\n\n    this.isInCall = true;\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n    this.callDuration = 0;\n\n    // Démarrer le timer d'appel\n    this.startCallTimer();\n\n    // Utiliser le vrai service WebRTC\n    this.MessageService.initiateCall(\n      recipientId,\n      callType,\n      this.conversation?.id\n    ).subscribe({\n      next: (call: Call) => {\n        console.log('✅ Call initiated successfully:', call);\n        this.activeCall = call;\n        this.isCallConnected = false;\n        this.toastService.showSuccess(\n          `Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`\n        );\n      },\n      error: (error) => {\n        console.error('❌ Error initiating call:', error);\n        this.endCall();\n        this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n      },\n    });\n  }\n\n  acceptCall(incomingCall: IncomingCall): void {\n    console.log('🔄 Accepting incoming call:', incomingCall);\n\n    this.MessageService.acceptCall(incomingCall).subscribe({\n      next: (call: Call) => {\n        console.log('✅ Call accepted successfully:', call);\n        this.activeCall = call;\n        this.isInCall = true;\n        this.isCallConnected = true;\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n        this.startCallTimer();\n        this.toastService.showSuccess('Appel accepté');\n      },\n      error: (error) => {\n        console.error('❌ Error accepting call:', error);\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\n      },\n    });\n  }\n\n  rejectCall(incomingCall: IncomingCall): void {\n    console.log('🔄 Rejecting incoming call:', incomingCall);\n\n    this.MessageService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n      next: () => {\n        console.log('✅ Call rejected successfully');\n        this.toastService.showSuccess('Appel rejeté');\n      },\n      error: (error) => {\n        console.error('❌ Error rejecting call:', error);\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n      },\n    });\n  }\n\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // endCall - Cette méthode est déjà définie plus loin dans le fichier\n\n  private startCallTimer(): void {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n\n  private resetCallState(): void {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n  }\n\n  // === CONTRÔLES D'APPEL ===\n  toggleMute(): void {\n    if (!this.activeCall) return;\n\n    this.isMuted = !this.isMuted;\n\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(\n      this.activeCall.id,\n      undefined, // video unchanged\n      !this.isMuted // audio state\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(\n          this.isMuted ? 'Micro coupé' : 'Micro activé'\n        );\n      },\n      error: (error) => {\n        console.error('❌ Error toggling mute:', error);\n        // Revert state on error\n        this.isMuted = !this.isMuted;\n        this.toastService.showError('Erreur lors du changement du micro');\n      },\n    });\n  }\n\n  toggleVideo(): void {\n    if (!this.activeCall) return;\n\n    this.isVideoEnabled = !this.isVideoEnabled;\n\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(\n      this.activeCall.id,\n      this.isVideoEnabled, // video state\n      undefined // audio unchanged\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(\n          this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\n        );\n      },\n      error: (error) => {\n        console.error('❌ Error toggling video:', error);\n        // Revert state on error\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.toastService.showError('Erreur lors du changement de la caméra');\n      },\n    });\n  }\n\n  formatCallDuration(duration: number): string {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor((duration % 3600) / 60);\n    const seconds = duration % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds\n        .toString()\n        .padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n\n  async startVoiceRecording(): Promise<void> {\n    console.log('🎤 [Voice] Starting voice recording...');\n\n    try {\n      // Vérifier le support du navigateur\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n        throw new Error(\n          \"Votre navigateur ne supporte pas l'enregistrement audio\"\n        );\n      }\n\n      // Vérifier si MediaRecorder est supporté\n      if (!window.MediaRecorder) {\n        throw new Error(\n          \"MediaRecorder n'est pas supporté par votre navigateur\"\n        );\n      }\n\n      console.log('🎤 [Voice] Requesting microphone access...');\n\n      // Demander l'accès au microphone avec des contraintes optimisées\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n          sampleRate: 44100,\n          channelCount: 1,\n        },\n      });\n\n      console.log('🎤 [Voice] Microphone access granted');\n\n      // Vérifier les types MIME supportés\n      let mimeType = 'audio/webm;codecs=opus';\n      if (!MediaRecorder.isTypeSupported(mimeType)) {\n        mimeType = 'audio/webm';\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\n          mimeType = 'audio/mp4';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = ''; // Laisser le navigateur choisir\n          }\n        }\n      }\n\n      console.log('🎤 [Voice] Using MIME type:', mimeType);\n\n      // Créer le MediaRecorder\n      this.mediaRecorder = new MediaRecorder(stream, {\n        mimeType: mimeType || undefined,\n      });\n\n      // Initialiser les variables\n      this.audioChunks = [];\n      this.isRecordingVoice = true;\n      this.voiceRecordingDuration = 0;\n      this.voiceRecordingState = 'recording';\n\n      console.log('🎤 [Voice] MediaRecorder created, starting timer...');\n\n      // Démarrer le timer\n      this.recordingTimer = setInterval(() => {\n        this.voiceRecordingDuration++;\n        // Animer les waves\n        this.animateVoiceWaves();\n        this.cdr.detectChanges();\n      }, 1000);\n\n      // Gérer les événements du MediaRecorder\n      this.mediaRecorder.ondataavailable = (event) => {\n        console.log('🎤 [Voice] Data available:', event.data.size, 'bytes');\n        if (event.data.size > 0) {\n          this.audioChunks.push(event.data);\n        }\n      };\n\n      this.mediaRecorder.onstop = () => {\n        console.log('🎤 [Voice] MediaRecorder stopped, processing audio...');\n        this.processRecordedAudio();\n      };\n\n      this.mediaRecorder.onerror = (event: any) => {\n        console.error('🎤 [Voice] MediaRecorder error:', event.error);\n        this.toastService.showError(\"Erreur lors de l'enregistrement\");\n        this.cancelVoiceRecording();\n      };\n\n      // Démarrer l'enregistrement\n      this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n      console.log('🎤 [Voice] Recording started successfully');\n\n      this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n    } catch (error: any) {\n      console.error('🎤 [Voice] Error starting recording:', error);\n\n      let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n\n      if (error.name === 'NotAllowedError') {\n        errorMessage =\n          \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n      } else if (error.name === 'NotFoundError') {\n        errorMessage =\n          'Aucun microphone détecté. Veuillez connecter un microphone.';\n      } else if (error.name === 'NotSupportedError') {\n        errorMessage =\n          \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      this.toastService.showError(errorMessage);\n      this.cancelVoiceRecording();\n    }\n  }\n\n  stopVoiceRecording(): void {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n\n  cancelVoiceRecording(): void {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n      this.mediaRecorder = null;\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n\n  private async processRecordedAudio(): Promise<void> {\n    console.log('🎤 [Voice] Processing recorded audio...');\n\n    try {\n      // Vérifier qu'on a des données audio\n      if (this.audioChunks.length === 0) {\n        console.error('🎤 [Voice] No audio chunks available');\n        this.toastService.showError('Aucun audio enregistré');\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      console.log(\n        '🎤 [Voice] Audio chunks:',\n        this.audioChunks.length,\n        'Duration:',\n        this.voiceRecordingDuration\n      );\n\n      // Vérifier la durée minimale\n      if (this.voiceRecordingDuration < 1) {\n        console.error(\n          '🎤 [Voice] Recording too short:',\n          this.voiceRecordingDuration\n        );\n        this.toastService.showError(\n          'Enregistrement trop court (minimum 1 seconde)'\n        );\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      // Déterminer le type MIME du blob\n      let mimeType = 'audio/webm;codecs=opus';\n      if (this.mediaRecorder?.mimeType) {\n        mimeType = this.mediaRecorder.mimeType;\n      }\n\n      console.log('🎤 [Voice] Creating audio blob with MIME type:', mimeType);\n\n      // Créer le blob audio\n      const audioBlob = new Blob(this.audioChunks, {\n        type: mimeType,\n      });\n\n      console.log('🎤 [Voice] Audio blob created:', {\n        size: audioBlob.size,\n        type: audioBlob.type,\n      });\n\n      // Déterminer l'extension du fichier\n      let extension = '.webm';\n      if (mimeType.includes('mp4')) {\n        extension = '.mp4';\n      } else if (mimeType.includes('wav')) {\n        extension = '.wav';\n      } else if (mimeType.includes('ogg')) {\n        extension = '.ogg';\n      }\n\n      // Créer le fichier\n      const audioFile = new File(\n        [audioBlob],\n        `voice_${Date.now()}${extension}`,\n        {\n          type: mimeType,\n        }\n      );\n\n      console.log('🎤 [Voice] Audio file created:', {\n        name: audioFile.name,\n        size: audioFile.size,\n        type: audioFile.type,\n      });\n\n      // Envoyer le message vocal\n      this.voiceRecordingState = 'processing';\n      await this.sendVoiceMessage(audioFile);\n\n      console.log('🎤 [Voice] Voice message sent successfully');\n      this.toastService.showSuccess('🎤 Message vocal envoyé');\n    } catch (error: any) {\n      console.error('🎤 [Voice] Error processing audio:', error);\n      this.toastService.showError(\n        \"Erreur lors de l'envoi du message vocal: \" +\n          (error.message || 'Erreur inconnue')\n      );\n    } finally {\n      // Nettoyer l'état\n      this.voiceRecordingState = 'idle';\n      this.voiceRecordingDuration = 0;\n      this.audioChunks = [];\n      this.isRecordingVoice = false;\n\n      console.log('🎤 [Voice] Audio processing completed, state reset');\n    }\n  }\n\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        '',\n        audioFile,\n        'AUDIO' as any,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n          reject(error);\n        },\n      });\n    });\n  }\n\n  formatRecordingDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n\n  onRecordStart(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record start triggered');\n    console.log('🎤 [Voice] Current state:', {\n      isRecordingVoice: this.isRecordingVoice,\n      voiceRecordingState: this.voiceRecordingState,\n      voiceRecordingDuration: this.voiceRecordingDuration,\n      mediaRecorder: !!this.mediaRecorder,\n    });\n\n    // Vérifier si on peut enregistrer\n    if (this.voiceRecordingState === 'processing') {\n      console.log('🎤 [Voice] Already processing, ignoring start');\n      this.toastService.showWarning('Traitement en cours...');\n      return;\n    }\n\n    if (this.isRecordingVoice) {\n      console.log('🎤 [Voice] Already recording, ignoring start');\n      this.toastService.showWarning('Enregistrement déjà en cours...');\n      return;\n    }\n\n    // Afficher un message de début\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n\n    // Démarrer l'enregistrement\n    this.startVoiceRecording().catch((error) => {\n      console.error('🎤 [Voice] Failed to start recording:', error);\n      this.toastService.showError(\n        \"Impossible de démarrer l'enregistrement vocal: \" +\n          (error.message || 'Erreur inconnue')\n      );\n    });\n  }\n\n  onRecordEnd(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record end triggered');\n\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring end');\n      return;\n    }\n\n    // Arrêter l'enregistrement et envoyer\n    this.stopVoiceRecording();\n  }\n\n  onRecordCancel(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record cancel triggered');\n\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring cancel');\n      return;\n    }\n\n    // Annuler l'enregistrement\n    this.cancelVoiceRecording();\n  }\n\n  getRecordingFormat(): string {\n    if (this.mediaRecorder?.mimeType) {\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n    }\n    return 'Auto';\n  }\n\n  // === ANIMATION DES WAVES VOCALES ===\n\n  private animateVoiceWaves(): void {\n    // Animer les waves pendant l'enregistrement\n    this.voiceWaves = this.voiceWaves.map(() => {\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n    });\n  }\n\n  onFileSelected(event: any): void {\n    console.log('📁 [Upload] File selection triggered');\n    const files = event.target.files;\n\n    if (!files || files.length === 0) {\n      console.log('📁 [Upload] No files selected');\n      return;\n    }\n\n    console.log(`📁 [Upload] ${files.length} file(s) selected:`, files);\n\n    for (let file of files) {\n      console.log(\n        `📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`\n      );\n      this.uploadFile(file);\n    }\n  }\n\n  private uploadFile(file: File): void {\n    console.log(`📁 [Upload] Starting upload for file: ${file.name}`);\n\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      console.error('📁 [Upload] No receiver ID found');\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    console.log(`📁 [Upload] Receiver ID: ${receiverId}`);\n\n    // Vérifier la taille du fichier (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\n      return;\n    }\n\n    // 🖼️ Compression d'image si nécessaire\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n      // > 1MB\n      console.log(\n        '🖼️ [Compression] Compressing image:',\n        file.name,\n        'Original size:',\n        file.size\n      );\n      this.compressImage(file)\n        .then((compressedFile) => {\n          console.log(\n            '🖼️ [Compression] ✅ Image compressed successfully. New size:',\n            compressedFile.size\n          );\n          this.sendFileToServer(compressedFile, receiverId);\n        })\n        .catch((error) => {\n          console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n          // Envoyer le fichier original en cas d'erreur\n          this.sendFileToServer(file, receiverId);\n        });\n      return;\n    }\n\n    // Envoyer le fichier sans compression\n    this.sendFileToServer(file, receiverId);\n  }\n\n  private sendFileToServer(file: File, receiverId: string): void {\n    const messageType = this.getFileMessageType(file);\n    console.log(`📁 [Upload] Message type determined: ${messageType}`);\n    console.log(`📁 [Upload] Conversation ID: ${this.conversation.id}`);\n\n    this.isSendingMessage = true;\n    this.isUploading = true;\n    this.uploadProgress = 0;\n    console.log('📁 [Upload] Calling MessageService.sendMessage...');\n\n    // Simuler la progression d'upload\n    const progressInterval = setInterval(() => {\n      this.uploadProgress += Math.random() * 15;\n      if (this.uploadProgress >= 90) {\n        clearInterval(progressInterval);\n      }\n      this.cdr.detectChanges();\n    }, 300);\n\n    this.MessageService.sendMessage(\n      receiverId,\n      '',\n      file,\n      messageType,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        console.log('📁 [Upload] ✅ File sent successfully:', message);\n        console.log('📁 [Debug] Sent message structure:', {\n          id: message.id,\n          type: message.type,\n          attachments: message.attachments,\n          hasImage: this.hasImage(message),\n          hasFile: this.hasFile(message),\n          imageUrl: this.getImageUrl(message),\n        });\n\n        clearInterval(progressInterval);\n        this.uploadProgress = 100;\n\n        setTimeout(() => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Fichier envoyé avec succès');\n          this.resetUploadState();\n        }, 500);\n      },\n      error: (error: any) => {\n        console.error('📁 [Upload] ❌ Error sending file:', error);\n        clearInterval(progressInterval);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n        this.resetUploadState();\n      },\n    });\n  }\n\n  private getFileMessageType(file: File): any {\n    if (file.type.startsWith('image/')) return 'IMAGE' as any;\n    if (file.type.startsWith('video/')) return 'VIDEO' as any;\n    if (file.type.startsWith('audio/')) return 'AUDIO' as any;\n    return 'FILE' as any;\n  }\n\n  getFileAcceptTypes(): string {\n    return '*/*';\n  }\n\n  resetUploadState(): void {\n    this.isSendingMessage = false;\n    this.isUploading = false;\n    this.uploadProgress = 0;\n  }\n\n  // === DRAG & DROP ===\n\n  onDragOver(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n\n  onDragLeave(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\n    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\n    const x = event.clientX;\n    const y = event.clientY;\n\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      this.isDragOver = false;\n    }\n  }\n\n  onDrop(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      console.log('📁 [Drag&Drop] Files dropped:', files.length);\n\n      // Traiter chaque fichier\n      Array.from(files).forEach((file) => {\n        console.log(\n          '📁 [Drag&Drop] Processing file:',\n          file.name,\n          file.type,\n          file.size\n        );\n        this.uploadFile(file);\n      });\n\n      this.toastService.showSuccess(\n        `${files.length} fichier(s) en cours d'envoi`\n      );\n    }\n  }\n\n  // === COMPRESSION D'IMAGES ===\n\n  private compressImage(file: File, quality: number = 0.8): Promise<File> {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n\n      img.onload = () => {\n        // Calculer les nouvelles dimensions (max 1920x1080)\n        const maxWidth = 1920;\n        const maxHeight = 1080;\n        let { width, height } = img;\n\n        if (width > maxWidth || height > maxHeight) {\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\n          width *= ratio;\n          height *= ratio;\n        }\n\n        canvas.width = width;\n        canvas.height = height;\n\n        // Dessiner l'image redimensionnée\n        ctx?.drawImage(img, 0, 0, width, height);\n\n        // Convertir en blob avec compression\n        canvas.toBlob(\n          (blob) => {\n            if (blob) {\n              const compressedFile = new File([blob], file.name, {\n                type: file.type,\n                lastModified: Date.now(),\n              });\n              resolve(compressedFile);\n            } else {\n              reject(new Error('Failed to compress image'));\n            }\n          },\n          file.type,\n          quality\n        );\n      };\n\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  }\n\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n\n  private handleTypingIndicator(): void {\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\n      this.sendTypingIndicator(true);\n    }\n\n    // Reset le timer\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Arrêter l'indicateur de frappe\n      this.sendTypingIndicator(false);\n    }, 2000);\n  }\n\n  private sendTypingIndicator(isTyping: boolean): void {\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (receiverId && this.conversation?.id) {\n      console.log(\n        `📝 Sending typing indicator: ${isTyping} to user ${receiverId}`\n      );\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n    }\n  }\n\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n\n  onCallAccepted(call: Call): void {\n    console.log('🔄 Call accepted from interface:', call);\n    this.activeCall = call;\n    this.isInCall = true;\n    this.isCallConnected = true;\n    this.startCallTimer();\n    this.toastService.showSuccess('Appel accepté');\n  }\n\n  onCallRejected(): void {\n    console.log('🔄 Call rejected from interface');\n    this.endCall();\n    this.toastService.showInfo('Appel rejeté');\n  }\n\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n\n  playVoiceMessage(message: any): void {\n    console.log('🎵 [Voice] Playing voice message:', message.id);\n    this.toggleVoicePlayback(message);\n  }\n\n  isVoicePlaying(messageId: string): boolean {\n    return this.playingMessageId === messageId;\n  }\n\n  toggleVoicePlayback(message: any): void {\n    const messageId = message.id;\n    const audioUrl = this.getVoiceUrl(message);\n\n    if (!audioUrl) {\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\n      this.toastService.showError('Fichier audio introuvable');\n      return;\n    }\n\n    // Si c'est déjà en cours de lecture, arrêter\n    if (this.isVoicePlaying(messageId)) {\n      this.stopVoicePlayback();\n      return;\n    }\n\n    // Arrêter toute autre lecture en cours\n    this.stopVoicePlayback();\n\n    // Démarrer la nouvelle lecture\n    this.startVoicePlayback(message, audioUrl);\n  }\n\n  private startVoicePlayback(message: any, audioUrl: string): void {\n    const messageId = message.id;\n\n    try {\n      console.log(\n        '🎵 [Voice] Starting playback for:',\n        messageId,\n        'URL:',\n        audioUrl\n      );\n\n      this.currentAudio = new Audio(audioUrl);\n      this.playingMessageId = messageId;\n\n      // Initialiser les valeurs par défaut avec la nouvelle structure\n      const currentData = this.getVoicePlaybackData(messageId);\n      this.setVoicePlaybackData(messageId, {\n        progress: 0,\n        currentTime: 0,\n        speed: currentData.speed || 1,\n        duration: currentData.duration || 0,\n      });\n\n      // Configurer la vitesse de lecture\n      this.currentAudio.playbackRate = currentData.speed || 1;\n\n      // Événements audio\n      this.currentAudio.addEventListener('loadedmetadata', () => {\n        if (this.currentAudio) {\n          this.setVoicePlaybackData(messageId, {\n            duration: this.currentAudio.duration,\n          });\n          console.log(\n            '🎵 [Voice] Audio loaded, duration:',\n            this.currentAudio.duration\n          );\n        }\n      });\n\n      this.currentAudio.addEventListener('timeupdate', () => {\n        if (this.currentAudio && this.playingMessageId === messageId) {\n          const currentTime = this.currentAudio.currentTime;\n          const progress = (currentTime / this.currentAudio.duration) * 100;\n          this.setVoicePlaybackData(messageId, { currentTime, progress });\n          this.cdr.detectChanges();\n        }\n      });\n\n      this.currentAudio.addEventListener('ended', () => {\n        console.log('🎵 [Voice] Playback ended for:', messageId);\n        this.stopVoicePlayback();\n      });\n\n      this.currentAudio.addEventListener('error', (error) => {\n        console.error('🎵 [Voice] Audio error:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      });\n\n      // Démarrer la lecture\n      this.currentAudio\n        .play()\n        .then(() => {\n          console.log('🎵 [Voice] Playback started successfully');\n          this.toastService.showSuccess('🎵 Lecture du message vocal');\n        })\n        .catch((error) => {\n          console.error('🎵 [Voice] Error starting playback:', error);\n          this.toastService.showError('Impossible de lire le message vocal');\n          this.stopVoicePlayback();\n        });\n    } catch (error) {\n      console.error('🎵 [Voice] Error creating audio:', error);\n      this.toastService.showError('Erreur lors de la lecture audio');\n      this.stopVoicePlayback();\n    }\n  }\n\n  private stopVoicePlayback(): void {\n    if (this.currentAudio) {\n      console.log('🎵 [Voice] Stopping playback for:', this.playingMessageId);\n      this.currentAudio.pause();\n      this.currentAudio.currentTime = 0;\n      this.currentAudio = null;\n    }\n    this.playingMessageId = null;\n    this.cdr.detectChanges();\n  }\n\n  getVoiceUrl(message: any): string {\n    // Vérifier les propriétés directes d'audio\n    if (message.voiceUrl) return message.voiceUrl;\n    if (message.audioUrl) return message.audioUrl;\n    if (message.voice) return message.voice;\n\n    // Vérifier les attachments audio\n    const audioAttachment = message.attachments?.find(\n      (att: any) => att.type?.startsWith('audio/') || att.type === 'AUDIO'\n    );\n\n    if (audioAttachment) {\n      return audioAttachment.url || audioAttachment.path || '';\n    }\n\n    return '';\n  }\n\n  getVoiceWaves(message: any): number[] {\n    // Générer des waves basées sur l'ID du message pour la cohérence\n    const messageId = message.id || '';\n    const seed = messageId\n      .split('')\n      .reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);\n    const waves: number[] = [];\n\n    for (let i = 0; i < 16; i++) {\n      const height = 4 + ((seed + i * 7) % 20);\n      waves.push(height);\n    }\n\n    return waves;\n  }\n\n  getVoiceProgress(message: any): number {\n    const data = this.getVoicePlaybackData(message.id);\n    const totalWaves = 16;\n    return Math.floor((data.progress / 100) * totalWaves);\n  }\n\n  getVoiceCurrentTime(message: any): string {\n    const data = this.getVoicePlaybackData(message.id);\n    return this.formatAudioTime(data.currentTime);\n  }\n\n  getVoiceDuration(message: any): string {\n    const data = this.getVoicePlaybackData(message.id);\n    const duration = data.duration || message.metadata?.duration || 0;\n\n    if (typeof duration === 'string') {\n      return duration; // Déjà formaté\n    }\n\n    return this.formatAudioTime(duration);\n  }\n\n  private formatAudioTime(seconds: number): string {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  seekVoiceMessage(message: any, waveIndex: number): void {\n    const messageId = message.id;\n\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\n      return;\n    }\n\n    const totalWaves = 16;\n    const seekPercentage = (waveIndex / totalWaves) * 100;\n    const seekTime = (seekPercentage / 100) * this.currentAudio.duration;\n\n    this.currentAudio.currentTime = seekTime;\n    console.log('🎵 [Voice] Seeking to:', seekTime, 'seconds');\n  }\n\n  toggleVoiceSpeed(message: any): void {\n    const messageId = message.id;\n    const data = this.getVoicePlaybackData(messageId);\n\n    // Cycle entre 1x, 1.5x, 2x\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n\n    this.setVoicePlaybackData(messageId, { speed: newSpeed });\n\n    if (this.currentAudio && this.playingMessageId === messageId) {\n      this.currentAudio.playbackRate = newSpeed;\n    }\n\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.unsubscribe();\n\n    // Nettoyer les timers\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    // Nettoyer les ressources audio\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream?.getTracks().forEach((track) => track.stop());\n    }\n\n    // Nettoyer la lecture audio\n    this.stopVoicePlayback();\n  }\n}\n"], "mappings": ";;AAAA,SACEA,SAAS,EAGTC,SAAS,QAGJ,eAAe;AACtB,SAAiCC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,QAAQ,MAAM;AAGnC,SAASC,QAAQ,QAA4B,kCAAkC;AAMxE,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAmJ/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,cAA8B,EAC9BC,YAA0B,EAC1BC,GAAsB;IAJtB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IAlJb;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,QAAQ,GAAU,EAAE;IACpB,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,eAAe,GAAG,KAAK;IAEvB,KAAAC,gBAAgB,GAAQ,IAAI;IAE5B;IACA,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,mBAAmB,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,qBAAqB,GAAQ,IAAI;IACjC,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,YAAY,GAAG,OAAO,CAAC,CAAC;IACxB,KAAAC,MAAM,GAAG,CACP;MAAEC,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAS,CAAE,EACpE;MAAEH,EAAE,EAAE,MAAM;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAS,CAAE,EACrE;MAAEH,EAAE,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAS,CAAE,EACpE;MAAEH,EAAE,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAS,CAAE,CACrE;IACD,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAElB;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,sBAAsB,GAAG,CAAC;IAC1B,KAAAC,mBAAmB,GAAwC,MAAM;IACzD,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAClC,KAAAC,UAAU,GAAa,CACrB,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CACzD;IAED;IACQ,KAAAC,YAAY,GAA4B,IAAI;IAC5C,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,aAAa,GAOjB,EAAE;IAEN;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAA6B,IAAI;IACzC,KAAAC,YAAY,GAAG,CAAC;IACR,KAAAC,SAAS,GAAQ,IAAI;IAE7B;IACA,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAElD;IACA,KAAAC,eAAe,GAAU,CACvB;MACE9B,EAAE,EAAE,SAAS;MACbC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,IAAI;MACV6B,MAAM,EAAE,CACN;QAAEC,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAe,CAAE,EACtC;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAwB,CAAE;KAElD,EACD;MACED,EAAE,EAAE,QAAQ;MACZC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,IAAI;MACV6B,MAAM,EAAE,CACN;QAAEC,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAW,CAAE;KAErC,EACD;MACED,EAAE,EAAE,QAAQ;MACZC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,IAAI;MACV6B,MAAM,EAAE,CACN;QAAEC,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAa,CAAE,EACpC;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE+B,KAAK,EAAE,IAAI;QAAE/B,IAAI,EAAE;MAAO,CAAE;KAEjC,CACF;IACD,KAAAgC,qBAAqB,GAAG,IAAI,CAACH,eAAe,CAAC,CAAC,CAAC;IAE/C;IACiB,KAAAI,oBAAoB,GAAG,EAAE;IAClC,KAAAC,WAAW,GAAG,CAAC;IAEvB;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,KAAK;IACZ,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAG,IAAI3E,YAAY,EAAE;IASxC,IAAI,CAAC4E,WAAW,GAAG,IAAI,CAACxE,EAAE,CAACyE,KAAK,CAAC;MAC/BC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC/E,UAAU,CAACgF,QAAQ,EAAEhF,UAAU,CAACiF,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEA;EACAC,eAAeA,CAAA;IACb,OACE,CAAC,IAAI,CAACpE,gBAAgB,IAAI,IAAI,CAACiC,gBAAgB,IAAI,IAAI,CAACvB,gBAAgB;EAE5E;EAEA;EACQ2D,gBAAgBA,CAAA;IACtB,MAAMC,cAAc,GAAG,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,SAAS,CAAC;IACtD,IAAI,IAAI,CAACH,eAAe,EAAE,EAAE;MAC1BE,cAAc,EAAEE,OAAO,EAAE;KAC1B,MAAM;MACLF,cAAc,EAAEG,MAAM,EAAE;;EAE5B;EAEAC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQD,eAAeA,CAAA;IACrB;IACA,MAAME,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,OAAO;IAClE,IAAI,CAAC5D,YAAY,GAAG0D,UAAU;IAC9B,IAAI,CAAC3D,UAAU,GAAG2D,UAAU,KAAK,MAAM;IACvC,IAAI,CAACG,UAAU,CAACH,UAAU,CAAC;EAC7B;EAEA;EAEQG,UAAUA,CAACC,OAAe;IAChC,MAAMC,IAAI,GAAGC,QAAQ,CAACC,eAAe;IAErC;IACAF,IAAI,CAACG,SAAS,CAACC,MAAM,CACnB,MAAM,EACN,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,YAAY,CACb;IAED;IACAJ,IAAI,CAACG,SAAS,CAACE,GAAG,CAAC,SAASN,OAAO,EAAE,CAAC;IACtC,IAAIA,OAAO,KAAK,MAAM,EAAE;MACtBC,IAAI,CAACG,SAAS,CAACE,GAAG,CAAC,MAAM,CAAC;;EAE9B;EAEAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACpE,MAAM,CAACqE,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACrE,EAAE,KAAK,IAAI,CAACF,YAAY,CAAC;EAC5D;EAEAwE,mBAAmBA,CAAA;IACjB,IAAI,CAAClE,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;EAClD;EAEAmE,WAAWA,CAACX,OAAe;IACzB,IAAI,CAAC9D,YAAY,GAAG8D,OAAO;IAC3B,IAAI,CAAC/D,UAAU,GAAG+D,OAAO,KAAK,MAAM;IACpC,IAAI,CAACD,UAAU,CAACC,OAAO,CAAC;IACxBH,YAAY,CAACe,OAAO,CAAC,cAAc,EAAEZ,OAAO,CAAC;IAC7C,IAAI,CAACxD,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACf,YAAY,GAAG,KAAK;IAEzB,MAAMoF,SAAS,GACb,IAAI,CAAC1E,MAAM,CAACqE,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACrE,EAAE,KAAK4D,OAAO,CAAC,EAAE3D,IAAI,IAAI2D,OAAO;IAC5D,IAAI,CAACzF,YAAY,CAACuG,WAAW,CAAC,SAASD,SAAS,WAAW,CAAC;EAC9D;EAEQlB,mBAAmBA,CAAA;IACzB,IAAI,CAACoB,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA,IAAI,CAACtC,aAAa,CAAC2B,GAAG,CACpB,IAAI,CAAChG,cAAc,CAAC4G,aAAa,CAACC,SAAS,CAAC;MAC1CC,IAAI,EAAGC,YAAY,IAAI;QACrB,IAAIA,YAAY,EAAE;UAChB7B,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4B,YAAY,CAAC;UACvD,IAAI,CAACC,kBAAkB,CAACD,YAAY,CAAC;;MAEzC,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACf/B,OAAO,CAAC+B,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;KACD,CAAC,CACH;IAED;IACA,IAAI,CAAC5C,aAAa,CAAC2B,GAAG,CACpB,IAAI,CAAChG,cAAc,CAACkH,WAAW,CAACL,SAAS,CAAC;MACxCC,IAAI,EAAGK,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACRjC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgC,IAAI,CAAC;UAC5C,IAAI,CAAC7D,UAAU,GAAG6D,IAAI;;MAE1B,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACf/B,OAAO,CAAC+B,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC9D;KACD,CAAC,CACH;EACH;EAEQD,kBAAkBA,CAACD,YAA0B;IACnD;IACA;IACA7B,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjC4B,YAAY,CAACK,MAAM,CAACC,QAAQ,CAC7B;IAED;IACA,IAAI,CAACrH,cAAc,CAACsH,IAAI,CAAC,UAAU,CAAC;IAEpC;IACA;IACA;EACF;;EAEQb,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMc,UAAU,GAAGhC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/CN,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEoC,UAAU,CAAC;MAEzD,IAAI,CAACA,UAAU,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,WAAW,EAAE;QACtErC,OAAO,CAAC+B,KAAK,CAAC,gCAAgC,CAAC;QAC/C,IAAI,CAAC5G,aAAa,GAAG,IAAI;QACzB,IAAI,CAACC,eAAe,GAAG,KAAK;QAC5B;;MAGF,MAAMkH,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,UAAU,CAAC;MACnCrC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEqC,IAAI,CAAC;MAE3C;MACA,MAAMG,MAAM,GAAGH,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAAC1F,EAAE,IAAI0F,IAAI,CAACG,MAAM;MACjDzC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CyC,GAAG,EAAEJ,IAAI,CAACI,GAAG;QACb9F,EAAE,EAAE0F,IAAI,CAAC1F,EAAE;QACX6F,MAAM,EAAEH,IAAI,CAACG,MAAM;QACnBE,SAAS,EAAEF;OACZ,CAAC;MAEF,IAAIA,MAAM,EAAE;QACV,IAAI,CAACtH,aAAa,GAAGsH,MAAM;QAC3B,IAAI,CAACrH,eAAe,GAAGkH,IAAI,CAACH,QAAQ,IAAIG,IAAI,CAACzF,IAAI,IAAI,KAAK;QAC1DmD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;UACjDrD,EAAE,EAAE,IAAI,CAACzB,aAAa;UACtBgH,QAAQ,EAAE,IAAI,CAAC/G;SAChB,CAAC;OACH,MAAM;QACL4E,OAAO,CAAC+B,KAAK,CAAC,0CAA0C,EAAEO,IAAI,CAAC;QAC/D,IAAI,CAACnH,aAAa,GAAG,IAAI;QACzB,IAAI,CAACC,eAAe,GAAG,KAAK;;KAE/B,CAAC,OAAO2G,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAI,CAAC5G,aAAa,GAAG,IAAI;MACzB,IAAI,CAACC,eAAe,GAAG,KAAK;;EAEhC;EAEQoG,gBAAgBA,CAAA;IACtB,MAAMoB,cAAc,GAAG,IAAI,CAAC/H,KAAK,CAACgI,QAAQ,CAACC,QAAQ,CAAClD,GAAG,CAAC,IAAI,CAAC;IAC7DI,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2C,cAAc,CAAC;IAE5D,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAAC7H,YAAY,CAACgI,SAAS,CAAC,6BAA6B,CAAC;MAC1D;;IAGF,IAAI,CAACzH,SAAS,GAAG,IAAI;IACrB,IAAI,CAACR,cAAc,CAACkI,eAAe,CAACJ,cAAc,CAAC,CAACjB,SAAS,CAAC;MAC5DC,IAAI,EAAG3G,YAAY,IAAI;QACrB+E,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEhF,YAAY,CAAC;QACjE+E,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;UACxCrD,EAAE,EAAE3B,YAAY,EAAE2B,EAAE;UACpBqG,YAAY,EAAEhI,YAAY,EAAEgI,YAAY;UACxCC,iBAAiB,EAAEjI,YAAY,EAAEgI,YAAY,EAAEE,MAAM;UACrDC,OAAO,EAAEnI,YAAY,EAAEmI,OAAO;UAC9BlI,QAAQ,EAAED,YAAY,EAAEC,QAAQ;UAChCmI,aAAa,EAAEpI,YAAY,EAAEC,QAAQ,EAAEiI;SACxC,CAAC;QACF,IAAI,CAAClI,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACqI,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;QAEnB;QACA,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC;MACDzB,KAAK,EAAGA,KAAK,IAAI;QACf/B,OAAO,CAAC+B,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAAChH,YAAY,CAACgI,SAAS,CACzB,8CAA8C,CAC/C;QACD,IAAI,CAACzH,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQgI,mBAAmBA,CAAA;IACzB,IACE,CAAC,IAAI,CAACrI,YAAY,EAAEgI,YAAY,IAChC,IAAI,CAAChI,YAAY,CAACgI,YAAY,CAACE,MAAM,KAAK,CAAC,EAC3C;MACAnD,OAAO,CAACyD,IAAI,CAAC,uCAAuC,CAAC;MACrD,IAAI,CAACpI,gBAAgB,GAAG,IAAI;MAC5B;;IAGF2E,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC9E,aAAa,CAAC;IACnD6E,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAChF,YAAY,CAACgI,YAAY,CAAC;IAEhE;IACA;IAEA,IAAI,IAAI,CAAChI,YAAY,CAACmI,OAAO,EAAE;MAC7B;MACA;MACA,IAAI,CAAC/H,gBAAgB,GAAG,IAAI,CAACJ,YAAY,CAACgI,YAAY,CAACjC,IAAI,CAAE0C,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAC9G,EAAE,IAAI8G,CAAC,CAAChB,GAAG;QACnC,OAAOkB,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAACzI,aAAa,CAAC;MAC7D,CAAC,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAACJ,YAAY,CAACgI,YAAY,CAACjC,IAAI,CAAE0C,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAC9G,EAAE,IAAI8G,CAAC,CAAChB,GAAG;QACnC1C,OAAO,CAACC,GAAG,CACT,2BAA2B,EAC3B0D,aAAa,EACb,uBAAuB,EACvB,IAAI,CAACxI,aAAa,CACnB;QACD,OAAOyI,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAACzI,aAAa,CAAC;MAC7D,CAAC,CAAC;;IAGJ;IACA,IAAI,CAAC,IAAI,CAACE,gBAAgB,IAAI,IAAI,CAACJ,YAAY,CAACgI,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;MACvEnD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,IAAI,CAAC5E,gBAAgB,GAAG,IAAI,CAACJ,YAAY,CAACgI,YAAY,CAAC,CAAC,CAAC;MAEzD;MACA,IAAI,IAAI,CAAChI,YAAY,CAACgI,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMU,kBAAkB,GACtB,IAAI,CAACxI,gBAAgB,CAACuB,EAAE,IAAI,IAAI,CAACvB,gBAAgB,CAACqH,GAAG;QACvD,IAAIkB,MAAM,CAACC,kBAAkB,CAAC,KAAKD,MAAM,CAAC,IAAI,CAACzI,aAAa,CAAC,EAAE;UAC7D6E,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACD,IAAI,CAAC5E,gBAAgB,GAAG,IAAI,CAACJ,YAAY,CAACgI,YAAY,CAAC,CAAC,CAAC;;;;IAK/D;IACA,IAAI,IAAI,CAAC5H,gBAAgB,EAAE;MACzB2E,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QACnDrD,EAAE,EAAE,IAAI,CAACvB,gBAAgB,CAACuB,EAAE,IAAI,IAAI,CAACvB,gBAAgB,CAACqH,GAAG;QACzDP,QAAQ,EAAE,IAAI,CAAC9G,gBAAgB,CAAC8G,QAAQ;QACxC2B,KAAK,EAAE,IAAI,CAACzI,gBAAgB,CAACyI,KAAK;QAClCC,QAAQ,EAAE,IAAI,CAAC1I,gBAAgB,CAAC0I;OACjC,CAAC;MAEF;MACA/D,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAC5E,gBAAgB,CAAC8G,QAAQ,CAC/B;MACDnC,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/B,IAAI,CAAC5E,gBAAgB,CAAC8G,QAAQ,CAC/B;KACF,MAAM;MACLnC,OAAO,CAAC+B,KAAK,CAAC,uDAAuD,CAAC;MACtE/B,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAChF,YAAY,CAACgI,YAAY,CAAC;MACzEjD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC9E,aAAa,CAAC;MAEnD;MACA6E,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;IAGrD;IACA,IAAI,CAACP,gBAAgB,EAAE;EACzB;EAEQ6D,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACtI,YAAY,EAAE2B,EAAE,EAAE;IAE5B;IACA,IAAI1B,QAAQ,GAAG,IAAI,CAACD,YAAY,CAACC,QAAQ,IAAI,EAAE;IAE/C;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAAC8I,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;MAC/C,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACI,SAAS,IAAIJ,CAAC,CAACK,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,MAAMC,KAAK,GAAG,IAAIJ,IAAI,CAACF,CAAC,CAACG,SAAS,IAAIH,CAAC,CAACI,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,OAAOJ,KAAK,GAAGK,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC;;IAEFxE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CwE,KAAK,EAAE,IAAI,CAACvJ,QAAQ,CAACiI,MAAM;MAC3BuB,KAAK,EAAE,IAAI,CAACxJ,QAAQ,CAAC,CAAC,CAAC,EAAEoE,OAAO;MAChCqF,IAAI,EAAE,IAAI,CAACzJ,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACiI,MAAM,GAAG,CAAC,CAAC,EAAE7D;KAChD,CAAC;IAEF,IAAI,CAAC9D,eAAe,GAAG,IAAI,CAACN,QAAQ,CAACiI,MAAM,KAAK,IAAI,CAACrE,oBAAoB;IACzE,IAAI,CAACxD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACsJ,cAAc,EAAE;EACvB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACtJ,aAAa,IAAI,CAAC,IAAI,CAACC,eAAe,IAAI,CAAC,IAAI,CAACP,YAAY,EAAE2B,EAAE,EACvE;IAEF,IAAI,CAACrB,aAAa,GAAG,IAAI;IACzB,IAAI,CAACwD,WAAW,EAAE;IAElB;IACA,MAAM+F,MAAM,GAAG,IAAI,CAAC5J,QAAQ,CAACiI,MAAM;IAEnC,IAAI,CAACrI,cAAc,CAACiK,WAAW,CAC7B,IAAI,CAAC5J,aAAc;IAAE;IACrB,IAAI,CAACE,gBAAgB,EAAEuB,EAAE,IAAI,IAAI,CAACvB,gBAAgB,EAAEqH,GAAI;IAAE;IAC1D,IAAI,CAACzH,YAAY,CAAC2B,EAAE,EACpB,IAAI,CAACmC,WAAW,EAChB,IAAI,CAACD,oBAAoB,CAC1B,CAAC6C,SAAS,CAAC;MACVC,IAAI,EAAGoD,WAAkB,IAAI;QAC3B,IAAIA,WAAW,IAAIA,WAAW,CAAC7B,MAAM,GAAG,CAAC,EAAE;UACzC;UACA,IAAI,CAACjI,QAAQ,GAAG,CAAC,GAAG8J,WAAW,CAACC,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC/J,QAAQ,CAAC;UAC5D,IAAI,CAACM,eAAe,GAClBwJ,WAAW,CAAC7B,MAAM,KAAK,IAAI,CAACrE,oBAAoB;SACnD,MAAM;UACL,IAAI,CAACtD,eAAe,GAAG,KAAK;;QAE9B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACDwG,KAAK,EAAGA,KAAK,IAAI;QACf/B,OAAO,CAAC+B,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAAChH,YAAY,CAACgI,SAAS,CAAC,wCAAwC,CAAC;QACrE,IAAI,CAACxH,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACwD,WAAW,EAAE,CAAC,CAAC;MACtB;KACD,CAAC;EACJ;;EAEQyE,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACvI,YAAY,EAAE2B,EAAE,EAAE;MAC1BoD,OAAO,CAACyD,IAAI,CAAC,kDAAkD,CAAC;MAChE;;IAGFzD,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzD,IAAI,CAAChF,YAAY,CAAC2B,EAAE,CACrB;IAED;IACAoD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACpD,IAAI,CAACd,aAAa,CAAC2B,GAAG,CACpB,IAAI,CAAChG,cAAc,CAACoK,sBAAsB,CACxC,IAAI,CAACjK,YAAY,CAAC2B,EAAE,CACrB,CAAC+E,SAAS,CAAC;MACVC,IAAI,EAAGuD,UAAe,IAAI;QACxBnF,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEkF,UAAU,CAAC;QACpEnF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;UACnCrD,EAAE,EAAEuI,UAAU,CAACvI,EAAE;UACjBwI,IAAI,EAAED,UAAU,CAACC,IAAI;UACrB9F,OAAO,EAAE6F,UAAU,CAAC7F,OAAO;UAC3B+F,MAAM,EAAEF,UAAU,CAACE,MAAM;UACzBC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;UAC7BC,UAAU,EAAEJ,UAAU,CAACI,UAAU;UACjCC,WAAW,EAAEL,UAAU,CAACK;SACzB,CAAC;QAEF;QACAxF,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnC,IAAI,CAACwF,cAAc,CAACN,UAAU,CAAC,CAChC;QACDnF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACyF,QAAQ,CAACP,UAAU,CAAC,CAAC;QAC/DnF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC0F,OAAO,CAACR,UAAU,CAAC,CAAC;QAC7DnF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC2F,WAAW,CAACT,UAAU,CAAC,CAAC;QAClE,IAAIA,UAAU,CAACK,WAAW,EAAE;UAC1BL,UAAU,CAACK,WAAW,CAACK,OAAO,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAI;YACzD/F,OAAO,CAACC,GAAG,CAAC,yBAAyB8F,KAAK,GAAG,EAAE;cAC7CX,IAAI,EAAEU,GAAG,CAACV,IAAI;cACdY,GAAG,EAAEF,GAAG,CAACE,GAAG;cACZC,IAAI,EAAEH,GAAG,CAACG,IAAI;cACdpJ,IAAI,EAAEiJ,GAAG,CAACjJ,IAAI;cACdqJ,IAAI,EAAEJ,GAAG,CAACI;aACX,CAAC;UACJ,CAAC,CAAC;;QAGJ;QACA,MAAMC,aAAa,GAAG,IAAI,CAACjL,QAAQ,CAACkL,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAACzJ,EAAE,KAAKuI,UAAU,CAACvI,EAAE,CAClC;QACD,IAAI,CAACuJ,aAAa,EAAE;UAClB;UACA,IAAI,CAACjL,QAAQ,CAACoL,IAAI,CAACnB,UAAU,CAAC;UAC9BnF,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAAC/E,QAAQ,CAACiI,MAAM,CACrB;UAED;UACA,IAAI,CAACnI,GAAG,CAACuL,aAAa,EAAE;UAExB;UACAC,UAAU,CAAC,MAAK;YACd,IAAI,CAAC5B,cAAc,EAAE;UACvB,CAAC,EAAE,EAAE,CAAC;UAEN;UACA,MAAMU,QAAQ,GAAGH,UAAU,CAACE,MAAM,EAAEzI,EAAE,IAAIuI,UAAU,CAACG,QAAQ;UAC7DtF,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;YAC9DqF,QAAQ;YACRnK,aAAa,EAAE,IAAI,CAACA,aAAa;YACjCsL,gBAAgB,EAAEnB,QAAQ,KAAK,IAAI,CAACnK;WACrC,CAAC;UAEF,IAAImK,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAACnK,aAAa,EAAE;YAC/C,IAAI,CAACuL,iBAAiB,CAACvB,UAAU,CAACvI,EAAE,CAAC;;;MAG3C,CAAC;MACDmF,KAAK,EAAGA,KAAK,IAAI;QACf/B,OAAO,CAAC+B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC,CACH;IAED;IACA/B,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7D,IAAI,CAACd,aAAa,CAAC2B,GAAG,CACpB,IAAI,CAAChG,cAAc,CAAC6L,0BAA0B,CAC5C,IAAI,CAAC1L,YAAY,CAAC2B,EAAE,CACrB,CAAC+E,SAAS,CAAC;MACVC,IAAI,EAAGgF,UAAe,IAAI;QACxB5G,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2G,UAAU,CAAC;QAExD;QACA,IAAIA,UAAU,CAACnE,MAAM,KAAK,IAAI,CAACtH,aAAa,EAAE;UAC5C,IAAI,CAACa,iBAAiB,GAAG4K,UAAU,CAAC5H,QAAQ;UAC5C,IAAI,CAAChE,GAAG,CAACuL,aAAa,EAAE;;MAE5B,CAAC;MACDxE,KAAK,EAAGA,KAAU,IAAI;QACpB/B,OAAO,CAAC+B,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;KACD,CAAC,CACH;IAED;IACA,IAAI,CAAC5C,aAAa,CAAC2B,GAAG,CACpB,IAAI,CAAChG,cAAc,CAAC+L,8BAA8B,CAChD,IAAI,CAAC5L,YAAY,CAAC2B,EAAE,CACrB,CAAC+E,SAAS,CAAC;MACVC,IAAI,EAAGkF,kBAAuB,IAAI;QAChC9G,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE6G,kBAAkB,CAAC;QAE1D;QACA,IAAIA,kBAAkB,CAAClK,EAAE,KAAK,IAAI,CAAC3B,YAAY,CAAC2B,EAAE,EAAE;UAClD,IAAI,CAAC3B,YAAY,GAAG;YAAE,GAAG,IAAI,CAACA,YAAY;YAAE,GAAG6L;UAAkB,CAAE;UACnE,IAAI,CAAC9L,GAAG,CAACuL,aAAa,EAAE;;MAE5B,CAAC;MACDxE,KAAK,EAAGA,KAAU,IAAI;QACpB/B,OAAO,CAAC+B,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D;KACD,CAAC,CACH;EACH;EAEQ2E,iBAAiBA,CAACK,SAAiB;IACzC,IAAI,CAACjM,cAAc,CAAC4L,iBAAiB,CAACK,SAAS,CAAC,CAACpF,SAAS,CAAC;MACzDC,IAAI,EAAEA,CAAA,KAAK;QACT5B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE8G,SAAS,CAAC;MACrD,CAAC;MACDhF,KAAK,EAAGA,KAAK,IAAI;QACf/B,OAAO,CAAC+B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC;EACJ;EAEA;EACAiF,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC5H,WAAW,CAAC6H,KAAK,IAAI,CAAC,IAAI,CAAChM,YAAY,EAAE2B,EAAE,EAAE;IAEvD,MAAM0C,OAAO,GAAG,IAAI,CAACF,WAAW,CAACQ,GAAG,CAAC,SAAS,CAAC,EAAEsH,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAAC7H,OAAO,EAAE;IAEd,MAAMiG,UAAU,GAAG,IAAI,CAAClK,gBAAgB,EAAEuB,EAAE,IAAI,IAAI,CAACvB,gBAAgB,EAAEqH,GAAG;IAE1E,IAAI,CAAC6C,UAAU,EAAE;MACf,IAAI,CAACxK,YAAY,CAACgI,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF;IACA,IAAI,CAAChH,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC2D,gBAAgB,EAAE;IAEvBM,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCX,OAAO;MACPiG,UAAU;MACV3C,cAAc,EAAE,IAAI,CAAC3H,YAAY,CAAC2B;KACnC,CAAC;IAEF,IAAI,CAAC9B,cAAc,CAACkM,WAAW,CAC7BzB,UAAU,EACVjG,OAAO,EACP8H,SAAS,EACT,MAAa,EACb,IAAI,CAACnM,YAAY,CAAC2B,EAAE,CACrB,CAAC+E,SAAS,CAAC;MACVC,IAAI,EAAGyF,OAAY,IAAI;QACrBrH,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEoH,OAAO,CAAC;QAEpD;QACA,MAAMlB,aAAa,GAAG,IAAI,CAACjL,QAAQ,CAACkL,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAACzJ,EAAE,KAAKyK,OAAO,CAACzK,EAAE,CAC/B;QACD,IAAI,CAACuJ,aAAa,EAAE;UAClB,IAAI,CAACjL,QAAQ,CAACoL,IAAI,CAACe,OAAO,CAAC;UAC3BrH,OAAO,CAACC,GAAG,CACT,wCAAwC,EACxC,IAAI,CAAC/E,QAAQ,CAACiI,MAAM,CACrB;;QAGH;QACA,IAAI,CAAC/D,WAAW,CAACkI,KAAK,EAAE;QACxB,IAAI,CAACvL,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC2D,gBAAgB,EAAE;QAEvB;QACA,IAAI,CAAC1E,GAAG,CAACuL,aAAa,EAAE;QACxBC,UAAU,CAAC,MAAK;UACd,IAAI,CAAC5B,cAAc,EAAE;QACvB,CAAC,EAAE,EAAE,CAAC;MACR,CAAC;MACD7C,KAAK,EAAGA,KAAU,IAAI;QACpB/B,OAAO,CAAC+B,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D,IAAI,CAAChH,YAAY,CAACgI,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAAChH,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC2D,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEAkF,cAAcA,CAAA;IACZ4B,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACe,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAC,gBAAgBA,CAACC,UAAgC;IAC/C,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IAEpC,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CACzB,CAAC5D,IAAI,CAAC6D,GAAG,EAAE,GAAG,IAAI7D,IAAI,CAACyD,UAAU,CAAC,CAACtD,OAAO,EAAE,IAAI,KAAK,CACtD;IAED,IAAIuD,QAAQ,GAAG,CAAC,EAAE,OAAO,aAAa;IACtC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,UAAUA,QAAQ,MAAM;IAClD,IAAIA,QAAQ,GAAG,IAAI,EAAE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC,GAAG;IAClE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,IAAI,CAAC,GAAG;EACjD;EAEA;EACAI,oBAAoBA,CAACnB,SAAiB;IACpC,OACE,IAAI,CAAChJ,aAAa,CAACgJ,SAAS,CAAC,IAAI;MAC/BoB,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,CAAC;MACdC,KAAK,EAAE;KACR;EAEL;EAEQC,oBAAoBA,CAC1BxB,SAAiB,EACjByB,IAAkD;IAElD,IAAI,CAACzK,aAAa,CAACgJ,SAAS,CAAC,GAAG;MAC9B,GAAG,IAAI,CAACmB,oBAAoB,CAACnB,SAAS,CAAC;MACvC,GAAGyB;KACJ;EACH;EAEA;EAEA;EACA;EAEA;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACpN,gBAAgB,EAAEuB,EAAE,EAAE;MAC9B,IAAI,CAAC7B,YAAY,CAACgI,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,IAAI,CAAC9E,QAAQ,GAAG,OAAO;IACvB,IAAI,CAACD,QAAQ,GAAG,IAAI;IACpBgC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC5E,gBAAgB,CAAC8G,QAAQ,CAAC;EAC7E;EAEAuG,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACrN,gBAAgB,EAAEuB,EAAE,EAAE;MAC9B,IAAI,CAAC7B,YAAY,CAACgI,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,IAAI,CAAC9E,QAAQ,GAAG,OAAO;IACvB,IAAI,CAACD,QAAQ,GAAG,IAAI;IACpBgC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC5E,gBAAgB,CAAC8G,QAAQ,CAAC;EAC7E;EAEAwG,OAAOA,CAAA;IACL,IAAI,CAAC3K,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACG,UAAU,GAAG,IAAI;IACtB4B,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;EAEA;EACA;EAEA;EAEA2I,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOd,IAAI,CAACe,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOd,IAAI,CAACe,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAE,YAAYA,CAAC1B,OAAY;IACvB,MAAM2B,cAAc,GAAG3B,OAAO,CAAC7B,WAAW,EAAExE,IAAI,CAC7C8E,GAAQ,IAAK,CAACA,GAAG,CAACV,IAAI,EAAE6D,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAID,cAAc,EAAEhD,GAAG,EAAE;MACvB,MAAMkD,IAAI,GAAGxI,QAAQ,CAACyI,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAGJ,cAAc,CAAChD,GAAG;MAC9BkD,IAAI,CAACG,QAAQ,GAAGL,cAAc,CAACnM,IAAI,IAAI,MAAM;MAC7CqM,IAAI,CAACI,MAAM,GAAG,QAAQ;MACtB5I,QAAQ,CAAC6I,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,EAAE;MACZ/I,QAAQ,CAAC6I,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/B,IAAI,CAACnO,YAAY,CAACuG,WAAW,CAAC,wBAAwB,CAAC;;EAE3D;EAEA;EAEAqI,WAAWA,CAAA;IACT,MAAMC,QAAQ,GAAG,IAAI,CAACnN,UAAU,GAAG,OAAO,GAAG,MAAM;IACnD,IAAI,CAAC0E,WAAW,CAACyI,QAAQ,CAAC;EAC5B;EAEAC,YAAYA,CAAA;IACV,IAAI,CAAC/N,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACH,UAAU,GAAG,IAAI,CAACG,UAAU;EACnC;EAEAgO,cAAcA,CAAA;IACZ,IAAI,CAAC7N,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA8N,qBAAqBA,CAAA;IACnB;IACA/J,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;EAC/C;EAEA;EAEA+J,aAAaA,CAAA;IACX,IAAI,CAACvO,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACO,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACK,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACS,iBAAiB,GAAG,KAAK;EAChC;EAEAiN,oBAAoBA,CAAC5C,OAAY,EAAE6C,KAAiB;IAClDA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAAChO,eAAe,GAAGkL,OAAO;IAC9B,IAAI,CAACjL,mBAAmB,GAAG;MAAEC,CAAC,EAAE6N,KAAK,CAACE,OAAO;MAAE9N,CAAC,EAAE4N,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAACnO,sBAAsB,GAAG,IAAI;EACpC;EAEAoO,kBAAkBA,CAACjD,OAAY,EAAE6C,KAAiB;IAChDA,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAAC/N,qBAAqB,GAAG6K,OAAO;IACpC,IAAI,CAACjL,mBAAmB,GAAG;MAAEC,CAAC,EAAE6N,KAAK,CAACE,OAAO;MAAE9N,CAAC,EAAE4N,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAAC9N,kBAAkB,GAAG,IAAI;EAChC;EAEAiO,UAAUA,CAAC5L,KAAa;IACtB,IAAI,IAAI,CAACpC,qBAAqB,EAAE;MAC9B,IAAI,CAACiO,cAAc,CAAC,IAAI,CAACjO,qBAAqB,CAACI,EAAE,EAAEgC,KAAK,CAAC;;IAE3D,IAAI,CAACrC,kBAAkB,GAAG,KAAK;EACjC;EAEAkO,cAAcA,CAAC1D,SAAiB,EAAEnI,KAAa;IAC7CoB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAErB,KAAK,EAAE,cAAc,EAAEmI,SAAS,CAAC;IACtE;EACF;;EAEA2D,cAAcA,CAACC,QAAa,EAAElI,MAAc;IAC1C,OAAOkI,QAAQ,CAAClI,MAAM,KAAKA,MAAM;EACnC;EAEAmI,cAAcA,CAACvD,OAAY;IACzBrH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEoH,OAAO,CAACzK,EAAE,CAAC;IAClD,IAAI,CAACoN,aAAa,EAAE;EACtB;EAEAa,cAAcA,CAACxD,OAAY;IACzBrH,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoH,OAAO,CAACzK,EAAE,CAAC;IACjD,IAAI,CAACoN,aAAa,EAAE;EACtB;EAEAc,aAAaA,CAACzD,OAAY;IACxBrH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEoH,OAAO,CAACzK,EAAE,CAAC;IAChD,IAAI,CAACoN,aAAa,EAAE;EACtB;EAEA;EAEAe,iBAAiBA,CAAA;IACf,IAAI,CAACtP,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAuP,mBAAmBA,CAACC,QAAa;IAC/B,IAAI,CAACpM,qBAAqB,GAAGoM,QAAQ;EACvC;EAEAC,oBAAoBA,CAACD,QAAa;IAChC,OAAOA,QAAQ,EAAEtM,MAAM,IAAI,EAAE;EAC/B;EAEAwM,WAAWA,CAACvM,KAAU;IACpB,MAAMwM,cAAc,GAAG,IAAI,CAAChM,WAAW,CAACQ,GAAG,CAAC,SAAS,CAAC,EAAEsH,KAAK,IAAI,EAAE;IACnE,MAAMmE,UAAU,GAAGD,cAAc,GAAGxM,KAAK,CAACA,KAAK;IAC/C,IAAI,CAACQ,WAAW,CAACkM,UAAU,CAAC;MAAEhM,OAAO,EAAE+L;IAAU,CAAE,CAAC;IACpD,IAAI,CAAC5P,eAAe,GAAG,KAAK;EAC9B;EAEA8P,oBAAoBA,CAAA;IAClB,IAAI,CAAC7P,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA;EACA;EAEA;EACA;EAEA;EAEA;EACA;EAEA;EAEA8P,gBAAgBA,CAACzF,KAAa,EAAEsB,OAAY;IAC1C,OAAOA,OAAO,CAACzK,EAAE,IAAIyK,OAAO,CAAC3E,GAAG,IAAIqD,KAAK,CAAC0F,QAAQ,EAAE;EACtD;EAEA;EAEAC,cAAcA,CAAA;IACZ1L,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC,MAAM0L,WAAW,GAAG;MAClB/O,EAAE,EAAE,QAAQwH,IAAI,CAAC6D,GAAG,EAAE,EAAE;MACxB3I,OAAO,EAAE,mBAAmB,IAAI8E,IAAI,EAAE,CAACwH,kBAAkB,EAAE,EAAE;MAC7DvH,SAAS,EAAE,IAAID,IAAI,EAAE,CAACyH,WAAW,EAAE;MACnCxG,MAAM,EAAE;QACNzI,EAAE,EAAE,IAAI,CAACvB,gBAAgB,EAAEuB,EAAE,IAAI,WAAW;QAC5CuF,QAAQ,EAAE,IAAI,CAAC9G,gBAAgB,EAAE8G,QAAQ,IAAI,WAAW;QACxD2B,KAAK,EACH,IAAI,CAACzI,gBAAgB,EAAEyI,KAAK,IAAI;OACnC;MACDsB,IAAI,EAAE,MAAM;MACZ0G,MAAM,EAAE;KACT;IACD,IAAI,CAAC5Q,QAAQ,CAACoL,IAAI,CAACqF,WAAW,CAAC;IAC/B,IAAI,CAAC3Q,GAAG,CAACuL,aAAa,EAAE;IACxBC,UAAU,CAAC,MAAM,IAAI,CAAC5B,cAAc,EAAE,EAAE,EAAE,CAAC;EAC7C;EAEAmH,mBAAmBA,CAAA;IACjB,OACE,IAAI,CAAC9Q,YAAY,EAAEmI,OAAO,IAC1B,IAAI,CAACnI,YAAY,EAAEgI,YAAY,EAAEE,MAAM,GAAG,CAAC,IAC3C,KAAK;EAET;EAEA6I,UAAUA,CAAA;IACRhM,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC,IAAI,CAACvE,kBAAkB,GAAG,KAAK;IAC/B;EACF;;EAEAuQ,SAASA,CAACC,MAAc;IACtB,MAAMC,YAAY,GAAGzL,QAAQ,CAAC0L,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChB,MAAME,gBAAgB,GAAGF,YAAY,CAACG,KAAK,CAACC,SAAS,IAAI,UAAU;MACnE,MAAMC,YAAY,GAAGC,UAAU,CAC7BJ,gBAAgB,CAACK,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CACvD;MACD,MAAMC,QAAQ,GAAG5E,IAAI,CAAC6E,GAAG,CAAC,GAAG,EAAE7E,IAAI,CAAC8E,GAAG,CAAC,CAAC,EAAEL,YAAY,GAAGN,MAAM,CAAC,CAAC;MAClEC,YAAY,CAACG,KAAK,CAACC,SAAS,GAAG,SAASI,QAAQ,GAAG;MACnD,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChBR,YAAY,CAACvL,SAAS,CAACE,GAAG,CAAC,QAAQ,CAAC;OACrC,MAAM;QACLqL,YAAY,CAACvL,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;;EAG7C;EAEAiM,SAASA,CAAA;IACP,MAAMX,YAAY,GAAGzL,QAAQ,CAAC0L,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChBA,YAAY,CAACG,KAAK,CAACC,SAAS,GAAG,UAAU;MACzCJ,YAAY,CAACvL,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;EAE3C;EAEA;EACA;EACA;EACA;EAEAkM,gBAAgBA,CAAC3H,IAAa;IAC5B,MAAM4H,KAAK,GAAG,IAAI,CAACC,SAAS,EAAExF,aAAa;IAC3C,IAAI,CAACuF,KAAK,EAAE;MACVhN,OAAO,CAAC+B,KAAK,CAAC,8BAA8B,CAAC;MAC7C;;IAGF;IACA,IAAIqD,IAAI,KAAK,OAAO,EAAE;MACpB4H,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAI9H,IAAI,KAAK,OAAO,EAAE;MAC3B4H,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAI9H,IAAI,KAAK,UAAU,EAAE;MAC9B4H,KAAK,CAACE,MAAM,GAAG,iCAAiC;KACjD,MAAM;MACLF,KAAK,CAACE,MAAM,GAAG,KAAK;;IAGtB;IACAF,KAAK,CAAC9F,KAAK,GAAG,EAAE;IAEhB;IACA8F,KAAK,CAACvD,KAAK,EAAE;IACb,IAAI,CAAC/N,kBAAkB,GAAG,KAAK;EACjC;EAEAyR,iBAAiBA,CAAC9I,SAAwB;IACxC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAM+I,IAAI,GAAG,IAAIhJ,IAAI,CAACC,SAAS,CAAC;IAChC,OAAO+I,IAAI,CAACxB,kBAAkB,CAAC,OAAO,EAAE;MACtCyB,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAC,mBAAmBA,CAAClJ,SAAwB;IAC1C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAM+I,IAAI,GAAG,IAAIhJ,IAAI,CAACC,SAAS,CAAC;IAChC,MAAMmJ,KAAK,GAAG,IAAIpJ,IAAI,EAAE;IACxB,MAAMqJ,SAAS,GAAG,IAAIrJ,IAAI,CAACoJ,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIP,IAAI,CAACQ,YAAY,EAAE,KAAKJ,KAAK,CAACI,YAAY,EAAE,EAAE;MAChD,OAAO,aAAa;KACrB,MAAM,IAAIR,IAAI,CAACQ,YAAY,EAAE,KAAKH,SAAS,CAACG,YAAY,EAAE,EAAE;MAC3D,OAAO,MAAM;KACd,MAAM;MACL,OAAOR,IAAI,CAACS,kBAAkB,CAAC,OAAO,CAAC;;EAE3C;EAEAC,oBAAoBA,CAACxO,OAAe;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB;IACA,MAAMyO,QAAQ,GAAG,sBAAsB;IACvC,OAAOzO,OAAO,CAAC0O,OAAO,CACpBD,QAAQ,EACR,qEAAqE,CACtE;EACH;EAEAE,uBAAuBA,CAAClI,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,MAAMmI,cAAc,GAAG,IAAI,CAAChT,QAAQ,CAAC6K,KAAK,CAAC;IAC3C,MAAMoI,eAAe,GAAG,IAAI,CAACjT,QAAQ,CAAC6K,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACmI,cAAc,EAAE7J,SAAS,IAAI,CAAC8J,eAAe,EAAE9J,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAM+J,WAAW,GAAG,IAAIhK,IAAI,CAAC8J,cAAc,CAAC7J,SAAS,CAAC,CAACuJ,YAAY,EAAE;IACrE,MAAMS,YAAY,GAAG,IAAIjK,IAAI,CAAC+J,eAAe,CAAC9J,SAAS,CAAC,CAACuJ,YAAY,EAAE;IAEvE,OAAOQ,WAAW,KAAKC,YAAY;EACrC;EAEAC,gBAAgBA,CAACvI,KAAa;IAC5B,MAAMmI,cAAc,GAAG,IAAI,CAAChT,QAAQ,CAAC6K,KAAK,CAAC;IAC3C,MAAMwI,WAAW,GAAG,IAAI,CAACrT,QAAQ,CAAC6K,KAAK,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACwI,WAAW,EAAE,OAAO,IAAI;IAE7B,OAAOL,cAAc,CAAC7I,MAAM,EAAEzI,EAAE,KAAK2R,WAAW,CAAClJ,MAAM,EAAEzI,EAAE;EAC7D;EAEA4R,oBAAoBA,CAACzI,KAAa;IAChC,MAAMmI,cAAc,GAAG,IAAI,CAAChT,QAAQ,CAAC6K,KAAK,CAAC;IAC3C,MAAMoI,eAAe,GAAG,IAAI,CAACjT,QAAQ,CAAC6K,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACoI,eAAe,EAAE,OAAO,IAAI;IAEjC,OAAOD,cAAc,CAAC7I,MAAM,EAAEzI,EAAE,KAAKuR,eAAe,CAAC9I,MAAM,EAAEzI,EAAE;EACjE;EAEA6I,cAAcA,CAAC4B,OAAY;IACzB;IACA,IAAIA,OAAO,CAACjC,IAAI,EAAE;MAChB,IAAIiC,OAAO,CAACjC,IAAI,KAAK,OAAO,IAAIiC,OAAO,CAACjC,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAIiC,OAAO,CAACjC,IAAI,KAAK,OAAO,IAAIiC,OAAO,CAACjC,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAIiC,OAAO,CAACjC,IAAI,KAAK,OAAO,IAAIiC,OAAO,CAACjC,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAIiC,OAAO,CAACjC,IAAI,KAAK,eAAe,EAAE,OAAO,OAAO;MACpD,IAAIiC,OAAO,CAACjC,IAAI,KAAK,MAAM,IAAIiC,OAAO,CAACjC,IAAI,KAAK,MAAM,EAAE,OAAO,MAAM;;IAGvE;IACA,IAAIiC,OAAO,CAAC7B,WAAW,IAAI6B,OAAO,CAAC7B,WAAW,CAACrC,MAAM,GAAG,CAAC,EAAE;MACzD,MAAMsL,UAAU,GAAGpH,OAAO,CAAC7B,WAAW,CAAC,CAAC,CAAC;MACzC,IAAIiJ,UAAU,CAACrJ,IAAI,EAAE6D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIwF,UAAU,CAACrJ,IAAI,EAAE6D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIwF,UAAU,CAACrJ,IAAI,EAAE6D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,OAAO,MAAM;;IAGf;IACA,IAAI5B,OAAO,CAACqH,QAAQ,IAAIrH,OAAO,CAACsH,QAAQ,IAAItH,OAAO,CAACuH,KAAK,EAAE,OAAO,OAAO;IAEzE,OAAO,MAAM;EACf;EAEAlJ,QAAQA,CAAC2B,OAAY;IACnBrH,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEoH,OAAO,CAACzK,EAAE,EAAE;MAClEwI,IAAI,EAAEiC,OAAO,CAACjC,IAAI;MAClBI,WAAW,EAAE6B,OAAO,CAAC7B,WAAW;MAChCqJ,QAAQ,EAAExH,OAAO,CAACwH,QAAQ;MAC1B/K,KAAK,EAAEuD,OAAO,CAACvD;KAChB,CAAC;IAEF;IACA,IAAIuD,OAAO,CAACjC,IAAI,KAAK,OAAO,IAAIiC,OAAO,CAACjC,IAAI,KAAK,OAAO,EAAE;MACxDpF,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,OAAO,IAAI;;IAGb;IACA,MAAM6O,kBAAkB,GACtBzH,OAAO,CAAC7B,WAAW,EAAEY,IAAI,CAAEN,GAAQ,IAAI;MACrC,MAAMiJ,OAAO,GAAGjJ,GAAG,CAACV,IAAI,EAAE6D,UAAU,CAAC,QAAQ,CAAC,IAAInD,GAAG,CAACV,IAAI,KAAK,OAAO;MACtEpF,OAAO,CAACC,GAAG,CACT,kCAAkC,EAClC6F,GAAG,EACH,UAAU,EACViJ,OAAO,CACR;MACD,OAAOA,OAAO;IAChB,CAAC,CAAC,IAAI,KAAK;IAEb;IACA,MAAMC,WAAW,GAAG,CAAC,EAAE3H,OAAO,CAACwH,QAAQ,IAAIxH,OAAO,CAACvD,KAAK,CAAC;IAEzD,MAAMmL,MAAM,GAAGH,kBAAkB,IAAIE,WAAW;IAChDhP,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgP,MAAM,EAAE;MAClDH,kBAAkB;MAClBE;KACD,CAAC;IAEF,OAAOC,MAAM;EACf;EAEAtJ,OAAOA,CAAC0B,OAAY;IAClBrH,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEoH,OAAO,CAACzK,EAAE,EAAE;MAChEwI,IAAI,EAAEiC,OAAO,CAACjC,IAAI;MAClBI,WAAW,EAAE6B,OAAO,CAAC7B;KACtB,CAAC;IAEF;IACA,IAAI6B,OAAO,CAACjC,IAAI,KAAK,MAAM,IAAIiC,OAAO,CAACjC,IAAI,KAAK,MAAM,EAAE;MACtDpF,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAC9C,OAAO,IAAI;;IAGb;IACA,MAAMiP,iBAAiB,GACrB7H,OAAO,CAAC7B,WAAW,EAAEY,IAAI,CAAEN,GAAQ,IAAI;MACrC,MAAMqJ,MAAM,GAAG,CAACrJ,GAAG,CAACV,IAAI,EAAE6D,UAAU,CAAC,QAAQ,CAAC,IAAInD,GAAG,CAACV,IAAI,KAAK,OAAO;MACtEpF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE6F,GAAG,EAAE,SAAS,EAAEqJ,MAAM,CAAC;MACtE,OAAOA,MAAM;IACf,CAAC,CAAC,IAAI,KAAK;IAEbnP,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEiP,iBAAiB,CAAC;IAC5D,OAAOA,iBAAiB;EAC1B;EAEAtJ,WAAWA,CAACyB,OAAY;IACtBrH,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEoH,OAAO,CAACzK,EAAE,CAAC;IAEtE;IACA,IAAIyK,OAAO,CAACwH,QAAQ,EAAE;MACpB7O,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEoH,OAAO,CAACwH,QAAQ,CAAC;MAC5D,OAAOxH,OAAO,CAACwH,QAAQ;;IAEzB,IAAIxH,OAAO,CAACvD,KAAK,EAAE;MACjB9D,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoH,OAAO,CAACvD,KAAK,CAAC;MACtD,OAAOuD,OAAO,CAACvD,KAAK;;IAGtB;IACA,MAAMsL,eAAe,GAAG/H,OAAO,CAAC7B,WAAW,EAAExE,IAAI,CAC9C8E,GAAQ,IAAKA,GAAG,CAACV,IAAI,EAAE6D,UAAU,CAAC,QAAQ,CAAC,IAAInD,GAAG,CAACV,IAAI,KAAK,OAAO,CACrE;IAEDpF,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEmP,eAAe,CAAC;IAEnE,IAAIA,eAAe,EAAE;MACnB,MAAMpJ,GAAG,GAAGoJ,eAAe,CAACpJ,GAAG,IAAIoJ,eAAe,CAACnJ,IAAI,IAAI,EAAE;MAC7DjG,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE+F,GAAG,CAAC;MAC5DhG,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtCsC,IAAI,CAAC8M,SAAS,CAACD,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,CACzC;MACD,OAAOpJ,GAAG;;IAGZhG,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACrE,OAAO,EAAE;EACX;EAEAqP,WAAWA,CAACjI,OAAY;IACtB,MAAM2B,cAAc,GAAG3B,OAAO,CAAC7B,WAAW,EAAExE,IAAI,CAC7C8E,GAAQ,IAAK,CAACA,GAAG,CAACV,IAAI,EAAE6D,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,OAAOD,cAAc,EAAEnM,IAAI,IAAI,SAAS;EAC1C;EAEA0S,WAAWA,CAAClI,OAAY;IACtB,MAAM2B,cAAc,GAAG3B,OAAO,CAAC7B,WAAW,EAAExE,IAAI,CAC7C8E,GAAQ,IAAK,CAACA,GAAG,CAACV,IAAI,EAAE6D,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAE9C,IAAI,EAAE,OAAO,EAAE;IAEpC,MAAM2C,KAAK,GAAGG,cAAc,CAAC9C,IAAI;IACjC,IAAI2C,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOd,IAAI,CAACe,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOd,IAAI,CAACe,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEA2G,WAAWA,CAACnI,OAAY;IACtB,MAAM2B,cAAc,GAAG3B,OAAO,CAAC7B,WAAW,EAAExE,IAAI,CAC7C8E,GAAQ,IAAK,CAACA,GAAG,CAACV,IAAI,EAAE6D,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAE5D,IAAI,EAAE,OAAO,aAAa;IAE/C,IAAI4D,cAAc,CAAC5D,IAAI,CAAC6D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAAC5D,IAAI,CAAC6D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAAC5D,IAAI,CAACqK,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IACjE,IAAIzG,cAAc,CAAC5D,IAAI,CAACqK,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,kBAAkB;IACnE,IAAIzG,cAAc,CAAC5D,IAAI,CAACqK,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IACrE,OAAO,aAAa;EACtB;EAEAC,YAAYA,CAACjN,MAAc;IACzB;IACA,MAAMkN,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAM5J,KAAK,GAAGtD,MAAM,CAACmN,UAAU,CAAC,CAAC,CAAC,GAAGD,MAAM,CAACxM,MAAM;IAClD,OAAOwM,MAAM,CAAC5J,KAAK,CAAC;EACtB;EAEA;EACA8J,cAAcA,CAACxI,OAAY,EAAE6C,KAAU;IACrClK,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEoH,OAAO,CAAC;EAC1C;EAEAyI,aAAaA,CAAC5F,KAAU;IACtB;IACA,IAAI,CAAC6F,qBAAqB,EAAE;EAC9B;EAEAC,cAAcA,CAAC9F,KAAoB;IACjC,IAAIA,KAAK,CAAC+F,GAAG,KAAK,OAAO,IAAI,CAAC/F,KAAK,CAACgG,QAAQ,EAAE;MAC5ChG,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAACnD,WAAW,EAAE;;EAEtB;EAEAmJ,YAAYA,CAAA;IACV;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFC,QAAQA,CAACnG,KAAU;IACjB;IACA,MAAM1C,OAAO,GAAG0C,KAAK,CAACZ,MAAM;IAC5B,IACE9B,OAAO,CAACE,SAAS,KAAK,CAAC,IACvB,IAAI,CAAClM,eAAe,IACpB,CAAC,IAAI,CAACD,aAAa,EACnB;MACA,IAAI,CAACsJ,gBAAgB,EAAE;;EAE3B;EAEAyL,eAAeA,CAAC7N,MAAc;IAC5BzC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEwC,MAAM,CAAC;EAClD;EAEA8N,WAAWA,CAACrG,KAAU,EAAE7C,OAAY;IAClCrH,OAAO,CAACC,GAAG,CACT,oDAAoD,EACpDoH,OAAO,CAACzK,EAAE,EACVsN,KAAK,CAACZ,MAAM,CAACkH,GAAG,CACjB;EACH;EAEAC,YAAYA,CAACvG,KAAU,EAAE7C,OAAY;IACnCrH,OAAO,CAAC+B,KAAK,CAAC,+CAA+C,EAAEsF,OAAO,CAACzK,EAAE,EAAE;MACzE4T,GAAG,EAAEtG,KAAK,CAACZ,MAAM,CAACkH,GAAG;MACrBzO,KAAK,EAAEmI;KACR,CAAC;IACF;IACAA,KAAK,CAACZ,MAAM,CAACkH,GAAG,GACd,4WAA4W;EAChX;EAEAE,eAAeA,CAACrJ,OAAY;IAC1B,MAAM+H,eAAe,GAAG/H,OAAO,CAAC7B,WAAW,EAAExE,IAAI,CAAE8E,GAAQ,IACzDA,GAAG,CAACV,IAAI,EAAE6D,UAAU,CAAC,QAAQ,CAAC,CAC/B;IACD,IAAImG,eAAe,EAAEpJ,GAAG,EAAE;MACxB,IAAI,CAAC9I,aAAa,GAAG;QACnB8I,GAAG,EAAEoJ,eAAe,CAACpJ,GAAG;QACxBnJ,IAAI,EAAEuS,eAAe,CAACvS,IAAI,IAAI,OAAO;QACrCqJ,IAAI,EAAE,IAAI,CAAC0C,cAAc,CAACwG,eAAe,CAAClJ,IAAI,IAAI,CAAC,CAAC;QACpDmB,OAAO,EAAEA;OACV;MACD,IAAI,CAACpK,eAAe,GAAG,IAAI;MAC3B+C,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC/C,aAAa,CAAC;;EAEvE;EAEAyT,gBAAgBA,CAAA;IACd,IAAI,CAAC1T,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB8C,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC;EAEA2Q,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC1T,aAAa,EAAE8I,GAAG,EAAE;MAC3B,MAAMkD,IAAI,GAAGxI,QAAQ,CAACyI,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAG,IAAI,CAAClM,aAAa,CAAC8I,GAAG;MAClCkD,IAAI,CAACG,QAAQ,GAAG,IAAI,CAACnM,aAAa,CAACL,IAAI,IAAI,OAAO;MAClDqM,IAAI,CAACI,MAAM,GAAG,QAAQ;MACtB5I,QAAQ,CAAC6I,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,EAAE;MACZ/I,QAAQ,CAAC6I,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/B,IAAI,CAACnO,YAAY,CAACuG,WAAW,CAAC,wBAAwB,CAAC;MACvDtB,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAC/C,aAAa,CAACL,IAAI,CACxB;;EAEL;EAEA;EACA;EACA;EAEAgU,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACjV,WAAW,CAACuL,IAAI,EAAE,EAAE;MAC5B,IAAI,CAACtL,aAAa,GAAG,EAAE;MACvB;;IAGF,IAAI,CAACA,aAAa,GAAG,IAAI,CAACX,QAAQ,CAAC4V,MAAM,CACtCzJ,OAAO,IACNA,OAAO,CAAC/H,OAAO,EACXyR,WAAW,EAAE,CACdtB,QAAQ,CAAC,IAAI,CAAC7T,WAAW,CAACmV,WAAW,EAAE,CAAC,IAC3C1J,OAAO,CAAChC,MAAM,EAAElD,QAAQ,EACpB4O,WAAW,EAAE,CACdtB,QAAQ,CAAC,IAAI,CAAC7T,WAAW,CAACmV,WAAW,EAAE,CAAC,CAC9C;EACH;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACH,cAAc,EAAE;EACvB;EAEAI,WAAWA,CAAA;IACT,IAAI,CAACrV,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,aAAa,GAAG,EAAE;EACzB;EAEAqV,aAAaA,CAACnK,SAAiB;IAC7B,MAAMoK,cAAc,GAAGzQ,QAAQ,CAAC0Q,cAAc,CAAC,WAAWrK,SAAS,EAAE,CAAC;IACtE,IAAIoK,cAAc,EAAE;MAClBA,cAAc,CAACE,cAAc,CAAC;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAQ,CAAE,CAAC;MACtE;MACAJ,cAAc,CAACvQ,SAAS,CAACE,GAAG,CAAC,WAAW,CAAC;MACzC0F,UAAU,CAAC,MAAK;QACd2K,cAAc,CAACvQ,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;MAC9C,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;EACA;EACA;EAEA2Q,gBAAgBA,CAAA;IACd,IAAI,CAACtV,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,eAAe,GAAG,IAAI;EAC7B;EAEA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EAEQsV,YAAYA,CAACxT,QAAkB;IACrC,IAAI,CAAC,IAAI,CAAC5C,gBAAgB,EAAE;MAC1B,IAAI,CAACN,YAAY,CAACgI,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,MAAM2O,WAAW,GAAG,IAAI,CAACrW,gBAAgB,CAACuB,EAAE,IAAI,IAAI,CAACvB,gBAAgB,CAACqH,GAAG;IACzE,IAAI,CAACgP,WAAW,EAAE;MAChB,IAAI,CAAC3W,YAAY,CAACgI,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF/C,OAAO,CAACC,GAAG,CAAC,iBAAiBhC,QAAQ,gBAAgB,EAAEyT,WAAW,CAAC;IAEnE,IAAI,CAAC1T,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,KAAKxD,QAAQ,CAACkX,KAAK,GAAG,OAAO,GAAG,OAAO;IAC/D,IAAI,CAACzT,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,CAAC0T,cAAc,EAAE;IAErB;IACA,IAAI,CAAC9W,cAAc,CAAC2W,YAAY,CAC9BC,WAAW,EACXzT,QAAQ,EACR,IAAI,CAAChD,YAAY,EAAE2B,EAAE,CACtB,CAAC+E,SAAS,CAAC;MACVC,IAAI,EAAGK,IAAU,IAAI;QACnBjC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEgC,IAAI,CAAC;QACnD,IAAI,CAAC7D,UAAU,GAAG6D,IAAI;QACtB,IAAI,CAAC5D,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACtD,YAAY,CAACuG,WAAW,CAC3B,SAASrD,QAAQ,KAAKxD,QAAQ,CAACkX,KAAK,GAAG,OAAO,GAAG,OAAO,SAAS,CAClE;MACH,CAAC;MACD5P,KAAK,EAAGA,KAAK,IAAI;QACf/B,OAAO,CAAC+B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAAC4G,OAAO,EAAE;QACd,IAAI,CAAC5N,YAAY,CAACgI,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACJ;EAEA8O,UAAUA,CAAChQ,YAA0B;IACnC7B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE4B,YAAY,CAAC;IAExD,IAAI,CAAC/G,cAAc,CAAC+W,UAAU,CAAChQ,YAAY,CAAC,CAACF,SAAS,CAAC;MACrDC,IAAI,EAAGK,IAAU,IAAI;QACnBjC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEgC,IAAI,CAAC;QAClD,IAAI,CAAC7D,UAAU,GAAG6D,IAAI;QACtB,IAAI,CAACjE,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACK,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACJ,QAAQ,GAAGgE,IAAI,CAACmD,IAAI,KAAK3K,QAAQ,CAACkX,KAAK,GAAG,OAAO,GAAG,OAAO;QAChE,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAAC7W,YAAY,CAACuG,WAAW,CAAC,eAAe,CAAC;MAChD,CAAC;MACDS,KAAK,EAAGA,KAAK,IAAI;QACf/B,OAAO,CAAC+B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAChH,YAAY,CAACgI,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;EACJ;EAEA+O,UAAUA,CAACjQ,YAA0B;IACnC7B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE4B,YAAY,CAAC;IAExD,IAAI,CAAC/G,cAAc,CAACgX,UAAU,CAACjQ,YAAY,CAACjF,EAAE,EAAE,eAAe,CAAC,CAAC+E,SAAS,CAAC;MACzEC,IAAI,EAAEA,CAAA,KAAK;QACT5B,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3C,IAAI,CAAClF,YAAY,CAACuG,WAAW,CAAC,cAAc,CAAC;MAC/C,CAAC;MACDS,KAAK,EAAGA,KAAK,IAAI;QACf/B,OAAO,CAAC+B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAChH,YAAY,CAACgI,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;EACJ;EAEA;EACA;EAEQ6O,cAAcA,CAAA;IACpB,IAAI,CAAC1T,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,SAAS,GAAG4T,WAAW,CAAC,MAAK;MAChC,IAAI,CAAC7T,YAAY,EAAE;MACnB,IAAI,CAAClD,GAAG,CAACuL,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV;EAEQyL,cAAcA,CAAA;IACpB,IAAI,IAAI,CAAC7T,SAAS,EAAE;MAClB8T,aAAa,CAAC,IAAI,CAAC9T,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;EAEA;EACA2T,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC9T,UAAU,EAAE;IAEtB,IAAI,CAACE,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,IAAI,CAACxD,cAAc,CAACqX,WAAW,CAC7B,IAAI,CAAC/T,UAAU,CAACxB,EAAE,EAClBwK,SAAS;IAAE;IACX,CAAC,IAAI,CAAC9I,OAAO,CAAC;KACf,CAACqD,SAAS,CAAC;MACVC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC7G,YAAY,CAACuG,WAAW,CAC3B,IAAI,CAAChD,OAAO,GAAG,aAAa,GAAG,cAAc,CAC9C;MACH,CAAC;MACDyD,KAAK,EAAGA,KAAK,IAAI;QACf/B,OAAO,CAAC+B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACA,IAAI,CAACzD,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;QAC5B,IAAI,CAACvD,YAAY,CAACgI,SAAS,CAAC,oCAAoC,CAAC;MACnE;KACD,CAAC;EACJ;EAEAqP,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAChU,UAAU,EAAE;IAEtB,IAAI,CAACG,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C;IACA,IAAI,CAACzD,cAAc,CAACqX,WAAW,CAC7B,IAAI,CAAC/T,UAAU,CAACxB,EAAE,EAClB,IAAI,CAAC2B,cAAc;IAAE;IACrB6I,SAAS,CAAC;KACX,CAACzF,SAAS,CAAC;MACVC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC7G,YAAY,CAACuG,WAAW,CAC3B,IAAI,CAAC/C,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACDwD,KAAK,EAAGA,KAAK,IAAI;QACf/B,OAAO,CAAC+B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;QACA,IAAI,CAACxD,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,CAACxD,YAAY,CAACgI,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACJ;EAEAsP,kBAAkBA,CAACjK,QAAgB;IACjC,MAAMkK,KAAK,GAAGvK,IAAI,CAACC,KAAK,CAACI,QAAQ,GAAG,IAAI,CAAC;IACzC,MAAMmK,OAAO,GAAGxK,IAAI,CAACC,KAAK,CAAEI,QAAQ,GAAG,IAAI,GAAI,EAAE,CAAC;IAClD,MAAMoK,OAAO,GAAGpK,QAAQ,GAAG,EAAE;IAE7B,IAAIkK,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIC,OAAO,CAAC9G,QAAQ,EAAE,CAACgH,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CAC9D/G,QAAQ,EAAE,CACVgH,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAEvB,OAAO,GAAGF,OAAO,IAAIC,OAAO,CAAC/G,QAAQ,EAAE,CAACgH,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EACA;EAEMC,mBAAmBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACvB5S,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MAErD,IAAI;QACF;QACA,IAAI,CAAC4S,SAAS,CAACC,YAAY,IAAI,CAACD,SAAS,CAACC,YAAY,CAACC,YAAY,EAAE;UACnE,MAAM,IAAIC,KAAK,CACb,yDAAyD,CAC1D;;QAGH;QACA,IAAI,CAACC,MAAM,CAACC,aAAa,EAAE;UACzB,MAAM,IAAIF,KAAK,CACb,uDAAuD,CACxD;;QAGHhT,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QAEzD;QACA,MAAMkT,MAAM,SAASN,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDK,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE,IAAI;YACrBC,UAAU,EAAE,KAAK;YACjBC,YAAY,EAAE;;SAEjB,CAAC;QAEFzT,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QAEnD;QACA,IAAIyT,QAAQ,GAAG,wBAAwB;QACvC,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;UAC5CA,QAAQ,GAAG,YAAY;UACvB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;YAC5CA,QAAQ,GAAG,WAAW;YACtB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;cAC5CA,QAAQ,GAAG,EAAE,CAAC,CAAC;;;;;QAKrB1T,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEyT,QAAQ,CAAC;QAEpD;QACAf,KAAI,CAAClV,aAAa,GAAG,IAAIyV,aAAa,CAACC,MAAM,EAAE;UAC7CO,QAAQ,EAAEA,QAAQ,IAAItM;SACvB,CAAC;QAEF;QACAuL,KAAI,CAACjV,WAAW,GAAG,EAAE;QACrBiV,KAAI,CAACrV,gBAAgB,GAAG,IAAI;QAC5BqV,KAAI,CAACpV,sBAAsB,GAAG,CAAC;QAC/BoV,KAAI,CAACnV,mBAAmB,GAAG,WAAW;QAEtCwC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;QAElE;QACA0S,KAAI,CAAChV,cAAc,GAAGoU,WAAW,CAAC,MAAK;UACrCY,KAAI,CAACpV,sBAAsB,EAAE;UAC7B;UACAoV,KAAI,CAACiB,iBAAiB,EAAE;UACxBjB,KAAI,CAAC3X,GAAG,CAACuL,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAER;QACAoM,KAAI,CAAClV,aAAa,CAACoW,eAAe,GAAI3J,KAAK,IAAI;UAC7ClK,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEiK,KAAK,CAAC1B,IAAI,CAACtC,IAAI,EAAE,OAAO,CAAC;UACnE,IAAIgE,KAAK,CAAC1B,IAAI,CAACtC,IAAI,GAAG,CAAC,EAAE;YACvByM,KAAI,CAACjV,WAAW,CAAC4I,IAAI,CAAC4D,KAAK,CAAC1B,IAAI,CAAC;;QAErC,CAAC;QAEDmK,KAAI,CAAClV,aAAa,CAACqW,MAAM,GAAG,MAAK;UAC/B9T,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;UACpE0S,KAAI,CAACoB,oBAAoB,EAAE;QAC7B,CAAC;QAEDpB,KAAI,CAAClV,aAAa,CAACuW,OAAO,GAAI9J,KAAU,IAAI;UAC1ClK,OAAO,CAAC+B,KAAK,CAAC,iCAAiC,EAAEmI,KAAK,CAACnI,KAAK,CAAC;UAC7D4Q,KAAI,CAAC5X,YAAY,CAACgI,SAAS,CAAC,iCAAiC,CAAC;UAC9D4P,KAAI,CAACsB,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACAtB,KAAI,CAAClV,aAAa,CAACyW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/BlU,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QAExD0S,KAAI,CAAC5X,YAAY,CAACuG,WAAW,CAAC,iCAAiC,CAAC;OACjE,CAAC,OAAOS,KAAU,EAAE;QACnB/B,OAAO,CAAC+B,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAE5D,IAAIoS,YAAY,GAAG,+CAA+C;QAElE,IAAIpS,KAAK,CAAClF,IAAI,KAAK,iBAAiB,EAAE;UACpCsX,YAAY,GACV,iGAAiG;SACpG,MAAM,IAAIpS,KAAK,CAAClF,IAAI,KAAK,eAAe,EAAE;UACzCsX,YAAY,GACV,6DAA6D;SAChE,MAAM,IAAIpS,KAAK,CAAClF,IAAI,KAAK,mBAAmB,EAAE;UAC7CsX,YAAY,GACV,0DAA0D;SAC7D,MAAM,IAAIpS,KAAK,CAACsF,OAAO,EAAE;UACxB8M,YAAY,GAAGpS,KAAK,CAACsF,OAAO;;QAG9BsL,KAAI,CAAC5X,YAAY,CAACgI,SAAS,CAACoR,YAAY,CAAC;QACzCxB,KAAI,CAACsB,oBAAoB,EAAE;;IAC5B;EACH;EAEAG,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC3W,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC4W,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAAC5W,aAAa,CAAC6W,IAAI,EAAE;MACzB,IAAI,CAAC7W,aAAa,CAAC0V,MAAM,CAACoB,SAAS,EAAE,CAAC1O,OAAO,CAAE2O,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAAC3W,cAAc,EAAE;MACvBsU,aAAa,CAAC,IAAI,CAACtU,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACL,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,mBAAmB,GAAG,YAAY;EACzC;EAEAyW,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACxW,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAAC4W,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAAC5W,aAAa,CAAC6W,IAAI,EAAE;;MAE3B,IAAI,CAAC7W,aAAa,CAAC0V,MAAM,CAACoB,SAAS,EAAE,CAAC1O,OAAO,CAAE2O,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;MACtE,IAAI,CAAC7W,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvBsU,aAAa,CAAC,IAAI,CAACtU,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACL,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACE,WAAW,GAAG,EAAE;EACvB;EAEcqW,oBAAoBA,CAAA;IAAA,IAAAU,MAAA;IAAA,OAAA7B,iBAAA;MAChC5S,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MAEtD,IAAI;QACF;QACA,IAAIwU,MAAI,CAAC/W,WAAW,CAACyF,MAAM,KAAK,CAAC,EAAE;UACjCnD,OAAO,CAAC+B,KAAK,CAAC,sCAAsC,CAAC;UACrD0S,MAAI,CAAC1Z,YAAY,CAACgI,SAAS,CAAC,wBAAwB,CAAC;UACrD0R,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGFjU,OAAO,CAACC,GAAG,CACT,0BAA0B,EAC1BwU,MAAI,CAAC/W,WAAW,CAACyF,MAAM,EACvB,WAAW,EACXsR,MAAI,CAAClX,sBAAsB,CAC5B;QAED;QACA,IAAIkX,MAAI,CAAClX,sBAAsB,GAAG,CAAC,EAAE;UACnCyC,OAAO,CAAC+B,KAAK,CACX,iCAAiC,EACjC0S,MAAI,CAAClX,sBAAsB,CAC5B;UACDkX,MAAI,CAAC1Z,YAAY,CAACgI,SAAS,CACzB,+CAA+C,CAChD;UACD0R,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGF;QACA,IAAIP,QAAQ,GAAG,wBAAwB;QACvC,IAAIe,MAAI,CAAChX,aAAa,EAAEiW,QAAQ,EAAE;UAChCA,QAAQ,GAAGe,MAAI,CAAChX,aAAa,CAACiW,QAAQ;;QAGxC1T,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEyT,QAAQ,CAAC;QAEvE;QACA,MAAMgB,SAAS,GAAG,IAAIC,IAAI,CAACF,MAAI,CAAC/W,WAAW,EAAE;UAC3C0H,IAAI,EAAEsO;SACP,CAAC;QAEF1T,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CiG,IAAI,EAAEwO,SAAS,CAACxO,IAAI;UACpBd,IAAI,EAAEsP,SAAS,CAACtP;SACjB,CAAC;QAEF;QACA,IAAIwP,SAAS,GAAG,OAAO;QACvB,IAAIlB,QAAQ,CAACjE,QAAQ,CAAC,KAAK,CAAC,EAAE;UAC5BmF,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIlB,QAAQ,CAACjE,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnCmF,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIlB,QAAQ,CAACjE,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnCmF,SAAS,GAAG,MAAM;;QAGpB;QACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CACxB,CAACJ,SAAS,CAAC,EACX,SAAStQ,IAAI,CAAC6D,GAAG,EAAE,GAAG2M,SAAS,EAAE,EACjC;UACExP,IAAI,EAAEsO;SACP,CACF;QAED1T,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CpD,IAAI,EAAEgY,SAAS,CAAChY,IAAI;UACpBqJ,IAAI,EAAE2O,SAAS,CAAC3O,IAAI;UACpBd,IAAI,EAAEyP,SAAS,CAACzP;SACjB,CAAC;QAEF;QACAqP,MAAI,CAACjX,mBAAmB,GAAG,YAAY;QACvC,MAAMiX,MAAI,CAACM,gBAAgB,CAACF,SAAS,CAAC;QAEtC7U,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzDwU,MAAI,CAAC1Z,YAAY,CAACuG,WAAW,CAAC,yBAAyB,CAAC;OACzD,CAAC,OAAOS,KAAU,EAAE;QACnB/B,OAAO,CAAC+B,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D0S,MAAI,CAAC1Z,YAAY,CAACgI,SAAS,CACzB,2CAA2C,IACxChB,KAAK,CAACsF,OAAO,IAAI,iBAAiB,CAAC,CACvC;OACF,SAAS;QACR;QACAoN,MAAI,CAACjX,mBAAmB,GAAG,MAAM;QACjCiX,MAAI,CAAClX,sBAAsB,GAAG,CAAC;QAC/BkX,MAAI,CAAC/W,WAAW,GAAG,EAAE;QACrB+W,MAAI,CAACnX,gBAAgB,GAAG,KAAK;QAE7B0C,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;;IAClE;EACH;EAEc8U,gBAAgBA,CAACF,SAAe;IAAA,IAAAG,MAAA;IAAA,OAAApC,iBAAA;MAC5C,MAAMrN,UAAU,GAAGyP,MAAI,CAAC3Z,gBAAgB,EAAEuB,EAAE,IAAIoY,MAAI,CAAC3Z,gBAAgB,EAAEqH,GAAG;MAE1E,IAAI,CAAC6C,UAAU,EAAE;QACf,MAAM,IAAIyN,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIiC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCH,MAAI,CAACla,cAAc,CAACkM,WAAW,CAC7BzB,UAAU,EACV,EAAE,EACFsP,SAAS,EACT,OAAc,EACdG,MAAI,CAAC/Z,YAAY,CAAC2B,EAAE,CACrB,CAAC+E,SAAS,CAAC;UACVC,IAAI,EAAGyF,OAAY,IAAI;YACrB2N,MAAI,CAAC9Z,QAAQ,CAACoL,IAAI,CAACe,OAAO,CAAC;YAC3B2N,MAAI,CAACpQ,cAAc,EAAE;YACrBsQ,OAAO,EAAE;UACX,CAAC;UACDnT,KAAK,EAAGA,KAAU,IAAI;YACpB/B,OAAO,CAAC+B,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChEoT,MAAM,CAACpT,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEAqT,uBAAuBA,CAAChN,QAAgB;IACtC,MAAMmK,OAAO,GAAGxK,IAAI,CAACC,KAAK,CAACI,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMoK,OAAO,GAAGpK,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAGmK,OAAO,IAAIC,OAAO,CAAC/G,QAAQ,EAAE,CAACgH,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EAEA4C,aAAaA,CAACnL,KAAY;IACxBA,KAAK,CAACC,cAAc,EAAE;IACtBnK,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvC3C,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCE,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CD,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;MACnDE,aAAa,EAAE,CAAC,CAAC,IAAI,CAACA;KACvB,CAAC;IAEF;IACA,IAAI,IAAI,CAACD,mBAAmB,KAAK,YAAY,EAAE;MAC7CwC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,IAAI,CAAClF,YAAY,CAACua,WAAW,CAAC,wBAAwB,CAAC;MACvD;;IAGF,IAAI,IAAI,CAAChY,gBAAgB,EAAE;MACzB0C,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D,IAAI,CAAClF,YAAY,CAACua,WAAW,CAAC,iCAAiC,CAAC;MAChE;;IAGF;IACA,IAAI,CAACva,YAAY,CAACwa,QAAQ,CAAC,2CAA2C,CAAC;IAEvE;IACA,IAAI,CAAC7C,mBAAmB,EAAE,CAAC8C,KAAK,CAAEzT,KAAK,IAAI;MACzC/B,OAAO,CAAC+B,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAAChH,YAAY,CAACgI,SAAS,CACzB,iDAAiD,IAC9ChB,KAAK,CAACsF,OAAO,IAAI,iBAAiB,CAAC,CACvC;IACH,CAAC,CAAC;EACJ;EAEAoO,WAAWA,CAACvL,KAAY;IACtBA,KAAK,CAACC,cAAc,EAAE;IACtBnK,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,IAAI,CAAC,IAAI,CAAC3C,gBAAgB,EAAE;MAC1B0C,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD;;IAGF;IACA,IAAI,CAACmU,kBAAkB,EAAE;EAC3B;EAEAsB,cAAcA,CAACxL,KAAY;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtBnK,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD,IAAI,CAAC,IAAI,CAAC3C,gBAAgB,EAAE;MAC1B0C,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD;;IAGF;IACA,IAAI,CAACgU,oBAAoB,EAAE;EAC7B;EAEA0B,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAClY,aAAa,EAAEiW,QAAQ,EAAE;MAChC,IAAI,IAAI,CAACjW,aAAa,CAACiW,QAAQ,CAACjE,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,MAAM;MAC/D,IAAI,IAAI,CAAChS,aAAa,CAACiW,QAAQ,CAACjE,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAAChS,aAAa,CAACiW,QAAQ,CAACjE,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAAChS,aAAa,CAACiW,QAAQ,CAACjE,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;;IAE/D,OAAO,MAAM;EACf;EAEA;EAEQmE,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAAChW,UAAU,GAAG,IAAI,CAACA,UAAU,CAACgY,GAAG,CAAC,MAAK;MACzC,OAAO7N,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC8N,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;EACJ;;EAEAC,cAAcA,CAAC5L,KAAU;IACvBlK,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD,MAAM8V,KAAK,GAAG7L,KAAK,CAACZ,MAAM,CAACyM,KAAK;IAEhC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAC5S,MAAM,KAAK,CAAC,EAAE;MAChCnD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C;;IAGFD,OAAO,CAACC,GAAG,CAAC,eAAe8V,KAAK,CAAC5S,MAAM,oBAAoB,EAAE4S,KAAK,CAAC;IAEnE,KAAK,IAAIC,IAAI,IAAID,KAAK,EAAE;MACtB/V,OAAO,CAACC,GAAG,CACT,gCAAgC+V,IAAI,CAACnZ,IAAI,WAAWmZ,IAAI,CAAC9P,IAAI,WAAW8P,IAAI,CAAC5Q,IAAI,EAAE,CACpF;MACD,IAAI,CAAC6Q,UAAU,CAACD,IAAI,CAAC;;EAEzB;EAEQC,UAAUA,CAACD,IAAU;IAC3BhW,OAAO,CAACC,GAAG,CAAC,yCAAyC+V,IAAI,CAACnZ,IAAI,EAAE,CAAC;IAEjE,MAAM0I,UAAU,GAAG,IAAI,CAAClK,gBAAgB,EAAEuB,EAAE,IAAI,IAAI,CAACvB,gBAAgB,EAAEqH,GAAG;IAE1E,IAAI,CAAC6C,UAAU,EAAE;MACfvF,OAAO,CAAC+B,KAAK,CAAC,kCAAkC,CAAC;MACjD,IAAI,CAAChH,YAAY,CAACgI,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF/C,OAAO,CAACC,GAAG,CAAC,4BAA4BsF,UAAU,EAAE,CAAC;IAErD;IACA,MAAM2Q,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,IAAIF,IAAI,CAAC9P,IAAI,GAAGgQ,OAAO,EAAE;MACvBlW,OAAO,CAAC+B,KAAK,CAAC,+BAA+BiU,IAAI,CAAC9P,IAAI,QAAQ,CAAC;MAC/D,IAAI,CAACnL,YAAY,CAACgI,SAAS,CAAC,oCAAoC,CAAC;MACjE;;IAGF;IACA,IAAIiT,IAAI,CAAC5Q,IAAI,CAAC6D,UAAU,CAAC,QAAQ,CAAC,IAAI+M,IAAI,CAAC9P,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;MAC7D;MACAlG,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtC+V,IAAI,CAACnZ,IAAI,EACT,gBAAgB,EAChBmZ,IAAI,CAAC9P,IAAI,CACV;MACD,IAAI,CAACiQ,aAAa,CAACH,IAAI,CAAC,CACrBI,IAAI,CAAEC,cAAc,IAAI;QACvBrW,OAAO,CAACC,GAAG,CACT,8DAA8D,EAC9DoW,cAAc,CAACnQ,IAAI,CACpB;QACD,IAAI,CAACoQ,gBAAgB,CAACD,cAAc,EAAE9Q,UAAU,CAAC;MACnD,CAAC,CAAC,CACDiQ,KAAK,CAAEzT,KAAK,IAAI;QACf/B,OAAO,CAAC+B,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE;QACA,IAAI,CAACuU,gBAAgB,CAACN,IAAI,EAAEzQ,UAAU,CAAC;MACzC,CAAC,CAAC;MACJ;;IAGF;IACA,IAAI,CAAC+Q,gBAAgB,CAACN,IAAI,EAAEzQ,UAAU,CAAC;EACzC;EAEQ+Q,gBAAgBA,CAACN,IAAU,EAAEzQ,UAAkB;IACrD,MAAMgR,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACR,IAAI,CAAC;IACjDhW,OAAO,CAACC,GAAG,CAAC,wCAAwCsW,WAAW,EAAE,CAAC;IAClEvW,OAAO,CAACC,GAAG,CAAC,gCAAgC,IAAI,CAAChF,YAAY,CAAC2B,EAAE,EAAE,CAAC;IAEnE,IAAI,CAACb,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACqB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACD,cAAc,GAAG,CAAC;IACvB6C,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhE;IACA,MAAMwW,gBAAgB,GAAG1E,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC5U,cAAc,IAAI4K,IAAI,CAAC8N,MAAM,EAAE,GAAG,EAAE;MACzC,IAAI,IAAI,CAAC1Y,cAAc,IAAI,EAAE,EAAE;QAC7B8U,aAAa,CAACwE,gBAAgB,CAAC;;MAEjC,IAAI,CAACzb,GAAG,CAACuL,aAAa,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,CAACzL,cAAc,CAACkM,WAAW,CAC7BzB,UAAU,EACV,EAAE,EACFyQ,IAAI,EACJO,WAAW,EACX,IAAI,CAACtb,YAAY,CAAC2B,EAAE,CACrB,CAAC+E,SAAS,CAAC;MACVC,IAAI,EAAGyF,OAAY,IAAI;QACrBrH,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEoH,OAAO,CAAC;QAC7DrH,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;UAChDrD,EAAE,EAAEyK,OAAO,CAACzK,EAAE;UACdwI,IAAI,EAAEiC,OAAO,CAACjC,IAAI;UAClBI,WAAW,EAAE6B,OAAO,CAAC7B,WAAW;UAChCE,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC2B,OAAO,CAAC;UAChC1B,OAAO,EAAE,IAAI,CAACA,OAAO,CAAC0B,OAAO,CAAC;UAC9BwH,QAAQ,EAAE,IAAI,CAACjJ,WAAW,CAACyB,OAAO;SACnC,CAAC;QAEF4K,aAAa,CAACwE,gBAAgB,CAAC;QAC/B,IAAI,CAACtZ,cAAc,GAAG,GAAG;QAEzBqJ,UAAU,CAAC,MAAK;UACd,IAAI,CAACtL,QAAQ,CAACoL,IAAI,CAACe,OAAO,CAAC;UAC3B,IAAI,CAACzC,cAAc,EAAE;UACrB,IAAI,CAAC7J,YAAY,CAACuG,WAAW,CAAC,4BAA4B,CAAC;UAC3D,IAAI,CAACoV,gBAAgB,EAAE;QACzB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD3U,KAAK,EAAGA,KAAU,IAAI;QACpB/B,OAAO,CAAC+B,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDkQ,aAAa,CAACwE,gBAAgB,CAAC;QAC/B,IAAI,CAAC1b,YAAY,CAACgI,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAAC2T,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEQF,kBAAkBA,CAACR,IAAU;IACnC,IAAIA,IAAI,CAAC5Q,IAAI,CAAC6D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAI+M,IAAI,CAAC5Q,IAAI,CAAC6D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAI+M,IAAI,CAAC5Q,IAAI,CAAC6D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,OAAO,MAAa;EACtB;EAEA0N,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEAD,gBAAgBA,CAAA;IACd,IAAI,CAAC3a,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACqB,WAAW,GAAG,KAAK;IACxB,IAAI,CAACD,cAAc,GAAG,CAAC;EACzB;EAEA;EAEAyZ,UAAUA,CAAC1M,KAAgB;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAAClN,UAAU,GAAG,IAAI;EACxB;EAEAwZ,WAAWA,CAAC3M,KAAgB;IAC1BA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB;IACA,MAAMuM,IAAI,GAAI5M,KAAK,CAAC6M,aAA6B,CAACC,qBAAqB,EAAE;IACzE,MAAM3a,CAAC,GAAG6N,KAAK,CAACE,OAAO;IACvB,MAAM9N,CAAC,GAAG4N,KAAK,CAACG,OAAO;IAEvB,IAAIhO,CAAC,GAAGya,IAAI,CAACG,IAAI,IAAI5a,CAAC,GAAGya,IAAI,CAACI,KAAK,IAAI5a,CAAC,GAAGwa,IAAI,CAACK,GAAG,IAAI7a,CAAC,GAAGwa,IAAI,CAACM,MAAM,EAAE;MACtE,IAAI,CAAC/Z,UAAU,GAAG,KAAK;;EAE3B;EAEAga,MAAMA,CAACnN,KAAgB;IACrBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAAClN,UAAU,GAAG,KAAK;IAEvB,MAAM0Y,KAAK,GAAG7L,KAAK,CAACoN,YAAY,EAAEvB,KAAK;IACvC,IAAIA,KAAK,IAAIA,KAAK,CAAC5S,MAAM,GAAG,CAAC,EAAE;MAC7BnD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE8V,KAAK,CAAC5S,MAAM,CAAC;MAE1D;MACAoU,KAAK,CAACC,IAAI,CAACzB,KAAK,CAAC,CAAClQ,OAAO,CAAEmQ,IAAI,IAAI;QACjChW,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjC+V,IAAI,CAACnZ,IAAI,EACTmZ,IAAI,CAAC5Q,IAAI,EACT4Q,IAAI,CAAC9P,IAAI,CACV;QACD,IAAI,CAAC+P,UAAU,CAACD,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,IAAI,CAACjb,YAAY,CAACuG,WAAW,CAC3B,GAAGyU,KAAK,CAAC5S,MAAM,8BAA8B,CAC9C;;EAEL;EAEA;EAEQgT,aAAaA,CAACH,IAAU,EAAEyB,OAAA,GAAkB,GAAG;IACrD,OAAO,IAAIxC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMuC,MAAM,GAAGhX,QAAQ,CAACyI,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMwO,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;MAEvBD,GAAG,CAACE,MAAM,GAAG,MAAK;QAChB;QACA,MAAMC,QAAQ,GAAG,IAAI;QACrB,MAAMC,SAAS,GAAG,IAAI;QACtB,IAAI;UAAEC,KAAK;UAAEC;QAAM,CAAE,GAAGN,GAAG;QAE3B,IAAIK,KAAK,GAAGF,QAAQ,IAAIG,MAAM,GAAGF,SAAS,EAAE;UAC1C,MAAMG,KAAK,GAAGrQ,IAAI,CAAC8E,GAAG,CAACmL,QAAQ,GAAGE,KAAK,EAAED,SAAS,GAAGE,MAAM,CAAC;UAC5DD,KAAK,IAAIE,KAAK;UACdD,MAAM,IAAIC,KAAK;;QAGjBV,MAAM,CAACQ,KAAK,GAAGA,KAAK;QACpBR,MAAM,CAACS,MAAM,GAAGA,MAAM;QAEtB;QACAR,GAAG,EAAEU,SAAS,CAACR,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEK,KAAK,EAAEC,MAAM,CAAC;QAExC;QACAT,MAAM,CAACY,MAAM,CACVC,IAAI,IAAI;UACP,IAAIA,IAAI,EAAE;YACR,MAAMlC,cAAc,GAAG,IAAIvB,IAAI,CAAC,CAACyD,IAAI,CAAC,EAAEvC,IAAI,CAACnZ,IAAI,EAAE;cACjDuI,IAAI,EAAE4Q,IAAI,CAAC5Q,IAAI;cACfoT,YAAY,EAAEpU,IAAI,CAAC6D,GAAG;aACvB,CAAC;YACFiN,OAAO,CAACmB,cAAc,CAAC;WACxB,MAAM;YACLlB,MAAM,CAAC,IAAInC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;QAEjD,CAAC,EACDgD,IAAI,CAAC5Q,IAAI,EACTqS,OAAO,CACR;MACH,CAAC;MAEDI,GAAG,CAAC7D,OAAO,GAAG,MAAMmB,MAAM,CAAC,IAAInC,KAAK,CAAC,sBAAsB,CAAC,CAAC;MAC7D6E,GAAG,CAACrH,GAAG,GAAGiI,GAAG,CAACC,eAAe,CAAC1C,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA;EAEQjG,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAAC/Q,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB;MACA,IAAI,CAAC2Z,mBAAmB,CAAC,IAAI,CAAC;;IAGhC;IACA,IAAI,IAAI,CAACzZ,aAAa,EAAE;MACtB0Z,YAAY,CAAC,IAAI,CAAC1Z,aAAa,CAAC;;IAGlC,IAAI,CAACA,aAAa,GAAGsH,UAAU,CAAC,MAAK;MACnC,IAAI,CAACxH,QAAQ,GAAG,KAAK;MACrB;MACA,IAAI,CAAC2Z,mBAAmB,CAAC,KAAK,CAAC;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;EAEQA,mBAAmBA,CAAC3Z,QAAiB;IAC3C;IACA,MAAMuG,UAAU,GAAG,IAAI,CAAClK,gBAAgB,EAAEuB,EAAE,IAAI,IAAI,CAACvB,gBAAgB,EAAEqH,GAAG;IAC1E,IAAI6C,UAAU,IAAI,IAAI,CAACtK,YAAY,EAAE2B,EAAE,EAAE;MACvCoD,OAAO,CAACC,GAAG,CACT,gCAAgCjB,QAAQ,YAAYuG,UAAU,EAAE,CACjE;MACD;MACA;;EAEJ;EAEA;EAEAsT,cAAcA,CAAC5W,IAAU;IACvBjC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEgC,IAAI,CAAC;IACrD,IAAI,CAAC7D,UAAU,GAAG6D,IAAI;IACtB,IAAI,CAACjE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACK,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACuT,cAAc,EAAE;IACrB,IAAI,CAAC7W,YAAY,CAACuG,WAAW,CAAC,eAAe,CAAC;EAChD;EAEAwX,cAAcA,CAAA;IACZ9Y,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAAC0I,OAAO,EAAE;IACd,IAAI,CAAC5N,YAAY,CAACwa,QAAQ,CAAC,cAAc,CAAC;EAC5C;EAEA;EAEAwD,gBAAgBA,CAAC1R,OAAY;IAC3BrH,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEoH,OAAO,CAACzK,EAAE,CAAC;IAC5D,IAAI,CAACoc,mBAAmB,CAAC3R,OAAO,CAAC;EACnC;EAEA4R,cAAcA,CAAClS,SAAiB;IAC9B,OAAO,IAAI,CAACjJ,gBAAgB,KAAKiJ,SAAS;EAC5C;EAEAiS,mBAAmBA,CAAC3R,OAAY;IAC9B,MAAMN,SAAS,GAAGM,OAAO,CAACzK,EAAE;IAC5B,MAAM+R,QAAQ,GAAG,IAAI,CAACuK,WAAW,CAAC7R,OAAO,CAAC;IAE1C,IAAI,CAACsH,QAAQ,EAAE;MACb3O,OAAO,CAAC+B,KAAK,CAAC,4CAA4C,EAAEgF,SAAS,CAAC;MACtE,IAAI,CAAChM,YAAY,CAACgI,SAAS,CAAC,2BAA2B,CAAC;MACxD;;IAGF;IACA,IAAI,IAAI,CAACkW,cAAc,CAAClS,SAAS,CAAC,EAAE;MAClC,IAAI,CAACoS,iBAAiB,EAAE;MACxB;;IAGF;IACA,IAAI,CAACA,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,kBAAkB,CAAC/R,OAAO,EAAEsH,QAAQ,CAAC;EAC5C;EAEQyK,kBAAkBA,CAAC/R,OAAY,EAAEsH,QAAgB;IACvD,MAAM5H,SAAS,GAAGM,OAAO,CAACzK,EAAE;IAE5B,IAAI;MACFoD,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnC8G,SAAS,EACT,MAAM,EACN4H,QAAQ,CACT;MAED,IAAI,CAAC9Q,YAAY,GAAG,IAAIwb,KAAK,CAAC1K,QAAQ,CAAC;MACvC,IAAI,CAAC7Q,gBAAgB,GAAGiJ,SAAS;MAEjC;MACA,MAAMuS,WAAW,GAAG,IAAI,CAACpR,oBAAoB,CAACnB,SAAS,CAAC;MACxD,IAAI,CAACwB,oBAAoB,CAACxB,SAAS,EAAE;QACnCoB,QAAQ,EAAE,CAAC;QACXE,WAAW,EAAE,CAAC;QACdC,KAAK,EAAEgR,WAAW,CAAChR,KAAK,IAAI,CAAC;QAC7BF,QAAQ,EAAEkR,WAAW,CAAClR,QAAQ,IAAI;OACnC,CAAC;MAEF;MACA,IAAI,CAACvK,YAAY,CAAC0b,YAAY,GAAGD,WAAW,CAAChR,KAAK,IAAI,CAAC;MAEvD;MACA,IAAI,CAACzK,YAAY,CAAC2b,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QACxD,IAAI,IAAI,CAAC3b,YAAY,EAAE;UACrB,IAAI,CAAC0K,oBAAoB,CAACxB,SAAS,EAAE;YACnCqB,QAAQ,EAAE,IAAI,CAACvK,YAAY,CAACuK;WAC7B,CAAC;UACFpI,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAACpC,YAAY,CAACuK,QAAQ,CAC3B;;MAEL,CAAC,CAAC;MAEF,IAAI,CAACvK,YAAY,CAAC2b,gBAAgB,CAAC,YAAY,EAAE,MAAK;QACpD,IAAI,IAAI,CAAC3b,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKiJ,SAAS,EAAE;UAC5D,MAAMsB,WAAW,GAAG,IAAI,CAACxK,YAAY,CAACwK,WAAW;UACjD,MAAMF,QAAQ,GAAIE,WAAW,GAAG,IAAI,CAACxK,YAAY,CAACuK,QAAQ,GAAI,GAAG;UACjE,IAAI,CAACG,oBAAoB,CAACxB,SAAS,EAAE;YAAEsB,WAAW;YAAEF;UAAQ,CAAE,CAAC;UAC/D,IAAI,CAACnN,GAAG,CAACuL,aAAa,EAAE;;MAE5B,CAAC,CAAC;MAEF,IAAI,CAAC1I,YAAY,CAAC2b,gBAAgB,CAAC,OAAO,EAAE,MAAK;QAC/CxZ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE8G,SAAS,CAAC;QACxD,IAAI,CAACoS,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF,IAAI,CAACtb,YAAY,CAAC2b,gBAAgB,CAAC,OAAO,EAAGzX,KAAK,IAAI;QACpD/B,OAAO,CAAC+B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAChH,YAAY,CAACgI,SAAS,CAAC,iCAAiC,CAAC;QAC9D,IAAI,CAACoW,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF;MACA,IAAI,CAACtb,YAAY,CACduE,IAAI,EAAE,CACNgU,IAAI,CAAC,MAAK;QACTpW,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,IAAI,CAAClF,YAAY,CAACuG,WAAW,CAAC,6BAA6B,CAAC;MAC9D,CAAC,CAAC,CACDkU,KAAK,CAAEzT,KAAK,IAAI;QACf/B,OAAO,CAAC+B,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAChH,YAAY,CAACgI,SAAS,CAAC,qCAAqC,CAAC;QAClE,IAAI,CAACoW,iBAAiB,EAAE;MAC1B,CAAC,CAAC;KACL,CAAC,OAAOpX,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,IAAI,CAAChH,YAAY,CAACgI,SAAS,CAAC,iCAAiC,CAAC;MAC9D,IAAI,CAACoW,iBAAiB,EAAE;;EAE5B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACtb,YAAY,EAAE;MACrBmC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACnC,gBAAgB,CAAC;MACvE,IAAI,CAACD,YAAY,CAAC4b,KAAK,EAAE;MACzB,IAAI,CAAC5b,YAAY,CAACwK,WAAW,GAAG,CAAC;MACjC,IAAI,CAACxK,YAAY,GAAG,IAAI;;IAE1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC9C,GAAG,CAACuL,aAAa,EAAE;EAC1B;EAEA2S,WAAWA,CAAC7R,OAAY;IACtB;IACA,IAAIA,OAAO,CAACqH,QAAQ,EAAE,OAAOrH,OAAO,CAACqH,QAAQ;IAC7C,IAAIrH,OAAO,CAACsH,QAAQ,EAAE,OAAOtH,OAAO,CAACsH,QAAQ;IAC7C,IAAItH,OAAO,CAACuH,KAAK,EAAE,OAAOvH,OAAO,CAACuH,KAAK;IAEvC;IACA,MAAM8K,eAAe,GAAGrS,OAAO,CAAC7B,WAAW,EAAExE,IAAI,CAC9C8E,GAAQ,IAAKA,GAAG,CAACV,IAAI,EAAE6D,UAAU,CAAC,QAAQ,CAAC,IAAInD,GAAG,CAACV,IAAI,KAAK,OAAO,CACrE;IAED,IAAIsU,eAAe,EAAE;MACnB,OAAOA,eAAe,CAAC1T,GAAG,IAAI0T,eAAe,CAACzT,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEA0T,aAAaA,CAACtS,OAAY;IACxB;IACA,MAAMN,SAAS,GAAGM,OAAO,CAACzK,EAAE,IAAI,EAAE;IAClC,MAAMgd,IAAI,GAAG7S,SAAS,CACnB8S,KAAK,CAAC,EAAE,CAAC,CACTC,MAAM,CAAC,CAACC,GAAW,EAAEC,IAAY,KAAKD,GAAG,GAAGC,IAAI,CAACpK,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,MAAMqK,KAAK,GAAa,EAAE;IAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAM/B,MAAM,GAAG,CAAC,GAAI,CAACyB,IAAI,GAAGM,CAAC,GAAG,CAAC,IAAI,EAAG;MACxCD,KAAK,CAAC3T,IAAI,CAAC6R,MAAM,CAAC;;IAGpB,OAAO8B,KAAK;EACd;EAEAE,gBAAgBA,CAAC9S,OAAY;IAC3B,MAAMmB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACb,OAAO,CAACzK,EAAE,CAAC;IAClD,MAAMwd,UAAU,GAAG,EAAE;IACrB,OAAOrS,IAAI,CAACC,KAAK,CAAEQ,IAAI,CAACL,QAAQ,GAAG,GAAG,GAAIiS,UAAU,CAAC;EACvD;EAEAC,mBAAmBA,CAAChT,OAAY;IAC9B,MAAMmB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACb,OAAO,CAACzK,EAAE,CAAC;IAClD,OAAO,IAAI,CAAC0d,eAAe,CAAC9R,IAAI,CAACH,WAAW,CAAC;EAC/C;EAEAkS,gBAAgBA,CAAClT,OAAY;IAC3B,MAAMmB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACb,OAAO,CAACzK,EAAE,CAAC;IAClD,MAAMwL,QAAQ,GAAGI,IAAI,CAACJ,QAAQ,IAAIf,OAAO,CAACmT,QAAQ,EAAEpS,QAAQ,IAAI,CAAC;IAEjE,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAOA,QAAQ,CAAC,CAAC;;;IAGnB,OAAO,IAAI,CAACkS,eAAe,CAAClS,QAAQ,CAAC;EACvC;EAEQkS,eAAeA,CAAC9H,OAAe;IACrC,MAAMD,OAAO,GAAGxK,IAAI,CAACC,KAAK,CAACwK,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMiI,gBAAgB,GAAG1S,IAAI,CAACC,KAAK,CAACwK,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGD,OAAO,IAAIkI,gBAAgB,CAAChP,QAAQ,EAAE,CAACgH,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEAiI,gBAAgBA,CAACrT,OAAY,EAAEsT,SAAiB;IAC9C,MAAM5T,SAAS,GAAGM,OAAO,CAACzK,EAAE;IAE5B,IAAI,CAAC,IAAI,CAACiB,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKiJ,SAAS,EAAE;MAC7D;;IAGF,MAAMqT,UAAU,GAAG,EAAE;IACrB,MAAMQ,cAAc,GAAID,SAAS,GAAGP,UAAU,GAAI,GAAG;IACrD,MAAMS,QAAQ,GAAID,cAAc,GAAG,GAAG,GAAI,IAAI,CAAC/c,YAAY,CAACuK,QAAQ;IAEpE,IAAI,CAACvK,YAAY,CAACwK,WAAW,GAAGwS,QAAQ;IACxC7a,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE4a,QAAQ,EAAE,SAAS,CAAC;EAC5D;EAEAC,gBAAgBA,CAACzT,OAAY;IAC3B,MAAMN,SAAS,GAAGM,OAAO,CAACzK,EAAE;IAC5B,MAAM4L,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACnB,SAAS,CAAC;IAEjD;IACA,MAAMgU,QAAQ,GAAGvS,IAAI,CAACF,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGE,IAAI,CAACF,KAAK,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;IAEpE,IAAI,CAACC,oBAAoB,CAACxB,SAAS,EAAE;MAAEuB,KAAK,EAAEyS;IAAQ,CAAE,CAAC;IAEzD,IAAI,IAAI,CAACld,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKiJ,SAAS,EAAE;MAC5D,IAAI,CAAClJ,YAAY,CAAC0b,YAAY,GAAGwB,QAAQ;;IAG3C,IAAI,CAAChgB,YAAY,CAACuG,WAAW,CAAC,YAAYyZ,QAAQ,GAAG,CAAC;EACxD;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC7b,aAAa,CAAC8b,WAAW,EAAE;IAEhC;IACA,IAAI,IAAI,CAAC9c,SAAS,EAAE;MAClB8T,aAAa,CAAC,IAAI,CAAC9T,SAAS,CAAC;;IAE/B,IAAI,IAAI,CAACR,cAAc,EAAE;MACvBsU,aAAa,CAAC,IAAI,CAACtU,cAAc,CAAC;;IAEpC,IAAI,IAAI,CAACuB,aAAa,EAAE;MACtB0Z,YAAY,CAAC,IAAI,CAAC1Z,aAAa,CAAC;;IAGlC;IACA,IAAI,IAAI,CAACzB,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAAC4W,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAAC5W,aAAa,CAAC6W,IAAI,EAAE;;MAE3B,IAAI,CAAC7W,aAAa,CAAC0V,MAAM,EAAEoB,SAAS,EAAE,CAAC1O,OAAO,CAAE2O,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGzE;IACA,IAAI,CAAC6E,iBAAiB,EAAE;EAC1B;CACD;AA98EyC+B,UAAA,EAAvC5gB,SAAS,CAAC,mBAAmB,CAAC,C,8DAAwC;AAEvE4gB,UAAA,EADC5gB,SAAS,CAAC,WAAW,EAAE;EAAE6gB,MAAM,EAAE;AAAK,CAAE,CAAC,C,sDACD;AAJ9BzgB,oBAAoB,GAAAwgB,UAAA,EAJhC7gB,SAAS,CAAC;EACT+gB,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE;CACd,CAAC,C,EACW3gB,oBAAoB,CAg9EhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}