<!-- Chat Moderne Futuriste -->
<div
  style="
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #1f2937;
  "
>
  <!-- En-tête -->
  <header
    style="
      display: flex;
      align-items: center;
      padding: 12px 16px;
      background: #ffffff;
      border-bottom: 1px solid #e5e7eb;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    "
  >
    <!-- Bouton retour -->
    <button
      (click)="goBackToConversations()"
      style="
        padding: 8px;
        margin-right: 12px;
        border-radius: 50%;
        border: none;
        background: transparent;
        cursor: pointer;
        transition: all 0.2s;
      "
      onmouseover="this.style.background='#f3f4f6'"
      onmouseout="this.style.background='transparent'"
    >
      <i class="fas fa-arrow-left" style="color: #6b7280"></i>
    </button>

    <!-- Info utilisateur -->
    <div style="display: flex; align-items: center; flex: 1; min-width: 0">
      <div style="position: relative; margin-right: 12px">
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          [alt]="otherParticipant?.username"
          style="
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #10b981;
            cursor: pointer;
            transition: transform 0.2s;
          "
          (click)="openUserProfile(otherParticipant?.id!)"
          onmouseover="this.style.transform='scale(1.05)'"
          onmouseout="this.style.transform='scale(1)'"
        />
        <div
          *ngIf="otherParticipant?.isOnline"
          style="
            position: absolute;
            bottom: 0;
            right: 0;
            width: 12px;
            height: 12px;
            background: #10b981;
            border: 2px solid #ffffff;
            border-radius: 50%;
            animation: pulse 2s infinite;
          "
        ></div>
      </div>

      <div style="flex: 1; min-width: 0">
        <h3
          style="
            font-weight: 600;
            color: #111827;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          "
        >
          {{ otherParticipant?.username || "Utilisateur" }}
        </h3>
        <div style="font-size: 14px; color: #6b7280">
          <div
            *ngIf="isUserTyping"
            style="display: flex; align-items: center; gap: 4px; color: #10b981"
          >
            <span>En train d'écrire</span>
            <div style="display: flex; gap: 2px">
              <div
                style="
                  width: 4px;
                  height: 4px;
                  background: #10b981;
                  border-radius: 50%;
                  animation: bounce 1s infinite;
                "
              ></div>
              <div
                style="
                  width: 4px;
                  height: 4px;
                  background: #10b981;
                  border-radius: 50%;
                  animation: bounce 1s infinite 0.1s;
                "
              ></div>
              <div
                style="
                  width: 4px;
                  height: 4px;
                  background: #10b981;
                  border-radius: 50%;
                  animation: bounce 1s infinite 0.2s;
                "
              ></div>
            </div>
          </div>
          <span *ngIf="!isUserTyping">
            {{
              otherParticipant?.isOnline
                ? "En ligne"
                : formatLastActive(otherParticipant?.lastActive)
            }}
          </span>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div style="display: flex; align-items: center; gap: 8px">
      <button
        (click)="startVideoCall()"
        style="
          padding: 8px;
          border-radius: 50%;
          border: none;
          background: transparent;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s;
        "
        title="Appel vidéo"
        onmouseover="this.style.background='#f3f4f6'"
        onmouseout="this.style.background='transparent'"
      >
        <i class="fas fa-video"></i>
      </button>
      <button
        (click)="startVoiceCall()"
        style="
          padding: 8px;
          border-radius: 50%;
          border: none;
          background: transparent;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s;
        "
        title="Appel vocal"
        onmouseover="this.style.background='#f3f4f6'"
        onmouseout="this.style.background='transparent'"
      >
        <i class="fas fa-phone"></i>
      </button>
      <button
        (click)="toggleSearch()"
        style="
          padding: 8px;
          border-radius: 50%;
          border: none;
          background: transparent;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s;
        "
        [style.background]="searchMode ? '#dcfce7' : 'transparent'"
        [style.color]="searchMode ? '#16a34a' : '#6b7280'"
        title="Rechercher"
        onmouseover="this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background"
        onmouseout="this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background"
      >
        <i class="fas fa-search"></i>
      </button>

      <button
        (click)="toggleMainMenu()"
        style="
          padding: 8px;
          border-radius: 50%;
          border: none;
          background: transparent;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s;
          position: relative;
        "
        [style.background]="showMainMenu ? '#dcfce7' : 'transparent'"
        [style.color]="showMainMenu ? '#16a34a' : '#6b7280'"
        title="Menu"
        onmouseover="this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background"
        onmouseout="this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background"
      >
        <i class="fas fa-ellipsis-v"></i>
      </button>
    </div>

    <!-- Menu principal dropdown -->
    <div
      *ngIf="showMainMenu"
      style="
        position: absolute;
        top: 64px;
        right: 16px;
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        z-index: 50;
        min-width: 192px;
      "
    >
      <div style="padding: 8px">
        <button
          (click)="toggleSearch(); showMainMenu = false"
          style="
            width: 100%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
          "
          onmouseover="this.style.background='#f3f4f6'"
          onmouseout="this.style.background='transparent'"
        >
          <i class="fas fa-search" style="color: #3b82f6"></i>
          <span style="color: #374151">Rechercher</span>
        </button>
        <button
          style="
            width: 100%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
          "
          onmouseover="this.style.background='#f3f4f6'"
          onmouseout="this.style.background='transparent'"
        >
          <i class="fas fa-user" style="color: #10b981"></i>
          <span style="color: #374151">Voir le profil</span>
        </button>
        <button
          style="
            width: 100%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
          "
          onmouseover="this.style.background='#f3f4f6'"
          onmouseout="this.style.background='transparent'"
        >
          <i class="fas fa-bell" style="color: #f59e0b"></i>
          <span style="color: #374151">Notifications</span>
        </button>

        <hr style="margin: 8px 0; border-color: #e5e7eb" />
        <button
          style="
            width: 100%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
          "
          onmouseover="this.style.background='#f3f4f6'"
          onmouseout="this.style.background='transparent'"
        >
          <i class="fas fa-cog" style="color: #6b7280"></i>
          <span style="color: #374151">Paramètres</span>
        </button>
      </div>
    </div>
  </header>

  <!-- Zone de messages avec drag & drop -->
  <main
    style="flex: 1; overflow-y: auto; padding: 16px; position: relative"
    #messagesContainer
    (scroll)="onScroll($event)"
    (dragover)="onDragOver($event)"
    (dragleave)="onDragLeave($event)"
    (drop)="onDrop($event)"
    [style.background]="isDragOver ? 'rgba(34, 197, 94, 0.1)' : 'transparent'"
  >
    <!-- Overlay drag & drop -->
    <div
      *ngIf="isDragOver"
      style="
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(34, 197, 94, 0.2);
        border: 2px dashed #10b981;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 50;
        backdrop-filter: blur(2px);
        animation: pulse 2s infinite;
      "
    >
      <div
        style="
          text-align: center;
          background: #ffffff;
          padding: 24px;
          border-radius: 12px;
          box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
          border: 1px solid #10b981;
        "
      >
        <i
          style="
            font-size: 48px;
            color: #10b981;
            margin-bottom: 12px;
            animation: bounce 1s infinite;
          "
          class="fas fa-cloud-upload-alt"
        ></i>
        <p
          style="
            font-size: 20px;
            font-weight: bold;
            color: #047857;
            margin-bottom: 8px;
          "
        >
          Déposez vos fichiers ici
        </p>
        <p style="font-size: 14px; color: #10b981">
          Images, vidéos, documents...
        </p>
        <div
          style="
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 12px;
          "
        >
          <span
            style="
              width: 8px;
              height: 8px;
              background: #10b981;
              border-radius: 50%;
              animation: ping 1s infinite;
            "
          ></span>
          <span
            style="
              width: 8px;
              height: 8px;
              background: #10b981;
              border-radius: 50%;
              animation: ping 1s infinite 0.2s;
            "
          ></span>
          <span
            style="
              width: 8px;
              height: 8px;
              background: #10b981;
              border-radius: 50%;
              animation: ping 1s infinite 0.4s;
            "
          ></span>
        </div>
      </div>
    </div>

    <!-- Chargement -->
    <div
      *ngIf="isLoading"
      style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 32px 0;
      "
    >
      <div
        style="
          width: 32px;
          height: 32px;
          border: 2px solid #e5e7eb;
          border-bottom-color: #10b981;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 16px;
        "
      ></div>
      <span style="color: #6b7280">Chargement des messages...</span>
    </div>

    <!-- État vide -->
    <div
      *ngIf="!isLoading && messages.length === 0"
      style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 64px 0;
      "
    >
      <div style="font-size: 64px; color: #d1d5db; margin-bottom: 16px">
        <i class="fas fa-comments"></i>
      </div>
      <h3
        style="
          font-size: 20px;
          font-weight: 600;
          color: #374151;
          margin-bottom: 8px;
        "
      >
        Aucun message
      </h3>
      <p style="color: #6b7280; text-align: center">
        Commencez votre conversation avec {{ otherParticipant?.username }}
      </p>
    </div>

    <!-- Messages -->
    <div
      *ngIf="!isLoading && messages.length > 0"
      style="display: flex; flex-direction: column; gap: 8px"
    >
      <ng-container
        *ngFor="
          let message of messages;
          let i = index;
          trackBy: trackByMessageId
        "
      >
        <!-- Séparateur de date -->
        <div
          *ngIf="shouldShowDateSeparator(i)"
          style="display: flex; justify-content: center; margin: 16px 0"
        >
          <div
            style="
              background: #ffffff;
              padding: 4px 12px;
              border-radius: 20px;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            "
          >
            <span style="font-size: 12px; color: #6b7280">
              {{ formatDateSeparator(message.timestamp) }}
            </span>
          </div>
        </div>

        <!-- Message -->
        <div
          style="display: flex"
          [style.justify-content]="
            message.sender?.id === currentUserId ? 'flex-end' : 'flex-start'
          "
          [id]="'message-' + message.id"
          (click)="onMessageClick(message, $event)"
          (contextmenu)="onMessageContextMenu(message, $event)"
        >
          <!-- Avatar pour les autres -->
          <div
            *ngIf="message.sender?.id !== currentUserId && shouldShowAvatar(i)"
            style="margin-right: 8px; flex-shrink: 0"
          >
            <img
              [src]="
                message.sender?.image || 'assets/images/default-avatar.png'
              "
              [alt]="message.sender?.username"
              style="
                width: 32px;
                height: 32px;
                border-radius: 50%;
                object-fit: cover;
                cursor: pointer;
                transition: transform 0.2s;
              "
              (click)="openUserProfile(message.sender?.id!)"
              onmouseover="this.style.transform='scale(1.05)'"
              onmouseout="this.style.transform='scale(1)'"
            />
          </div>

          <!-- Bulle de message -->
          <div
            [style.background-color]="
              message.sender?.id === currentUserId ? '#3b82f6' : '#ffffff'
            "
            [style.color]="
              message.sender?.id === currentUserId ? '#ffffff' : '#111827'
            "
            style="
              max-width: 320px;
              padding: 12px 16px;
              border-radius: 18px;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
              position: relative;
              word-wrap: break-word;
              overflow-wrap: break-word;
              border: none;
            "
          >
            <!-- Nom expéditeur (groupes) -->
            <div
              *ngIf="
                isGroupConversation() &&
                message.sender?.id !== currentUserId &&
                shouldShowSenderName(i)
              "
              style="
                font-size: 12px;
                font-weight: 600;
                margin-bottom: 4px;
                opacity: 0.75;
              "
              [style.color]="getUserColor(message.sender?.id!)"
            >
              {{ message.sender?.username }}
            </div>

            <!-- Contenu texte -->
            <div
              *ngIf="getMessageType(message) === 'text'"
              style="word-wrap: break-word; overflow-wrap: break-word"
            >
              <div [innerHTML]="formatMessageContent(message.content)"></div>
            </div>

            <!-- Image dans l'espace de chat -->
            <div *ngIf="hasImage(message)" style="margin: 8px 0">
              <img
                [src]="getImageUrl(message)"
                [alt]="message.content || 'Image'"
                (click)="openImageViewer(message)"
                (load)="onImageLoad($event, message)"
                (error)="onImageError($event, message)"
                style="
                  max-width: 280px;
                  height: auto;
                  border-radius: 12px;
                  cursor: pointer;
                  transition: transform 0.2s;
                "
                onmouseover="this.style.transform='scale(1.02)'"
                onmouseout="this.style.transform='scale(1)'"
              />
              <!-- Légende de l'image -->
              <div
                *ngIf="message.content"
                [style.color]="
                  message.sender?.id === currentUserId ? '#ffffff' : '#111827'
                "
                style="font-size: 14px; margin-top: 8px; line-height: 1.4"
                [innerHTML]="formatMessageContent(message.content)"
              ></div>
            </div>

            <!-- Message vocal -->
            <div
              *ngIf="getMessageType(message) === 'audio'"
              class="flex items-center gap-3 p-3 rounded-xl min-w-[250px] max-w-xs cursor-pointer transition-all duration-300 hover:shadow-lg group border bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600"
              [class.shadow-lg]="isVoicePlaying(message.id)"
              [class.ring-2]="isVoicePlaying(message.id)"
              [class.ring-blue-300]="isVoicePlaying(message.id)"
              style="position: relative; overflow: hidden"
            >
              <!-- Bouton play/pause -->
              <button
                class="p-2 rounded-full text-white transition-all duration-300 flex-shrink-0 border-none outline-none cursor-pointer"
                [class.bg-blue-500]="!isVoicePlaying(message.id)"
                [class.hover:bg-blue-600]="!isVoicePlaying(message.id)"
                [class.bg-red-500]="isVoicePlaying(message.id)"
                [class.hover:bg-red-600]="isVoicePlaying(message.id)"
                [class.animate-pulse]="isVoicePlaying(message.id)"
                (click)="$event.stopPropagation(); toggleVoicePlayback(message)"
                style="
                  width: 40px;
                  height: 40px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                "
                [style.transform]="
                  isVoicePlaying(message.id) ? 'scale(1.05)' : 'scale(1)'
                "
              >
                <i
                  class="fas text-sm transition-transform duration-200"
                  [class.fa-play]="!isVoicePlaying(message.id)"
                  [class.fa-pause]="isVoicePlaying(message.id)"
                  [class.scale-110]="isVoicePlaying(message.id)"
                ></i>
              </button>

              <div class="flex-1 min-w-0">
                <!-- Waves audio WhatsApp style avec progression -->
                <div class="flex items-center gap-1 h-8 mb-2 px-1">
                  <div
                    *ngFor="let wave of getVoiceWaves(message); let i = index"
                    class="w-1 rounded-full transition-all duration-300 cursor-pointer hover:scale-110 hover:opacity-80"
                    [class.bg-gray-400]="!isVoicePlaying(message.id)"
                    [class.bg-blue-500]="
                      isVoicePlaying(message.id) &&
                      i <= getVoiceProgress(message)
                    "
                    [class.bg-gray-300]="
                      isVoicePlaying(message.id) &&
                      i > getVoiceProgress(message.id)
                    "
                    [class.dark:bg-gray-500]="
                      isVoicePlaying(message.id) &&
                      i > getVoiceProgress(message.id)
                    "
                    [style.height.px]="wave"
                    [class.animate-pulse]="
                      isVoicePlaying(message.id) &&
                      i <= getVoiceProgress(message)
                    "
                    (click)="
                      $event.stopPropagation(); seekVoiceMessage(message, i)
                    "
                    style="
                      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                      min-height: 4px;
                    "
                    [style.transform]="
                      'scaleY(' +
                      (isVoicePlaying(message.id) &&
                      i <= getVoiceProgress(message)
                        ? '1.3'
                        : '1') +
                      ')'
                    "
                    [style.box-shadow]="
                      isVoicePlaying(message.id) &&
                      i <= getVoiceProgress(message)
                        ? '0 0 8px rgba(59, 130, 246, 0.5)'
                        : 'none'
                    "
                  ></div>
                </div>

                <!-- Durée et progression -->
                <div class="flex justify-between items-center text-xs mt-1">
                  <div class="flex items-center gap-2">
                    <span
                      class="text-gray-600 dark:text-gray-300 font-mono text-xs"
                    >
                      {{ getVoiceCurrentTime(message) }}
                    </span>
                    <span class="text-gray-400 dark:text-gray-500">/</span>
                    <span
                      class="text-gray-500 dark:text-gray-400 font-mono text-xs"
                    >
                      {{ getVoiceDuration(message) }}
                    </span>
                  </div>
                  <div class="flex items-center gap-1">
                    <!-- Indicateur de vitesse -->
                    <span
                      *ngIf="getVoicePlaybackData(message.id).speed !== 1"
                      class="text-green-600 dark:text-green-400 font-semibold text-xs px-1 py-0.5 bg-green-100 dark:bg-green-900 rounded"
                    >
                      {{ getVoicePlaybackData(message.id).speed }}x
                    </span>
                    <!-- Indicateur de lecture -->
                    <div
                      *ngIf="isVoicePlaying(message.id)"
                      class="flex items-center gap-0.5"
                    >
                      <div
                        class="w-1 h-1 bg-green-500 rounded-full animate-pulse"
                      ></div>
                      <div
                        class="w-1 h-1 bg-green-500 rounded-full animate-pulse"
                        style="animation-delay: 0.2s"
                      ></div>
                      <div
                        class="w-1 h-1 bg-green-500 rounded-full animate-pulse"
                        style="animation-delay: 0.4s"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Menu vitesse (apparaît au hover) -->
              <div
                class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col gap-1"
              >
                <button
                  class="p-1.5 rounded-full hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-500 dark:text-gray-400 transition-all duration-200"
                  (click)="$event.stopPropagation(); toggleVoiceSpeed(message)"
                  title="Vitesse de lecture"
                  style="border: none; outline: none; cursor: pointer"
                >
                  <i class="fas fa-tachometer-alt text-xs"></i>
                </button>
              </div>
            </div>

            <!-- Fichier -->
            <div
              *ngIf="
                hasFile(message) &&
                getMessageType(message) !== 'audio' &&
                !hasImage(message)
              "
              class="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors"
              (click)="downloadFile(message)"
            >
              <div class="text-2xl text-gray-500 dark:text-gray-400">
                <i [class]="getFileIcon(message)"></i>
              </div>
              <div class="flex-1 min-w-0">
                <div class="font-medium text-sm truncate">
                  {{ getFileName(message) }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ getFileSize(message) }}
                </div>
              </div>
              <button
                class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-400 transition-colors"
              >
                <i class="fas fa-download text-sm"></i>
              </button>
            </div>

            <!-- Métadonnées -->
            <div
              class="flex items-center justify-end gap-1 mt-1 text-xs opacity-75"
            >
              <span>{{ formatMessageTime(message.timestamp) }}</span>
              <div
                *ngIf="message.sender?.id === currentUserId"
                class="flex items-center"
              >
                <i
                  class="fas fa-clock"
                  *ngIf="message.status === 'SENDING'"
                  title="Envoi en cours"
                ></i>
                <i
                  class="fas fa-check"
                  *ngIf="message.status === 'SENT'"
                  title="Envoyé"
                ></i>
                <i
                  class="fas fa-check-double"
                  *ngIf="message.status === 'DELIVERED'"
                  title="Livré"
                ></i>
                <i
                  class="fas fa-check-double text-blue-400"
                  *ngIf="message.status === 'READ'"
                  title="Lu"
                ></i>
              </div>
            </div>

            <!-- Réactions -->
            <div
              *ngIf="message.reactions && message.reactions.length > 0"
              class="flex gap-1 mt-2"
            >
              <button
                *ngFor="let reaction of message.reactions"
                (click)="toggleReaction(message.id!, reaction.emoji)"
                class="flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
                [class.bg-green-100]="
                  hasUserReacted(reaction, currentUserId || '')
                "
                [class.text-green-600]="
                  hasUserReacted(reaction, currentUserId || '')
                "
              >
                <span>{{ reaction.emoji }}</span>
                <span>{{ reaction.count || 1 }}</span>
              </button>
            </div>

            <!-- Bouton de réaction rapide (apparaît au hover) -->
            <button
              (click)="showQuickReactions(message, $event)"
              class="absolute -bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-full p-1 shadow-sm hover:shadow-md"
              title="Ajouter une réaction"
            >
              <i
                class="fas fa-smile text-gray-500 dark:text-gray-400 text-xs"
              ></i>
            </button>
          </div>
        </div>
      </ng-container>

      <!-- Indicateur de frappe (seulement quand l'autre personne tape) -->
      <div *ngIf="otherUserIsTyping" class="flex items-start gap-2">
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          [alt]="otherParticipant?.username"
          class="w-8 h-8 rounded-full object-cover"
        />
        <div class="bg-white dark:bg-gray-700 px-4 py-2 rounded-2xl shadow-sm">
          <div class="flex gap-1">
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div
              class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
              style="animation-delay: 0.1s"
            ></div>
            <div
              class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
              style="animation-delay: 0.2s"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Progress bar pour upload -->
  <div
    *ngIf="isUploading"
    class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2"
  >
    <div class="flex items-center gap-3">
      <div class="flex-1">
        <div
          class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1"
        >
          <span>Envoi en cours...</span>
          <span>{{ uploadProgress.toFixed(0) }}%</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            class="bg-green-500 h-2 rounded-full transition-all duration-300"
            [style.width.%]="uploadProgress"
          ></div>
        </div>
      </div>
      <button
        (click)="resetUploadState()"
        class="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500"
        title="Annuler"
      >
        <i class="fas fa-times text-sm"></i>
      </button>
    </div>
  </div>

  <!-- Zone d'input -->
  <footer
    class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4"
  >
    <form
      [formGroup]="messageForm"
      (ngSubmit)="sendMessage()"
      class="flex items-end gap-3"
    >
      <!-- Actions gauche -->
      <div class="flex gap-2">
        <button
          type="button"
          (click)="toggleEmojiPicker()"
          class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
          [class.bg-green-100]="showEmojiPicker"
          [class.text-green-600]="showEmojiPicker"
          title="Émojis"
        >
          <i class="fas fa-smile"></i>
        </button>
        <button
          type="button"
          (click)="toggleAttachmentMenu()"
          class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
          [class.bg-green-100]="showAttachmentMenu"
          [class.text-green-600]="showAttachmentMenu"
          title="Joindre un fichier"
        >
          <i class="fas fa-paperclip"></i>
        </button>
      </div>

      <!-- Champ de saisie -->
      <div class="flex-1">
        <textarea
          formControlName="content"
          #messageTextarea
          placeholder="Tapez votre message..."
          class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          [class.opacity-50]="isInputDisabled()"
          [class.cursor-not-allowed]="isInputDisabled()"
          (input)="onInputChange($event)"
          (keydown)="onInputKeyDown($event)"
          (focus)="onInputFocus()"
          (blur)="onInputBlur()"
          rows="1"
          maxlength="4096"
          autocomplete="off"
          spellcheck="true"
        >
        </textarea>
      </div>

      <!-- Actions droite -->
      <div class="flex gap-2">
        <!-- Enregistrement vocal -->
        <button
          *ngIf="!messageForm.get('content')?.value?.trim()"
          type="button"
          (mousedown)="onRecordStart($event)"
          (mouseup)="onRecordEnd($event)"
          (mouseleave)="onRecordCancel($event)"
          (touchstart)="onRecordStart($event)"
          (touchend)="onRecordEnd($event)"
          (touchcancel)="onRecordCancel($event)"
          [ngClass]="{
            'voice-record-button': true,
            recording: isRecordingVoice,
            processing: voiceRecordingState === 'processing'
          }"
          [disabled]="voiceRecordingState === 'processing'"
          title="Maintenir pour enregistrer un message vocal"
        >
          <i
            class="fas fa-microphone"
            *ngIf="voiceRecordingState !== 'processing'"
          ></i>
          <i
            class="fas fa-spinner"
            *ngIf="voiceRecordingState === 'processing'"
          ></i>
        </button>

        <!-- Bouton d'envoi -->
        <button
          *ngIf="messageForm.get('content')?.value?.trim()"
          type="button"
          (click)="sendMessage()"
          class="p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50"
          [disabled]="isSendingMessage"
          title="Envoyer"
        >
          <i class="fas fa-paper-plane" *ngIf="!isSendingMessage"></i>
          <i class="fas fa-spinner fa-spin" *ngIf="isSendingMessage"></i>
        </button>
      </div>
    </form>
  </footer>

  <!-- Interface d'enregistrement vocal style WhatsApp -->
  <div
    *ngIf="isRecordingVoice"
    class="absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4 z-50"
  >
    <div class="flex items-center gap-4">
      <!-- Bouton annuler -->
      <button
        (click)="cancelVoiceRecording()"
        class="p-2 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
        title="Annuler"
      >
        <i class="fas fa-times"></i>
      </button>

      <!-- Indicateur d'enregistrement -->
      <div class="flex items-center gap-3 flex-1">
        <!-- Icône micro animée -->
        <div class="relative">
          <i class="fas fa-microphone text-red-500 text-xl animate-pulse"></i>
          <div
            class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping"
          ></div>
        </div>

        <!-- Waves d'animation -->
        <div class="flex items-center gap-1">
          <div
            *ngFor="let wave of voiceWaves; let i = index"
            class="bg-green-500 rounded-full transition-all duration-150"
            [style.width.px]="2"
            [style.height.px]="wave"
            [style.animation-delay.ms]="i * 100"
          ></div>
        </div>

        <!-- Durée d'enregistrement -->
        <div class="text-gray-600 dark:text-gray-300 font-mono">
          {{ formatRecordingDuration(voiceRecordingDuration) }}
        </div>
      </div>

      <!-- Bouton envoyer -->
      <button
        (click)="stopVoiceRecording()"
        class="p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors"
        title="Envoyer l'enregistrement"
      >
        <i class="fas fa-paper-plane"></i>
      </button>
    </div>

    <!-- Barre de progression -->
    <div class="mt-3 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
      <div
        class="bg-green-500 h-1 rounded-full transition-all duration-300"
        [style.width.%]="(voiceRecordingDuration / 60) * 100"
      ></div>
    </div>

    <!-- Instructions -->
    <div class="mt-2 text-center text-sm text-gray-500 dark:text-gray-400">
      <div class="flex items-center justify-center gap-2">
        <i class="fas fa-info-circle"></i>
        <span>Relâchez pour envoyer • Glissez vers le haut pour annuler</span>
      </div>
      <div class="mt-1 text-xs">
        Durée max: 60 secondes • Format: {{ getRecordingFormat() }}
      </div>
    </div>
  </div>

  <!-- Sélecteur d'émojis -->
  <div
    *ngIf="showEmojiPicker"
    class="absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50"
  >
    <div class="p-4">
      <div class="flex gap-2 mb-4 overflow-x-auto">
        <button
          *ngFor="let category of emojiCategories"
          (click)="selectEmojiCategory(category)"
          class="px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0"
          [class.bg-green-100]="selectedEmojiCategory === category"
          [class.text-green-600]="selectedEmojiCategory === category"
          [class.hover:bg-gray-100]="selectedEmojiCategory !== category"
          [class.dark:hover:bg-gray-700]="selectedEmojiCategory !== category"
        >
          {{ category.icon }}
        </button>
      </div>
      <div class="grid grid-cols-8 gap-2 max-h-48 overflow-y-auto">
        <button
          *ngFor="let emoji of getEmojisForCategory(selectedEmojiCategory)"
          (click)="insertEmoji(emoji)"
          class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl"
          [title]="emoji.name"
        >
          {{ emoji.emoji }}
        </button>
      </div>
    </div>
  </div>

  <!-- Menu des pièces jointes -->
  <div
    *ngIf="showAttachmentMenu"
    class="absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50"
  >
    <div class="p-4">
      <div class="grid grid-cols-2 gap-3">
        <button
          (click)="triggerFileInput('image')"
          class="flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <div
            class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-image text-blue-600 dark:text-blue-400"></i>
          </div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Photos</span
          >
        </button>
        <button
          (click)="triggerFileInput('video')"
          class="flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <div
            class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-video text-purple-600 dark:text-purple-400"></i>
          </div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Vidéos</span
          >
        </button>
        <button
          (click)="triggerFileInput('document')"
          class="flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <div
            class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-file text-orange-600 dark:text-orange-400"></i>
          </div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Documents</span
          >
        </button>
        <button
          (click)="openCamera()"
          class="flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <div
            class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-camera text-green-600 dark:text-green-400"></i>
          </div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Caméra</span
          >
        </button>
      </div>
    </div>
  </div>

  <!-- Input caché pour fichiers -->
  <input
    #fileInput
    type="file"
    class="hidden"
    (change)="onFileSelected($event)"
    [accept]="getFileAcceptTypes()"
    multiple
  />

  <!-- Sélecteur de réaction rapide -->
  <div
    *ngIf="showReactionPicker"
    class="fixed bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 p-3"
    [style.left.px]="contextMenuPosition.x - 100"
    [style.top.px]="contextMenuPosition.y - 60"
  >
    <div class="flex gap-2">
      <button
        *ngFor="let emoji of ['❤️', '😂', '😮', '😢', '😡', '👍']"
        (click)="quickReact(emoji)"
        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl"
      >
        {{ emoji }}
      </button>
    </div>
  </div>

  <!-- Menu contextuel pour messages -->
  <div
    *ngIf="showMessageContextMenu"
    class="fixed bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-48"
    [style.left.px]="contextMenuPosition.x"
    [style.top.px]="contextMenuPosition.y"
  >
    <div class="p-2">
      <button
        (click)="replyToMessage(selectedMessage)"
        class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left"
      >
        <i class="fas fa-reply text-blue-500"></i>
        <span class="text-gray-700 dark:text-gray-300">Répondre</span>
      </button>
      <button
        (click)="forwardMessage(selectedMessage)"
        class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left"
      >
        <i class="fas fa-share text-green-500"></i>
        <span class="text-gray-700 dark:text-gray-300">Transférer</span>
      </button>
      <button
        (click)="showQuickReactions(selectedMessage, $event)"
        class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left"
      >
        <i class="fas fa-smile text-yellow-500"></i>
        <span class="text-gray-700 dark:text-gray-300">Réagir</span>
      </button>
      <hr class="my-2 border-gray-200 dark:border-gray-600" />
      <button
        (click)="deleteMessage(selectedMessage)"
        class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left text-red-600"
      >
        <i class="fas fa-trash text-red-500"></i>
        <span>Supprimer</span>
      </button>
    </div>
  </div>

  <!-- Overlay pour fermer les menus -->
  <div
    *ngIf="
      showEmojiPicker ||
      showAttachmentMenu ||
      showMainMenu ||
      showMessageContextMenu ||
      showReactionPicker
    "
    class="fixed inset-0 bg-black bg-opacity-25 z-40"
    (click)="closeAllMenus()"
  ></div>
</div>

<!-- Interface d'appel WebRTC -->
<app-call-interface
  [isVisible]="isInCall"
  [activeCall]="activeCall"
  [callType]="callType"
  [otherParticipant]="otherParticipant"
  (callEnded)="endCall()"
  (callAccepted)="onCallAccepted($event)"
  (callRejected)="onCallRejected()"
></app-call-interface>

<!-- Visionneuse d'images plein écran -->
<div
  *ngIf="showImageViewer"
  class="fixed inset-0 bg-black bg-opacity-95 z-50 flex items-center justify-center"
  (click)="closeImageViewer()"
>
  <div class="relative max-w-full max-h-full p-4">
    <!-- Bouton fermer -->
    <button
      (click)="closeImageViewer()"
      class="absolute top-4 right-4 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all"
      title="Fermer"
    >
      <i class="fas fa-times text-xl"></i>
    </button>

    <!-- Bouton télécharger -->
    <button
      (click)="downloadImage(); $event.stopPropagation()"
      class="absolute top-4 left-4 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all"
      title="Télécharger"
    >
      <i class="fas fa-download text-xl"></i>
    </button>

    <!-- Image -->
    <img
      [src]="selectedImage?.url"
      [alt]="selectedImage?.name || 'Image'"
      class="max-w-full max-h-full object-contain rounded-lg shadow-2xl image-viewer-zoom"
      (click)="$event.stopPropagation()"
      style="max-height: 90vh; max-width: 90vw"
    />

    <!-- Informations de l'image -->
    <div
      class="absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 text-white p-3 rounded-lg"
      (click)="$event.stopPropagation()"
    >
      <div class="flex items-center justify-between">
        <div>
          <p class="font-medium">{{ selectedImage?.name || "Image" }}</p>
          <p class="text-sm opacity-75">
            {{ selectedImage?.size || "Taille inconnue" }}
          </p>
        </div>
        <div class="flex gap-2">
          <!-- Bouton zoom -->
          <button
            (click)="zoomImage(1.2); $event.stopPropagation()"
            class="p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all"
            title="Zoom +"
          >
            <i class="fas fa-search-plus"></i>
          </button>
          <button
            (click)="zoomImage(0.8); $event.stopPropagation()"
            class="p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all"
            title="Zoom -"
          >
            <i class="fas fa-search-minus"></i>
          </button>
          <!-- Bouton reset zoom -->
          <button
            (click)="resetZoom(); $event.stopPropagation()"
            class="p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all"
            title="Taille originale"
          >
            <i class="fas fa-expand-arrows-alt"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
