<!-- Chat WhatsApp Moderne avec Tailwind CSS -->
<div
  class="flex flex-col h-screen bg-gradient-to-br from-gray-50 to-green-50 dark:from-gray-900 dark:to-gray-800"
>
  <!-- En-tête -->
  <header
    class="flex items-center px-4 py-3 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm"
  >
    <!-- Bouton retour -->
    <button
      (click)="goBackToConversations()"
      class="p-2 mr-3 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
    >
      <i class="fas fa-arrow-left text-gray-600 dark:text-gray-300"></i>
    </button>

    <!-- Info utilisateur -->
    <div class="flex items-center flex-1 min-w-0">
      <div class="relative mr-3">
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          [alt]="otherParticipant?.username"
          class="w-10 h-10 rounded-full object-cover border-2 border-green-500 cursor-pointer hover:scale-105 transition-transform"
          (click)="openUserProfile(otherParticipant?.id!)"
        />
        <div
          *ngIf="otherParticipant?.isOnline"
          class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full animate-pulse"
        ></div>
      </div>

      <div class="flex-1 min-w-0">
        <h3 class="font-semibold text-gray-900 dark:text-white truncate">
          {{ otherParticipant?.username || "Utilisateur" }}
        </h3>
        <div class="text-sm text-gray-500 dark:text-gray-400">
          <div
            *ngIf="isUserTyping"
            class="flex items-center gap-1 text-green-600"
          >
            <span>En train d'écrire</span>
            <div class="flex gap-1">
              <div
                class="w-1 h-1 bg-green-600 rounded-full animate-bounce"
              ></div>
              <div
                class="w-1 h-1 bg-green-600 rounded-full animate-bounce"
                style="animation-delay: 0.1s"
              ></div>
              <div
                class="w-1 h-1 bg-green-600 rounded-full animate-bounce"
                style="animation-delay: 0.2s"
              ></div>
            </div>
          </div>
          <span *ngIf="!isUserTyping">
            {{
              otherParticipant?.isOnline
                ? "En ligne"
                : formatLastActive(otherParticipant?.lastActive)
            }}
          </span>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex items-center gap-2">
      <button
        (click)="startVideoCall()"
        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
        title="Appel vidéo"
      >
        <i class="fas fa-video"></i>
      </button>
      <button
        (click)="startVoiceCall()"
        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
        title="Appel vocal"
      >
        <i class="fas fa-phone"></i>
      </button>
      <button
        (click)="toggleSearch()"
        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
        [class.bg-green-100]="searchMode"
        [class.text-green-600]="searchMode"
        title="Rechercher"
      >
        <i class="fas fa-search"></i>
      </button>
      <button
        (click)="toggleTheme()"
        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
        title="Changer le thème"
      >
        <i class="fas fa-moon" *ngIf="!isDarkMode"></i>
        <i class="fas fa-sun" *ngIf="isDarkMode"></i>
      </button>
      <button
        (click)="toggleMainMenu()"
        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors relative"
        [class.bg-green-100]="showMainMenu"
        [class.text-green-600]="showMainMenu"
        title="Menu"
      >
        <i class="fas fa-ellipsis-v"></i>
      </button>

      <!-- Bouton de test temporaire -->
      <button
        (click)="testAddMessage()"
        class="p-2 rounded-full hover:bg-red-100 dark:hover:bg-red-700 text-red-600 dark:text-red-300 transition-colors"
        title="Test: Ajouter message"
      >
        <i class="fas fa-plus"></i>
      </button>
    </div>

    <!-- Menu principal dropdown -->
    <div
      *ngIf="showMainMenu"
      class="absolute top-16 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-48"
    >
      <div class="p-2">
        <button
          (click)="toggleSearch(); showMainMenu = false"
          class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left"
        >
          <i class="fas fa-search text-blue-500"></i>
          <span class="text-gray-700 dark:text-gray-300">Rechercher</span>
        </button>
        <button
          class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left"
        >
          <i class="fas fa-user text-green-500"></i>
          <span class="text-gray-700 dark:text-gray-300">Voir le profil</span>
        </button>
        <button
          class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left"
        >
          <i class="fas fa-bell text-yellow-500"></i>
          <span class="text-gray-700 dark:text-gray-300">Notifications</span>
        </button>

        <!-- Bouton de thème avec sélecteur -->
        <div class="relative">
          <button
            (click)="toggleThemeSelector()"
            class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left"
            [class.bg-gray-100]="showThemeSelector"
            [class.dark:bg-gray-700]="showThemeSelector"
          >
            <i
              [class]="getCurrentTheme()?.icon"
              [style.color]="getCurrentTheme()?.color"
            ></i>
            <span class="text-gray-700 dark:text-gray-300"
              >Thème: {{ getCurrentTheme()?.name }}</span
            >
            <i
              class="fas fa-chevron-right ml-auto text-xs text-gray-400 transition-transform"
              [class.rotate-90]="showThemeSelector"
            ></i>
          </button>

          <!-- Sous-menu des thèmes -->
          <div
            *ngIf="showThemeSelector"
            class="absolute left-full top-0 ml-2 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-40"
          >
            <div class="p-2">
              <button
                *ngFor="let theme of themes"
                (click)="selectTheme(theme.id)"
                class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left"
                [class.bg-blue-50]="currentTheme === theme.id"
                [class.dark:bg-blue-900]="currentTheme === theme.id"
                [class.text-blue-600]="currentTheme === theme.id"
                [class.dark:text-blue-400]="currentTheme === theme.id"
              >
                <i [class]="theme.icon" [style.color]="theme.color"></i>
                <span
                  class="text-gray-700 dark:text-gray-300"
                  [class.text-blue-600]="currentTheme === theme.id"
                  [class.dark:text-blue-400]="currentTheme === theme.id"
                  >{{ theme.name }}</span
                >
                <i
                  *ngIf="currentTheme === theme.id"
                  class="fas fa-check ml-auto text-xs text-blue-600 dark:text-blue-400"
                ></i>
              </button>
            </div>
          </div>
        </div>

        <hr class="my-2 border-gray-200 dark:border-gray-600" />
        <button
          class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left"
        >
          <i class="fas fa-cog text-gray-500"></i>
          <span class="text-gray-700 dark:text-gray-300">Paramètres</span>
        </button>
      </div>
    </div>
  </header>

  <!-- Zone de messages avec drag & drop -->
  <main
    class="flex-1 overflow-y-auto p-4 space-y-4 relative"
    #messagesContainer
    (scroll)="onScroll($event)"
    (dragover)="onDragOver($event)"
    (dragleave)="onDragLeave($event)"
    (drop)="onDrop($event)"
    [class.drag-over]="isDragOver"
  >
    <!-- Overlay drag & drop -->
    <div
      *ngIf="isDragOver"
      class="absolute inset-0 bg-green-500 bg-opacity-20 border-2 border-dashed border-green-500 rounded-lg flex items-center justify-center z-50 animate-pulse"
      style="
        backdrop-filter: blur(2px);
        background: linear-gradient(
          45deg,
          rgba(34, 197, 94, 0.1) 0%,
          rgba(34, 197, 94, 0.2) 50%,
          rgba(34, 197, 94, 0.1) 100%
        );
        animation: dragShimmer 2s infinite;
      "
    >
      <div
        class="text-center bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-green-300 dark:border-green-600"
      >
        <i
          class="fas fa-cloud-upload-alt text-5xl text-green-600 mb-3 animate-bounce"
        ></i>
        <p class="text-xl font-bold text-green-700 dark:text-green-400 mb-2">
          Déposez vos fichiers ici
        </p>
        <p class="text-sm text-green-600 dark:text-green-300">
          Images, vidéos, documents...
        </p>
        <div class="flex justify-center gap-2 mt-3">
          <span class="w-2 h-2 bg-green-500 rounded-full animate-ping"></span>
          <span
            class="w-2 h-2 bg-green-500 rounded-full animate-ping"
            style="animation-delay: 0.2s"
          ></span>
          <span
            class="w-2 h-2 bg-green-500 rounded-full animate-ping"
            style="animation-delay: 0.4s"
          ></span>
        </div>
      </div>
    </div>

    <!-- Styles CSS maintenant externalisés dans message-chat.component.css -->
    <!-- Chargement -->
    <div
      *ngIf="isLoading"
      class="flex flex-col items-center justify-center py-8"
    >
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mb-4"
      ></div>
      <span class="text-gray-500 dark:text-gray-400"
        >Chargement des messages...</span
      >
    </div>

    <!-- État vide -->
    <div
      *ngIf="!isLoading && messages.length === 0"
      class="flex flex-col items-center justify-center py-16"
    >
      <div class="text-6xl text-gray-300 dark:text-gray-600 mb-4">
        <i class="fas fa-comments"></i>
      </div>
      <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
        Aucun message
      </h3>
      <p class="text-gray-500 dark:text-gray-400 text-center">
        Commencez votre conversation avec {{ otherParticipant?.username }}
      </p>
    </div>

    <!-- Messages -->
    <div *ngIf="!isLoading && messages.length > 0" class="space-y-2">
      <ng-container
        *ngFor="
          let message of messages;
          let i = index;
          trackBy: trackByMessageId
        "
      >
        <!-- Séparateur de date -->
        <div
          *ngIf="shouldShowDateSeparator(i)"
          class="flex justify-center my-4"
        >
          <div
            class="bg-white dark:bg-gray-700 px-3 py-1 rounded-full shadow-sm"
          >
            <span class="text-xs text-gray-500 dark:text-gray-400">
              {{ formatDateSeparator(message.timestamp) }}
            </span>
          </div>
        </div>

        <!-- Message -->
        <div
          class="flex"
          [class.justify-end]="message.sender?.id === currentUserId"
          [class.justify-start]="message.sender?.id !== currentUserId"
          [id]="'message-' + message.id"
          (click)="onMessageClick(message, $event)"
          (contextmenu)="onMessageContextMenu(message, $event)"
        >
          <!-- Avatar pour les autres -->
          <div
            *ngIf="message.sender?.id !== currentUserId && shouldShowAvatar(i)"
            class="mr-2 flex-shrink-0"
          >
            <img
              [src]="
                message.sender?.image || 'assets/images/default-avatar.png'
              "
              [alt]="message.sender?.username"
              class="w-8 h-8 rounded-full object-cover cursor-pointer hover:scale-105 transition-transform"
              (click)="openUserProfile(message.sender?.id!)"
            />
          </div>

          <!-- Bulle de message -->
          <div
            class="max-w-xs lg:max-w-md px-4 py-2 rounded-2xl shadow-sm relative group"
            [class.bg-green-500]="message.sender?.id === currentUserId"
            [class.text-white]="message.sender?.id === currentUserId"
            [class.rounded-br-sm]="message.sender?.id === currentUserId"
            [class.bg-white]="message.sender?.id !== currentUserId"
            [class.text-gray-900]="message.sender?.id !== currentUserId"
            [class.rounded-bl-sm]="message.sender?.id !== currentUserId"
            [class.dark:bg-gray-700]="message.sender?.id !== currentUserId"
            [class.dark:text-white]="message.sender?.id !== currentUserId"
            style="word-wrap: break-word; overflow-wrap: break-word"
          >
            <!-- Nom expéditeur (groupes) -->
            <div
              *ngIf="
                isGroupConversation() &&
                message.sender?.id !== currentUserId &&
                shouldShowSenderName(i)
              "
              class="text-xs font-semibold mb-1 opacity-75"
              [style.color]="getUserColor(message.sender?.id!)"
            >
              {{ message.sender?.username }}
            </div>

            <!-- Contenu texte -->
            <div *ngIf="getMessageType(message) === 'text'" class="break-words">
              <div [innerHTML]="formatMessageContent(message.content)"></div>
            </div>

            <!-- DEBUG: Test de condition -->
            <div class="text-xs text-red-500 mb-1">
              🔍 DEBUG: hasImage({{ message.id }}) = {{ hasImage(message) }} |
              type = {{ message.type }} | attachments =
              {{ message.attachments?.length || 0 }}
            </div>

            <!-- Image dans l'espace de chat -->
            <div *ngIf="hasImage(message)" class="space-y-2">
              <div class="text-xs text-gray-500 mb-1">
                🖼️ DEBUG: Image container rendered for message {{ message.id }}
              </div>
              <!-- DEBUG: Test simple de l'image -->
              <div class="text-xs text-blue-500 mb-2">
                🖼️ URL: {{ getImageUrl(message) }}
              </div>

              <img
                [src]="getImageUrl(message)"
                [alt]="message.content || 'Image'"
                class="border-4 border-red-500 bg-yellow-200"
                style="
                  width: 200px !important;
                  height: 150px !important;
                  display: block !important;
                  visibility: visible !important;
                  opacity: 1 !important;
                  position: relative !important;
                  z-index: 9999 !important;
                "
                (load)="onImageLoad($event, message)"
                (error)="onImageError($event, message)"
                loading="eager"
              />
              <!-- Légende de l'image -->
              <div
                *ngIf="message.content"
                class="text-sm mt-2 break-words"
                [innerHTML]="formatMessageContent(message.content)"
              ></div>
            </div>

            <!-- Message vocal style WhatsApp -->
            <div
              *ngIf="getMessageType(message) === 'audio'"
              class="flex items-center gap-3 p-3 rounded-xl min-w-[250px] max-w-xs cursor-pointer transition-all duration-300 hover:shadow-lg group border"
              [class.bg-white]="message.sender?.id !== currentUserId"
              [class.border-gray-200]="message.sender?.id !== currentUserId"
              [class.dark:bg-gray-700]="message.sender?.id !== currentUserId"
              [class.dark:border-gray-600]="
                message.sender?.id !== currentUserId
              "
              [class.bg-green-50]="message.sender?.id === currentUserId"
              [class.border-green-200]="message.sender?.id === currentUserId"
              [class.dark:bg-green-900]="message.sender?.id === currentUserId"
              [class.dark:border-green-700]="
                message.sender?.id === currentUserId
              "
              [class.shadow-lg]="isVoicePlaying(message.id)"
              [class.ring-2]="isVoicePlaying(message.id)"
              [class.ring-green-300]="isVoicePlaying(message.id)"
              style="position: relative; overflow: hidden"
            >
              <!-- Bouton play/pause -->
              <button
                class="p-2 rounded-full text-white transition-all duration-300 flex-shrink-0 border-none outline-none cursor-pointer"
                [class.bg-green-500]="!isVoicePlaying(message.id)"
                [class.hover:bg-green-600]="!isVoicePlaying(message.id)"
                [class.bg-red-500]="isVoicePlaying(message.id)"
                [class.hover:bg-red-600]="isVoicePlaying(message.id)"
                [class.animate-pulse]="isVoicePlaying(message.id)"
                (click)="$event.stopPropagation(); toggleVoicePlayback(message)"
                style="
                  width: 40px;
                  height: 40px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                "
                [style.transform]="
                  isVoicePlaying(message.id) ? 'scale(1.05)' : 'scale(1)'
                "
              >
                <i
                  class="fas text-sm transition-transform duration-200"
                  [class.fa-play]="!isVoicePlaying(message.id)"
                  [class.fa-pause]="isVoicePlaying(message.id)"
                  [class.scale-110]="isVoicePlaying(message.id)"
                ></i>
              </button>

              <div class="flex-1 min-w-0">
                <!-- Waves audio WhatsApp style avec progression -->
                <div class="flex items-center gap-1 h-8 mb-2 px-1">
                  <div
                    *ngFor="let wave of getVoiceWaves(message); let i = index"
                    class="w-1 rounded-full transition-all duration-300 cursor-pointer hover:scale-110 hover:opacity-80"
                    [class.bg-green-600]="
                      !isVoicePlaying(message.id) &&
                      message.sender?.id === currentUserId
                    "
                    [class.bg-gray-400]="
                      !isVoicePlaying(message.id) &&
                      message.sender?.id !== currentUserId
                    "
                    [class.bg-blue-500]="
                      isVoicePlaying(message.id) &&
                      i <= getVoiceProgress(message)
                    "
                    [class.bg-gray-300]="
                      isVoicePlaying(message.id) &&
                      i > getVoiceProgress(message.id)
                    "
                    [class.dark:bg-gray-500]="
                      isVoicePlaying(message.id) &&
                      i > getVoiceProgress(message.id)
                    "
                    [style.height.px]="wave"
                    [class.animate-pulse]="
                      isVoicePlaying(message.id) &&
                      i <= getVoiceProgress(message)
                    "
                    (click)="
                      $event.stopPropagation(); seekVoiceMessage(message, i)
                    "
                    style="
                      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                      min-height: 4px;
                    "
                    [style.transform]="
                      'scaleY(' +
                      (isVoicePlaying(message.id) &&
                      i <= getVoiceProgress(message)
                        ? '1.3'
                        : '1') +
                      ')'
                    "
                    [style.box-shadow]="
                      isVoicePlaying(message.id) &&
                      i <= getVoiceProgress(message)
                        ? '0 0 8px rgba(59, 130, 246, 0.5)'
                        : 'none'
                    "
                  ></div>
                </div>

                <!-- Durée et progression -->
                <div class="flex justify-between items-center text-xs mt-1">
                  <div class="flex items-center gap-2">
                    <span
                      class="text-gray-600 dark:text-gray-300 font-mono text-xs"
                    >
                      {{ getVoiceCurrentTime(message) }}
                    </span>
                    <span class="text-gray-400 dark:text-gray-500">/</span>
                    <span
                      class="text-gray-500 dark:text-gray-400 font-mono text-xs"
                    >
                      {{ getVoiceDuration(message) }}
                    </span>
                  </div>
                  <div class="flex items-center gap-1">
                    <!-- Indicateur de vitesse -->
                    <span
                      *ngIf="getVoicePlaybackData(message.id).speed !== 1"
                      class="text-green-600 dark:text-green-400 font-semibold text-xs px-1 py-0.5 bg-green-100 dark:bg-green-900 rounded"
                    >
                      {{ getVoicePlaybackData(message.id).speed }}x
                    </span>
                    <!-- Indicateur de lecture -->
                    <div
                      *ngIf="isVoicePlaying(message.id)"
                      class="flex items-center gap-0.5"
                    >
                      <div
                        class="w-1 h-1 bg-green-500 rounded-full animate-pulse"
                      ></div>
                      <div
                        class="w-1 h-1 bg-green-500 rounded-full animate-pulse"
                        style="animation-delay: 0.2s"
                      ></div>
                      <div
                        class="w-1 h-1 bg-green-500 rounded-full animate-pulse"
                        style="animation-delay: 0.4s"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Menu vitesse (apparaît au hover) -->
              <div
                class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col gap-1"
              >
                <button
                  class="p-1.5 rounded-full hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-500 dark:text-gray-400 transition-all duration-200"
                  (click)="$event.stopPropagation(); toggleVoiceSpeed(message)"
                  title="Vitesse de lecture"
                  style="border: none; outline: none; cursor: pointer"
                >
                  <i class="fas fa-tachometer-alt text-xs"></i>
                </button>
              </div>
            </div>

            <!-- Fichier -->
            <div
              *ngIf="
                hasFile(message) &&
                getMessageType(message) !== 'audio' &&
                !hasImage(message)
              "
              class="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors"
              (click)="downloadFile(message)"
            >
              <div class="text-2xl text-gray-500 dark:text-gray-400">
                <i [class]="getFileIcon(message)"></i>
              </div>
              <div class="flex-1 min-w-0">
                <div class="font-medium text-sm truncate">
                  {{ getFileName(message) }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ getFileSize(message) }}
                </div>
              </div>
              <button
                class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-400 transition-colors"
              >
                <i class="fas fa-download text-sm"></i>
              </button>
            </div>

            <!-- Métadonnées -->
            <div
              class="flex items-center justify-end gap-1 mt-1 text-xs opacity-75"
            >
              <span>{{ formatMessageTime(message.timestamp) }}</span>
              <div
                *ngIf="message.sender?.id === currentUserId"
                class="flex items-center"
              >
                <i
                  class="fas fa-clock"
                  *ngIf="message.status === 'SENDING'"
                  title="Envoi en cours"
                ></i>
                <i
                  class="fas fa-check"
                  *ngIf="message.status === 'SENT'"
                  title="Envoyé"
                ></i>
                <i
                  class="fas fa-check-double"
                  *ngIf="message.status === 'DELIVERED'"
                  title="Livré"
                ></i>
                <i
                  class="fas fa-check-double text-blue-400"
                  *ngIf="message.status === 'READ'"
                  title="Lu"
                ></i>
              </div>
            </div>

            <!-- Réactions -->
            <div
              *ngIf="message.reactions && message.reactions.length > 0"
              class="flex gap-1 mt-2"
            >
              <button
                *ngFor="let reaction of message.reactions"
                (click)="toggleReaction(message.id!, reaction.emoji)"
                class="flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
                [class.bg-green-100]="
                  hasUserReacted(reaction, currentUserId || '')
                "
                [class.text-green-600]="
                  hasUserReacted(reaction, currentUserId || '')
                "
              >
                <span>{{ reaction.emoji }}</span>
                <span>{{ reaction.count || 1 }}</span>
              </button>
            </div>

            <!-- Bouton de réaction rapide (apparaît au hover) -->
            <button
              (click)="showQuickReactions(message, $event)"
              class="absolute -bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-full p-1 shadow-sm hover:shadow-md"
              title="Ajouter une réaction"
            >
              <i
                class="fas fa-smile text-gray-500 dark:text-gray-400 text-xs"
              ></i>
            </button>
          </div>
        </div>
      </ng-container>

      <!-- Indicateur de frappe (seulement quand l'autre personne tape) -->
      <div *ngIf="otherUserIsTyping" class="flex items-start gap-2">
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          [alt]="otherParticipant?.username"
          class="w-8 h-8 rounded-full object-cover"
        />
        <div class="bg-white dark:bg-gray-700 px-4 py-2 rounded-2xl shadow-sm">
          <div class="flex gap-1">
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div
              class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
              style="animation-delay: 0.1s"
            ></div>
            <div
              class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
              style="animation-delay: 0.2s"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Progress bar pour upload -->
  <div
    *ngIf="isUploading"
    class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2"
  >
    <div class="flex items-center gap-3">
      <div class="flex-1">
        <div
          class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1"
        >
          <span>Envoi en cours...</span>
          <span>{{ uploadProgress.toFixed(0) }}%</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            class="bg-green-500 h-2 rounded-full transition-all duration-300"
            [style.width.%]="uploadProgress"
          ></div>
        </div>
      </div>
      <button
        (click)="resetUploadState()"
        class="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500"
        title="Annuler"
      >
        <i class="fas fa-times text-sm"></i>
      </button>
    </div>
  </div>

  <!-- Zone d'input -->
  <footer
    class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4"
  >
    <form
      [formGroup]="messageForm"
      (ngSubmit)="sendMessage()"
      class="flex items-end gap-3"
    >
      <!-- Actions gauche -->
      <div class="flex gap-2">
        <button
          type="button"
          (click)="toggleEmojiPicker()"
          class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
          [class.bg-green-100]="showEmojiPicker"
          [class.text-green-600]="showEmojiPicker"
          title="Émojis"
        >
          <i class="fas fa-smile"></i>
        </button>
        <button
          type="button"
          (click)="toggleAttachmentMenu()"
          class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
          [class.bg-green-100]="showAttachmentMenu"
          [class.text-green-600]="showAttachmentMenu"
          title="Joindre un fichier"
        >
          <i class="fas fa-paperclip"></i>
        </button>
      </div>

      <!-- Champ de saisie -->
      <div class="flex-1">
        <textarea
          formControlName="content"
          #messageTextarea
          placeholder="Tapez votre message..."
          class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          [class.opacity-50]="isInputDisabled()"
          [class.cursor-not-allowed]="isInputDisabled()"
          (input)="onInputChange($event)"
          (keydown)="onInputKeyDown($event)"
          (focus)="onInputFocus()"
          (blur)="onInputBlur()"
          rows="1"
          maxlength="4096"
          autocomplete="off"
          spellcheck="true"
        >
        </textarea>
      </div>

      <!-- Actions droite -->
      <div class="flex gap-2">
        <!-- Enregistrement vocal -->
        <button
          *ngIf="!messageForm.get('content')?.value?.trim()"
          type="button"
          (mousedown)="onRecordStart($event)"
          (mouseup)="onRecordEnd($event)"
          (mouseleave)="onRecordCancel($event)"
          (touchstart)="onRecordStart($event)"
          (touchend)="onRecordEnd($event)"
          (touchcancel)="onRecordCancel($event)"
          [ngClass]="{
            'voice-record-button': true,
            recording: isRecordingVoice,
            processing: voiceRecordingState === 'processing'
          }"
          [disabled]="voiceRecordingState === 'processing'"
          title="Maintenir pour enregistrer un message vocal"
        >
          <i
            class="fas fa-microphone"
            *ngIf="voiceRecordingState !== 'processing'"
          ></i>
          <i
            class="fas fa-spinner"
            *ngIf="voiceRecordingState === 'processing'"
          ></i>
        </button>

        <!-- Bouton d'envoi -->
        <button
          *ngIf="messageForm.get('content')?.value?.trim()"
          type="button"
          (click)="sendMessage()"
          class="p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50"
          [disabled]="isSendingMessage"
          title="Envoyer"
        >
          <i class="fas fa-paper-plane" *ngIf="!isSendingMessage"></i>
          <i class="fas fa-spinner fa-spin" *ngIf="isSendingMessage"></i>
        </button>
      </div>
    </form>
  </footer>

  <!-- Interface d'enregistrement vocal style WhatsApp -->
  <div
    *ngIf="isRecordingVoice"
    class="absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4 z-50"
  >
    <div class="flex items-center gap-4">
      <!-- Bouton annuler -->
      <button
        (click)="cancelVoiceRecording()"
        class="p-2 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
        title="Annuler"
      >
        <i class="fas fa-times"></i>
      </button>

      <!-- Indicateur d'enregistrement -->
      <div class="flex items-center gap-3 flex-1">
        <!-- Icône micro animée -->
        <div class="relative">
          <i class="fas fa-microphone text-red-500 text-xl animate-pulse"></i>
          <div
            class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping"
          ></div>
        </div>

        <!-- Waves d'animation -->
        <div class="flex items-center gap-1">
          <div
            *ngFor="let wave of voiceWaves; let i = index"
            class="bg-green-500 rounded-full transition-all duration-150"
            [style.width.px]="2"
            [style.height.px]="wave"
            [style.animation-delay.ms]="i * 100"
          ></div>
        </div>

        <!-- Durée d'enregistrement -->
        <div class="text-gray-600 dark:text-gray-300 font-mono">
          {{ formatRecordingDuration(voiceRecordingDuration) }}
        </div>
      </div>

      <!-- Bouton envoyer -->
      <button
        (click)="stopVoiceRecording()"
        class="p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors"
        title="Envoyer l'enregistrement"
      >
        <i class="fas fa-paper-plane"></i>
      </button>
    </div>

    <!-- Barre de progression -->
    <div class="mt-3 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
      <div
        class="bg-green-500 h-1 rounded-full transition-all duration-300"
        [style.width.%]="(voiceRecordingDuration / 60) * 100"
      ></div>
    </div>

    <!-- Instructions -->
    <div class="mt-2 text-center text-sm text-gray-500 dark:text-gray-400">
      <div class="flex items-center justify-center gap-2">
        <i class="fas fa-info-circle"></i>
        <span>Relâchez pour envoyer • Glissez vers le haut pour annuler</span>
      </div>
      <div class="mt-1 text-xs">
        Durée max: 60 secondes • Format: {{ getRecordingFormat() }}
      </div>
    </div>
  </div>

  <!-- Sélecteur d'émojis -->
  <div
    *ngIf="showEmojiPicker"
    class="absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50"
  >
    <div class="p-4">
      <div class="flex gap-2 mb-4 overflow-x-auto">
        <button
          *ngFor="let category of emojiCategories"
          (click)="selectEmojiCategory(category)"
          class="px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0"
          [class.bg-green-100]="selectedEmojiCategory === category"
          [class.text-green-600]="selectedEmojiCategory === category"
          [class.hover:bg-gray-100]="selectedEmojiCategory !== category"
          [class.dark:hover:bg-gray-700]="selectedEmojiCategory !== category"
        >
          {{ category.icon }}
        </button>
      </div>
      <div class="grid grid-cols-8 gap-2 max-h-48 overflow-y-auto">
        <button
          *ngFor="let emoji of getEmojisForCategory(selectedEmojiCategory)"
          (click)="insertEmoji(emoji)"
          class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl"
          [title]="emoji.name"
        >
          {{ emoji.emoji }}
        </button>
      </div>
    </div>
  </div>

  <!-- Menu des pièces jointes -->
  <div
    *ngIf="showAttachmentMenu"
    class="absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50"
  >
    <div class="p-4">
      <div class="grid grid-cols-2 gap-3">
        <button
          (click)="triggerFileInput('image')"
          class="flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <div
            class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-image text-blue-600 dark:text-blue-400"></i>
          </div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Photos</span
          >
        </button>
        <button
          (click)="triggerFileInput('video')"
          class="flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <div
            class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-video text-purple-600 dark:text-purple-400"></i>
          </div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Vidéos</span
          >
        </button>
        <button
          (click)="triggerFileInput('document')"
          class="flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <div
            class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-file text-orange-600 dark:text-orange-400"></i>
          </div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Documents</span
          >
        </button>
        <button
          (click)="openCamera()"
          class="flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <div
            class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-camera text-green-600 dark:text-green-400"></i>
          </div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Caméra</span
          >
        </button>
      </div>
    </div>
  </div>

  <!-- Input caché pour fichiers -->
  <input
    #fileInput
    type="file"
    class="hidden"
    (change)="onFileSelected($event)"
    [accept]="getFileAcceptTypes()"
    multiple
  />

  <!-- Sélecteur de réaction rapide -->
  <div
    *ngIf="showReactionPicker"
    class="fixed bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 p-3"
    [style.left.px]="contextMenuPosition.x - 100"
    [style.top.px]="contextMenuPosition.y - 60"
  >
    <div class="flex gap-2">
      <button
        *ngFor="let emoji of ['❤️', '😂', '😮', '😢', '😡', '👍']"
        (click)="quickReact(emoji)"
        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl"
      >
        {{ emoji }}
      </button>
    </div>
  </div>

  <!-- Menu contextuel pour messages -->
  <div
    *ngIf="showMessageContextMenu"
    class="fixed bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-48"
    [style.left.px]="contextMenuPosition.x"
    [style.top.px]="contextMenuPosition.y"
  >
    <div class="p-2">
      <button
        (click)="replyToMessage(selectedMessage)"
        class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left"
      >
        <i class="fas fa-reply text-blue-500"></i>
        <span class="text-gray-700 dark:text-gray-300">Répondre</span>
      </button>
      <button
        (click)="forwardMessage(selectedMessage)"
        class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left"
      >
        <i class="fas fa-share text-green-500"></i>
        <span class="text-gray-700 dark:text-gray-300">Transférer</span>
      </button>
      <button
        (click)="showQuickReactions(selectedMessage, $event)"
        class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left"
      >
        <i class="fas fa-smile text-yellow-500"></i>
        <span class="text-gray-700 dark:text-gray-300">Réagir</span>
      </button>
      <hr class="my-2 border-gray-200 dark:border-gray-600" />
      <button
        (click)="deleteMessage(selectedMessage)"
        class="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left text-red-600"
      >
        <i class="fas fa-trash text-red-500"></i>
        <span>Supprimer</span>
      </button>
    </div>
  </div>

  <!-- Overlay pour fermer les menus -->
  <div
    *ngIf="
      showEmojiPicker ||
      showAttachmentMenu ||
      showMainMenu ||
      showMessageContextMenu ||
      showReactionPicker
    "
    class="fixed inset-0 bg-black bg-opacity-25 z-40"
    (click)="closeAllMenus()"
  ></div>
</div>

<!-- Interface d'appel WebRTC -->
<app-call-interface
  [isVisible]="isInCall"
  [activeCall]="activeCall"
  [callType]="callType"
  [otherParticipant]="otherParticipant"
  (callEnded)="endCall()"
  (callAccepted)="onCallAccepted($event)"
  (callRejected)="onCallRejected()"
></app-call-interface>

<!-- Visionneuse d'images plein écran -->
<div
  *ngIf="showImageViewer"
  class="fixed inset-0 bg-black bg-opacity-95 z-50 flex items-center justify-center"
  (click)="closeImageViewer()"
>
  <div class="relative max-w-full max-h-full p-4">
    <!-- Bouton fermer -->
    <button
      (click)="closeImageViewer()"
      class="absolute top-4 right-4 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all"
      title="Fermer"
    >
      <i class="fas fa-times text-xl"></i>
    </button>

    <!-- Bouton télécharger -->
    <button
      (click)="downloadImage(); $event.stopPropagation()"
      class="absolute top-4 left-4 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all"
      title="Télécharger"
    >
      <i class="fas fa-download text-xl"></i>
    </button>

    <!-- Image -->
    <img
      [src]="selectedImage?.url"
      [alt]="selectedImage?.name || 'Image'"
      class="max-w-full max-h-full object-contain rounded-lg shadow-2xl image-viewer-zoom"
      (click)="$event.stopPropagation()"
      style="max-height: 90vh; max-width: 90vw"
    />

    <!-- Informations de l'image -->
    <div
      class="absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 text-white p-3 rounded-lg"
      (click)="$event.stopPropagation()"
    >
      <div class="flex items-center justify-between">
        <div>
          <p class="font-medium">{{ selectedImage?.name || "Image" }}</p>
          <p class="text-sm opacity-75">
            {{ selectedImage?.size || "Taille inconnue" }}
          </p>
        </div>
        <div class="flex gap-2">
          <!-- Bouton zoom -->
          <button
            (click)="zoomImage(1.2); $event.stopPropagation()"
            class="p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all"
            title="Zoom +"
          >
            <i class="fas fa-search-plus"></i>
          </button>
          <button
            (click)="zoomImage(0.8); $event.stopPropagation()"
            class="p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all"
            title="Zoom -"
          >
            <i class="fas fa-search-minus"></i>
          </button>
          <!-- Bouton reset zoom -->
          <button
            (click)="resetZoom(); $event.stopPropagation()"
            class="p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all"
            title="Taille originale"
          >
            <i class="fas fa-expand-arrows-alt"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
