{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { CallType } from '../../../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../services/message.service\";\nimport * as i4 from \"../../../../services/toast.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../call-interface/call-interface.component\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 57);\n  }\n}\nfunction MessageChatComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"span\");\n    i0.ɵɵtext(2, \"En train d'\\u00E9crire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 59);\n    i0.ɵɵelement(4, \"div\", 60)(5, \"div\", 61)(6, \"div\", 62);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.isOnline) ? \"En ligne\" : ctx_r2.formatLastActive(ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_i_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 63);\n  }\n}\nfunction MessageChatComponent_i_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 64);\n  }\n}\nfunction MessageChatComponent_div_28_div_20_button_2_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 81);\n  }\n}\nfunction MessageChatComponent_div_28_div_20_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_28_div_20_button_2_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const theme_r25 = restoredCtx.$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r27.selectTheme(theme_r25.id));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\", 69);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, MessageChatComponent_div_28_div_20_button_2_i_4_Template, 1, 0, \"i\", 80);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const theme_r25 = ctx.$implicit;\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bg-blue-50\", ctx_r24.currentTheme === theme_r25.id)(\"dark:bg-blue-900\", ctx_r24.currentTheme === theme_r25.id)(\"text-blue-600\", ctx_r24.currentTheme === theme_r25.id)(\"dark:text-blue-400\", ctx_r24.currentTheme === theme_r25.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(theme_r25.icon);\n    i0.ɵɵstyleProp(\"color\", theme_r25.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"text-blue-600\", ctx_r24.currentTheme === theme_r25.id)(\"dark:text-blue-400\", ctx_r24.currentTheme === theme_r25.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(theme_r25.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r24.currentTheme === theme_r25.id);\n  }\n}\nfunction MessageChatComponent_div_28_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 66);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_28_div_20_button_2_Template, 5, 18, \"button\", 79);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r23.themes);\n  }\n}\nfunction MessageChatComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66)(2, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_28_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      ctx_r29.toggleSearch();\n      return i0.ɵɵresetView(ctx_r29.showMainMenu = false);\n    });\n    i0.ɵɵelement(3, \"i\", 68);\n    i0.ɵɵelementStart(4, \"span\", 69);\n    i0.ɵɵtext(5, \"Rechercher\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 70);\n    i0.ɵɵelement(7, \"i\", 71);\n    i0.ɵɵelementStart(8, \"span\", 69);\n    i0.ɵɵtext(9, \"Voir le profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 70);\n    i0.ɵɵelement(11, \"i\", 72);\n    i0.ɵɵelementStart(12, \"span\", 69);\n    i0.ɵɵtext(13, \"Notifications\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 73)(15, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_28_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.toggleThemeSelector());\n    });\n    i0.ɵɵelement(16, \"i\");\n    i0.ɵɵelementStart(17, \"span\", 69);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"i\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, MessageChatComponent_div_28_div_20_Template, 3, 1, \"div\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"hr\", 76);\n    i0.ɵɵelementStart(22, \"button\", 70);\n    i0.ɵɵelement(23, \"i\", 77);\n    i0.ɵɵelementStart(24, \"span\", 69);\n    i0.ɵɵtext(25, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    i0.ɵɵadvance(15);\n    i0.ɵɵclassProp(\"bg-gray-100\", ctx_r5.showThemeSelector)(\"dark:bg-gray-700\", ctx_r5.showThemeSelector);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap((tmp_2_0 = ctx_r5.getCurrentTheme()) == null ? null : tmp_2_0.icon);\n    i0.ɵɵstyleProp(\"color\", (tmp_3_0 = ctx_r5.getCurrentTheme()) == null ? null : tmp_3_0.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Th\\u00E8me: \", (tmp_4_0 = ctx_r5.getCurrentTheme()) == null ? null : tmp_4_0.name, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"rotate-90\", ctx_r5.showThemeSelector);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showThemeSelector);\n  }\n}\nfunction MessageChatComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"div\", 83);\n    i0.ɵɵelement(2, \"i\", 84);\n    i0.ɵɵelementStart(3, \"p\", 85);\n    i0.ɵɵtext(4, \" D\\u00E9posez vos fichiers ici \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 86);\n    i0.ɵɵtext(6, \" Images, vid\\u00E9os, documents... \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 87);\n    i0.ɵɵelement(8, \"span\", 88)(9, \"span\", 89)(10, \"span\", 90);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵelement(1, \"div\", 92);\n    i0.ɵɵelementStart(2, \"span\", 93);\n    i0.ɵɵtext(3, \"Chargement des messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 95);\n    i0.ɵɵelement(2, \"i\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 97);\n    i0.ɵɵtext(4, \" Aucun message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 98);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Commencez votre conversation avec \", ctx_r9.otherParticipant == null ? null : ctx_r9.otherParticipant.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 115)(1, \"div\", 116)(2, \"span\", 117);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r34 = i0.ɵɵnextContext().$implicit;\n    const ctx_r36 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r36.formatDateSeparator(message_r34.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 118)(1, \"img\", 119);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_34_ng_container_1_div_3_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const message_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r46 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r46.openUserProfile(message_r34.sender == null ? null : message_r34.sender.id));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r34 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r34.sender == null ? null : message_r34.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r34.sender == null ? null : message_r34.sender.username);\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 120);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r34 = i0.ɵɵnextContext().$implicit;\n    const ctx_r38 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r38.getUserColor(message_r34.sender == null ? null : message_r34.sender.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r34.sender == null ? null : message_r34.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 121);\n    i0.ɵɵelement(1, \"div\", 122);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r34 = i0.ɵɵnextContext().$implicit;\n    const ctx_r39 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r39.formatMessageContent(message_r34.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 122);\n  }\n  if (rf & 2) {\n    const message_r34 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r52 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", (message_r34.sender == null ? null : message_r34.sender.id) !== ctx_r52.currentUserId && ctx_r52.isDarkMode ? \"#9ca3af\" : \"\");\n    i0.ɵɵclassProp(\"text-white\", (message_r34.sender == null ? null : message_r34.sender.id) === ctx_r52.currentUserId)(\"text-gray-900\", (message_r34.sender == null ? null : message_r34.sender.id) !== ctx_r52.currentUserId);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r52.formatMessageContent(message_r34.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"img\", 123);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_34_ng_container_1_div_7_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const message_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.openImageViewer(message_r34));\n    })(\"load\", function MessageChatComponent_div_34_ng_container_1_div_7_Template_img_load_1_listener($event) {\n      i0.ɵɵrestoreView(_r56);\n      const message_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r57.onImageLoad($event, message_r34));\n    })(\"error\", function MessageChatComponent_div_34_ng_container_1_div_7_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r56);\n      const message_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r59.onImageError($event, message_r34));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, MessageChatComponent_div_34_ng_container_1_div_7_div_2_Template, 1, 7, \"div\", 124);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r34 = i0.ɵɵnextContext().$implicit;\n    const ctx_r40 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r40.getImageUrl(message_r34), i0.ɵɵsanitizeUrl)(\"alt\", message_r34.content || \"Image\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r34.content);\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_8_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 140);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_34_ng_container_1_div_8_div_5_Template_div_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r69);\n      const i_r66 = restoredCtx.index;\n      const message_r34 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r67 = i0.ɵɵnextContext(2);\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r67.seekVoiceMessage(message_r34, i_r66));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const wave_r65 = ctx.$implicit;\n    const i_r66 = ctx.index;\n    const message_r34 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r62 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", wave_r65, \"px\")(\"transform\", \"scaleY(\" + (ctx_r62.isVoicePlaying(message_r34.id) && i_r66 <= ctx_r62.getVoiceProgress(message_r34) ? \"1.3\" : \"1\") + \")\")(\"box-shadow\", ctx_r62.isVoicePlaying(message_r34.id) && i_r66 <= ctx_r62.getVoiceProgress(message_r34) ? \"0 0 8px rgba(59, 130, 246, 0.5)\" : \"none\");\n    i0.ɵɵclassProp(\"bg-gray-400\", !ctx_r62.isVoicePlaying(message_r34.id))(\"bg-blue-500\", ctx_r62.isVoicePlaying(message_r34.id) && i_r66 <= ctx_r62.getVoiceProgress(message_r34))(\"bg-gray-300\", ctx_r62.isVoicePlaying(message_r34.id) && i_r66 > ctx_r62.getVoiceProgress(message_r34.id))(\"dark:bg-gray-500\", ctx_r62.isVoicePlaying(message_r34.id) && i_r66 > ctx_r62.getVoiceProgress(message_r34.id))(\"animate-pulse\", ctx_r62.isVoicePlaying(message_r34.id) && i_r66 <= ctx_r62.getVoiceProgress(message_r34));\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_8_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 141);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r34 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r63 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r63.getVoicePlaybackData(message_r34.id).speed, \"x \");\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_8_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142);\n    i0.ɵɵelement(1, \"div\", 143)(2, \"div\", 144)(3, \"div\", 145);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 125)(1, \"button\", 126);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_34_ng_container_1_div_8_Template_button_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r74);\n      const message_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r72 = i0.ɵɵnextContext(2);\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r72.toggleVoicePlayback(message_r34));\n    });\n    i0.ɵɵelement(2, \"i\", 127);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 8)(4, \"div\", 128);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_34_ng_container_1_div_8_div_5_Template, 1, 16, \"div\", 129);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 130)(7, \"div\", 13)(8, \"span\", 131);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 132);\n    i0.ɵɵtext(11, \"/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 133);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 134);\n    i0.ɵɵtemplate(15, MessageChatComponent_div_34_ng_container_1_div_8_span_15_Template, 2, 1, \"span\", 135);\n    i0.ɵɵtemplate(16, MessageChatComponent_div_34_ng_container_1_div_8_div_16_Template, 4, 0, \"div\", 136);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 137)(18, \"button\", 138);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_34_ng_container_1_div_8_Template_button_click_18_listener($event) {\n      i0.ɵɵrestoreView(_r74);\n      const message_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r75 = i0.ɵɵnextContext(2);\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r75.toggleVoiceSpeed(message_r34));\n    });\n    i0.ɵɵelement(19, \"i\", 139);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r34 = i0.ɵɵnextContext().$implicit;\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"shadow-lg\", ctx_r41.isVoicePlaying(message_r34.id))(\"ring-2\", ctx_r41.isVoicePlaying(message_r34.id))(\"ring-blue-300\", ctx_r41.isVoicePlaying(message_r34.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"transform\", ctx_r41.isVoicePlaying(message_r34.id) ? \"scale(1.05)\" : \"scale(1)\");\n    i0.ɵɵclassProp(\"bg-blue-500\", !ctx_r41.isVoicePlaying(message_r34.id))(\"hover:bg-blue-600\", !ctx_r41.isVoicePlaying(message_r34.id))(\"bg-red-500\", ctx_r41.isVoicePlaying(message_r34.id))(\"hover:bg-red-600\", ctx_r41.isVoicePlaying(message_r34.id))(\"animate-pulse\", ctx_r41.isVoicePlaying(message_r34.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"fa-play\", !ctx_r41.isVoicePlaying(message_r34.id))(\"fa-pause\", ctx_r41.isVoicePlaying(message_r34.id))(\"scale-110\", ctx_r41.isVoicePlaying(message_r34.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r41.getVoiceWaves(message_r34));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r41.getVoiceCurrentTime(message_r34), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r41.getVoiceDuration(message_r34), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.getVoicePlaybackData(message_r34.id).speed !== 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.isVoicePlaying(message_r34.id));\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r80 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 146);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_34_ng_container_1_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r80);\n      const message_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r78 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r78.downloadFile(message_r34));\n    });\n    i0.ɵɵelementStart(1, \"div\", 147);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 8)(4, \"div\", 148);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 117);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 149);\n    i0.ɵɵelement(9, \"i\", 150);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r34 = i0.ɵɵnextContext().$implicit;\n    const ctx_r42 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r42.getFileIcon(message_r34));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r42.getFileName(message_r34), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r42.getFileSize(message_r34), \" \");\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_13_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 156);\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_13_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 157);\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_13_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 158);\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_13_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 159);\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 151);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_34_ng_container_1_div_13_i_1_Template, 1, 0, \"i\", 152);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_34_ng_container_1_div_13_i_2_Template, 1, 0, \"i\", 153);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_34_ng_container_1_div_13_i_3_Template, 1, 0, \"i\", 154);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_34_ng_container_1_div_13_i_4_Template, 1, 0, \"i\", 155);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r34 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r34.status === \"SENDING\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r34.status === \"SENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r34.status === \"DELIVERED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r34.status === \"READ\");\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_14_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r91 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_34_ng_container_1_div_14_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r91);\n      const reaction_r88 = restoredCtx.$implicit;\n      const message_r34 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r89 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r89.toggleReaction(message_r34.id, reaction_r88.emoji));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reaction_r88 = ctx.$implicit;\n    const ctx_r87 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"bg-green-100\", ctx_r87.hasUserReacted(reaction_r88, ctx_r87.currentUserId || \"\"))(\"text-green-600\", ctx_r87.hasUserReacted(reaction_r88, ctx_r87.currentUserId || \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r88.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r88.count || 1);\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 160);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_34_ng_container_1_div_14_button_1_Template, 5, 6, \"button\", 161);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r34 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", message_r34.reactions);\n  }\n}\nfunction MessageChatComponent_div_34_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r94 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_34_ng_container_1_div_1_Template, 4, 1, \"div\", 102);\n    i0.ɵɵelementStart(2, \"div\", 103);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_34_ng_container_1_Template_div_click_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r94);\n      const message_r34 = restoredCtx.$implicit;\n      const ctx_r93 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r93.onMessageClick(message_r34, $event));\n    })(\"contextmenu\", function MessageChatComponent_div_34_ng_container_1_Template_div_contextmenu_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r94);\n      const message_r34 = restoredCtx.$implicit;\n      const ctx_r95 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r95.onMessageContextMenu(message_r34, $event));\n    });\n    i0.ɵɵtemplate(3, MessageChatComponent_div_34_ng_container_1_div_3_Template, 2, 2, \"div\", 104);\n    i0.ɵɵelementStart(4, \"div\", 105);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_34_ng_container_1_div_5_Template, 2, 3, \"div\", 106);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_34_ng_container_1_div_6_Template, 2, 1, \"div\", 107);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_34_ng_container_1_div_7_Template, 3, 3, \"div\", 12);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_34_ng_container_1_div_8_Template, 20, 29, \"div\", 108);\n    i0.ɵɵtemplate(9, MessageChatComponent_div_34_ng_container_1_div_9_Template, 10, 4, \"div\", 109);\n    i0.ɵɵelementStart(10, \"div\", 110)(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, MessageChatComponent_div_34_ng_container_1_div_13_Template, 5, 4, \"div\", 111);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, MessageChatComponent_div_34_ng_container_1_div_14_Template, 2, 1, \"div\", 112);\n    i0.ɵɵelementStart(15, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_34_ng_container_1_Template_button_click_15_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r94);\n      const message_r34 = restoredCtx.$implicit;\n      const ctx_r96 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r96.showQuickReactions(message_r34, $event));\n    });\n    i0.ɵɵelement(16, \"i\", 114);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r34 = ctx.$implicit;\n    const i_r35 = ctx.index;\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.shouldShowDateSeparator(i_r35));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"justify-end\", (message_r34.sender == null ? null : message_r34.sender.id) === ctx_r32.currentUserId)(\"justify-start\", (message_r34.sender == null ? null : message_r34.sender.id) !== ctx_r32.currentUserId);\n    i0.ɵɵproperty(\"id\", \"message-\" + message_r34.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r34.sender == null ? null : message_r34.sender.id) !== ctx_r32.currentUserId && ctx_r32.shouldShowAvatar(i_r35));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", (message_r34.sender == null ? null : message_r34.sender.id) === ctx_r32.currentUserId && ctx_r32.isDarkMode ? \"#9ca3af\" : \"\")(\"color\", (message_r34.sender == null ? null : message_r34.sender.id) !== ctx_r32.currentUserId && ctx_r32.isDarkMode ? \"#9ca3af\" : \"\");\n    i0.ɵɵclassProp(\"bg-blue-500\", (message_r34.sender == null ? null : message_r34.sender.id) === ctx_r32.currentUserId)(\"text-white\", (message_r34.sender == null ? null : message_r34.sender.id) === ctx_r32.currentUserId)(\"bg-white\", (message_r34.sender == null ? null : message_r34.sender.id) !== ctx_r32.currentUserId)(\"text-gray-900\", (message_r34.sender == null ? null : message_r34.sender.id) !== ctx_r32.currentUserId)(\"dark:bg-gray-700\", (message_r34.sender == null ? null : message_r34.sender.id) !== ctx_r32.currentUserId)(\"dark:text-blue-400\", (message_r34.sender == null ? null : message_r34.sender.id) !== ctx_r32.currentUserId)(\"rounded-br-sm\", (message_r34.sender == null ? null : message_r34.sender.id) === ctx_r32.currentUserId)(\"rounded-bl-sm\", (message_r34.sender == null ? null : message_r34.sender.id) !== ctx_r32.currentUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.isGroupConversation() && (message_r34.sender == null ? null : message_r34.sender.id) !== ctx_r32.currentUserId && ctx_r32.shouldShowSenderName(i_r35));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.getMessageType(message_r34) === \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.hasImage(message_r34));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.getMessageType(message_r34) === \"audio\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.hasFile(message_r34) && ctx_r32.getMessageType(message_r34) !== \"audio\" && !ctx_r32.hasImage(message_r34));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r32.formatMessageTime(message_r34.timestamp));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r34.sender == null ? null : message_r34.sender.id) === ctx_r32.currentUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r34.reactions && message_r34.reactions.length > 0);\n  }\n}\nfunction MessageChatComponent_div_34_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 163);\n    i0.ɵɵelement(1, \"img\", 164);\n    i0.ɵɵelementStart(2, \"div\", 165)(3, \"div\", 59);\n    i0.ɵɵelement(4, \"div\", 166)(5, \"div\", 167)(6, \"div\", 168);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (ctx_r33.otherParticipant == null ? null : ctx_r33.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r33.otherParticipant == null ? null : ctx_r33.otherParticipant.username);\n  }\n}\nfunction MessageChatComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_34_ng_container_1_Template, 17, 35, \"ng-container\", 100);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_34_div_2_Template, 7, 2, \"div\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.messages)(\"ngForTrackBy\", ctx_r10.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.otherUserIsTyping);\n  }\n}\nfunction MessageChatComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r98 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 169)(1, \"div\", 170)(2, \"div\", 42)(3, \"div\", 171)(4, \"span\");\n    i0.ɵɵtext(5, \"Envoi en cours...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 172);\n    i0.ɵɵelement(9, \"div\", 173);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 174);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_35_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r98);\n      const ctx_r97 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r97.resetUploadState());\n    });\n    i0.ɵɵelement(11, \"i\", 175);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r11.uploadProgress.toFixed(0), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r11.uploadProgress, \"%\");\n  }\n}\nfunction MessageChatComponent_button_48_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 179);\n  }\n}\nfunction MessageChatComponent_button_48_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 180);\n  }\n}\nconst _c2 = function (a1, a2) {\n  return {\n    \"voice-record-button\": true,\n    recording: a1,\n    processing: a2\n  };\n};\nfunction MessageChatComponent_button_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r102 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 176);\n    i0.ɵɵlistener(\"mousedown\", function MessageChatComponent_button_48_Template_button_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r101 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r101.onRecordStart($event));\n    })(\"mouseup\", function MessageChatComponent_button_48_Template_button_mouseup_0_listener($event) {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r103 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r103.onRecordEnd($event));\n    })(\"mouseleave\", function MessageChatComponent_button_48_Template_button_mouseleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r104 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r104.onRecordCancel($event));\n    })(\"touchstart\", function MessageChatComponent_button_48_Template_button_touchstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r105 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r105.onRecordStart($event));\n    })(\"touchend\", function MessageChatComponent_button_48_Template_button_touchend_0_listener($event) {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r106 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r106.onRecordEnd($event));\n    })(\"touchcancel\", function MessageChatComponent_button_48_Template_button_touchcancel_0_listener($event) {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r107 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r107.onRecordCancel($event));\n    });\n    i0.ɵɵtemplate(1, MessageChatComponent_button_48_i_1_Template, 1, 0, \"i\", 177);\n    i0.ɵɵtemplate(2, MessageChatComponent_button_48_i_2_Template, 1, 0, \"i\", 178);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c2, ctx_r13.isRecordingVoice, ctx_r13.voiceRecordingState === \"processing\"))(\"disabled\", ctx_r13.voiceRecordingState === \"processing\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.voiceRecordingState !== \"processing\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.voiceRecordingState === \"processing\");\n  }\n}\nfunction MessageChatComponent_button_49_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 184);\n  }\n}\nfunction MessageChatComponent_button_49_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 185);\n  }\n}\nfunction MessageChatComponent_button_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r111 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 181);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_button_49_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r111);\n      const ctx_r110 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r110.sendMessage());\n    });\n    i0.ɵɵtemplate(1, MessageChatComponent_button_49_i_1_Template, 1, 0, \"i\", 182);\n    i0.ɵɵtemplate(2, MessageChatComponent_button_49_i_2_Template, 1, 0, \"i\", 183);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r14.isSendingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r14.isSendingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.isSendingMessage);\n  }\n}\nfunction MessageChatComponent_div_50_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 202);\n  }\n  if (rf & 2) {\n    const wave_r113 = ctx.$implicit;\n    const i_r114 = ctx.index;\n    i0.ɵɵstyleProp(\"width\", 2, \"px\")(\"height\", wave_r113, \"px\")(\"animation-delay\", i_r114 * 100, \"ms\");\n  }\n}\nfunction MessageChatComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r116 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 186)(1, \"div\", 187)(2, \"button\", 188);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_50_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r116);\n      const ctx_r115 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r115.cancelVoiceRecording());\n    });\n    i0.ɵɵelement(3, \"i\", 189);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 190)(5, \"div\", 73);\n    i0.ɵɵelement(6, \"i\", 191)(7, \"div\", 192);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 134);\n    i0.ɵɵtemplate(9, MessageChatComponent_div_50_div_9_Template, 1, 6, \"div\", 193);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 194);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 195);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_50_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r116);\n      const ctx_r117 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r117.stopVoiceRecording());\n    });\n    i0.ɵɵelement(13, \"i\", 184);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 196);\n    i0.ɵɵelement(15, \"div\", 197);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 198)(17, \"div\", 199);\n    i0.ɵɵelement(18, \"i\", 200);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"Rel\\u00E2chez pour envoyer \\u2022 Glissez vers le haut pour annuler\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 201);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r15.voiceWaves);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.formatRecordingDuration(ctx_r15.voiceRecordingDuration), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"width\", ctx_r15.voiceRecordingDuration / 60 * 100, \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" Dur\\u00E9e max: 60 secondes \\u2022 Format: \", ctx_r15.getRecordingFormat(), \" \");\n  }\n}\nfunction MessageChatComponent_div_51_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r122 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 209);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_51_button_3_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r122);\n      const category_r120 = restoredCtx.$implicit;\n      const ctx_r121 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r121.selectEmojiCategory(category_r120));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r120 = ctx.$implicit;\n    const ctx_r118 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-green-100\", ctx_r118.selectedEmojiCategory === category_r120)(\"text-green-600\", ctx_r118.selectedEmojiCategory === category_r120)(\"hover:bg-gray-100\", ctx_r118.selectedEmojiCategory !== category_r120)(\"dark:hover:bg-gray-700\", ctx_r118.selectedEmojiCategory !== category_r120);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r120.icon, \" \");\n  }\n}\nfunction MessageChatComponent_div_51_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r125 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 210);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_51_button_5_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r125);\n      const emoji_r123 = restoredCtx.$implicit;\n      const ctx_r124 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r124.insertEmoji(emoji_r123));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r123 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r123.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r123.emoji, \" \");\n  }\n}\nfunction MessageChatComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 203)(1, \"div\", 204)(2, \"div\", 205);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_51_button_3_Template, 2, 9, \"button\", 206);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 207);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_51_button_5_Template, 2, 2, \"button\", 208);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.emojiCategories);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.getEmojisForCategory(ctx_r16.selectedEmojiCategory));\n  }\n}\nfunction MessageChatComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r127 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 211)(1, \"div\", 204)(2, \"div\", 212)(3, \"button\", 213);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_52_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r127);\n      const ctx_r126 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r126.triggerFileInput(\"image\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 214);\n    i0.ɵɵelement(5, \"i\", 215);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 216);\n    i0.ɵɵtext(7, \"Photos\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 213);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_52_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r127);\n      const ctx_r128 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r128.triggerFileInput(\"video\"));\n    });\n    i0.ɵɵelementStart(9, \"div\", 217);\n    i0.ɵɵelement(10, \"i\", 218);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 216);\n    i0.ɵɵtext(12, \"Vid\\u00E9os\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 213);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_52_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r127);\n      const ctx_r129 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r129.triggerFileInput(\"document\"));\n    });\n    i0.ɵɵelementStart(14, \"div\", 219);\n    i0.ɵɵelement(15, \"i\", 220);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 216);\n    i0.ɵɵtext(17, \"Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 213);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_52_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r127);\n      const ctx_r130 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r130.openCamera());\n    });\n    i0.ɵɵelementStart(19, \"div\", 221);\n    i0.ɵɵelement(20, \"i\", 222);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 216);\n    i0.ɵɵtext(22, \"Cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_55_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r134 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 225);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_55_button_2_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r134);\n      const emoji_r132 = restoredCtx.$implicit;\n      const ctx_r133 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r133.quickReact(emoji_r132));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r132 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r132, \" \");\n  }\n}\nconst _c3 = function () {\n  return [\"\\u2764\\uFE0F\", \"\\uD83D\\uDE02\", \"\\uD83D\\uDE2E\", \"\\uD83D\\uDE22\", \"\\uD83D\\uDE21\", \"\\uD83D\\uDC4D\"];\n};\nfunction MessageChatComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 223)(1, \"div\", 37);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_55_button_2_Template, 2, 1, \"button\", 224);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"left\", ctx_r19.contextMenuPosition.x - 100, \"px\")(\"top\", ctx_r19.contextMenuPosition.y - 60, \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(5, _c3));\n  }\n}\nfunction MessageChatComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r136 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 226)(1, \"div\", 66)(2, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_56_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r136);\n      const ctx_r135 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r135.replyToMessage(ctx_r135.selectedMessage));\n    });\n    i0.ɵɵelement(3, \"i\", 227);\n    i0.ɵɵelementStart(4, \"span\", 69);\n    i0.ɵɵtext(5, \"R\\u00E9pondre\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_56_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r136);\n      const ctx_r137 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r137.forwardMessage(ctx_r137.selectedMessage));\n    });\n    i0.ɵɵelement(7, \"i\", 228);\n    i0.ɵɵelementStart(8, \"span\", 69);\n    i0.ɵɵtext(9, \"Transf\\u00E9rer\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_56_Template_button_click_10_listener($event) {\n      i0.ɵɵrestoreView(_r136);\n      const ctx_r138 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r138.showQuickReactions(ctx_r138.selectedMessage, $event));\n    });\n    i0.ɵɵelement(11, \"i\", 229);\n    i0.ɵɵelementStart(12, \"span\", 69);\n    i0.ɵɵtext(13, \"R\\u00E9agir\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(14, \"hr\", 76);\n    i0.ɵɵelementStart(15, \"button\", 230);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_56_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r136);\n      const ctx_r139 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r139.deleteMessage(ctx_r139.selectedMessage));\n    });\n    i0.ɵɵelement(16, \"i\", 231);\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18, \"Supprimer\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"left\", ctx_r20.contextMenuPosition.x, \"px\")(\"top\", ctx_r20.contextMenuPosition.y, \"px\");\n  }\n}\nfunction MessageChatComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r141 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 232);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_57_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r141);\n      const ctx_r140 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r140.closeAllMenus());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r143 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 233);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r143);\n      const ctx_r142 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r142.closeImageViewer());\n    });\n    i0.ɵɵelementStart(1, \"div\", 234)(2, \"button\", 235);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r143);\n      const ctx_r144 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r144.closeImageViewer());\n    });\n    i0.ɵɵelement(3, \"i\", 236);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 237);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_button_click_4_listener($event) {\n      i0.ɵɵrestoreView(_r143);\n      const ctx_r145 = i0.ɵɵnextContext();\n      ctx_r145.downloadImage();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(5, \"i\", 238);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"img\", 239);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_img_click_6_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 240);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_div_click_7_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(8, \"div\", 241)(9, \"div\")(10, \"p\", 242);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 243);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 37)(15, \"button\", 244);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_button_click_15_listener($event) {\n      i0.ɵɵrestoreView(_r143);\n      const ctx_r148 = i0.ɵɵnextContext();\n      ctx_r148.zoomImage(1.2);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(16, \"i\", 245);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 246);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_button_click_17_listener($event) {\n      i0.ɵɵrestoreView(_r143);\n      const ctx_r149 = i0.ɵɵnextContext();\n      ctx_r149.zoomImage(0.8);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(18, \"i\", 247);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 248);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_button_click_19_listener($event) {\n      i0.ɵɵrestoreView(_r143);\n      const ctx_r150 = i0.ɵɵnextContext();\n      ctx_r150.resetZoom();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(20, \"i\", 249);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r22.selectedImage == null ? null : ctx_r22.selectedImage.url, i0.ɵɵsanitizeUrl)(\"alt\", (ctx_r22.selectedImage == null ? null : ctx_r22.selectedImage.name) || \"Image\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r22.selectedImage == null ? null : ctx_r22.selectedImage.name) || \"Image\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r22.selectedImage == null ? null : ctx_r22.selectedImage.size) || \"Taille inconnue\", \" \");\n  }\n}\nexport class MessageChatComponent {\n  constructor(fb, route, MessageService, toastService, cdr) {\n    this.fb = fb;\n    this.route = route;\n    this.MessageService = MessageService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    // === DONNÉES PRINCIPALES ===\n    this.conversation = null;\n    this.messages = [];\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    // === ÉTATS DE L'INTERFACE ===\n    this.isLoading = false;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.searchMode = false;\n    this.isSendingMessage = false;\n    this.otherUserIsTyping = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n    this.contextMenuPosition = {\n      x: 0,\n      y: 0\n    };\n    this.showReactionPicker = false;\n    this.reactionPickerMessage = null;\n    this.isDarkMode = false;\n    this.currentTheme = 'light'; // 'light', 'dark', 'blue', 'pink'\n    this.themes = [{\n      id: 'light',\n      name: 'Clair',\n      icon: 'fas fa-sun',\n      color: '#f59e0b'\n    }, {\n      id: 'dark',\n      name: 'Sombre',\n      icon: 'fas fa-moon',\n      color: '#6b7280'\n    }, {\n      id: 'blue',\n      name: 'Bleu',\n      icon: 'fas fa-water',\n      color: '#3b82f6'\n    }, {\n      id: 'pink',\n      name: 'Rose',\n      icon: 'fas fa-heart',\n      color: '#ec4899'\n    }];\n    this.showThemeSelector = false;\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    this.uploadProgress = 0;\n    this.isUploading = false;\n    this.isDragOver = false;\n    // === GESTION VOCALE OPTIMISÉE ===\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.recordingTimer = null;\n    this.voiceWaves = [4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8];\n    // Lecture des messages vocaux\n    this.currentAudio = null;\n    this.playingMessageId = null;\n    this.voicePlayback = {};\n    // === APPELS WEBRTC ===\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // État de l'appel WebRTC\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    // === ÉMOJIS ===\n    this.emojiCategories = [{\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [{\n        emoji: '😀',\n        name: 'grinning face'\n      }, {\n        emoji: '😃',\n        name: 'grinning face with big eyes'\n      }, {\n        emoji: '😄',\n        name: 'grinning face with smiling eyes'\n      }, {\n        emoji: '😁',\n        name: 'beaming face with smiling eyes'\n      }, {\n        emoji: '😆',\n        name: 'grinning squinting face'\n      }, {\n        emoji: '😅',\n        name: 'grinning face with sweat'\n      }, {\n        emoji: '😂',\n        name: 'face with tears of joy'\n      }, {\n        emoji: '🤣',\n        name: 'rolling on the floor laughing'\n      }, {\n        emoji: '😊',\n        name: 'smiling face with smiling eyes'\n      }, {\n        emoji: '😇',\n        name: 'smiling face with halo'\n      }]\n    }, {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [{\n        emoji: '👶',\n        name: 'baby'\n      }, {\n        emoji: '🧒',\n        name: 'child'\n      }, {\n        emoji: '👦',\n        name: 'boy'\n      }, {\n        emoji: '👧',\n        name: 'girl'\n      }, {\n        emoji: '🧑',\n        name: 'person'\n      }, {\n        emoji: '👨',\n        name: 'man'\n      }, {\n        emoji: '👩',\n        name: 'woman'\n      }, {\n        emoji: '👴',\n        name: 'old man'\n      }, {\n        emoji: '👵',\n        name: 'old woman'\n      }]\n    }, {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [{\n        emoji: '🐶',\n        name: 'dog face'\n      }, {\n        emoji: '🐱',\n        name: 'cat face'\n      }, {\n        emoji: '🐭',\n        name: 'mouse face'\n      }, {\n        emoji: '🐹',\n        name: 'hamster'\n      }, {\n        emoji: '🐰',\n        name: 'rabbit face'\n      }, {\n        emoji: '🦊',\n        name: 'fox'\n      }, {\n        emoji: '🐻',\n        name: 'bear'\n      }, {\n        emoji: '🐼',\n        name: 'panda'\n      }]\n    }];\n    this.selectedEmojiCategory = this.emojiCategories[0];\n    // === PAGINATION ===\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.currentPage = 1;\n    // === AUTRES ÉTATS ===\n    this.isTyping = false;\n    this.isUserTyping = false;\n    this.typingTimeout = null;\n    this.subscriptions = new Subscription();\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\n  isInputDisabled() {\n    return !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage;\n  }\n  // Méthode pour gérer l'état du contrôle de saisie\n  updateInputState() {\n    const contentControl = this.messageForm.get('content');\n    if (this.isInputDisabled()) {\n      contentControl?.disable();\n    } else {\n      contentControl?.enable();\n    }\n  }\n  ngOnInit() {\n    console.log('🚀 MessageChatComponent initialized');\n    this.initializeTheme();\n    this.initializeComponent();\n  }\n  initializeTheme() {\n    // Charger le thème sauvegardé ou utiliser 'light' par défaut\n    const savedTheme = localStorage.getItem('currentTheme') || 'light';\n    this.currentTheme = savedTheme;\n    this.isDarkMode = savedTheme === 'dark';\n    this.applyTheme(savedTheme);\n  }\n  // === GESTION DES THÈMES OPTIMISÉE ===\n  applyTheme(themeId) {\n    const html = document.documentElement;\n    // Supprimer toutes les classes de thème existantes\n    html.classList.remove('dark', 'theme-light', 'theme-dark', 'theme-blue', 'theme-pink');\n    // Appliquer le nouveau thème\n    html.classList.add(`theme-${themeId}`);\n    if (themeId === 'dark') {\n      html.classList.add('dark');\n    }\n  }\n  getCurrentTheme() {\n    return this.themes.find(t => t.id === this.currentTheme);\n  }\n  toggleThemeSelector() {\n    this.showThemeSelector = !this.showThemeSelector;\n  }\n  selectTheme(themeId) {\n    this.currentTheme = themeId;\n    this.isDarkMode = themeId === 'dark';\n    this.applyTheme(themeId);\n    localStorage.setItem('currentTheme', themeId);\n    this.showThemeSelector = false;\n    this.showMainMenu = false;\n    const themeName = this.themes.find(t => t.id === themeId)?.name || themeId;\n    this.toastService.showSuccess(`Thème ${themeName} appliqué`);\n  }\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupCallSubscriptions();\n  }\n  setupCallSubscriptions() {\n    // S'abonner aux appels entrants\n    this.subscriptions.add(this.MessageService.incomingCall$.subscribe({\n      next: incomingCall => {\n        if (incomingCall) {\n          console.log('📞 Incoming call received:', incomingCall);\n          this.handleIncomingCall(incomingCall);\n        }\n      },\n      error: error => {\n        console.error('❌ Error in incoming call subscription:', error);\n      }\n    }));\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(this.MessageService.activeCall$.subscribe({\n      next: call => {\n        if (call) {\n          console.log('📞 Active call updated:', call);\n          this.activeCall = call;\n        }\n      },\n      error: error => {\n        console.error('❌ Error in active call subscription:', error);\n      }\n    }));\n  }\n  handleIncomingCall(incomingCall) {\n    // Afficher une notification ou modal d'appel entrant\n    // Pour l'instant, on log juste\n    console.log('🔔 Handling incoming call from:', incomingCall.caller.username);\n    // Jouer la sonnerie\n    this.MessageService.play('ringtone');\n    // Ici on pourrait afficher une modal ou notification\n    // Pour l'instant, on accepte automatiquement pour tester\n    // this.acceptCall(incomingCall);\n  }\n\n  loadCurrentUser() {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('🔍 Raw user from localStorage:', userString);\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n      const user = JSON.parse(userString);\n      console.log('🔍 Parsed user object:', user);\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      console.log('🔍 Trying to extract user ID:', {\n        _id: user._id,\n        id: user.id,\n        userId: user.userId,\n        extracted: userId\n      });\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n        console.log('✅ Current user loaded successfully:', {\n          id: this.currentUserId,\n          username: this.currentUsername\n        });\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n  loadConversation() {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: conversation => {\n        console.log('🔍 Conversation loaded successfully:', conversation);\n        console.log('🔍 Conversation structure:', {\n          id: conversation?.id,\n          participants: conversation?.participants,\n          participantsCount: conversation?.participants?.length,\n          isGroup: conversation?.isGroup,\n          messages: conversation?.messages,\n          messagesCount: conversation?.messages?.length\n        });\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n        // Configurer les subscriptions temps réel après le chargement de la conversation\n        this.setupSubscriptions();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError('Erreur lors du chargement de la conversation');\n        this.isLoading = false;\n      }\n    });\n  }\n  setOtherParticipant() {\n    if (!this.conversation?.participants || this.conversation.participants.length === 0) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n    console.log('Setting other participant...');\n    console.log('Current user ID:', this.currentUserId);\n    console.log('All participants:', this.conversation.participants);\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        console.log('Comparing participant ID:', participantId, 'with current user ID:', this.currentUserId);\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      console.log('Fallback: using first participant');\n      this.otherParticipant = this.conversation.participants[0];\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId = this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log('First participant is current user, using second participant');\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      console.log('✅ Other participant set successfully:', {\n        id: this.otherParticipant.id || this.otherParticipant._id,\n        username: this.otherParticipant.username,\n        image: this.otherParticipant.image,\n        isOnline: this.otherParticipant.isOnline\n      });\n      // Log très visible pour debug\n      console.log('🎯 FINAL RESULT: otherParticipant =', this.otherParticipant.username);\n      console.log('🎯 Should display in sidebar:', this.otherParticipant.username);\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      console.log('Conversation participants:', this.conversation.participants);\n      console.log('Current user ID:', this.currentUserId);\n      // Log très visible pour debug\n      console.log('🚨 ERROR: No otherParticipant found!');\n    }\n    // Mettre à jour l'état du champ de saisie\n    this.updateInputState();\n  }\n  loadMessages() {\n    if (!this.conversation?.id) return;\n    // Les messages sont déjà chargés avec la conversation\n    let messages = this.conversation.messages || [];\n    // Trier les messages par timestamp (plus anciens en premier)\n    this.messages = messages.sort((a, b) => {\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\n    });\n\n    console.log('📋 Messages loaded and sorted:', {\n      total: this.messages.length,\n      first: this.messages[0]?.content,\n      last: this.messages[this.messages.length - 1]?.content\n    });\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id) return;\n    this.isLoadingMore = true;\n    this.currentPage++;\n    // Calculer l'offset basé sur les messages déjà chargés\n    const offset = this.messages.length;\n    this.MessageService.getMessages(this.currentUserId,\n    // senderId\n    this.otherParticipant?.id || this.otherParticipant?._id,\n    // receiverId\n    this.conversation.id, this.currentPage, this.MAX_MESSAGES_TO_LOAD).subscribe({\n      next: newMessages => {\n        if (newMessages && newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste\n          this.messages = [...newMessages.reverse(), ...this.messages];\n          this.hasMoreMessages = newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      }\n    });\n  }\n\n  setupSubscriptions() {\n    if (!this.conversation?.id) {\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\n      return;\n    }\n    console.log('🔄 Setting up real-time subscriptions for conversation:', this.conversation.id);\n    // Subscription pour les nouveaux messages\n    console.log('📨 Setting up message subscription...');\n    this.subscriptions.add(this.MessageService.subscribeToNewMessages(this.conversation.id).subscribe({\n      next: newMessage => {\n        console.log('📨 New message received via subscription:', newMessage);\n        console.log('📨 Message structure:', {\n          id: newMessage.id,\n          type: newMessage.type,\n          content: newMessage.content,\n          sender: newMessage.sender,\n          senderId: newMessage.senderId,\n          receiverId: newMessage.receiverId,\n          attachments: newMessage.attachments\n        });\n        // Debug des attachments\n        console.log('📨 [Debug] Message type detected:', this.getMessageType(newMessage));\n        console.log('📨 [Debug] Has image:', this.hasImage(newMessage));\n        console.log('📨 [Debug] Has file:', this.hasFile(newMessage));\n        console.log('📨 [Debug] Image URL:', this.getImageUrl(newMessage));\n        if (newMessage.attachments) {\n          newMessage.attachments.forEach((att, index) => {\n            console.log(`📨 [Debug] Attachment ${index}:`, {\n              type: att.type,\n              url: att.url,\n              path: att.path,\n              name: att.name,\n              size: att.size\n            });\n          });\n        }\n        // Ajouter le message à la liste s'il n'existe pas déjà\n        const messageExists = this.messages.some(msg => msg.id === newMessage.id);\n        if (!messageExists) {\n          // Ajouter le nouveau message à la fin (en bas)\n          this.messages.push(newMessage);\n          console.log('✅ Message added to list, total messages:', this.messages.length);\n          // Forcer la détection de changements\n          this.cdr.detectChanges();\n          // Scroll vers le bas après un court délai\n          setTimeout(() => {\n            this.scrollToBottom();\n          }, 50);\n          // Marquer comme lu si ce n'est pas notre message\n          const senderId = newMessage.sender?.id || newMessage.senderId;\n          console.log('📨 Checking if message should be marked as read:', {\n            senderId,\n            currentUserId: this.currentUserId,\n            shouldMarkAsRead: senderId !== this.currentUserId\n          });\n          if (senderId && senderId !== this.currentUserId) {\n            this.markMessageAsRead(newMessage.id);\n          }\n        }\n      },\n      error: error => {\n        console.error('❌ Error in message subscription:', error);\n      }\n    }));\n    // Subscription pour les indicateurs de frappe\n    console.log('📝 Setting up typing indicator subscription...');\n    this.subscriptions.add(this.MessageService.subscribeToTypingIndicator(this.conversation.id).subscribe({\n      next: typingData => {\n        console.log('📝 Typing indicator received:', typingData);\n        // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n        if (typingData.userId !== this.currentUserId) {\n          this.otherUserIsTyping = typingData.isTyping;\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in typing subscription:', error);\n      }\n    }));\n    // Subscription pour les mises à jour de conversation\n    this.subscriptions.add(this.MessageService.subscribeToConversationUpdates(this.conversation.id).subscribe({\n      next: conversationUpdate => {\n        console.log('📋 Conversation update:', conversationUpdate);\n        // Mettre à jour la conversation si nécessaire\n        if (conversationUpdate.id === this.conversation.id) {\n          this.conversation = {\n            ...this.conversation,\n            ...conversationUpdate\n          };\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in conversation subscription:', error);\n      }\n    }));\n  }\n  markMessageAsRead(messageId) {\n    this.MessageService.markMessageAsRead(messageId).subscribe({\n      next: () => {\n        console.log('✅ Message marked as read:', messageId);\n      },\n      error: error => {\n        console.error('❌ Error marking message as read:', error);\n      }\n    });\n  }\n  // === ENVOI DE MESSAGES ===\n  sendMessage() {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    // Désactiver le bouton d'envoi\n    this.isSendingMessage = true;\n    this.updateInputState();\n    console.log('📤 Sending message:', {\n      content,\n      receiverId,\n      conversationId: this.conversation.id\n    });\n    this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({\n      next: message => {\n        console.log('✅ Message sent successfully:', message);\n        // Ajouter le message à la liste s'il n'y est pas déjà\n        const messageExists = this.messages.some(msg => msg.id === message.id);\n        if (!messageExists) {\n          this.messages.push(message);\n          console.log('📋 Message added to local list, total:', this.messages.length);\n        }\n        // Réinitialiser le formulaire\n        this.messageForm.reset();\n        this.isSendingMessage = false;\n        this.updateInputState();\n        // Forcer la détection de changements et scroll\n        this.cdr.detectChanges();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 50);\n      },\n      error: error => {\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        this.isSendingMessage = false;\n        this.updateInputState();\n      }\n    });\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const diffMins = Math.floor((Date.now() - new Date(lastActive).getTime()) / 60000);\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\n  }\n  // Méthodes utilitaires pour les messages vocaux\n  getVoicePlaybackData(messageId) {\n    return this.voicePlayback[messageId] || {\n      progress: 0,\n      duration: 0,\n      currentTime: 0,\n      speed: 1\n    };\n  }\n  setVoicePlaybackData(messageId, data) {\n    this.voicePlayback[messageId] = {\n      ...this.getVoicePlaybackData(messageId),\n      ...data\n    };\n  }\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // isVoicePlaying, playVoiceMessage, toggleVoicePlayback - définies plus loin\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n  startVideoCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    this.callType = 'VIDEO';\n    this.isInCall = true;\n    console.log('📹 Starting video call with:', this.otherParticipant.username);\n  }\n  startVoiceCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    this.callType = 'AUDIO';\n    this.isInCall = true;\n    console.log('📞 Starting voice call with:', this.otherParticipant.username);\n  }\n  endCall() {\n    this.isInCall = false;\n    this.callType = null;\n    this.activeCall = null;\n    console.log('📞 Call ended');\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // onCallAccepted, onCallRejected - définies plus loin\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n  formatFileSize(bytes) {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  downloadFile(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (fileAttachment?.url) {\n      const link = document.createElement('a');\n      link.href = fileAttachment.url;\n      link.download = fileAttachment.name || 'file';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n  toggleTheme() {\n    const newTheme = this.isDarkMode ? 'light' : 'dark';\n    this.selectTheme(newTheme);\n  }\n  toggleSearch() {\n    this.searchMode = !this.searchMode;\n    this.showSearch = this.searchMode;\n  }\n  toggleMainMenu() {\n    this.showMainMenu = !this.showMainMenu;\n  }\n  goBackToConversations() {\n    // Navigation vers la liste des conversations\n    console.log('🔙 Going back to conversations');\n  }\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.showReactionPicker = false;\n    this.showThemeSelector = false;\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    this.selectedMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showMessageContextMenu = true;\n  }\n  showQuickReactions(message, event) {\n    event.stopPropagation();\n    this.reactionPickerMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showReactionPicker = true;\n  }\n  quickReact(emoji) {\n    if (this.reactionPickerMessage) {\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\n    }\n    this.showReactionPicker = false;\n  }\n  toggleReaction(messageId, emoji) {\n    console.log('👍 Toggling reaction:', emoji, 'for message:', messageId);\n    // Implémentation de la réaction\n  }\n\n  hasUserReacted(reaction, userId) {\n    return reaction.userId === userId;\n  }\n  replyToMessage(message) {\n    console.log('↩️ Replying to message:', message.id);\n    this.closeAllMenus();\n  }\n  forwardMessage(message) {\n    console.log('➡️ Forwarding message:', message.id);\n    this.closeAllMenus();\n  }\n  deleteMessage(message) {\n    console.log('🗑️ Deleting message:', message.id);\n    this.closeAllMenus();\n  }\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n  selectEmojiCategory(category) {\n    this.selectedEmojiCategory = category;\n  }\n  getEmojisForCategory(category) {\n    return category?.emojis || [];\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.showEmojiPicker = false;\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODES POUR LA GESTION DE LA FRAPPE ===\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // handleTypingIndicator - définie plus loin\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n  trackByMessageId(index, message) {\n    return message.id || message._id || index.toString();\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  testAddMessage() {\n    console.log('🧪 Test: Adding message');\n    const testMessage = {\n      id: `test-${Date.now()}`,\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\n      timestamp: new Date().toISOString(),\n      sender: {\n        id: this.otherParticipant?.id || 'test-user',\n        username: this.otherParticipant?.username || 'Test User',\n        image: this.otherParticipant?.image || 'assets/images/default-avatar.png'\n      },\n      type: 'TEXT',\n      isRead: false\n    };\n    this.messages.push(testMessage);\n    this.cdr.detectChanges();\n    setTimeout(() => this.scrollToBottom(), 50);\n  }\n  isGroupConversation() {\n    return this.conversation?.isGroup || this.conversation?.participants?.length > 2 || false;\n  }\n  openCamera() {\n    console.log('📷 Opening camera');\n    this.showAttachmentMenu = false;\n    // TODO: Implémenter l'ouverture de la caméra\n  }\n\n  zoomImage(factor) {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      const currentTransform = imageElement.style.transform || 'scale(1)';\n      const currentScale = parseFloat(currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1');\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n      imageElement.style.transform = `scale(${newScale})`;\n      if (newScale > 1) {\n        imageElement.classList.add('zoomed');\n      } else {\n        imageElement.classList.remove('zoomed');\n      }\n    }\n  }\n  resetZoom() {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      imageElement.style.transform = 'scale(1)';\n      imageElement.classList.remove('zoomed');\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  triggerFileInput(type) {\n    const input = this.fileInput?.nativeElement;\n    if (!input) {\n      console.error('File input element not found');\n      return;\n    }\n    // Configurer le type de fichier accepté\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    } else {\n      input.accept = '*/*';\n    }\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\n    input.value = '';\n    // Déclencher la sélection de fichier\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n  formatMessageContent(content) {\n    if (!content) return '';\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(urlRegex, '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>');\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  shouldShowAvatar(index) {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    if (!nextMessage) return true;\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n  shouldShowSenderName(index) {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!previousMessage) return true;\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n  getMessageType(message) {\n    // Vérifier d'abord le type de message explicite\n    if (message.type) {\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\n    }\n    // Ensuite vérifier les attachments\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    // Vérifier si c'est un message vocal basé sur les propriétés\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n    return 'text';\n  }\n  hasImage(message) {\n    // Vérifier le type de message\n    if (message.type === 'IMAGE' || message.type === 'image') {\n      return true;\n    }\n    // Vérifier les attachments\n    const hasImageAttachment = message.attachments?.some(att => {\n      return att.type?.startsWith('image/') || att.type === 'IMAGE';\n    }) || false;\n    // Vérifier les propriétés directes d'image\n    const hasImageUrl = !!(message.imageUrl || message.image);\n    return hasImageAttachment || hasImageUrl;\n  }\n  hasFile(message) {\n    // Vérifier le type de message\n    if (message.type === 'FILE' || message.type === 'file') {\n      return true;\n    }\n    // Vérifier les attachments non-image\n    const hasFileAttachment = message.attachments?.some(att => {\n      return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n    }) || false;\n    return hasFileAttachment;\n  }\n  getImageUrl(message) {\n    // Vérifier les propriétés directes d'image\n    if (message.imageUrl) {\n      return message.imageUrl;\n    }\n    if (message.image) {\n      return message.image;\n    }\n    // Vérifier les attachments\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/') || att.type === 'IMAGE');\n    if (imageAttachment) {\n      return imageAttachment.url || imageAttachment.path || '';\n    }\n    return '';\n  }\n  getFileName(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    return fileAttachment?.name || 'Fichier';\n  }\n  getFileSize(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.size) return '';\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  getFileIcon(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.type) return 'fas fa-file';\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n  getUserColor(userId) {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message, event) {\n    console.log('Message clicked:', message);\n  }\n  onInputChange(event) {\n    // Gérer les changements dans le champ de saisie\n    this.handleTypingIndicator();\n  }\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  onInputFocus() {\n    // Gérer le focus sur le champ de saisie\n  }\n  onInputBlur() {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n  onScroll(event) {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {\n      this.loadMoreMessages();\n    }\n  }\n  openUserProfile(userId) {\n    console.log('Opening user profile for:', userId);\n  }\n  onImageLoad(event, message) {\n    console.log('🖼️ [Debug] Image loaded successfully for message:', message.id, event.target.src);\n  }\n  onImageError(event, message) {\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n      src: event.target.src,\n      error: event\n    });\n    // Optionnel : afficher une image de remplacement\n    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n  }\n  openImageViewer(message) {\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));\n    if (imageAttachment?.url) {\n      this.selectedImage = {\n        url: imageAttachment.url,\n        name: imageAttachment.name || 'Image',\n        size: this.formatFileSize(imageAttachment.size || 0),\n        message: message\n      };\n      this.showImageViewer = true;\n      console.log('🖼️ [ImageViewer] Opening image:', this.selectedImage);\n    }\n  }\n  closeImageViewer() {\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    console.log('🖼️ [ImageViewer] Closed');\n  }\n  downloadImage() {\n    if (this.selectedImage?.url) {\n      const link = document.createElement('a');\n      link.href = this.selectedImage.url;\n      link.download = this.selectedImage.name || 'image';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n      console.log('🖼️ [ImageViewer] Download started:', this.selectedImage.name);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  searchMessages() {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n    this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()) || message.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n  onSearchQueryChange() {\n    this.searchMessages();\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n  }\n  jumpToMessage(messageId) {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      // Highlight temporairement le message\n      messageElement.classList.add('highlight');\n      setTimeout(() => {\n        messageElement.classList.remove('highlight');\n      }, 2000);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  closeContextMenu() {\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // triggerFileInput - définie plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n  // goBackToConversations, startVideoCall, startVoiceCall\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  initiateCall(callType) {\n    if (!this.otherParticipant) {\n      this.toastService.showError('Aucun destinataire sélectionné');\n      return;\n    }\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n    if (!recipientId) {\n      this.toastService.showError('ID du destinataire introuvable');\n      return;\n    }\n    console.log(`🔄 Initiating ${callType} call to user:`, recipientId);\n    this.isInCall = true;\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n    this.callDuration = 0;\n    // Démarrer le timer d'appel\n    this.startCallTimer();\n    // Utiliser le vrai service WebRTC\n    this.MessageService.initiateCall(recipientId, callType, this.conversation?.id).subscribe({\n      next: call => {\n        console.log('✅ Call initiated successfully:', call);\n        this.activeCall = call;\n        this.isCallConnected = false;\n        this.toastService.showSuccess(`Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`);\n      },\n      error: error => {\n        console.error('❌ Error initiating call:', error);\n        this.endCall();\n        this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n      }\n    });\n  }\n  acceptCall(incomingCall) {\n    console.log('🔄 Accepting incoming call:', incomingCall);\n    this.MessageService.acceptCall(incomingCall).subscribe({\n      next: call => {\n        console.log('✅ Call accepted successfully:', call);\n        this.activeCall = call;\n        this.isInCall = true;\n        this.isCallConnected = true;\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n        this.startCallTimer();\n        this.toastService.showSuccess('Appel accepté');\n      },\n      error: error => {\n        console.error('❌ Error accepting call:', error);\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\n      }\n    });\n  }\n  rejectCall(incomingCall) {\n    console.log('🔄 Rejecting incoming call:', incomingCall);\n    this.MessageService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n      next: () => {\n        console.log('✅ Call rejected successfully');\n        this.toastService.showSuccess('Appel rejeté');\n      },\n      error: error => {\n        console.error('❌ Error rejecting call:', error);\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n      }\n    });\n  }\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // endCall - Cette méthode est déjà définie plus loin dans le fichier\n  startCallTimer() {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n  resetCallState() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n  }\n  // === CONTRÔLES D'APPEL ===\n  toggleMute() {\n    if (!this.activeCall) return;\n    this.isMuted = !this.isMuted;\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(this.activeCall.id, undefined,\n    // video unchanged\n    !this.isMuted // audio state\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isMuted ? 'Micro coupé' : 'Micro activé');\n      },\n      error: error => {\n        console.error('❌ Error toggling mute:', error);\n        // Revert state on error\n        this.isMuted = !this.isMuted;\n        this.toastService.showError('Erreur lors du changement du micro');\n      }\n    });\n  }\n  toggleVideo() {\n    if (!this.activeCall) return;\n    this.isVideoEnabled = !this.isVideoEnabled;\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(this.activeCall.id, this.isVideoEnabled,\n    // video state\n    undefined // audio unchanged\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      error: error => {\n        console.error('❌ Error toggling video:', error);\n        // Revert state on error\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.toastService.showError('Erreur lors du changement de la caméra');\n      }\n    });\n  }\n  formatCallDuration(duration) {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor(duration % 3600 / 60);\n    const seconds = duration % 60;\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎤 [Voice] Starting voice recording...');\n      try {\n        // Vérifier le support du navigateur\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n          throw new Error(\"Votre navigateur ne supporte pas l'enregistrement audio\");\n        }\n        // Vérifier si MediaRecorder est supporté\n        if (!window.MediaRecorder) {\n          throw new Error(\"MediaRecorder n'est pas supporté par votre navigateur\");\n        }\n        console.log('🎤 [Voice] Requesting microphone access...');\n        // Demander l'accès au microphone avec des contraintes optimisées\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true,\n            sampleRate: 44100,\n            channelCount: 1\n          }\n        });\n        console.log('🎤 [Voice] Microphone access granted');\n        // Vérifier les types MIME supportés\n        let mimeType = 'audio/webm;codecs=opus';\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\n          mimeType = 'audio/webm';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = 'audio/mp4';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n              mimeType = ''; // Laisser le navigateur choisir\n            }\n          }\n        }\n\n        console.log('🎤 [Voice] Using MIME type:', mimeType);\n        // Créer le MediaRecorder\n        _this.mediaRecorder = new MediaRecorder(stream, {\n          mimeType: mimeType || undefined\n        });\n        // Initialiser les variables\n        _this.audioChunks = [];\n        _this.isRecordingVoice = true;\n        _this.voiceRecordingDuration = 0;\n        _this.voiceRecordingState = 'recording';\n        console.log('🎤 [Voice] MediaRecorder created, starting timer...');\n        // Démarrer le timer\n        _this.recordingTimer = setInterval(() => {\n          _this.voiceRecordingDuration++;\n          // Animer les waves\n          _this.animateVoiceWaves();\n          _this.cdr.detectChanges();\n        }, 1000);\n        // Gérer les événements du MediaRecorder\n        _this.mediaRecorder.ondataavailable = event => {\n          console.log('🎤 [Voice] Data available:', event.data.size, 'bytes');\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        _this.mediaRecorder.onstop = () => {\n          console.log('🎤 [Voice] MediaRecorder stopped, processing audio...');\n          _this.processRecordedAudio();\n        };\n        _this.mediaRecorder.onerror = event => {\n          console.error('🎤 [Voice] MediaRecorder error:', event.error);\n          _this.toastService.showError(\"Erreur lors de l'enregistrement\");\n          _this.cancelVoiceRecording();\n        };\n        // Démarrer l'enregistrement\n        _this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n        console.log('🎤 [Voice] Recording started successfully');\n        _this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n      } catch (error) {\n        console.error('🎤 [Voice] Error starting recording:', error);\n        let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n        if (error.name === 'NotAllowedError') {\n          errorMessage = \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n        } else if (error.name === 'NotFoundError') {\n          errorMessage = 'Aucun microphone détecté. Veuillez connecter un microphone.';\n        } else if (error.name === 'NotSupportedError') {\n          errorMessage = \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n        _this.toastService.showError(errorMessage);\n        _this.cancelVoiceRecording();\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n  cancelVoiceRecording() {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      this.mediaRecorder = null;\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n  processRecordedAudio() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎤 [Voice] Processing recorded audio...');\n      try {\n        // Vérifier qu'on a des données audio\n        if (_this2.audioChunks.length === 0) {\n          console.error('🎤 [Voice] No audio chunks available');\n          _this2.toastService.showError('Aucun audio enregistré');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        console.log('🎤 [Voice] Audio chunks:', _this2.audioChunks.length, 'Duration:', _this2.voiceRecordingDuration);\n        // Vérifier la durée minimale\n        if (_this2.voiceRecordingDuration < 1) {\n          console.error('🎤 [Voice] Recording too short:', _this2.voiceRecordingDuration);\n          _this2.toastService.showError('Enregistrement trop court (minimum 1 seconde)');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        // Déterminer le type MIME du blob\n        let mimeType = 'audio/webm;codecs=opus';\n        if (_this2.mediaRecorder?.mimeType) {\n          mimeType = _this2.mediaRecorder.mimeType;\n        }\n        console.log('🎤 [Voice] Creating audio blob with MIME type:', mimeType);\n        // Créer le blob audio\n        const audioBlob = new Blob(_this2.audioChunks, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio blob created:', {\n          size: audioBlob.size,\n          type: audioBlob.type\n        });\n        // Déterminer l'extension du fichier\n        let extension = '.webm';\n        if (mimeType.includes('mp4')) {\n          extension = '.mp4';\n        } else if (mimeType.includes('wav')) {\n          extension = '.wav';\n        } else if (mimeType.includes('ogg')) {\n          extension = '.ogg';\n        }\n        // Créer le fichier\n        const audioFile = new File([audioBlob], `voice_${Date.now()}${extension}`, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio file created:', {\n          name: audioFile.name,\n          size: audioFile.size,\n          type: audioFile.type\n        });\n        // Envoyer le message vocal\n        _this2.voiceRecordingState = 'processing';\n        yield _this2.sendVoiceMessage(audioFile);\n        console.log('🎤 [Voice] Voice message sent successfully');\n        _this2.toastService.showSuccess('🎤 Message vocal envoyé');\n      } catch (error) {\n        console.error('🎤 [Voice] Error processing audio:', error);\n        _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal: \" + (error.message || 'Erreur inconnue'));\n      } finally {\n        // Nettoyer l'état\n        _this2.voiceRecordingState = 'idle';\n        _this2.voiceRecordingDuration = 0;\n        _this2.audioChunks = [];\n        _this2.isRecordingVoice = false;\n        console.log('🎤 [Voice] Audio processing completed, state reset');\n      }\n    })();\n  }\n  sendVoiceMessage(audioFile) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const receiverId = _this3.otherParticipant?.id || _this3.otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this3.MessageService.sendMessage(receiverId, '', audioFile, 'AUDIO', _this3.conversation.id).subscribe({\n          next: message => {\n            _this3.messages.push(message);\n            _this3.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n  onRecordStart(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record start triggered');\n    console.log('🎤 [Voice] Current state:', {\n      isRecordingVoice: this.isRecordingVoice,\n      voiceRecordingState: this.voiceRecordingState,\n      voiceRecordingDuration: this.voiceRecordingDuration,\n      mediaRecorder: !!this.mediaRecorder\n    });\n    // Vérifier si on peut enregistrer\n    if (this.voiceRecordingState === 'processing') {\n      console.log('🎤 [Voice] Already processing, ignoring start');\n      this.toastService.showWarning('Traitement en cours...');\n      return;\n    }\n    if (this.isRecordingVoice) {\n      console.log('🎤 [Voice] Already recording, ignoring start');\n      this.toastService.showWarning('Enregistrement déjà en cours...');\n      return;\n    }\n    // Afficher un message de début\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n    // Démarrer l'enregistrement\n    this.startVoiceRecording().catch(error => {\n      console.error('🎤 [Voice] Failed to start recording:', error);\n      this.toastService.showError(\"Impossible de démarrer l'enregistrement vocal: \" + (error.message || 'Erreur inconnue'));\n    });\n  }\n  onRecordEnd(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record end triggered');\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring end');\n      return;\n    }\n    // Arrêter l'enregistrement et envoyer\n    this.stopVoiceRecording();\n  }\n  onRecordCancel(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record cancel triggered');\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring cancel');\n      return;\n    }\n    // Annuler l'enregistrement\n    this.cancelVoiceRecording();\n  }\n  getRecordingFormat() {\n    if (this.mediaRecorder?.mimeType) {\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n    }\n    return 'Auto';\n  }\n  // === ANIMATION DES WAVES VOCALES ===\n  animateVoiceWaves() {\n    // Animer les waves pendant l'enregistrement\n    this.voiceWaves = this.voiceWaves.map(() => {\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n    });\n  }\n\n  onFileSelected(event) {\n    console.log('📁 [Upload] File selection triggered');\n    const files = event.target.files;\n    if (!files || files.length === 0) {\n      console.log('📁 [Upload] No files selected');\n      return;\n    }\n    console.log(`📁 [Upload] ${files.length} file(s) selected:`, files);\n    for (let file of files) {\n      console.log(`📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`);\n      this.uploadFile(file);\n    }\n  }\n  uploadFile(file) {\n    console.log(`📁 [Upload] Starting upload for file: ${file.name}`);\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      console.error('📁 [Upload] No receiver ID found');\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    console.log(`📁 [Upload] Receiver ID: ${receiverId}`);\n    // Vérifier la taille du fichier (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\n      return;\n    }\n    // 🖼️ Compression d'image si nécessaire\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n      // > 1MB\n      console.log('🖼️ [Compression] Compressing image:', file.name, 'Original size:', file.size);\n      this.compressImage(file).then(compressedFile => {\n        console.log('🖼️ [Compression] ✅ Image compressed successfully. New size:', compressedFile.size);\n        this.sendFileToServer(compressedFile, receiverId);\n      }).catch(error => {\n        console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n        // Envoyer le fichier original en cas d'erreur\n        this.sendFileToServer(file, receiverId);\n      });\n      return;\n    }\n    // Envoyer le fichier sans compression\n    this.sendFileToServer(file, receiverId);\n  }\n  sendFileToServer(file, receiverId) {\n    const messageType = this.getFileMessageType(file);\n    console.log(`📁 [Upload] Message type determined: ${messageType}`);\n    console.log(`📁 [Upload] Conversation ID: ${this.conversation.id}`);\n    this.isSendingMessage = true;\n    this.isUploading = true;\n    this.uploadProgress = 0;\n    console.log('📁 [Upload] Calling MessageService.sendMessage...');\n    // Simuler la progression d'upload\n    const progressInterval = setInterval(() => {\n      this.uploadProgress += Math.random() * 15;\n      if (this.uploadProgress >= 90) {\n        clearInterval(progressInterval);\n      }\n      this.cdr.detectChanges();\n    }, 300);\n    this.MessageService.sendMessage(receiverId, '', file, messageType, this.conversation.id).subscribe({\n      next: message => {\n        console.log('📁 [Upload] ✅ File sent successfully:', message);\n        console.log('📁 [Debug] Sent message structure:', {\n          id: message.id,\n          type: message.type,\n          attachments: message.attachments,\n          hasImage: this.hasImage(message),\n          hasFile: this.hasFile(message),\n          imageUrl: this.getImageUrl(message)\n        });\n        clearInterval(progressInterval);\n        this.uploadProgress = 100;\n        setTimeout(() => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Fichier envoyé avec succès');\n          this.resetUploadState();\n        }, 500);\n      },\n      error: error => {\n        console.error('📁 [Upload] ❌ Error sending file:', error);\n        clearInterval(progressInterval);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n        this.resetUploadState();\n      }\n    });\n  }\n  getFileMessageType(file) {\n    if (file.type.startsWith('image/')) return 'IMAGE';\n    if (file.type.startsWith('video/')) return 'VIDEO';\n    if (file.type.startsWith('audio/')) return 'AUDIO';\n    return 'FILE';\n  }\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  resetUploadState() {\n    this.isSendingMessage = false;\n    this.isUploading = false;\n    this.uploadProgress = 0;\n  }\n  // === DRAG & DROP ===\n  onDragOver(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n  onDragLeave(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\n    const rect = event.currentTarget.getBoundingClientRect();\n    const x = event.clientX;\n    const y = event.clientY;\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      this.isDragOver = false;\n    }\n  }\n  onDrop(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      console.log('📁 [Drag&Drop] Files dropped:', files.length);\n      // Traiter chaque fichier\n      Array.from(files).forEach(file => {\n        console.log('📁 [Drag&Drop] Processing file:', file.name, file.type, file.size);\n        this.uploadFile(file);\n      });\n      this.toastService.showSuccess(`${files.length} fichier(s) en cours d'envoi`);\n    }\n  }\n  // === COMPRESSION D'IMAGES ===\n  compressImage(file, quality = 0.8) {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n      img.onload = () => {\n        // Calculer les nouvelles dimensions (max 1920x1080)\n        const maxWidth = 1920;\n        const maxHeight = 1080;\n        let {\n          width,\n          height\n        } = img;\n        if (width > maxWidth || height > maxHeight) {\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\n          width *= ratio;\n          height *= ratio;\n        }\n        canvas.width = width;\n        canvas.height = height;\n        // Dessiner l'image redimensionnée\n        ctx?.drawImage(img, 0, 0, width, height);\n        // Convertir en blob avec compression\n        canvas.toBlob(blob => {\n          if (blob) {\n            const compressedFile = new File([blob], file.name, {\n              type: file.type,\n              lastModified: Date.now()\n            });\n            resolve(compressedFile);\n          } else {\n            reject(new Error('Failed to compress image'));\n          }\n        }, file.type, quality);\n      };\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  }\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n  handleTypingIndicator() {\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\n      this.sendTypingIndicator(true);\n    }\n    // Reset le timer\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Arrêter l'indicateur de frappe\n      this.sendTypingIndicator(false);\n    }, 2000);\n  }\n  sendTypingIndicator(isTyping) {\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (receiverId && this.conversation?.id) {\n      console.log(`📝 Sending typing indicator: ${isTyping} to user ${receiverId}`);\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n  onCallAccepted(call) {\n    console.log('🔄 Call accepted from interface:', call);\n    this.activeCall = call;\n    this.isInCall = true;\n    this.isCallConnected = true;\n    this.startCallTimer();\n    this.toastService.showSuccess('Appel accepté');\n  }\n  onCallRejected() {\n    console.log('🔄 Call rejected from interface');\n    this.endCall();\n    this.toastService.showInfo('Appel rejeté');\n  }\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n  playVoiceMessage(message) {\n    console.log('🎵 [Voice] Playing voice message:', message.id);\n    this.toggleVoicePlayback(message);\n  }\n  isVoicePlaying(messageId) {\n    return this.playingMessageId === messageId;\n  }\n  toggleVoicePlayback(message) {\n    const messageId = message.id;\n    const audioUrl = this.getVoiceUrl(message);\n    if (!audioUrl) {\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\n      this.toastService.showError('Fichier audio introuvable');\n      return;\n    }\n    // Si c'est déjà en cours de lecture, arrêter\n    if (this.isVoicePlaying(messageId)) {\n      this.stopVoicePlayback();\n      return;\n    }\n    // Arrêter toute autre lecture en cours\n    this.stopVoicePlayback();\n    // Démarrer la nouvelle lecture\n    this.startVoicePlayback(message, audioUrl);\n  }\n  startVoicePlayback(message, audioUrl) {\n    const messageId = message.id;\n    try {\n      console.log('🎵 [Voice] Starting playback for:', messageId, 'URL:', audioUrl);\n      this.currentAudio = new Audio(audioUrl);\n      this.playingMessageId = messageId;\n      // Initialiser les valeurs par défaut avec la nouvelle structure\n      const currentData = this.getVoicePlaybackData(messageId);\n      this.setVoicePlaybackData(messageId, {\n        progress: 0,\n        currentTime: 0,\n        speed: currentData.speed || 1,\n        duration: currentData.duration || 0\n      });\n      // Configurer la vitesse de lecture\n      this.currentAudio.playbackRate = currentData.speed || 1;\n      // Événements audio\n      this.currentAudio.addEventListener('loadedmetadata', () => {\n        if (this.currentAudio) {\n          this.setVoicePlaybackData(messageId, {\n            duration: this.currentAudio.duration\n          });\n          console.log('🎵 [Voice] Audio loaded, duration:', this.currentAudio.duration);\n        }\n      });\n      this.currentAudio.addEventListener('timeupdate', () => {\n        if (this.currentAudio && this.playingMessageId === messageId) {\n          const currentTime = this.currentAudio.currentTime;\n          const progress = currentTime / this.currentAudio.duration * 100;\n          this.setVoicePlaybackData(messageId, {\n            currentTime,\n            progress\n          });\n          this.cdr.detectChanges();\n        }\n      });\n      this.currentAudio.addEventListener('ended', () => {\n        console.log('🎵 [Voice] Playback ended for:', messageId);\n        this.stopVoicePlayback();\n      });\n      this.currentAudio.addEventListener('error', error => {\n        console.error('🎵 [Voice] Audio error:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      });\n      // Démarrer la lecture\n      this.currentAudio.play().then(() => {\n        console.log('🎵 [Voice] Playback started successfully');\n        this.toastService.showSuccess('🎵 Lecture du message vocal');\n      }).catch(error => {\n        console.error('🎵 [Voice] Error starting playback:', error);\n        this.toastService.showError('Impossible de lire le message vocal');\n        this.stopVoicePlayback();\n      });\n    } catch (error) {\n      console.error('🎵 [Voice] Error creating audio:', error);\n      this.toastService.showError('Erreur lors de la lecture audio');\n      this.stopVoicePlayback();\n    }\n  }\n  stopVoicePlayback() {\n    if (this.currentAudio) {\n      console.log('🎵 [Voice] Stopping playback for:', this.playingMessageId);\n      this.currentAudio.pause();\n      this.currentAudio.currentTime = 0;\n      this.currentAudio = null;\n    }\n    this.playingMessageId = null;\n    this.cdr.detectChanges();\n  }\n  getVoiceUrl(message) {\n    // Vérifier les propriétés directes d'audio\n    if (message.voiceUrl) return message.voiceUrl;\n    if (message.audioUrl) return message.audioUrl;\n    if (message.voice) return message.voice;\n    // Vérifier les attachments audio\n    const audioAttachment = message.attachments?.find(att => att.type?.startsWith('audio/') || att.type === 'AUDIO');\n    if (audioAttachment) {\n      return audioAttachment.url || audioAttachment.path || '';\n    }\n    return '';\n  }\n  getVoiceWaves(message) {\n    // Générer des waves basées sur l'ID du message pour la cohérence\n    const messageId = message.id || '';\n    const seed = messageId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);\n    const waves = [];\n    for (let i = 0; i < 16; i++) {\n      const height = 4 + (seed + i * 7) % 20;\n      waves.push(height);\n    }\n    return waves;\n  }\n  getVoiceProgress(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const totalWaves = 16;\n    return Math.floor(data.progress / 100 * totalWaves);\n  }\n  getVoiceCurrentTime(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    return this.formatAudioTime(data.currentTime);\n  }\n  getVoiceDuration(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const duration = data.duration || message.metadata?.duration || 0;\n    if (typeof duration === 'string') {\n      return duration; // Déjà formaté\n    }\n\n    return this.formatAudioTime(duration);\n  }\n  formatAudioTime(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  seekVoiceMessage(message, waveIndex) {\n    const messageId = message.id;\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\n      return;\n    }\n    const totalWaves = 16;\n    const seekPercentage = waveIndex / totalWaves * 100;\n    const seekTime = seekPercentage / 100 * this.currentAudio.duration;\n    this.currentAudio.currentTime = seekTime;\n    console.log('🎵 [Voice] Seeking to:', seekTime, 'seconds');\n  }\n  toggleVoiceSpeed(message) {\n    const messageId = message.id;\n    const data = this.getVoicePlaybackData(messageId);\n    // Cycle entre 1x, 1.5x, 2x\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n    this.setVoicePlaybackData(messageId, {\n      speed: newSpeed\n    });\n    if (this.currentAudio && this.playingMessageId === messageId) {\n      this.currentAudio.playbackRate = newSpeed;\n    }\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n    // Nettoyer les timers\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    // Nettoyer les ressources audio\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream?.getTracks().forEach(track => track.stop());\n    }\n    // Nettoyer la lecture audio\n    this.stopVoicePlayback();\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 60,\n      vars: 51,\n      consts: [[1, \"flex\", \"flex-col\", \"h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"to-green-50\", \"dark:from-gray-900\", \"dark:to-gray-800\"], [1, \"flex\", \"items-center\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-gray-800\", \"border-b\", \"border-gray-200\", \"dark:border-gray-700\", \"shadow-sm\"], [1, \"p-2\", \"mr-3\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"text-gray-600\", \"dark:text-gray-300\"], [1, \"flex\", \"items-center\", \"flex-1\", \"min-w-0\"], [1, \"relative\", \"mr-3\"], [1, \"w-10\", \"h-10\", \"rounded-full\", \"object-cover\", \"border-2\", \"border-green-500\", \"cursor-pointer\", \"hover:scale-105\", \"transition-transform\", 3, \"src\", \"alt\", \"click\"], [\"class\", \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full animate-pulse\", 4, \"ngIf\"], [1, \"flex-1\", \"min-w-0\"], [1, \"font-semibold\", \"text-gray-900\", \"dark:text-white\", \"truncate\"], [1, \"text-sm\", \"text-gray-500\", \"dark:text-gray-400\"], [\"class\", \"flex items-center gap-1 text-green-600\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"items-center\", \"gap-2\"], [\"title\", \"Appel vid\\u00E9o\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [\"title\", \"Appel vocal\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Rechercher\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"title\", \"Changer le th\\u00E8me\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [\"class\", \"fas fa-moon\", 4, \"ngIf\"], [\"class\", \"fas fa-sun\", 4, \"ngIf\"], [\"title\", \"Menu\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", \"relative\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [\"title\", \"Test: Ajouter message\", 1, \"p-2\", \"rounded-full\", \"hover:bg-red-100\", \"dark:hover:bg-red-700\", \"text-red-600\", \"dark:text-red-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [\"class\", \"absolute top-16 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-48\", 4, \"ngIf\"], [1, \"flex-1\", \"overflow-y-auto\", \"p-4\", \"space-y-4\", \"relative\", 3, \"scroll\", \"dragover\", \"dragleave\", \"drop\"], [\"messagesContainer\", \"\"], [\"class\", \"absolute inset-0 bg-green-500 bg-opacity-20 border-2 border-dashed border-green-500 rounded-lg flex items-center justify-center z-50 animate-pulse\", \"style\", \"\\n        backdrop-filter: blur(2px);\\n        background: linear-gradient(\\n          45deg,\\n          rgba(34, 197, 94, 0.1) 0%,\\n          rgba(34, 197, 94, 0.2) 50%,\\n          rgba(34, 197, 94, 0.1) 100%\\n        );\\n        animation: dragShimmer 2s infinite;\\n      \", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center py-8\", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"space-y-2\", 4, \"ngIf\"], [\"class\", \"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2\", 4, \"ngIf\"], [1, \"bg-white\", \"dark:bg-gray-800\", \"border-t\", \"border-gray-200\", \"dark:border-gray-700\", \"p-4\"], [1, \"flex\", \"items-end\", \"gap-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"gap-2\"], [\"type\", \"button\", \"title\", \"\\u00C9mojis\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"type\", \"button\", \"title\", \"Joindre un fichier\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [1, \"flex-1\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", \"rows\", \"1\", \"maxlength\", \"4096\", \"autocomplete\", \"off\", \"spellcheck\", \"true\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-gray-50\", \"dark:bg-gray-700\", \"border\", \"border-gray-200\", \"dark:border-gray-600\", \"rounded-2xl\", \"resize-none\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-green-500\", \"focus:border-transparent\", \"text-gray-900\", \"dark:text-white\", \"placeholder-gray-500\", \"dark:placeholder-gray-400\", 3, \"input\", \"keydown\", \"focus\", \"blur\"], [\"messageTextarea\", \"\"], [\"type\", \"button\", \"title\", \"Maintenir pour enregistrer un message vocal\", 3, \"ngClass\", \"disabled\", \"mousedown\", \"mouseup\", \"mouseleave\", \"touchstart\", \"touchend\", \"touchcancel\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50\", \"title\", \"Envoyer\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4 z-50\", 4, \"ngIf\"], [\"class\", \"absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\", 4, \"ngIf\"], [\"class\", \"absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 1, \"hidden\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"class\", \"fixed bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 p-3\", 3, \"left\", \"top\", 4, \"ngIf\"], [\"class\", \"fixed bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-48\", 3, \"left\", \"top\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-25 z-40\", 3, \"click\", 4, \"ngIf\"], [3, \"isVisible\", \"activeCall\", \"callType\", \"otherParticipant\", \"callEnded\", \"callAccepted\", \"callRejected\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-95 z-50 flex items-center justify-center\", 3, \"click\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-0\", \"right-0\", \"w-3\", \"h-3\", \"bg-green-500\", \"border-2\", \"border-white\", \"dark:border-gray-800\", \"rounded-full\", \"animate-pulse\"], [1, \"flex\", \"items-center\", \"gap-1\", \"text-green-600\"], [1, \"flex\", \"gap-1\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [1, \"fas\", \"fa-moon\"], [1, \"fas\", \"fa-sun\"], [1, \"absolute\", \"top-16\", \"right-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\", \"min-w-48\"], [1, \"p-2\"], [1, \"w-full\", \"flex\", \"items-center\", \"gap-3\", \"px-3\", \"py-2\", \"rounded-lg\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-left\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"text-blue-500\"], [1, \"text-gray-700\", \"dark:text-gray-300\"], [1, \"w-full\", \"flex\", \"items-center\", \"gap-3\", \"px-3\", \"py-2\", \"rounded-lg\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-left\"], [1, \"fas\", \"fa-user\", \"text-green-500\"], [1, \"fas\", \"fa-bell\", \"text-yellow-500\"], [1, \"relative\"], [1, \"fas\", \"fa-chevron-right\", \"ml-auto\", \"text-xs\", \"text-gray-400\", \"transition-transform\"], [\"class\", \"absolute left-full top-0 ml-2 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-40\", 4, \"ngIf\"], [1, \"my-2\", \"border-gray-200\", \"dark:border-gray-600\"], [1, \"fas\", \"fa-cog\", \"text-gray-500\"], [1, \"absolute\", \"left-full\", \"top-0\", \"ml-2\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\", \"min-w-40\"], [\"class\", \"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\", 3, \"bg-blue-50\", \"dark:bg-blue-900\", \"text-blue-600\", \"dark:text-blue-400\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"fas fa-check ml-auto text-xs text-blue-600 dark:text-blue-400\", 4, \"ngIf\"], [1, \"fas\", \"fa-check\", \"ml-auto\", \"text-xs\", \"text-blue-600\", \"dark:text-blue-400\"], [1, \"absolute\", \"inset-0\", \"bg-green-500\", \"bg-opacity-20\", \"border-2\", \"border-dashed\", \"border-green-500\", \"rounded-lg\", \"flex\", \"items-center\", \"justify-center\", \"z-50\", \"animate-pulse\", 2, \"backdrop-filter\", \"blur(2px)\", \"background\", \"linear-gradient(\\n          45deg,\\n          rgba(34, 197, 94, 0.1) 0%,\\n          rgba(34, 197, 94, 0.2) 50%,\\n          rgba(34, 197, 94, 0.1) 100%\\n        )\", \"animation\", \"dragShimmer 2s infinite\"], [1, \"text-center\", \"bg-white\", \"dark:bg-gray-800\", \"p-6\", \"rounded-xl\", \"shadow-lg\", \"border\", \"border-green-300\", \"dark:border-green-600\"], [1, \"fas\", \"fa-cloud-upload-alt\", \"text-5xl\", \"text-green-600\", \"mb-3\", \"animate-bounce\"], [1, \"text-xl\", \"font-bold\", \"text-green-700\", \"dark:text-green-400\", \"mb-2\"], [1, \"text-sm\", \"text-green-600\", \"dark:text-green-300\"], [1, \"flex\", \"justify-center\", \"gap-2\", \"mt-3\"], [1, \"w-2\", \"h-2\", \"bg-green-500\", \"rounded-full\", \"animate-ping\"], [1, \"w-2\", \"h-2\", \"bg-green-500\", \"rounded-full\", \"animate-ping\", 2, \"animation-delay\", \"0.2s\"], [1, \"w-2\", \"h-2\", \"bg-green-500\", \"rounded-full\", \"animate-ping\", 2, \"animation-delay\", \"0.4s\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-8\", \"w-8\", \"border-b-2\", \"border-green-500\", \"mb-4\"], [1, \"text-gray-500\", \"dark:text-gray-400\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"text-6xl\", \"text-gray-300\", \"dark:text-gray-600\", \"mb-4\"], [1, \"fas\", \"fa-comments\"], [1, \"text-xl\", \"font-semibold\", \"text-gray-700\", \"dark:text-gray-300\", \"mb-2\"], [1, \"text-gray-500\", \"dark:text-gray-400\", \"text-center\"], [1, \"space-y-2\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"flex items-start gap-2\", 4, \"ngIf\"], [\"class\", \"flex justify-center my-4\", 4, \"ngIf\"], [1, \"flex\", 3, \"id\", \"click\", \"contextmenu\"], [\"class\", \"mr-2 flex-shrink-0\", 4, \"ngIf\"], [1, \"max-w-xs\", \"lg:max-w-md\", \"px-4\", \"py-2\", \"rounded-2xl\", \"shadow-sm\", \"relative\", \"group\", 2, \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\"], [\"class\", \"text-xs font-semibold mb-1 opacity-75\", 3, \"color\", 4, \"ngIf\"], [\"class\", \"break-words\", 4, \"ngIf\"], [\"class\", \"flex items-center gap-3 p-3 rounded-xl min-w-[250px] max-w-xs cursor-pointer transition-all duration-300 hover:shadow-lg group border bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600\", \"style\", \"position: relative; overflow: hidden\", 3, \"shadow-lg\", \"ring-2\", \"ring-blue-300\", 4, \"ngIf\"], [\"class\", \"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-end\", \"gap-1\", \"mt-1\", \"text-xs\", \"opacity-75\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"flex gap-1 mt-2\", 4, \"ngIf\"], [\"title\", \"Ajouter une r\\u00E9action\", 1, \"absolute\", \"-bottom-2\", \"right-2\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"bg-white\", \"dark:bg-gray-700\", \"border\", \"border-gray-200\", \"dark:border-gray-600\", \"rounded-full\", \"p-1\", \"shadow-sm\", \"hover:shadow-md\", 3, \"click\"], [1, \"fas\", \"fa-smile\", \"text-gray-500\", \"dark:text-gray-400\", \"text-xs\"], [1, \"flex\", \"justify-center\", \"my-4\"], [1, \"bg-white\", \"dark:bg-gray-700\", \"px-3\", \"py-1\", \"rounded-full\", \"shadow-sm\"], [1, \"text-xs\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"mr-2\", \"flex-shrink-0\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", \"cursor-pointer\", \"hover:scale-105\", \"transition-transform\", 3, \"src\", \"alt\", \"click\"], [1, \"text-xs\", \"font-semibold\", \"mb-1\", \"opacity-75\"], [1, \"break-words\"], [3, \"innerHTML\"], [3, \"src\", \"alt\", \"click\", \"load\", \"error\"], [3, \"text-white\", \"text-gray-900\", \"color\", \"innerHTML\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"gap-3\", \"p-3\", \"rounded-xl\", \"min-w-[250px]\", \"max-w-xs\", \"cursor-pointer\", \"transition-all\", \"duration-300\", \"hover:shadow-lg\", \"group\", \"border\", \"bg-white\", \"dark:bg-gray-700\", \"border-gray-200\", \"dark:border-gray-600\", 2, \"position\", \"relative\", \"overflow\", \"hidden\"], [1, \"p-2\", \"rounded-full\", \"text-white\", \"transition-all\", \"duration-300\", \"flex-shrink-0\", \"border-none\", \"outline-none\", \"cursor-pointer\", 2, \"width\", \"40px\", \"height\", \"40px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"box-shadow\", \"0 2px 8px rgba(0, 0, 0, 0.2)\", 3, \"click\"], [1, \"fas\", \"text-sm\", \"transition-transform\", \"duration-200\"], [1, \"flex\", \"items-center\", \"gap-1\", \"h-8\", \"mb-2\", \"px-1\"], [\"class\", \"w-1 rounded-full transition-all duration-300 cursor-pointer hover:scale-110 hover:opacity-80\", \"style\", \"\\n                      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n                      min-height: 4px;\\n                    \", 3, \"bg-gray-400\", \"bg-blue-500\", \"bg-gray-300\", \"dark:bg-gray-500\", \"height\", \"animate-pulse\", \"transform\", \"box-shadow\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"text-xs\", \"mt-1\"], [1, \"text-gray-600\", \"dark:text-gray-300\", \"font-mono\", \"text-xs\"], [1, \"text-gray-400\", \"dark:text-gray-500\"], [1, \"text-gray-500\", \"dark:text-gray-400\", \"font-mono\", \"text-xs\"], [1, \"flex\", \"items-center\", \"gap-1\"], [\"class\", \"text-green-600 dark:text-green-400 font-semibold text-xs px-1 py-0.5 bg-green-100 dark:bg-green-900 rounded\", 4, \"ngIf\"], [\"class\", \"flex items-center gap-0.5\", 4, \"ngIf\"], [1, \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"duration-300\", \"flex\", \"flex-col\", \"gap-1\"], [\"title\", \"Vitesse de lecture\", 1, \"p-1.5\", \"rounded-full\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-500\", \"text-gray-500\", \"dark:text-gray-400\", \"transition-all\", \"duration-200\", 2, \"border\", \"none\", \"outline\", \"none\", \"cursor\", \"pointer\", 3, \"click\"], [1, \"fas\", \"fa-tachometer-alt\", \"text-xs\"], [1, \"w-1\", \"rounded-full\", \"transition-all\", \"duration-300\", \"cursor-pointer\", \"hover:scale-110\", \"hover:opacity-80\", 2, \"transition\", \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\", \"min-height\", \"4px\", 3, \"click\"], [1, \"text-green-600\", \"dark:text-green-400\", \"font-semibold\", \"text-xs\", \"px-1\", \"py-0.5\", \"bg-green-100\", \"dark:bg-green-900\", \"rounded\"], [1, \"flex\", \"items-center\", \"gap-0.5\"], [1, \"w-1\", \"h-1\", \"bg-green-500\", \"rounded-full\", \"animate-pulse\"], [1, \"w-1\", \"h-1\", \"bg-green-500\", \"rounded-full\", \"animate-pulse\", 2, \"animation-delay\", \"0.2s\"], [1, \"w-1\", \"h-1\", \"bg-green-500\", \"rounded-full\", \"animate-pulse\", 2, \"animation-delay\", \"0.4s\"], [1, \"flex\", \"items-center\", \"gap-3\", \"p-2\", \"bg-gray-50\", \"dark:bg-gray-600\", \"rounded-lg\", \"cursor-pointer\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-500\", \"transition-colors\", 3, \"click\"], [1, \"text-2xl\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"font-medium\", \"text-sm\", \"truncate\"], [1, \"p-1\", \"rounded-full\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-400\", \"transition-colors\"], [1, \"fas\", \"fa-download\", \"text-sm\"], [1, \"flex\", \"items-center\"], [\"class\", \"fas fa-clock\", \"title\", \"Envoi en cours\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", \"title\", \"Envoy\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"title\", \"Livr\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double text-blue-400\", \"title\", \"Lu\", 4, \"ngIf\"], [\"title\", \"Envoi en cours\", 1, \"fas\", \"fa-clock\"], [\"title\", \"Envoy\\u00E9\", 1, \"fas\", \"fa-check\"], [\"title\", \"Livr\\u00E9\", 1, \"fas\", \"fa-check-double\"], [\"title\", \"Lu\", 1, \"fas\", \"fa-check-double\", \"text-blue-400\"], [1, \"flex\", \"gap-1\", \"mt-2\"], [\"class\", \"flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors\", 3, \"bg-green-100\", \"text-green-600\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"gap-1\", \"px-2\", \"py-1\", \"bg-gray-100\", \"dark:bg-gray-600\", \"rounded-full\", \"text-xs\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-500\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-start\", \"gap-2\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"bg-white\", \"dark:bg-gray-700\", \"px-4\", \"py-2\", \"rounded-2xl\", \"shadow-sm\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [1, \"bg-white\", \"dark:bg-gray-800\", \"border-t\", \"border-gray-200\", \"dark:border-gray-700\", \"px-4\", \"py-2\"], [1, \"flex\", \"items-center\", \"gap-3\"], [1, \"flex\", \"justify-between\", \"text-sm\", \"text-gray-600\", \"dark:text-gray-400\", \"mb-1\"], [1, \"w-full\", \"bg-gray-200\", \"dark:bg-gray-700\", \"rounded-full\", \"h-2\"], [1, \"bg-green-500\", \"h-2\", \"rounded-full\", \"transition-all\", \"duration-300\"], [\"title\", \"Annuler\", 1, \"p-1\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-500\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-sm\"], [\"type\", \"button\", \"title\", \"Maintenir pour enregistrer un message vocal\", 3, \"ngClass\", \"disabled\", \"mousedown\", \"mouseup\", \"mouseleave\", \"touchstart\", \"touchend\", \"touchcancel\"], [\"class\", \"fas fa-microphone\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner\", 4, \"ngIf\"], [1, \"fas\", \"fa-microphone\"], [1, \"fas\", \"fa-spinner\"], [\"type\", \"button\", \"title\", \"Envoyer\", 1, \"p-3\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"text-white\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"absolute\", \"bottom-0\", \"left-0\", \"right-0\", \"bg-white\", \"dark:bg-gray-800\", \"border-t\", \"border-gray-200\", \"dark:border-gray-700\", \"p-4\", \"z-50\"], [1, \"flex\", \"items-center\", \"gap-4\"], [\"title\", \"Annuler\", 1, \"p-2\", \"rounded-full\", \"bg-red-100\", \"dark:bg-red-900\", \"text-red-600\", \"dark:text-red-400\", \"hover:bg-red-200\", \"dark:hover:bg-red-800\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"flex\", \"items-center\", \"gap-3\", \"flex-1\"], [1, \"fas\", \"fa-microphone\", \"text-red-500\", \"text-xl\", \"animate-pulse\"], [1, \"absolute\", \"-top-1\", \"-right-1\", \"w-3\", \"h-3\", \"bg-red-500\", \"rounded-full\", \"animate-ping\"], [\"class\", \"bg-green-500 rounded-full transition-all duration-150\", 3, \"width\", \"height\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-gray-600\", \"dark:text-gray-300\", \"font-mono\"], [\"title\", \"Envoyer l'enregistrement\", 1, \"p-3\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"text-white\", \"transition-colors\", 3, \"click\"], [1, \"mt-3\", \"w-full\", \"bg-gray-200\", \"dark:bg-gray-700\", \"rounded-full\", \"h-1\"], [1, \"bg-green-500\", \"h-1\", \"rounded-full\", \"transition-all\", \"duration-300\"], [1, \"mt-2\", \"text-center\", \"text-sm\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"flex\", \"items-center\", \"justify-center\", \"gap-2\"], [1, \"fas\", \"fa-info-circle\"], [1, \"mt-1\", \"text-xs\"], [1, \"bg-green-500\", \"rounded-full\", \"transition-all\", \"duration-150\"], [1, \"absolute\", \"bottom-20\", \"left-4\", \"right-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\"], [1, \"p-4\"], [1, \"flex\", \"gap-2\", \"mb-4\", \"overflow-x-auto\"], [\"class\", \"px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0\", 3, \"bg-green-100\", \"text-green-600\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\", \"grid-cols-8\", \"gap-2\", \"max-h-48\", \"overflow-y-auto\"], [\"class\", \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"px-3\", \"py-2\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"transition-colors\", \"flex-shrink-0\", 3, \"click\"], [1, \"p-2\", \"rounded-lg\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-xl\", 3, \"title\", \"click\"], [1, \"absolute\", \"bottom-20\", \"left-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\"], [1, \"grid\", \"grid-cols-2\", \"gap-3\"], [1, \"flex\", \"flex-col\", \"items-center\", \"gap-2\", \"p-4\", \"rounded-xl\", \"hover:bg-gray-50\", \"dark:hover:bg-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"w-12\", \"h-12\", \"bg-blue-100\", \"dark:bg-blue-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-image\", \"text-blue-600\", \"dark:text-blue-400\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\", \"dark:text-gray-300\"], [1, \"w-12\", \"h-12\", \"bg-purple-100\", \"dark:bg-purple-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-video\", \"text-purple-600\", \"dark:text-purple-400\"], [1, \"w-12\", \"h-12\", \"bg-orange-100\", \"dark:bg-orange-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-file\", \"text-orange-600\", \"dark:text-orange-400\"], [1, \"w-12\", \"h-12\", \"bg-green-100\", \"dark:bg-green-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-camera\", \"text-green-600\", \"dark:text-green-400\"], [1, \"fixed\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\", \"p-3\"], [\"class\", \"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-xl\", 3, \"click\"], [1, \"fixed\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\", \"min-w-48\"], [1, \"fas\", \"fa-reply\", \"text-blue-500\"], [1, \"fas\", \"fa-share\", \"text-green-500\"], [1, \"fas\", \"fa-smile\", \"text-yellow-500\"], [1, \"w-full\", \"flex\", \"items-center\", \"gap-3\", \"px-3\", \"py-2\", \"rounded-lg\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-left\", \"text-red-600\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"text-red-500\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-25\", \"z-40\", 3, \"click\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-95\", \"z-50\", \"flex\", \"items-center\", \"justify-center\", 3, \"click\"], [1, \"relative\", \"max-w-full\", \"max-h-full\", \"p-4\"], [\"title\", \"Fermer\", 1, \"absolute\", \"top-4\", \"right-4\", \"z-10\", \"p-2\", \"rounded-full\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"hover:bg-opacity-70\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-xl\"], [\"title\", \"T\\u00E9l\\u00E9charger\", 1, \"absolute\", \"top-4\", \"left-4\", \"z-10\", \"p-2\", \"rounded-full\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"hover:bg-opacity-70\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-download\", \"text-xl\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", \"rounded-lg\", \"shadow-2xl\", \"image-viewer-zoom\", 2, \"max-height\", \"90vh\", \"max-width\", \"90vw\", 3, \"src\", \"alt\", \"click\"], [1, \"absolute\", \"bottom-4\", \"left-4\", \"right-4\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"p-3\", \"rounded-lg\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"font-medium\"], [1, \"text-sm\", \"opacity-75\"], [\"title\", \"Zoom +\", 1, \"p-2\", \"rounded-full\", \"bg-white\", \"bg-opacity-20\", \"hover:bg-opacity-30\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-search-plus\"], [\"title\", \"Zoom -\", 1, \"p-2\", \"rounded-full\", \"bg-white\", \"bg-opacity-20\", \"hover:bg-opacity-30\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-search-minus\"], [\"title\", \"Taille originale\", 1, \"p-2\", \"rounded-full\", \"bg-white\", \"bg-opacity-20\", \"hover:bg-opacity-30\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-expand-arrows-alt\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_2_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"img\", 6);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_img_click_6_listener() {\n            return ctx.openUserProfile(ctx.otherParticipant == null ? null : ctx.otherParticipant.id);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, MessageChatComponent_div_7_Template, 1, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"h3\", 9);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10);\n          i0.ɵɵtemplate(12, MessageChatComponent_div_12_Template, 7, 0, \"div\", 11);\n          i0.ɵɵtemplate(13, MessageChatComponent_span_13_Template, 2, 1, \"span\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_15_listener() {\n            return ctx.startVideoCall();\n          });\n          i0.ɵɵelement(16, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_17_listener() {\n            return ctx.startVoiceCall();\n          });\n          i0.ɵɵelement(18, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_19_listener() {\n            return ctx.toggleSearch();\n          });\n          i0.ɵɵelement(20, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_21_listener() {\n            return ctx.toggleTheme();\n          });\n          i0.ɵɵtemplate(22, MessageChatComponent_i_22_Template, 1, 0, \"i\", 21);\n          i0.ɵɵtemplate(23, MessageChatComponent_i_23_Template, 1, 0, \"i\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_24_listener() {\n            return ctx.toggleMainMenu();\n          });\n          i0.ɵɵelement(25, \"i\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_26_listener() {\n            return ctx.testAddMessage();\n          });\n          i0.ɵɵelement(27, \"i\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(28, MessageChatComponent_div_28_Template, 26, 12, \"div\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"main\", 28, 29);\n          i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_main_scroll_29_listener($event) {\n            return ctx.onScroll($event);\n          })(\"dragover\", function MessageChatComponent_Template_main_dragover_29_listener($event) {\n            return ctx.onDragOver($event);\n          })(\"dragleave\", function MessageChatComponent_Template_main_dragleave_29_listener($event) {\n            return ctx.onDragLeave($event);\n          })(\"drop\", function MessageChatComponent_Template_main_drop_29_listener($event) {\n            return ctx.onDrop($event);\n          });\n          i0.ɵɵtemplate(31, MessageChatComponent_div_31_Template, 11, 0, \"div\", 30);\n          i0.ɵɵtemplate(32, MessageChatComponent_div_32_Template, 4, 0, \"div\", 31);\n          i0.ɵɵtemplate(33, MessageChatComponent_div_33_Template, 7, 1, \"div\", 32);\n          i0.ɵɵtemplate(34, MessageChatComponent_div_34_Template, 3, 3, \"div\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(35, MessageChatComponent_div_35_Template, 12, 3, \"div\", 34);\n          i0.ɵɵelementStart(36, \"footer\", 35)(37, \"form\", 36);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_37_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(38, \"div\", 37)(39, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_39_listener() {\n            return ctx.toggleEmojiPicker();\n          });\n          i0.ɵɵelement(40, \"i\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_41_listener() {\n            return ctx.toggleAttachmentMenu();\n          });\n          i0.ɵɵelement(42, \"i\", 41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\", 42)(44, \"textarea\", 43, 44);\n          i0.ɵɵlistener(\"input\", function MessageChatComponent_Template_textarea_input_44_listener($event) {\n            return ctx.onInputChange($event);\n          })(\"keydown\", function MessageChatComponent_Template_textarea_keydown_44_listener($event) {\n            return ctx.onInputKeyDown($event);\n          })(\"focus\", function MessageChatComponent_Template_textarea_focus_44_listener() {\n            return ctx.onInputFocus();\n          })(\"blur\", function MessageChatComponent_Template_textarea_blur_44_listener() {\n            return ctx.onInputBlur();\n          });\n          i0.ɵɵtext(46, \"        \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"div\", 37);\n          i0.ɵɵtemplate(48, MessageChatComponent_button_48_Template, 3, 7, \"button\", 45);\n          i0.ɵɵtemplate(49, MessageChatComponent_button_49_Template, 3, 3, \"button\", 46);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(50, MessageChatComponent_div_50_Template, 23, 5, \"div\", 47);\n          i0.ɵɵtemplate(51, MessageChatComponent_div_51_Template, 6, 2, \"div\", 48);\n          i0.ɵɵtemplate(52, MessageChatComponent_div_52_Template, 23, 0, \"div\", 49);\n          i0.ɵɵelementStart(53, \"input\", 50, 51);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_53_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(55, MessageChatComponent_div_55_Template, 3, 6, \"div\", 52);\n          i0.ɵɵtemplate(56, MessageChatComponent_div_56_Template, 19, 4, \"div\", 53);\n          i0.ɵɵtemplate(57, MessageChatComponent_div_57_Template, 1, 0, \"div\", 54);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"app-call-interface\", 55);\n          i0.ɵɵlistener(\"callEnded\", function MessageChatComponent_Template_app_call_interface_callEnded_58_listener() {\n            return ctx.endCall();\n          })(\"callAccepted\", function MessageChatComponent_Template_app_call_interface_callAccepted_58_listener($event) {\n            return ctx.onCallAccepted($event);\n          })(\"callRejected\", function MessageChatComponent_Template_app_call_interface_callRejected_58_listener() {\n            return ctx.onCallRejected();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(59, MessageChatComponent_div_59_Template, 21, 4, \"div\", 56);\n        }\n        if (rf & 2) {\n          let tmp_26_0;\n          let tmp_27_0;\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.otherParticipant == null ? null : ctx.otherParticipant.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.otherParticipant == null ? null : ctx.otherParticipant.username) || \"Utilisateur\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUserTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUserTyping);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.searchMode)(\"text-green-600\", ctx.searchMode);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isDarkMode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDarkMode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.showMainMenu)(\"text-green-600\", ctx.showMainMenu);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMainMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"drag-over\", ctx.isDragOver);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDragOver);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUploading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.showEmojiPicker)(\"text-green-600\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.showAttachmentMenu)(\"text-green-600\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"opacity-50\", ctx.isInputDisabled())(\"cursor-not-allowed\", ctx.isInputDisabled());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !((tmp_26_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_26_0.value == null ? null : tmp_26_0.value.trim()));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_27_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_27_0.value == null ? null : tmp_27_0.value.trim());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"accept\", ctx.getFileAcceptTypes());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showReactionPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessageContextMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker || ctx.showAttachmentMenu || ctx.showMainMenu || ctx.showMessageContextMenu || ctx.showReactionPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"isVisible\", ctx.isInCall)(\"activeCall\", ctx.activeCall)(\"callType\", ctx.callType)(\"otherParticipant\", ctx.otherParticipant);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showImageViewer);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i6.CallInterfaceComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "CallType", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "otherParticipant", "isOnline", "formatLastActive", "lastActive", "ɵɵlistener", "MessageChatComponent_div_28_div_20_button_2_Template_button_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r28", "theme_r25", "$implicit", "ctx_r27", "ɵɵnextContext", "ɵɵresetView", "selectTheme", "id", "ɵɵtemplate", "MessageChatComponent_div_28_div_20_button_2_i_4_Template", "ɵɵclassProp", "ctx_r24", "currentTheme", "ɵɵclassMap", "icon", "ɵɵstyleProp", "color", "ɵɵtextInterpolate", "name", "ɵɵproperty", "MessageChatComponent_div_28_div_20_button_2_Template", "ctx_r23", "themes", "MessageChatComponent_div_28_Template_button_click_2_listener", "_r30", "ctx_r29", "toggleSearch", "showMainMenu", "MessageChatComponent_div_28_Template_button_click_15_listener", "ctx_r31", "toggleThemeSelector", "MessageChatComponent_div_28_div_20_Template", "ctx_r5", "showThemeSelector", "tmp_2_0", "getCurrentTheme", "tmp_3_0", "tmp_4_0", "ctx_r9", "username", "ctx_r36", "formatDateSeparator", "message_r34", "timestamp", "MessageChatComponent_div_34_ng_container_1_div_3_Template_img_click_1_listener", "_r48", "ctx_r46", "openUserProfile", "sender", "image", "ɵɵsanitizeUrl", "ctx_r38", "getUserColor", "ctx_r39", "formatMessageContent", "content", "ɵɵsanitizeHtml", "ctx_r52", "currentUserId", "isDarkMode", "MessageChatComponent_div_34_ng_container_1_div_7_Template_img_click_1_listener", "_r56", "ctx_r54", "openImageViewer", "MessageChatComponent_div_34_ng_container_1_div_7_Template_img_load_1_listener", "$event", "ctx_r57", "onImageLoad", "MessageChatComponent_div_34_ng_container_1_div_7_Template_img_error_1_listener", "ctx_r59", "onImageError", "MessageChatComponent_div_34_ng_container_1_div_7_div_2_Template", "ctx_r40", "getImageUrl", "MessageChatComponent_div_34_ng_container_1_div_8_div_5_Template_div_click_0_listener", "_r69", "i_r66", "index", "ctx_r67", "stopPropagation", "seekVoiceMessage", "wave_r65", "ctx_r62", "isVoicePlaying", "getVoiceProgress", "ctx_r63", "getVoicePlaybackData", "speed", "MessageChatComponent_div_34_ng_container_1_div_8_Template_button_click_1_listener", "_r74", "ctx_r72", "toggleVoicePlayback", "MessageChatComponent_div_34_ng_container_1_div_8_div_5_Template", "MessageChatComponent_div_34_ng_container_1_div_8_span_15_Template", "MessageChatComponent_div_34_ng_container_1_div_8_div_16_Template", "MessageChatComponent_div_34_ng_container_1_div_8_Template_button_click_18_listener", "ctx_r75", "toggleVoiceSpeed", "ctx_r41", "getVoiceWaves", "getVoiceCurrentTime", "getVoiceDuration", "MessageChatComponent_div_34_ng_container_1_div_9_Template_div_click_0_listener", "_r80", "ctx_r78", "downloadFile", "ctx_r42", "getFileIcon", "getFileName", "getFileSize", "MessageChatComponent_div_34_ng_container_1_div_13_i_1_Template", "MessageChatComponent_div_34_ng_container_1_div_13_i_2_Template", "MessageChatComponent_div_34_ng_container_1_div_13_i_3_Template", "MessageChatComponent_div_34_ng_container_1_div_13_i_4_Template", "status", "MessageChatComponent_div_34_ng_container_1_div_14_button_1_Template_button_click_0_listener", "_r91", "reaction_r88", "ctx_r89", "toggleReaction", "emoji", "ctx_r87", "hasUserReacted", "count", "MessageChatComponent_div_34_ng_container_1_div_14_button_1_Template", "reactions", "ɵɵelementContainerStart", "MessageChatComponent_div_34_ng_container_1_div_1_Template", "MessageChatComponent_div_34_ng_container_1_Template_div_click_2_listener", "_r94", "ctx_r93", "onMessageClick", "MessageChatComponent_div_34_ng_container_1_Template_div_contextmenu_2_listener", "ctx_r95", "onMessageContextMenu", "MessageChatComponent_div_34_ng_container_1_div_3_Template", "MessageChatComponent_div_34_ng_container_1_div_5_Template", "MessageChatComponent_div_34_ng_container_1_div_6_Template", "MessageChatComponent_div_34_ng_container_1_div_7_Template", "MessageChatComponent_div_34_ng_container_1_div_8_Template", "MessageChatComponent_div_34_ng_container_1_div_9_Template", "MessageChatComponent_div_34_ng_container_1_div_13_Template", "MessageChatComponent_div_34_ng_container_1_div_14_Template", "MessageChatComponent_div_34_ng_container_1_Template_button_click_15_listener", "ctx_r96", "showQuickReactions", "ɵɵelementContainerEnd", "ctx_r32", "shouldShowDateSeparator", "i_r35", "shouldShowAvatar", "isGroupConversation", "shouldShowSenderName", "getMessageType", "hasImage", "hasFile", "formatMessageTime", "length", "ctx_r33", "MessageChatComponent_div_34_ng_container_1_Template", "MessageChatComponent_div_34_div_2_Template", "ctx_r10", "messages", "trackByMessageId", "otherUserIsTyping", "MessageChatComponent_div_35_Template_button_click_10_listener", "_r98", "ctx_r97", "resetUploadState", "ctx_r11", "uploadProgress", "toFixed", "MessageChatComponent_button_48_Template_button_mousedown_0_listener", "_r102", "ctx_r101", "onRecordStart", "MessageChatComponent_button_48_Template_button_mouseup_0_listener", "ctx_r103", "onRecordEnd", "MessageChatComponent_button_48_Template_button_mouseleave_0_listener", "ctx_r104", "onRecordCancel", "MessageChatComponent_button_48_Template_button_touchstart_0_listener", "ctx_r105", "MessageChatComponent_button_48_Template_button_touchend_0_listener", "ctx_r106", "MessageChatComponent_button_48_Template_button_touchcancel_0_listener", "ctx_r107", "MessageChatComponent_button_48_i_1_Template", "MessageChatComponent_button_48_i_2_Template", "ɵɵpureFunction2", "_c2", "ctx_r13", "isRecordingVoice", "voiceRecordingState", "MessageChatComponent_button_49_Template_button_click_0_listener", "_r111", "ctx_r110", "sendMessage", "MessageChatComponent_button_49_i_1_Template", "MessageChatComponent_button_49_i_2_Template", "ctx_r14", "isSendingMessage", "wave_r113", "i_r114", "MessageChatComponent_div_50_Template_button_click_2_listener", "_r116", "ctx_r115", "cancelVoiceRecording", "MessageChatComponent_div_50_div_9_Template", "MessageChatComponent_div_50_Template_button_click_12_listener", "ctx_r117", "stopVoiceRecording", "ctx_r15", "voiceWaves", "formatRecordingDuration", "voiceRecordingDuration", "getRecordingFormat", "MessageChatComponent_div_51_button_3_Template_button_click_0_listener", "_r122", "category_r120", "ctx_r121", "selectEmojiCategory", "ctx_r118", "selectedEmojiCategory", "MessageChatComponent_div_51_button_5_Template_button_click_0_listener", "_r125", "emoji_r123", "ctx_r124", "insert<PERSON><PERSON><PERSON>", "MessageChatComponent_div_51_button_3_Template", "MessageChatComponent_div_51_button_5_Template", "ctx_r16", "emojiCategories", "getEmojisForCategory", "MessageChatComponent_div_52_Template_button_click_3_listener", "_r127", "ctx_r126", "triggerFileInput", "MessageChatComponent_div_52_Template_button_click_8_listener", "ctx_r128", "MessageChatComponent_div_52_Template_button_click_13_listener", "ctx_r129", "MessageChatComponent_div_52_Template_button_click_18_listener", "ctx_r130", "openCamera", "MessageChatComponent_div_55_button_2_Template_button_click_0_listener", "_r134", "emoji_r132", "ctx_r133", "quickReact", "MessageChatComponent_div_55_button_2_Template", "ctx_r19", "contextMenuPosition", "x", "y", "ɵɵpureFunction0", "_c3", "MessageChatComponent_div_56_Template_button_click_2_listener", "_r136", "ctx_r135", "replyToMessage", "selectedMessage", "MessageChatComponent_div_56_Template_button_click_6_listener", "ctx_r137", "forwardMessage", "MessageChatComponent_div_56_Template_button_click_10_listener", "ctx_r138", "MessageChatComponent_div_56_Template_button_click_15_listener", "ctx_r139", "deleteMessage", "ctx_r20", "MessageChatComponent_div_57_Template_div_click_0_listener", "_r141", "ctx_r140", "closeAllMenus", "MessageChatComponent_div_59_Template_div_click_0_listener", "_r143", "ctx_r142", "closeImageViewer", "MessageChatComponent_div_59_Template_button_click_2_listener", "ctx_r144", "MessageChatComponent_div_59_Template_button_click_4_listener", "ctx_r145", "downloadImage", "MessageChatComponent_div_59_Template_img_click_6_listener", "MessageChatComponent_div_59_Template_div_click_7_listener", "MessageChatComponent_div_59_Template_button_click_15_listener", "ctx_r148", "zoomImage", "MessageChatComponent_div_59_Template_button_click_17_listener", "ctx_r149", "MessageChatComponent_div_59_Template_button_click_19_listener", "ctx_r150", "resetZoom", "ctx_r22", "selectedImage", "url", "size", "MessageChatComponent", "constructor", "fb", "route", "MessageService", "toastService", "cdr", "conversation", "currentUsername", "isLoading", "isLoadingMore", "hasMoreMessages", "showEmojiPicker", "showAttachmentMenu", "showSearch", "searchQuery", "searchResults", "searchMode", "showMessageContextMenu", "showReactionPicker", "reactionPickerMessage", "showImageViewer", "isUploading", "isDragOver", "mediaRecorder", "audioChunks", "recordingTimer", "currentAudio", "playingMessageId", "voicePlayback", "isInCall", "callType", "callDuration", "callTimer", "activeCall", "isCallConnected", "isMuted", "isVideoEnabled", "localVideoElement", "remoteVideoElement", "emojis", "MAX_MESSAGES_TO_LOAD", "currentPage", "isTyping", "isUserTyping", "typingTimeout", "subscriptions", "messageForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "isInputDisabled", "updateInputState", "contentControl", "get", "disable", "enable", "ngOnInit", "console", "log", "initializeTheme", "initializeComponent", "savedTheme", "localStorage", "getItem", "applyTheme", "themeId", "html", "document", "documentElement", "classList", "remove", "add", "find", "t", "setItem", "themeName", "showSuccess", "loadCurrentUser", "loadConversation", "setupCallSubscriptions", "incomingCall$", "subscribe", "next", "incomingCall", "handleIncomingCall", "error", "activeCall$", "call", "caller", "play", "userString", "user", "JSON", "parse", "userId", "_id", "extracted", "conversationId", "snapshot", "paramMap", "showError", "getConversation", "participants", "participantsCount", "isGroup", "messagesCount", "setOtherParticipant", "loadMessages", "setupSubscriptions", "warn", "p", "participantId", "String", "firstParticipantId", "sort", "a", "b", "dateA", "Date", "createdAt", "getTime", "dateB", "total", "first", "last", "scrollToBottom", "loadMoreMessages", "offset", "getMessages", "newMessages", "reverse", "subscribeToNewMessages", "newMessage", "type", "senderId", "receiverId", "attachments", "for<PERSON>ach", "att", "path", "messageExists", "some", "msg", "push", "detectChanges", "setTimeout", "shouldMarkAsRead", "markMessageAsRead", "subscribeToTypingIndicator", "typingData", "subscribeToConversationUpdates", "conversationUpdate", "messageId", "valid", "value", "trim", "undefined", "message", "reset", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "diffMins", "Math", "floor", "now", "progress", "duration", "currentTime", "setVoicePlaybackData", "data", "startVideoCall", "startVoiceCall", "endCall", "formatFileSize", "bytes", "round", "fileAttachment", "startsWith", "link", "createElement", "href", "download", "target", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "toggleTheme", "newTheme", "toggleMainMenu", "goBackToConversations", "event", "preventDefault", "clientX", "clientY", "reaction", "toggleEmojiPicker", "category", "currentC<PERSON>nt", "newContent", "patchValue", "toggleAttachmentMenu", "toString", "testAddMessage", "testMessage", "toLocaleTimeString", "toISOString", "isRead", "factor", "imageElement", "querySelector", "currentTransform", "style", "transform", "currentScale", "parseFloat", "match", "newScale", "max", "min", "input", "fileInput", "accept", "date", "hour", "minute", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "urlRegex", "replace", "currentMessage", "previousMessage", "currentDate", "previousDate", "nextMessage", "attachment", "voiceUrl", "audioUrl", "voice", "hasImageAttachment", "hasImageUrl", "imageUrl", "hasFileAttachment", "imageAttachment", "includes", "colors", "charCodeAt", "onInputChange", "handleTypingIndicator", "onInputKeyDown", "key", "shift<PERSON>ey", "onInputFocus", "onInputBlur", "onScroll", "src", "searchMessages", "filter", "toLowerCase", "onSearchQueryChange", "clearSearch", "jumpToMessage", "messageElement", "getElementById", "scrollIntoView", "behavior", "block", "closeContextMenu", "initiateCall", "recipientId", "VIDEO", "startCallTimer", "acceptCall", "rejectCall", "setInterval", "resetCallState", "clearInterval", "toggleMute", "toggleMedia", "toggleVideo", "formatCallDuration", "hours", "minutes", "seconds", "padStart", "startVoiceRecording", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "Error", "window", "MediaRecorder", "stream", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "channelCount", "mimeType", "isTypeSupported", "animateVoice<PERSON>aves", "ondataavailable", "onstop", "processRecordedAudio", "onerror", "start", "errorMessage", "state", "stop", "getTracks", "track", "_this2", "audioBlob", "Blob", "extension", "audioFile", "File", "sendVoiceMessage", "_this3", "Promise", "resolve", "reject", "showWarning", "showInfo", "catch", "map", "random", "onFileSelected", "files", "file", "uploadFile", "maxSize", "compressImage", "then", "compressedFile", "sendFileToServer", "messageType", "getFileMessageType", "progressInterval", "getFileAcceptTypes", "onDragOver", "onDragLeave", "rect", "currentTarget", "getBoundingClientRect", "left", "right", "top", "bottom", "onDrop", "dataTransfer", "Array", "from", "quality", "canvas", "ctx", "getContext", "img", "Image", "onload", "max<PERSON><PERSON><PERSON>", "maxHeight", "width", "height", "ratio", "drawImage", "toBlob", "blob", "lastModified", "URL", "createObjectURL", "sendTypingIndicator", "clearTimeout", "onCallAccepted", "onCallRejected", "playVoiceMessage", "getVoiceUrl", "stopVoicePlayback", "startVoicePlayback", "Audio", "currentData", "playbackRate", "addEventListener", "pause", "audioAttachment", "seed", "split", "reduce", "acc", "char", "waves", "i", "totalWaves", "formatAudioTime", "metadata", "remainingSeconds", "waveIndex", "seekPercentage", "seekTime", "newSpeed", "ngOnDestroy", "unsubscribe", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "i3", "i4", "ToastService", "ChangeDetectorRef", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "MessageChatComponent_Template_button_click_2_listener", "MessageChatComponent_Template_img_click_6_listener", "MessageChatComponent_div_7_Template", "MessageChatComponent_div_12_Template", "MessageChatComponent_span_13_Template", "MessageChatComponent_Template_button_click_15_listener", "MessageChatComponent_Template_button_click_17_listener", "MessageChatComponent_Template_button_click_19_listener", "MessageChatComponent_Template_button_click_21_listener", "MessageChatComponent_i_22_Template", "MessageChatComponent_i_23_Template", "MessageChatComponent_Template_button_click_24_listener", "MessageChatComponent_Template_button_click_26_listener", "MessageChatComponent_div_28_Template", "MessageChatComponent_Template_main_scroll_29_listener", "MessageChatComponent_Template_main_dragover_29_listener", "MessageChatComponent_Template_main_dragleave_29_listener", "MessageChatComponent_Template_main_drop_29_listener", "MessageChatComponent_div_31_Template", "MessageChatComponent_div_32_Template", "MessageChatComponent_div_33_Template", "MessageChatComponent_div_34_Template", "MessageChatComponent_div_35_Template", "MessageChatComponent_Template_form_ngSubmit_37_listener", "MessageChatComponent_Template_button_click_39_listener", "MessageChatComponent_Template_button_click_41_listener", "MessageChatComponent_Template_textarea_input_44_listener", "MessageChatComponent_Template_textarea_keydown_44_listener", "MessageChatComponent_Template_textarea_focus_44_listener", "MessageChatComponent_Template_textarea_blur_44_listener", "MessageChatComponent_button_48_Template", "MessageChatComponent_button_49_Template", "MessageChatComponent_div_50_Template", "MessageChatComponent_div_51_Template", "MessageChatComponent_div_52_Template", "MessageChatComponent_Template_input_change_53_listener", "MessageChatComponent_div_55_Template", "MessageChatComponent_div_56_Template", "MessageChatComponent_div_57_Template", "MessageChatComponent_Template_app_call_interface_callEnded_58_listener", "MessageChatComponent_Template_app_call_interface_callAccepted_58_listener", "MessageChatComponent_Template_app_call_interface_callRejected_58_listener", "MessageChatComponent_div_59_Template", "tmp_26_0", "tmp_27_0"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON>Child,\n  ElementRef,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { MessageService } from '../../../../services/message.service';\nimport { ToastService } from '../../../../services/toast.service';\nimport { CallType, Call, IncomingCall } from '../../../../models/message.model';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: './message-chat.component.html',\n})\nexport class MessageChatComponent implements OnInit, OnDestroy {\n  // === RÉFÉRENCES DOM ===\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  // === DONNÉES PRINCIPALES ===\n  conversation: any = null;\n  messages: any[] = [];\n  currentUserId: string | null = null;\n  currentUsername = 'You';\n  messageForm: FormGroup;\n  otherParticipant: any = null;\n\n  // === ÉTATS DE L'INTERFACE ===\n  isLoading = false;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  showEmojiPicker = false;\n  showAttachmentMenu = false;\n  showSearch = false;\n  searchQuery = '';\n  searchResults: any[] = [];\n  searchMode = false;\n  isSendingMessage = false;\n  otherUserIsTyping = false;\n  showMainMenu = false;\n  showMessageContextMenu = false;\n  selectedMessage: any = null;\n  contextMenuPosition = { x: 0, y: 0 };\n  showReactionPicker = false;\n  reactionPickerMessage: any = null;\n  isDarkMode = false;\n  currentTheme = 'light'; // 'light', 'dark', 'blue', 'pink'\n  themes = [\n    { id: 'light', name: 'Clair', icon: 'fas fa-sun', color: '#f59e0b' },\n    { id: 'dark', name: 'Sombre', icon: 'fas fa-moon', color: '#6b7280' },\n    { id: 'blue', name: 'Bleu', icon: 'fas fa-water', color: '#3b82f6' },\n    { id: 'pink', name: 'Rose', icon: 'fas fa-heart', color: '#ec4899' },\n  ];\n  showThemeSelector = false;\n  showImageViewer = false;\n  selectedImage: any = null;\n  uploadProgress = 0;\n  isUploading = false;\n  isDragOver = false;\n\n  // === GESTION VOCALE OPTIMISÉE ===\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';\n  private mediaRecorder: MediaRecorder | null = null;\n  private audioChunks: Blob[] = [];\n  private recordingTimer: any = null;\n  voiceWaves: number[] = [\n    4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8,\n  ];\n\n  // Lecture des messages vocaux\n  private currentAudio: HTMLAudioElement | null = null;\n  private playingMessageId: string | null = null;\n  private voicePlayback: {\n    [messageId: string]: {\n      progress: number;\n      duration: number;\n      currentTime: number;\n      speed: number;\n    };\n  } = {};\n\n  // === APPELS WEBRTC ===\n  isInCall = false;\n  callType: 'VIDEO' | 'AUDIO' | null = null;\n  callDuration = 0;\n  private callTimer: any = null;\n\n  // État de l'appel WebRTC\n  activeCall: any = null;\n  isCallConnected = false;\n  isMuted = false;\n  isVideoEnabled = true;\n  localVideoElement: HTMLVideoElement | null = null;\n  remoteVideoElement: HTMLVideoElement | null = null;\n\n  // === ÉMOJIS ===\n  emojiCategories: any[] = [\n    {\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [\n        { emoji: '😀', name: 'grinning face' },\n        { emoji: '😃', name: 'grinning face with big eyes' },\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\n        { emoji: '😆', name: 'grinning squinting face' },\n        { emoji: '😅', name: 'grinning face with sweat' },\n        { emoji: '😂', name: 'face with tears of joy' },\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\n        { emoji: '😇', name: 'smiling face with halo' },\n      ],\n    },\n    {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [\n        { emoji: '👶', name: 'baby' },\n        { emoji: '🧒', name: 'child' },\n        { emoji: '👦', name: 'boy' },\n        { emoji: '👧', name: 'girl' },\n        { emoji: '🧑', name: 'person' },\n        { emoji: '👨', name: 'man' },\n        { emoji: '👩', name: 'woman' },\n        { emoji: '👴', name: 'old man' },\n        { emoji: '👵', name: 'old woman' },\n      ],\n    },\n    {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [\n        { emoji: '🐶', name: 'dog face' },\n        { emoji: '🐱', name: 'cat face' },\n        { emoji: '🐭', name: 'mouse face' },\n        { emoji: '🐹', name: 'hamster' },\n        { emoji: '🐰', name: 'rabbit face' },\n        { emoji: '🦊', name: 'fox' },\n        { emoji: '🐻', name: 'bear' },\n        { emoji: '🐼', name: 'panda' },\n      ],\n    },\n  ];\n  selectedEmojiCategory = this.emojiCategories[0];\n\n  // === PAGINATION ===\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private currentPage = 1;\n\n  // === AUTRES ÉTATS ===\n  isTyping = false;\n  isUserTyping = false;\n  private typingTimeout: any = null;\n  private subscriptions = new Subscription();\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private MessageService: MessageService,\n    private toastService: ToastService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]],\n    });\n  }\n\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\n  isInputDisabled(): boolean {\n    return (\n      !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage\n    );\n  }\n\n  // Méthode pour gérer l'état du contrôle de saisie\n  private updateInputState(): void {\n    const contentControl = this.messageForm.get('content');\n    if (this.isInputDisabled()) {\n      contentControl?.disable();\n    } else {\n      contentControl?.enable();\n    }\n  }\n\n  ngOnInit(): void {\n    console.log('🚀 MessageChatComponent initialized');\n    this.initializeTheme();\n    this.initializeComponent();\n  }\n\n  private initializeTheme(): void {\n    // Charger le thème sauvegardé ou utiliser 'light' par défaut\n    const savedTheme = localStorage.getItem('currentTheme') || 'light';\n    this.currentTheme = savedTheme;\n    this.isDarkMode = savedTheme === 'dark';\n    this.applyTheme(savedTheme);\n  }\n\n  // === GESTION DES THÈMES OPTIMISÉE ===\n\n  private applyTheme(themeId: string): void {\n    const html = document.documentElement;\n\n    // Supprimer toutes les classes de thème existantes\n    html.classList.remove(\n      'dark',\n      'theme-light',\n      'theme-dark',\n      'theme-blue',\n      'theme-pink'\n    );\n\n    // Appliquer le nouveau thème\n    html.classList.add(`theme-${themeId}`);\n    if (themeId === 'dark') {\n      html.classList.add('dark');\n    }\n  }\n\n  getCurrentTheme() {\n    return this.themes.find((t) => t.id === this.currentTheme);\n  }\n\n  toggleThemeSelector(): void {\n    this.showThemeSelector = !this.showThemeSelector;\n  }\n\n  selectTheme(themeId: string): void {\n    this.currentTheme = themeId;\n    this.isDarkMode = themeId === 'dark';\n    this.applyTheme(themeId);\n    localStorage.setItem('currentTheme', themeId);\n    this.showThemeSelector = false;\n    this.showMainMenu = false;\n\n    const themeName =\n      this.themes.find((t) => t.id === themeId)?.name || themeId;\n    this.toastService.showSuccess(`Thème ${themeName} appliqué`);\n  }\n\n  private initializeComponent(): void {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupCallSubscriptions();\n  }\n\n  private setupCallSubscriptions(): void {\n    // S'abonner aux appels entrants\n    this.subscriptions.add(\n      this.MessageService.incomingCall$.subscribe({\n        next: (incomingCall) => {\n          if (incomingCall) {\n            console.log('📞 Incoming call received:', incomingCall);\n            this.handleIncomingCall(incomingCall);\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in incoming call subscription:', error);\n        },\n      })\n    );\n\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(\n      this.MessageService.activeCall$.subscribe({\n        next: (call) => {\n          if (call) {\n            console.log('📞 Active call updated:', call);\n            this.activeCall = call;\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in active call subscription:', error);\n        },\n      })\n    );\n  }\n\n  private handleIncomingCall(incomingCall: IncomingCall): void {\n    // Afficher une notification ou modal d'appel entrant\n    // Pour l'instant, on log juste\n    console.log(\n      '🔔 Handling incoming call from:',\n      incomingCall.caller.username\n    );\n\n    // Jouer la sonnerie\n    this.MessageService.play('ringtone');\n\n    // Ici on pourrait afficher une modal ou notification\n    // Pour l'instant, on accepte automatiquement pour tester\n    // this.acceptCall(incomingCall);\n  }\n\n  private loadCurrentUser(): void {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('🔍 Raw user from localStorage:', userString);\n\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n\n      const user = JSON.parse(userString);\n      console.log('🔍 Parsed user object:', user);\n\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      console.log('🔍 Trying to extract user ID:', {\n        _id: user._id,\n        id: user.id,\n        userId: user.userId,\n        extracted: userId,\n      });\n\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n        console.log('✅ Current user loaded successfully:', {\n          id: this.currentUserId,\n          username: this.currentUsername,\n        });\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n\n  private loadConversation(): void {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: (conversation) => {\n        console.log('🔍 Conversation loaded successfully:', conversation);\n        console.log('🔍 Conversation structure:', {\n          id: conversation?.id,\n          participants: conversation?.participants,\n          participantsCount: conversation?.participants?.length,\n          isGroup: conversation?.isGroup,\n          messages: conversation?.messages,\n          messagesCount: conversation?.messages?.length,\n        });\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n\n        // Configurer les subscriptions temps réel après le chargement de la conversation\n        this.setupSubscriptions();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError(\n          'Erreur lors du chargement de la conversation'\n        );\n        this.isLoading = false;\n      },\n    });\n  }\n\n  private setOtherParticipant(): void {\n    if (\n      !this.conversation?.participants ||\n      this.conversation.participants.length === 0\n    ) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n\n    console.log('Setting other participant...');\n    console.log('Current user ID:', this.currentUserId);\n    console.log('All participants:', this.conversation.participants);\n\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\n        const participantId = p.id || p._id;\n        console.log(\n          'Comparing participant ID:',\n          participantId,\n          'with current user ID:',\n          this.currentUserId\n        );\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      console.log('Fallback: using first participant');\n      this.otherParticipant = this.conversation.participants[0];\n\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId =\n          this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log(\n            'First participant is current user, using second participant'\n          );\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      console.log('✅ Other participant set successfully:', {\n        id: this.otherParticipant.id || this.otherParticipant._id,\n        username: this.otherParticipant.username,\n        image: this.otherParticipant.image,\n        isOnline: this.otherParticipant.isOnline,\n      });\n\n      // Log très visible pour debug\n      console.log(\n        '🎯 FINAL RESULT: otherParticipant =',\n        this.otherParticipant.username\n      );\n      console.log(\n        '🎯 Should display in sidebar:',\n        this.otherParticipant.username\n      );\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      console.log('Conversation participants:', this.conversation.participants);\n      console.log('Current user ID:', this.currentUserId);\n\n      // Log très visible pour debug\n      console.log('🚨 ERROR: No otherParticipant found!');\n    }\n\n    // Mettre à jour l'état du champ de saisie\n    this.updateInputState();\n  }\n\n  private loadMessages(): void {\n    if (!this.conversation?.id) return;\n\n    // Les messages sont déjà chargés avec la conversation\n    let messages = this.conversation.messages || [];\n\n    // Trier les messages par timestamp (plus anciens en premier)\n    this.messages = messages.sort((a: any, b: any) => {\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\n    });\n\n    console.log('📋 Messages loaded and sorted:', {\n      total: this.messages.length,\n      first: this.messages[0]?.content,\n      last: this.messages[this.messages.length - 1]?.content,\n    });\n\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id)\n      return;\n\n    this.isLoadingMore = true;\n    this.currentPage++;\n\n    // Calculer l'offset basé sur les messages déjà chargés\n    const offset = this.messages.length;\n\n    this.MessageService.getMessages(\n      this.currentUserId!, // senderId\n      this.otherParticipant?.id || this.otherParticipant?._id!, // receiverId\n      this.conversation.id,\n      this.currentPage,\n      this.MAX_MESSAGES_TO_LOAD\n    ).subscribe({\n      next: (newMessages: any[]) => {\n        if (newMessages && newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste\n          this.messages = [...newMessages.reverse(), ...this.messages];\n          this.hasMoreMessages =\n            newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      },\n    });\n  }\n\n  private setupSubscriptions(): void {\n    if (!this.conversation?.id) {\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\n      return;\n    }\n\n    console.log(\n      '🔄 Setting up real-time subscriptions for conversation:',\n      this.conversation.id\n    );\n\n    // Subscription pour les nouveaux messages\n    console.log('📨 Setting up message subscription...');\n    this.subscriptions.add(\n      this.MessageService.subscribeToNewMessages(\n        this.conversation.id\n      ).subscribe({\n        next: (newMessage: any) => {\n          console.log('📨 New message received via subscription:', newMessage);\n          console.log('📨 Message structure:', {\n            id: newMessage.id,\n            type: newMessage.type,\n            content: newMessage.content,\n            sender: newMessage.sender,\n            senderId: newMessage.senderId,\n            receiverId: newMessage.receiverId,\n            attachments: newMessage.attachments,\n          });\n\n          // Debug des attachments\n          console.log(\n            '📨 [Debug] Message type detected:',\n            this.getMessageType(newMessage)\n          );\n          console.log('📨 [Debug] Has image:', this.hasImage(newMessage));\n          console.log('📨 [Debug] Has file:', this.hasFile(newMessage));\n          console.log('📨 [Debug] Image URL:', this.getImageUrl(newMessage));\n          if (newMessage.attachments) {\n            newMessage.attachments.forEach((att: any, index: number) => {\n              console.log(`📨 [Debug] Attachment ${index}:`, {\n                type: att.type,\n                url: att.url,\n                path: att.path,\n                name: att.name,\n                size: att.size,\n              });\n            });\n          }\n\n          // Ajouter le message à la liste s'il n'existe pas déjà\n          const messageExists = this.messages.some(\n            (msg) => msg.id === newMessage.id\n          );\n          if (!messageExists) {\n            // Ajouter le nouveau message à la fin (en bas)\n            this.messages.push(newMessage);\n            console.log(\n              '✅ Message added to list, total messages:',\n              this.messages.length\n            );\n\n            // Forcer la détection de changements\n            this.cdr.detectChanges();\n\n            // Scroll vers le bas après un court délai\n            setTimeout(() => {\n              this.scrollToBottom();\n            }, 50);\n\n            // Marquer comme lu si ce n'est pas notre message\n            const senderId = newMessage.sender?.id || newMessage.senderId;\n            console.log('📨 Checking if message should be marked as read:', {\n              senderId,\n              currentUserId: this.currentUserId,\n              shouldMarkAsRead: senderId !== this.currentUserId,\n            });\n\n            if (senderId && senderId !== this.currentUserId) {\n              this.markMessageAsRead(newMessage.id);\n            }\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in message subscription:', error);\n        },\n      })\n    );\n\n    // Subscription pour les indicateurs de frappe\n    console.log('📝 Setting up typing indicator subscription...');\n    this.subscriptions.add(\n      this.MessageService.subscribeToTypingIndicator(\n        this.conversation.id\n      ).subscribe({\n        next: (typingData: any) => {\n          console.log('📝 Typing indicator received:', typingData);\n\n          // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n          if (typingData.userId !== this.currentUserId) {\n            this.otherUserIsTyping = typingData.isTyping;\n            this.cdr.detectChanges();\n          }\n        },\n        error: (error: any) => {\n          console.error('❌ Error in typing subscription:', error);\n        },\n      })\n    );\n\n    // Subscription pour les mises à jour de conversation\n    this.subscriptions.add(\n      this.MessageService.subscribeToConversationUpdates(\n        this.conversation.id\n      ).subscribe({\n        next: (conversationUpdate: any) => {\n          console.log('📋 Conversation update:', conversationUpdate);\n\n          // Mettre à jour la conversation si nécessaire\n          if (conversationUpdate.id === this.conversation.id) {\n            this.conversation = { ...this.conversation, ...conversationUpdate };\n            this.cdr.detectChanges();\n          }\n        },\n        error: (error: any) => {\n          console.error('❌ Error in conversation subscription:', error);\n        },\n      })\n    );\n  }\n\n  private markMessageAsRead(messageId: string): void {\n    this.MessageService.markMessageAsRead(messageId).subscribe({\n      next: () => {\n        console.log('✅ Message marked as read:', messageId);\n      },\n      error: (error) => {\n        console.error('❌ Error marking message as read:', error);\n      },\n    });\n  }\n\n  // === ENVOI DE MESSAGES ===\n  sendMessage(): void {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    // Désactiver le bouton d'envoi\n    this.isSendingMessage = true;\n    this.updateInputState();\n\n    console.log('📤 Sending message:', {\n      content,\n      receiverId,\n      conversationId: this.conversation.id,\n    });\n\n    this.MessageService.sendMessage(\n      receiverId,\n      content,\n      undefined,\n      'TEXT' as any,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        console.log('✅ Message sent successfully:', message);\n\n        // Ajouter le message à la liste s'il n'y est pas déjà\n        const messageExists = this.messages.some(\n          (msg) => msg.id === message.id\n        );\n        if (!messageExists) {\n          this.messages.push(message);\n          console.log(\n            '📋 Message added to local list, total:',\n            this.messages.length\n          );\n        }\n\n        // Réinitialiser le formulaire\n        this.messageForm.reset();\n        this.isSendingMessage = false;\n        this.updateInputState();\n\n        // Forcer la détection de changements et scroll\n        this.cdr.detectChanges();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 50);\n      },\n      error: (error: any) => {\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        this.isSendingMessage = false;\n        this.updateInputState();\n      },\n    });\n  }\n\n  scrollToBottom(): void {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n  formatLastActive(lastActive: string | Date | null): string {\n    if (!lastActive) return 'Hors ligne';\n\n    const diffMins = Math.floor(\n      (Date.now() - new Date(lastActive).getTime()) / 60000\n    );\n\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\n  }\n\n  // Méthodes utilitaires pour les messages vocaux\n  getVoicePlaybackData(messageId: string) {\n    return (\n      this.voicePlayback[messageId] || {\n        progress: 0,\n        duration: 0,\n        currentTime: 0,\n        speed: 1,\n      }\n    );\n  }\n\n  private setVoicePlaybackData(\n    messageId: string,\n    data: Partial<(typeof this.voicePlayback)[string]>\n  ) {\n    this.voicePlayback[messageId] = {\n      ...this.getVoicePlaybackData(messageId),\n      ...data,\n    };\n  }\n\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // isVoicePlaying, playVoiceMessage, toggleVoicePlayback - définies plus loin\n\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n\n  startVideoCall(): void {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n\n    this.callType = 'VIDEO';\n    this.isInCall = true;\n    console.log('📹 Starting video call with:', this.otherParticipant.username);\n  }\n\n  startVoiceCall(): void {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n\n    this.callType = 'AUDIO';\n    this.isInCall = true;\n    console.log('📞 Starting voice call with:', this.otherParticipant.username);\n  }\n\n  endCall(): void {\n    this.isInCall = false;\n    this.callType = null;\n    this.activeCall = null;\n    console.log('📞 Call ended');\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // onCallAccepted, onCallRejected - définies plus loin\n\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n\n  formatFileSize(bytes: number): string {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n\n  downloadFile(message: any): void {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (fileAttachment?.url) {\n      const link = document.createElement('a');\n      link.href = fileAttachment.url;\n      link.download = fileAttachment.name || 'file';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n    }\n  }\n\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n\n  toggleTheme(): void {\n    const newTheme = this.isDarkMode ? 'light' : 'dark';\n    this.selectTheme(newTheme);\n  }\n\n  toggleSearch(): void {\n    this.searchMode = !this.searchMode;\n    this.showSearch = this.searchMode;\n  }\n\n  toggleMainMenu(): void {\n    this.showMainMenu = !this.showMainMenu;\n  }\n\n  goBackToConversations(): void {\n    // Navigation vers la liste des conversations\n    console.log('🔙 Going back to conversations');\n  }\n\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n\n  closeAllMenus(): void {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.showReactionPicker = false;\n    this.showThemeSelector = false;\n  }\n\n  onMessageContextMenu(message: any, event: MouseEvent): void {\n    event.preventDefault();\n    this.selectedMessage = message;\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\n    this.showMessageContextMenu = true;\n  }\n\n  showQuickReactions(message: any, event: MouseEvent): void {\n    event.stopPropagation();\n    this.reactionPickerMessage = message;\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\n    this.showReactionPicker = true;\n  }\n\n  quickReact(emoji: string): void {\n    if (this.reactionPickerMessage) {\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\n    }\n    this.showReactionPicker = false;\n  }\n\n  toggleReaction(messageId: string, emoji: string): void {\n    console.log('👍 Toggling reaction:', emoji, 'for message:', messageId);\n    // Implémentation de la réaction\n  }\n\n  hasUserReacted(reaction: any, userId: string): boolean {\n    return reaction.userId === userId;\n  }\n\n  replyToMessage(message: any): void {\n    console.log('↩️ Replying to message:', message.id);\n    this.closeAllMenus();\n  }\n\n  forwardMessage(message: any): void {\n    console.log('➡️ Forwarding message:', message.id);\n    this.closeAllMenus();\n  }\n\n  deleteMessage(message: any): void {\n    console.log('🗑️ Deleting message:', message.id);\n    this.closeAllMenus();\n  }\n\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n\n  toggleEmojiPicker(): void {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n\n  selectEmojiCategory(category: any): void {\n    this.selectedEmojiCategory = category;\n  }\n\n  getEmojisForCategory(category: any): any[] {\n    return category?.emojis || [];\n  }\n\n  insertEmoji(emoji: any): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({ content: newContent });\n    this.showEmojiPicker = false;\n  }\n\n  toggleAttachmentMenu(): void {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  // === MÉTHODES POUR LA GESTION DE LA FRAPPE ===\n\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // handleTypingIndicator - définie plus loin\n\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n\n  trackByMessageId(index: number, message: any): string {\n    return message.id || message._id || index.toString();\n  }\n\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n\n  testAddMessage(): void {\n    console.log('🧪 Test: Adding message');\n    const testMessage = {\n      id: `test-${Date.now()}`,\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\n      timestamp: new Date().toISOString(),\n      sender: {\n        id: this.otherParticipant?.id || 'test-user',\n        username: this.otherParticipant?.username || 'Test User',\n        image:\n          this.otherParticipant?.image || 'assets/images/default-avatar.png',\n      },\n      type: 'TEXT',\n      isRead: false,\n    };\n    this.messages.push(testMessage);\n    this.cdr.detectChanges();\n    setTimeout(() => this.scrollToBottom(), 50);\n  }\n\n  isGroupConversation(): boolean {\n    return (\n      this.conversation?.isGroup ||\n      this.conversation?.participants?.length > 2 ||\n      false\n    );\n  }\n\n  openCamera(): void {\n    console.log('📷 Opening camera');\n    this.showAttachmentMenu = false;\n    // TODO: Implémenter l'ouverture de la caméra\n  }\n\n  zoomImage(factor: number): void {\n    const imageElement = document.querySelector(\n      '.image-viewer-zoom'\n    ) as HTMLElement;\n    if (imageElement) {\n      const currentTransform = imageElement.style.transform || 'scale(1)';\n      const currentScale = parseFloat(\n        currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1'\n      );\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n      imageElement.style.transform = `scale(${newScale})`;\n      if (newScale > 1) {\n        imageElement.classList.add('zoomed');\n      } else {\n        imageElement.classList.remove('zoomed');\n      }\n    }\n  }\n\n  resetZoom(): void {\n    const imageElement = document.querySelector(\n      '.image-viewer-zoom'\n    ) as HTMLElement;\n    if (imageElement) {\n      imageElement.style.transform = 'scale(1)';\n      imageElement.classList.remove('zoomed');\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  triggerFileInput(type?: string): void {\n    const input = this.fileInput?.nativeElement;\n    if (!input) {\n      console.error('File input element not found');\n      return;\n    }\n\n    // Configurer le type de fichier accepté\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    } else {\n      input.accept = '*/*';\n    }\n\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\n    input.value = '';\n\n    // Déclencher la sélection de fichier\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n\n  formatMessageTime(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  }\n\n  formatDateSeparator(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n\n  formatMessageContent(content: string): string {\n    if (!content) return '';\n\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(\n      urlRegex,\n      '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>'\n    );\n  }\n\n  shouldShowDateSeparator(index: number): boolean {\n    if (index === 0) return true;\n\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n\n    return currentDate !== previousDate;\n  }\n\n  shouldShowAvatar(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n\n    if (!nextMessage) return true;\n\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n\n  shouldShowSenderName(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!previousMessage) return true;\n\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n\n  getMessageType(message: any): string {\n    // Vérifier d'abord le type de message explicite\n    if (message.type) {\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\n    }\n\n    // Ensuite vérifier les attachments\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n\n    // Vérifier si c'est un message vocal basé sur les propriétés\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n\n    return 'text';\n  }\n\n  hasImage(message: any): boolean {\n    // Vérifier le type de message\n    if (message.type === 'IMAGE' || message.type === 'image') {\n      return true;\n    }\n\n    // Vérifier les attachments\n    const hasImageAttachment =\n      message.attachments?.some((att: any) => {\n        return att.type?.startsWith('image/') || att.type === 'IMAGE';\n      }) || false;\n\n    // Vérifier les propriétés directes d'image\n    const hasImageUrl = !!(message.imageUrl || message.image);\n\n    return hasImageAttachment || hasImageUrl;\n  }\n\n  hasFile(message: any): boolean {\n    // Vérifier le type de message\n    if (message.type === 'FILE' || message.type === 'file') {\n      return true;\n    }\n\n    // Vérifier les attachments non-image\n    const hasFileAttachment =\n      message.attachments?.some((att: any) => {\n        return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n      }) || false;\n\n    return hasFileAttachment;\n  }\n\n  getImageUrl(message: any): string {\n    // Vérifier les propriétés directes d'image\n    if (message.imageUrl) {\n      return message.imageUrl;\n    }\n    if (message.image) {\n      return message.image;\n    }\n\n    // Vérifier les attachments\n    const imageAttachment = message.attachments?.find(\n      (att: any) => att.type?.startsWith('image/') || att.type === 'IMAGE'\n    );\n\n    if (imageAttachment) {\n      return imageAttachment.url || imageAttachment.path || '';\n    }\n\n    return '';\n  }\n\n  getFileName(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    return fileAttachment?.name || 'Fichier';\n  }\n\n  getFileSize(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.size) return '';\n\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n\n  getFileIcon(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.type) return 'fas fa-file';\n\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n\n  getUserColor(userId: string): string {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = [\n      '#FF6B6B',\n      '#4ECDC4',\n      '#45B7D1',\n      '#96CEB4',\n      '#FFEAA7',\n      '#DDA0DD',\n      '#98D8C8',\n    ];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message: any, event: any): void {\n    console.log('Message clicked:', message);\n  }\n\n  onInputChange(event: any): void {\n    // Gérer les changements dans le champ de saisie\n    this.handleTypingIndicator();\n  }\n\n  onInputKeyDown(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  onInputFocus(): void {\n    // Gérer le focus sur le champ de saisie\n  }\n\n  onInputBlur(): void {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n\n  onScroll(event: any): void {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (\n      element.scrollTop === 0 &&\n      this.hasMoreMessages &&\n      !this.isLoadingMore\n    ) {\n      this.loadMoreMessages();\n    }\n  }\n\n  openUserProfile(userId: string): void {\n    console.log('Opening user profile for:', userId);\n  }\n\n  onImageLoad(event: any, message: any): void {\n    console.log(\n      '🖼️ [Debug] Image loaded successfully for message:',\n      message.id,\n      event.target.src\n    );\n  }\n\n  onImageError(event: any, message: any): void {\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n      src: event.target.src,\n      error: event,\n    });\n    // Optionnel : afficher une image de remplacement\n    event.target.src =\n      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n  }\n\n  openImageViewer(message: any): void {\n    const imageAttachment = message.attachments?.find((att: any) =>\n      att.type?.startsWith('image/')\n    );\n    if (imageAttachment?.url) {\n      this.selectedImage = {\n        url: imageAttachment.url,\n        name: imageAttachment.name || 'Image',\n        size: this.formatFileSize(imageAttachment.size || 0),\n        message: message,\n      };\n      this.showImageViewer = true;\n      console.log('🖼️ [ImageViewer] Opening image:', this.selectedImage);\n    }\n  }\n\n  closeImageViewer(): void {\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    console.log('🖼️ [ImageViewer] Closed');\n  }\n\n  downloadImage(): void {\n    if (this.selectedImage?.url) {\n      const link = document.createElement('a');\n      link.href = this.selectedImage.url;\n      link.download = this.selectedImage.name || 'image';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n      console.log(\n        '🖼️ [ImageViewer] Download started:',\n        this.selectedImage.name\n      );\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  searchMessages(): void {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n\n    this.searchResults = this.messages.filter(\n      (message) =>\n        message.content\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase()) ||\n        message.sender?.username\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase())\n    );\n  }\n\n  onSearchQueryChange(): void {\n    this.searchMessages();\n  }\n\n  clearSearch(): void {\n    this.searchQuery = '';\n    this.searchResults = [];\n  }\n\n  jumpToMessage(messageId: string): void {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\n      // Highlight temporairement le message\n      messageElement.classList.add('highlight');\n      setTimeout(() => {\n        messageElement.classList.remove('highlight');\n      }, 2000);\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  closeContextMenu(): void {\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // triggerFileInput - définie plus loin\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n  // goBackToConversations, startVideoCall, startVoiceCall\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  private initiateCall(callType: CallType): void {\n    if (!this.otherParticipant) {\n      this.toastService.showError('Aucun destinataire sélectionné');\n      return;\n    }\n\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n    if (!recipientId) {\n      this.toastService.showError('ID du destinataire introuvable');\n      return;\n    }\n\n    console.log(`🔄 Initiating ${callType} call to user:`, recipientId);\n\n    this.isInCall = true;\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n    this.callDuration = 0;\n\n    // Démarrer le timer d'appel\n    this.startCallTimer();\n\n    // Utiliser le vrai service WebRTC\n    this.MessageService.initiateCall(\n      recipientId,\n      callType,\n      this.conversation?.id\n    ).subscribe({\n      next: (call: Call) => {\n        console.log('✅ Call initiated successfully:', call);\n        this.activeCall = call;\n        this.isCallConnected = false;\n        this.toastService.showSuccess(\n          `Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`\n        );\n      },\n      error: (error) => {\n        console.error('❌ Error initiating call:', error);\n        this.endCall();\n        this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n      },\n    });\n  }\n\n  acceptCall(incomingCall: IncomingCall): void {\n    console.log('🔄 Accepting incoming call:', incomingCall);\n\n    this.MessageService.acceptCall(incomingCall).subscribe({\n      next: (call: Call) => {\n        console.log('✅ Call accepted successfully:', call);\n        this.activeCall = call;\n        this.isInCall = true;\n        this.isCallConnected = true;\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n        this.startCallTimer();\n        this.toastService.showSuccess('Appel accepté');\n      },\n      error: (error) => {\n        console.error('❌ Error accepting call:', error);\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\n      },\n    });\n  }\n\n  rejectCall(incomingCall: IncomingCall): void {\n    console.log('🔄 Rejecting incoming call:', incomingCall);\n\n    this.MessageService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n      next: () => {\n        console.log('✅ Call rejected successfully');\n        this.toastService.showSuccess('Appel rejeté');\n      },\n      error: (error) => {\n        console.error('❌ Error rejecting call:', error);\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n      },\n    });\n  }\n\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // endCall - Cette méthode est déjà définie plus loin dans le fichier\n\n  private startCallTimer(): void {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n\n  private resetCallState(): void {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n  }\n\n  // === CONTRÔLES D'APPEL ===\n  toggleMute(): void {\n    if (!this.activeCall) return;\n\n    this.isMuted = !this.isMuted;\n\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(\n      this.activeCall.id,\n      undefined, // video unchanged\n      !this.isMuted // audio state\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(\n          this.isMuted ? 'Micro coupé' : 'Micro activé'\n        );\n      },\n      error: (error) => {\n        console.error('❌ Error toggling mute:', error);\n        // Revert state on error\n        this.isMuted = !this.isMuted;\n        this.toastService.showError('Erreur lors du changement du micro');\n      },\n    });\n  }\n\n  toggleVideo(): void {\n    if (!this.activeCall) return;\n\n    this.isVideoEnabled = !this.isVideoEnabled;\n\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(\n      this.activeCall.id,\n      this.isVideoEnabled, // video state\n      undefined // audio unchanged\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(\n          this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\n        );\n      },\n      error: (error) => {\n        console.error('❌ Error toggling video:', error);\n        // Revert state on error\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.toastService.showError('Erreur lors du changement de la caméra');\n      },\n    });\n  }\n\n  formatCallDuration(duration: number): string {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor((duration % 3600) / 60);\n    const seconds = duration % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds\n        .toString()\n        .padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n\n  async startVoiceRecording(): Promise<void> {\n    console.log('🎤 [Voice] Starting voice recording...');\n\n    try {\n      // Vérifier le support du navigateur\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n        throw new Error(\n          \"Votre navigateur ne supporte pas l'enregistrement audio\"\n        );\n      }\n\n      // Vérifier si MediaRecorder est supporté\n      if (!window.MediaRecorder) {\n        throw new Error(\n          \"MediaRecorder n'est pas supporté par votre navigateur\"\n        );\n      }\n\n      console.log('🎤 [Voice] Requesting microphone access...');\n\n      // Demander l'accès au microphone avec des contraintes optimisées\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n          sampleRate: 44100,\n          channelCount: 1,\n        },\n      });\n\n      console.log('🎤 [Voice] Microphone access granted');\n\n      // Vérifier les types MIME supportés\n      let mimeType = 'audio/webm;codecs=opus';\n      if (!MediaRecorder.isTypeSupported(mimeType)) {\n        mimeType = 'audio/webm';\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\n          mimeType = 'audio/mp4';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = ''; // Laisser le navigateur choisir\n          }\n        }\n      }\n\n      console.log('🎤 [Voice] Using MIME type:', mimeType);\n\n      // Créer le MediaRecorder\n      this.mediaRecorder = new MediaRecorder(stream, {\n        mimeType: mimeType || undefined,\n      });\n\n      // Initialiser les variables\n      this.audioChunks = [];\n      this.isRecordingVoice = true;\n      this.voiceRecordingDuration = 0;\n      this.voiceRecordingState = 'recording';\n\n      console.log('🎤 [Voice] MediaRecorder created, starting timer...');\n\n      // Démarrer le timer\n      this.recordingTimer = setInterval(() => {\n        this.voiceRecordingDuration++;\n        // Animer les waves\n        this.animateVoiceWaves();\n        this.cdr.detectChanges();\n      }, 1000);\n\n      // Gérer les événements du MediaRecorder\n      this.mediaRecorder.ondataavailable = (event) => {\n        console.log('🎤 [Voice] Data available:', event.data.size, 'bytes');\n        if (event.data.size > 0) {\n          this.audioChunks.push(event.data);\n        }\n      };\n\n      this.mediaRecorder.onstop = () => {\n        console.log('🎤 [Voice] MediaRecorder stopped, processing audio...');\n        this.processRecordedAudio();\n      };\n\n      this.mediaRecorder.onerror = (event: any) => {\n        console.error('🎤 [Voice] MediaRecorder error:', event.error);\n        this.toastService.showError(\"Erreur lors de l'enregistrement\");\n        this.cancelVoiceRecording();\n      };\n\n      // Démarrer l'enregistrement\n      this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n      console.log('🎤 [Voice] Recording started successfully');\n\n      this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n    } catch (error: any) {\n      console.error('🎤 [Voice] Error starting recording:', error);\n\n      let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n\n      if (error.name === 'NotAllowedError') {\n        errorMessage =\n          \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n      } else if (error.name === 'NotFoundError') {\n        errorMessage =\n          'Aucun microphone détecté. Veuillez connecter un microphone.';\n      } else if (error.name === 'NotSupportedError') {\n        errorMessage =\n          \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      this.toastService.showError(errorMessage);\n      this.cancelVoiceRecording();\n    }\n  }\n\n  stopVoiceRecording(): void {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n\n  cancelVoiceRecording(): void {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n      this.mediaRecorder = null;\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n\n  private async processRecordedAudio(): Promise<void> {\n    console.log('🎤 [Voice] Processing recorded audio...');\n\n    try {\n      // Vérifier qu'on a des données audio\n      if (this.audioChunks.length === 0) {\n        console.error('🎤 [Voice] No audio chunks available');\n        this.toastService.showError('Aucun audio enregistré');\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      console.log(\n        '🎤 [Voice] Audio chunks:',\n        this.audioChunks.length,\n        'Duration:',\n        this.voiceRecordingDuration\n      );\n\n      // Vérifier la durée minimale\n      if (this.voiceRecordingDuration < 1) {\n        console.error(\n          '🎤 [Voice] Recording too short:',\n          this.voiceRecordingDuration\n        );\n        this.toastService.showError(\n          'Enregistrement trop court (minimum 1 seconde)'\n        );\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      // Déterminer le type MIME du blob\n      let mimeType = 'audio/webm;codecs=opus';\n      if (this.mediaRecorder?.mimeType) {\n        mimeType = this.mediaRecorder.mimeType;\n      }\n\n      console.log('🎤 [Voice] Creating audio blob with MIME type:', mimeType);\n\n      // Créer le blob audio\n      const audioBlob = new Blob(this.audioChunks, {\n        type: mimeType,\n      });\n\n      console.log('🎤 [Voice] Audio blob created:', {\n        size: audioBlob.size,\n        type: audioBlob.type,\n      });\n\n      // Déterminer l'extension du fichier\n      let extension = '.webm';\n      if (mimeType.includes('mp4')) {\n        extension = '.mp4';\n      } else if (mimeType.includes('wav')) {\n        extension = '.wav';\n      } else if (mimeType.includes('ogg')) {\n        extension = '.ogg';\n      }\n\n      // Créer le fichier\n      const audioFile = new File(\n        [audioBlob],\n        `voice_${Date.now()}${extension}`,\n        {\n          type: mimeType,\n        }\n      );\n\n      console.log('🎤 [Voice] Audio file created:', {\n        name: audioFile.name,\n        size: audioFile.size,\n        type: audioFile.type,\n      });\n\n      // Envoyer le message vocal\n      this.voiceRecordingState = 'processing';\n      await this.sendVoiceMessage(audioFile);\n\n      console.log('🎤 [Voice] Voice message sent successfully');\n      this.toastService.showSuccess('🎤 Message vocal envoyé');\n    } catch (error: any) {\n      console.error('🎤 [Voice] Error processing audio:', error);\n      this.toastService.showError(\n        \"Erreur lors de l'envoi du message vocal: \" +\n          (error.message || 'Erreur inconnue')\n      );\n    } finally {\n      // Nettoyer l'état\n      this.voiceRecordingState = 'idle';\n      this.voiceRecordingDuration = 0;\n      this.audioChunks = [];\n      this.isRecordingVoice = false;\n\n      console.log('🎤 [Voice] Audio processing completed, state reset');\n    }\n  }\n\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        '',\n        audioFile,\n        'AUDIO' as any,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n          reject(error);\n        },\n      });\n    });\n  }\n\n  formatRecordingDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n\n  onRecordStart(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record start triggered');\n    console.log('🎤 [Voice] Current state:', {\n      isRecordingVoice: this.isRecordingVoice,\n      voiceRecordingState: this.voiceRecordingState,\n      voiceRecordingDuration: this.voiceRecordingDuration,\n      mediaRecorder: !!this.mediaRecorder,\n    });\n\n    // Vérifier si on peut enregistrer\n    if (this.voiceRecordingState === 'processing') {\n      console.log('🎤 [Voice] Already processing, ignoring start');\n      this.toastService.showWarning('Traitement en cours...');\n      return;\n    }\n\n    if (this.isRecordingVoice) {\n      console.log('🎤 [Voice] Already recording, ignoring start');\n      this.toastService.showWarning('Enregistrement déjà en cours...');\n      return;\n    }\n\n    // Afficher un message de début\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n\n    // Démarrer l'enregistrement\n    this.startVoiceRecording().catch((error) => {\n      console.error('🎤 [Voice] Failed to start recording:', error);\n      this.toastService.showError(\n        \"Impossible de démarrer l'enregistrement vocal: \" +\n          (error.message || 'Erreur inconnue')\n      );\n    });\n  }\n\n  onRecordEnd(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record end triggered');\n\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring end');\n      return;\n    }\n\n    // Arrêter l'enregistrement et envoyer\n    this.stopVoiceRecording();\n  }\n\n  onRecordCancel(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record cancel triggered');\n\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring cancel');\n      return;\n    }\n\n    // Annuler l'enregistrement\n    this.cancelVoiceRecording();\n  }\n\n  getRecordingFormat(): string {\n    if (this.mediaRecorder?.mimeType) {\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n    }\n    return 'Auto';\n  }\n\n  // === ANIMATION DES WAVES VOCALES ===\n\n  private animateVoiceWaves(): void {\n    // Animer les waves pendant l'enregistrement\n    this.voiceWaves = this.voiceWaves.map(() => {\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n    });\n  }\n\n  onFileSelected(event: any): void {\n    console.log('📁 [Upload] File selection triggered');\n    const files = event.target.files;\n\n    if (!files || files.length === 0) {\n      console.log('📁 [Upload] No files selected');\n      return;\n    }\n\n    console.log(`📁 [Upload] ${files.length} file(s) selected:`, files);\n\n    for (let file of files) {\n      console.log(\n        `📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`\n      );\n      this.uploadFile(file);\n    }\n  }\n\n  private uploadFile(file: File): void {\n    console.log(`📁 [Upload] Starting upload for file: ${file.name}`);\n\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      console.error('📁 [Upload] No receiver ID found');\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    console.log(`📁 [Upload] Receiver ID: ${receiverId}`);\n\n    // Vérifier la taille du fichier (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\n      return;\n    }\n\n    // 🖼️ Compression d'image si nécessaire\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n      // > 1MB\n      console.log(\n        '🖼️ [Compression] Compressing image:',\n        file.name,\n        'Original size:',\n        file.size\n      );\n      this.compressImage(file)\n        .then((compressedFile) => {\n          console.log(\n            '🖼️ [Compression] ✅ Image compressed successfully. New size:',\n            compressedFile.size\n          );\n          this.sendFileToServer(compressedFile, receiverId);\n        })\n        .catch((error) => {\n          console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n          // Envoyer le fichier original en cas d'erreur\n          this.sendFileToServer(file, receiverId);\n        });\n      return;\n    }\n\n    // Envoyer le fichier sans compression\n    this.sendFileToServer(file, receiverId);\n  }\n\n  private sendFileToServer(file: File, receiverId: string): void {\n    const messageType = this.getFileMessageType(file);\n    console.log(`📁 [Upload] Message type determined: ${messageType}`);\n    console.log(`📁 [Upload] Conversation ID: ${this.conversation.id}`);\n\n    this.isSendingMessage = true;\n    this.isUploading = true;\n    this.uploadProgress = 0;\n    console.log('📁 [Upload] Calling MessageService.sendMessage...');\n\n    // Simuler la progression d'upload\n    const progressInterval = setInterval(() => {\n      this.uploadProgress += Math.random() * 15;\n      if (this.uploadProgress >= 90) {\n        clearInterval(progressInterval);\n      }\n      this.cdr.detectChanges();\n    }, 300);\n\n    this.MessageService.sendMessage(\n      receiverId,\n      '',\n      file,\n      messageType,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        console.log('📁 [Upload] ✅ File sent successfully:', message);\n        console.log('📁 [Debug] Sent message structure:', {\n          id: message.id,\n          type: message.type,\n          attachments: message.attachments,\n          hasImage: this.hasImage(message),\n          hasFile: this.hasFile(message),\n          imageUrl: this.getImageUrl(message),\n        });\n\n        clearInterval(progressInterval);\n        this.uploadProgress = 100;\n\n        setTimeout(() => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Fichier envoyé avec succès');\n          this.resetUploadState();\n        }, 500);\n      },\n      error: (error: any) => {\n        console.error('📁 [Upload] ❌ Error sending file:', error);\n        clearInterval(progressInterval);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n        this.resetUploadState();\n      },\n    });\n  }\n\n  private getFileMessageType(file: File): any {\n    if (file.type.startsWith('image/')) return 'IMAGE' as any;\n    if (file.type.startsWith('video/')) return 'VIDEO' as any;\n    if (file.type.startsWith('audio/')) return 'AUDIO' as any;\n    return 'FILE' as any;\n  }\n\n  getFileAcceptTypes(): string {\n    return '*/*';\n  }\n\n  resetUploadState(): void {\n    this.isSendingMessage = false;\n    this.isUploading = false;\n    this.uploadProgress = 0;\n  }\n\n  // === DRAG & DROP ===\n\n  onDragOver(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n\n  onDragLeave(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\n    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\n    const x = event.clientX;\n    const y = event.clientY;\n\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      this.isDragOver = false;\n    }\n  }\n\n  onDrop(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      console.log('📁 [Drag&Drop] Files dropped:', files.length);\n\n      // Traiter chaque fichier\n      Array.from(files).forEach((file) => {\n        console.log(\n          '📁 [Drag&Drop] Processing file:',\n          file.name,\n          file.type,\n          file.size\n        );\n        this.uploadFile(file);\n      });\n\n      this.toastService.showSuccess(\n        `${files.length} fichier(s) en cours d'envoi`\n      );\n    }\n  }\n\n  // === COMPRESSION D'IMAGES ===\n\n  private compressImage(file: File, quality: number = 0.8): Promise<File> {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n\n      img.onload = () => {\n        // Calculer les nouvelles dimensions (max 1920x1080)\n        const maxWidth = 1920;\n        const maxHeight = 1080;\n        let { width, height } = img;\n\n        if (width > maxWidth || height > maxHeight) {\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\n          width *= ratio;\n          height *= ratio;\n        }\n\n        canvas.width = width;\n        canvas.height = height;\n\n        // Dessiner l'image redimensionnée\n        ctx?.drawImage(img, 0, 0, width, height);\n\n        // Convertir en blob avec compression\n        canvas.toBlob(\n          (blob) => {\n            if (blob) {\n              const compressedFile = new File([blob], file.name, {\n                type: file.type,\n                lastModified: Date.now(),\n              });\n              resolve(compressedFile);\n            } else {\n              reject(new Error('Failed to compress image'));\n            }\n          },\n          file.type,\n          quality\n        );\n      };\n\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  }\n\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n\n  private handleTypingIndicator(): void {\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\n      this.sendTypingIndicator(true);\n    }\n\n    // Reset le timer\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Arrêter l'indicateur de frappe\n      this.sendTypingIndicator(false);\n    }, 2000);\n  }\n\n  private sendTypingIndicator(isTyping: boolean): void {\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (receiverId && this.conversation?.id) {\n      console.log(\n        `📝 Sending typing indicator: ${isTyping} to user ${receiverId}`\n      );\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n    }\n  }\n\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n\n  onCallAccepted(call: Call): void {\n    console.log('🔄 Call accepted from interface:', call);\n    this.activeCall = call;\n    this.isInCall = true;\n    this.isCallConnected = true;\n    this.startCallTimer();\n    this.toastService.showSuccess('Appel accepté');\n  }\n\n  onCallRejected(): void {\n    console.log('🔄 Call rejected from interface');\n    this.endCall();\n    this.toastService.showInfo('Appel rejeté');\n  }\n\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n\n  playVoiceMessage(message: any): void {\n    console.log('🎵 [Voice] Playing voice message:', message.id);\n    this.toggleVoicePlayback(message);\n  }\n\n  isVoicePlaying(messageId: string): boolean {\n    return this.playingMessageId === messageId;\n  }\n\n  toggleVoicePlayback(message: any): void {\n    const messageId = message.id;\n    const audioUrl = this.getVoiceUrl(message);\n\n    if (!audioUrl) {\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\n      this.toastService.showError('Fichier audio introuvable');\n      return;\n    }\n\n    // Si c'est déjà en cours de lecture, arrêter\n    if (this.isVoicePlaying(messageId)) {\n      this.stopVoicePlayback();\n      return;\n    }\n\n    // Arrêter toute autre lecture en cours\n    this.stopVoicePlayback();\n\n    // Démarrer la nouvelle lecture\n    this.startVoicePlayback(message, audioUrl);\n  }\n\n  private startVoicePlayback(message: any, audioUrl: string): void {\n    const messageId = message.id;\n\n    try {\n      console.log(\n        '🎵 [Voice] Starting playback for:',\n        messageId,\n        'URL:',\n        audioUrl\n      );\n\n      this.currentAudio = new Audio(audioUrl);\n      this.playingMessageId = messageId;\n\n      // Initialiser les valeurs par défaut avec la nouvelle structure\n      const currentData = this.getVoicePlaybackData(messageId);\n      this.setVoicePlaybackData(messageId, {\n        progress: 0,\n        currentTime: 0,\n        speed: currentData.speed || 1,\n        duration: currentData.duration || 0,\n      });\n\n      // Configurer la vitesse de lecture\n      this.currentAudio.playbackRate = currentData.speed || 1;\n\n      // Événements audio\n      this.currentAudio.addEventListener('loadedmetadata', () => {\n        if (this.currentAudio) {\n          this.setVoicePlaybackData(messageId, {\n            duration: this.currentAudio.duration,\n          });\n          console.log(\n            '🎵 [Voice] Audio loaded, duration:',\n            this.currentAudio.duration\n          );\n        }\n      });\n\n      this.currentAudio.addEventListener('timeupdate', () => {\n        if (this.currentAudio && this.playingMessageId === messageId) {\n          const currentTime = this.currentAudio.currentTime;\n          const progress = (currentTime / this.currentAudio.duration) * 100;\n          this.setVoicePlaybackData(messageId, { currentTime, progress });\n          this.cdr.detectChanges();\n        }\n      });\n\n      this.currentAudio.addEventListener('ended', () => {\n        console.log('🎵 [Voice] Playback ended for:', messageId);\n        this.stopVoicePlayback();\n      });\n\n      this.currentAudio.addEventListener('error', (error) => {\n        console.error('🎵 [Voice] Audio error:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      });\n\n      // Démarrer la lecture\n      this.currentAudio\n        .play()\n        .then(() => {\n          console.log('🎵 [Voice] Playback started successfully');\n          this.toastService.showSuccess('🎵 Lecture du message vocal');\n        })\n        .catch((error) => {\n          console.error('🎵 [Voice] Error starting playback:', error);\n          this.toastService.showError('Impossible de lire le message vocal');\n          this.stopVoicePlayback();\n        });\n    } catch (error) {\n      console.error('🎵 [Voice] Error creating audio:', error);\n      this.toastService.showError('Erreur lors de la lecture audio');\n      this.stopVoicePlayback();\n    }\n  }\n\n  private stopVoicePlayback(): void {\n    if (this.currentAudio) {\n      console.log('🎵 [Voice] Stopping playback for:', this.playingMessageId);\n      this.currentAudio.pause();\n      this.currentAudio.currentTime = 0;\n      this.currentAudio = null;\n    }\n    this.playingMessageId = null;\n    this.cdr.detectChanges();\n  }\n\n  getVoiceUrl(message: any): string {\n    // Vérifier les propriétés directes d'audio\n    if (message.voiceUrl) return message.voiceUrl;\n    if (message.audioUrl) return message.audioUrl;\n    if (message.voice) return message.voice;\n\n    // Vérifier les attachments audio\n    const audioAttachment = message.attachments?.find(\n      (att: any) => att.type?.startsWith('audio/') || att.type === 'AUDIO'\n    );\n\n    if (audioAttachment) {\n      return audioAttachment.url || audioAttachment.path || '';\n    }\n\n    return '';\n  }\n\n  getVoiceWaves(message: any): number[] {\n    // Générer des waves basées sur l'ID du message pour la cohérence\n    const messageId = message.id || '';\n    const seed = messageId\n      .split('')\n      .reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);\n    const waves: number[] = [];\n\n    for (let i = 0; i < 16; i++) {\n      const height = 4 + ((seed + i * 7) % 20);\n      waves.push(height);\n    }\n\n    return waves;\n  }\n\n  getVoiceProgress(message: any): number {\n    const data = this.getVoicePlaybackData(message.id);\n    const totalWaves = 16;\n    return Math.floor((data.progress / 100) * totalWaves);\n  }\n\n  getVoiceCurrentTime(message: any): string {\n    const data = this.getVoicePlaybackData(message.id);\n    return this.formatAudioTime(data.currentTime);\n  }\n\n  getVoiceDuration(message: any): string {\n    const data = this.getVoicePlaybackData(message.id);\n    const duration = data.duration || message.metadata?.duration || 0;\n\n    if (typeof duration === 'string') {\n      return duration; // Déjà formaté\n    }\n\n    return this.formatAudioTime(duration);\n  }\n\n  private formatAudioTime(seconds: number): string {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  seekVoiceMessage(message: any, waveIndex: number): void {\n    const messageId = message.id;\n\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\n      return;\n    }\n\n    const totalWaves = 16;\n    const seekPercentage = (waveIndex / totalWaves) * 100;\n    const seekTime = (seekPercentage / 100) * this.currentAudio.duration;\n\n    this.currentAudio.currentTime = seekTime;\n    console.log('🎵 [Voice] Seeking to:', seekTime, 'seconds');\n  }\n\n  toggleVoiceSpeed(message: any): void {\n    const messageId = message.id;\n    const data = this.getVoicePlaybackData(messageId);\n\n    // Cycle entre 1x, 1.5x, 2x\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n\n    this.setVoicePlaybackData(messageId, { speed: newSpeed });\n\n    if (this.currentAudio && this.playingMessageId === messageId) {\n      this.currentAudio.playbackRate = newSpeed;\n    }\n\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.unsubscribe();\n\n    // Nettoyer les timers\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    // Nettoyer les ressources audio\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream?.getTracks().forEach((track) => track.stop());\n    }\n\n    // Nettoyer la lecture audio\n    this.stopVoicePlayback();\n  }\n}\n", "<!-- Chat WhatsApp Moderne avec Tailwind CSS -->\n<div\n  class=\"flex flex-col h-screen bg-gradient-to-br from-gray-50 to-green-50 dark:from-gray-900 dark:to-gray-800\"\n>\n  <!-- En-tête -->\n  <header\n    class=\"flex items-center px-4 py-3 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm\"\n  >\n    <!-- Bouton retour -->\n    <button\n      (click)=\"goBackToConversations()\"\n      class=\"p-2 mr-3 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n    >\n      <i class=\"fas fa-arrow-left text-gray-600 dark:text-gray-300\"></i>\n    </button>\n\n    <!-- Info utilisateur -->\n    <div class=\"flex items-center flex-1 min-w-0\">\n      <div class=\"relative mr-3\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-10 h-10 rounded-full object-cover border-2 border-green-500 cursor-pointer hover:scale-105 transition-transform\"\n          (click)=\"openUserProfile(otherParticipant?.id!)\"\n        />\n        <div\n          *ngIf=\"otherParticipant?.isOnline\"\n          class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full animate-pulse\"\n        ></div>\n      </div>\n\n      <div class=\"flex-1 min-w-0\">\n        <h3 class=\"font-semibold text-gray-900 dark:text-white truncate\">\n          {{ otherParticipant?.username || \"Utilisateur\" }}\n        </h3>\n        <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n          <div\n            *ngIf=\"isUserTyping\"\n            class=\"flex items-center gap-1 text-green-600\"\n          >\n            <span>En train d'écrire</span>\n            <div class=\"flex gap-1\">\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n              ></div>\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n                style=\"animation-delay: 0.1s\"\n              ></div>\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n                style=\"animation-delay: 0.2s\"\n              ></div>\n            </div>\n          </div>\n          <span *ngIf=\"!isUserTyping\">\n            {{\n              otherParticipant?.isOnline\n                ? \"En ligne\"\n                : formatLastActive(otherParticipant?.lastActive)\n            }}\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Actions -->\n    <div class=\"flex items-center gap-2\">\n      <button\n        (click)=\"startVideoCall()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Appel vidéo\"\n      >\n        <i class=\"fas fa-video\"></i>\n      </button>\n      <button\n        (click)=\"startVoiceCall()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Appel vocal\"\n      >\n        <i class=\"fas fa-phone\"></i>\n      </button>\n      <button\n        (click)=\"toggleSearch()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        [class.bg-green-100]=\"searchMode\"\n        [class.text-green-600]=\"searchMode\"\n        title=\"Rechercher\"\n      >\n        <i class=\"fas fa-search\"></i>\n      </button>\n      <button\n        (click)=\"toggleTheme()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Changer le thème\"\n      >\n        <i class=\"fas fa-moon\" *ngIf=\"!isDarkMode\"></i>\n        <i class=\"fas fa-sun\" *ngIf=\"isDarkMode\"></i>\n      </button>\n      <button\n        (click)=\"toggleMainMenu()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors relative\"\n        [class.bg-green-100]=\"showMainMenu\"\n        [class.text-green-600]=\"showMainMenu\"\n        title=\"Menu\"\n      >\n        <i class=\"fas fa-ellipsis-v\"></i>\n      </button>\n\n      <!-- Bouton de test temporaire -->\n      <button\n        (click)=\"testAddMessage()\"\n        class=\"p-2 rounded-full hover:bg-red-100 dark:hover:bg-red-700 text-red-600 dark:text-red-300 transition-colors\"\n        title=\"Test: Ajouter message\"\n      >\n        <i class=\"fas fa-plus\"></i>\n      </button>\n    </div>\n\n    <!-- Menu principal dropdown -->\n    <div\n      *ngIf=\"showMainMenu\"\n      class=\"absolute top-16 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-48\"\n    >\n      <div class=\"p-2\">\n        <button\n          (click)=\"toggleSearch(); showMainMenu = false\"\n          class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n        >\n          <i class=\"fas fa-search text-blue-500\"></i>\n          <span class=\"text-gray-700 dark:text-gray-300\">Rechercher</span>\n        </button>\n        <button\n          class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n        >\n          <i class=\"fas fa-user text-green-500\"></i>\n          <span class=\"text-gray-700 dark:text-gray-300\">Voir le profil</span>\n        </button>\n        <button\n          class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n        >\n          <i class=\"fas fa-bell text-yellow-500\"></i>\n          <span class=\"text-gray-700 dark:text-gray-300\">Notifications</span>\n        </button>\n\n        <!-- Bouton de thème avec sélecteur -->\n        <div class=\"relative\">\n          <button\n            (click)=\"toggleThemeSelector()\"\n            class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n            [class.bg-gray-100]=\"showThemeSelector\"\n            [class.dark:bg-gray-700]=\"showThemeSelector\"\n          >\n            <i\n              [class]=\"getCurrentTheme()?.icon\"\n              [style.color]=\"getCurrentTheme()?.color\"\n            ></i>\n            <span class=\"text-gray-700 dark:text-gray-300\"\n              >Thème: {{ getCurrentTheme()?.name }}</span\n            >\n            <i\n              class=\"fas fa-chevron-right ml-auto text-xs text-gray-400 transition-transform\"\n              [class.rotate-90]=\"showThemeSelector\"\n            ></i>\n          </button>\n\n          <!-- Sous-menu des thèmes -->\n          <div\n            *ngIf=\"showThemeSelector\"\n            class=\"absolute left-full top-0 ml-2 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-40\"\n          >\n            <div class=\"p-2\">\n              <button\n                *ngFor=\"let theme of themes\"\n                (click)=\"selectTheme(theme.id)\"\n                class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n                [class.bg-blue-50]=\"currentTheme === theme.id\"\n                [class.dark:bg-blue-900]=\"currentTheme === theme.id\"\n                [class.text-blue-600]=\"currentTheme === theme.id\"\n                [class.dark:text-blue-400]=\"currentTheme === theme.id\"\n              >\n                <i [class]=\"theme.icon\" [style.color]=\"theme.color\"></i>\n                <span\n                  class=\"text-gray-700 dark:text-gray-300\"\n                  [class.text-blue-600]=\"currentTheme === theme.id\"\n                  [class.dark:text-blue-400]=\"currentTheme === theme.id\"\n                  >{{ theme.name }}</span\n                >\n                <i\n                  *ngIf=\"currentTheme === theme.id\"\n                  class=\"fas fa-check ml-auto text-xs text-blue-600 dark:text-blue-400\"\n                ></i>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <hr class=\"my-2 border-gray-200 dark:border-gray-600\" />\n        <button\n          class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n        >\n          <i class=\"fas fa-cog text-gray-500\"></i>\n          <span class=\"text-gray-700 dark:text-gray-300\">Paramètres</span>\n        </button>\n      </div>\n    </div>\n  </header>\n\n  <!-- Zone de messages avec drag & drop -->\n  <main\n    class=\"flex-1 overflow-y-auto p-4 space-y-4 relative\"\n    #messagesContainer\n    (scroll)=\"onScroll($event)\"\n    (dragover)=\"onDragOver($event)\"\n    (dragleave)=\"onDragLeave($event)\"\n    (drop)=\"onDrop($event)\"\n    [class.drag-over]=\"isDragOver\"\n  >\n    <!-- Overlay drag & drop -->\n    <div\n      *ngIf=\"isDragOver\"\n      class=\"absolute inset-0 bg-green-500 bg-opacity-20 border-2 border-dashed border-green-500 rounded-lg flex items-center justify-center z-50 animate-pulse\"\n      style=\"\n        backdrop-filter: blur(2px);\n        background: linear-gradient(\n          45deg,\n          rgba(34, 197, 94, 0.1) 0%,\n          rgba(34, 197, 94, 0.2) 50%,\n          rgba(34, 197, 94, 0.1) 100%\n        );\n        animation: dragShimmer 2s infinite;\n      \"\n    >\n      <div\n        class=\"text-center bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-green-300 dark:border-green-600\"\n      >\n        <i\n          class=\"fas fa-cloud-upload-alt text-5xl text-green-600 mb-3 animate-bounce\"\n        ></i>\n        <p class=\"text-xl font-bold text-green-700 dark:text-green-400 mb-2\">\n          Déposez vos fichiers ici\n        </p>\n        <p class=\"text-sm text-green-600 dark:text-green-300\">\n          Images, vidéos, documents...\n        </p>\n        <div class=\"flex justify-center gap-2 mt-3\">\n          <span class=\"w-2 h-2 bg-green-500 rounded-full animate-ping\"></span>\n          <span\n            class=\"w-2 h-2 bg-green-500 rounded-full animate-ping\"\n            style=\"animation-delay: 0.2s\"\n          ></span>\n          <span\n            class=\"w-2 h-2 bg-green-500 rounded-full animate-ping\"\n            style=\"animation-delay: 0.4s\"\n          ></span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Styles CSS maintenant externalisés dans message-chat.component.css -->\n    <!-- Chargement -->\n    <div\n      *ngIf=\"isLoading\"\n      class=\"flex flex-col items-center justify-center py-8\"\n    >\n      <div\n        class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mb-4\"\n      ></div>\n      <span class=\"text-gray-500 dark:text-gray-400\"\n        >Chargement des messages...</span\n      >\n    </div>\n\n    <!-- État vide -->\n    <div\n      *ngIf=\"!isLoading && messages.length === 0\"\n      class=\"flex flex-col items-center justify-center py-16\"\n    >\n      <div class=\"text-6xl text-gray-300 dark:text-gray-600 mb-4\">\n        <i class=\"fas fa-comments\"></i>\n      </div>\n      <h3 class=\"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2\">\n        Aucun message\n      </h3>\n      <p class=\"text-gray-500 dark:text-gray-400 text-center\">\n        Commencez votre conversation avec {{ otherParticipant?.username }}\n      </p>\n    </div>\n\n    <!-- Messages -->\n    <div *ngIf=\"!isLoading && messages.length > 0\" class=\"space-y-2\">\n      <ng-container\n        *ngFor=\"\n          let message of messages;\n          let i = index;\n          trackBy: trackByMessageId\n        \"\n      >\n        <!-- Séparateur de date -->\n        <div\n          *ngIf=\"shouldShowDateSeparator(i)\"\n          class=\"flex justify-center my-4\"\n        >\n          <div\n            class=\"bg-white dark:bg-gray-700 px-3 py-1 rounded-full shadow-sm\"\n          >\n            <span class=\"text-xs text-gray-500 dark:text-gray-400\">\n              {{ formatDateSeparator(message.timestamp) }}\n            </span>\n          </div>\n        </div>\n\n        <!-- Message -->\n        <div\n          class=\"flex\"\n          [class.justify-end]=\"message.sender?.id === currentUserId\"\n          [class.justify-start]=\"message.sender?.id !== currentUserId\"\n          [id]=\"'message-' + message.id\"\n          (click)=\"onMessageClick(message, $event)\"\n          (contextmenu)=\"onMessageContextMenu(message, $event)\"\n        >\n          <!-- Avatar pour les autres -->\n          <div\n            *ngIf=\"message.sender?.id !== currentUserId && shouldShowAvatar(i)\"\n            class=\"mr-2 flex-shrink-0\"\n          >\n            <img\n              [src]=\"\n                message.sender?.image || 'assets/images/default-avatar.png'\n              \"\n              [alt]=\"message.sender?.username\"\n              class=\"w-8 h-8 rounded-full object-cover cursor-pointer hover:scale-105 transition-transform\"\n              (click)=\"openUserProfile(message.sender?.id!)\"\n            />\n          </div>\n\n          <!-- Bulle de message -->\n          <div\n            class=\"max-w-xs lg:max-w-md px-4 py-2 rounded-2xl shadow-sm relative group\"\n            [class.bg-blue-500]=\"message.sender?.id === currentUserId\"\n            [class.text-white]=\"message.sender?.id === currentUserId\"\n            [style.background-color]=\"\n              message.sender?.id === currentUserId && isDarkMode\n                ? '#9ca3af'\n                : ''\n            \"\n            [class.bg-white]=\"message.sender?.id !== currentUserId\"\n            [class.text-gray-900]=\"message.sender?.id !== currentUserId\"\n            [class.dark:bg-gray-700]=\"message.sender?.id !== currentUserId\"\n            [style.color]=\"\n              message.sender?.id !== currentUserId && isDarkMode\n                ? '#9ca3af'\n                : ''\n            \"\n            [class.dark:text-blue-400]=\"message.sender?.id !== currentUserId\"\n            [class.rounded-br-sm]=\"message.sender?.id === currentUserId\"\n            [class.rounded-bl-sm]=\"message.sender?.id !== currentUserId\"\n            style=\"word-wrap: break-word; overflow-wrap: break-word\"\n          >\n            <!-- Nom expéditeur (groupes) -->\n            <div\n              *ngIf=\"\n                isGroupConversation() &&\n                message.sender?.id !== currentUserId &&\n                shouldShowSenderName(i)\n              \"\n              class=\"text-xs font-semibold mb-1 opacity-75\"\n              [style.color]=\"getUserColor(message.sender?.id!)\"\n            >\n              {{ message.sender?.username }}\n            </div>\n\n            <!-- Contenu texte -->\n            <div *ngIf=\"getMessageType(message) === 'text'\" class=\"break-words\">\n              <div [innerHTML]=\"formatMessageContent(message.content)\"></div>\n            </div>\n\n            <!-- Image dans l'espace de chat -->\n            <div *ngIf=\"hasImage(message)\">\n              <img\n                [src]=\"getImageUrl(message)\"\n                [alt]=\"message.content || 'Image'\"\n                (click)=\"openImageViewer(message)\"\n                (load)=\"onImageLoad($event, message)\"\n                (error)=\"onImageError($event, message)\"\n              />\n              <!-- Légende de l'image -->\n              <div\n                *ngIf=\"message.content\"\n                [class.text-white]=\"message.sender?.id === currentUserId\"\n                [class.text-gray-900]=\"message.sender?.id !== currentUserId\"\n                [style.color]=\"\n                  message.sender?.id !== currentUserId && isDarkMode\n                    ? '#9ca3af'\n                    : ''\n                \"\n                [innerHTML]=\"formatMessageContent(message.content)\"\n              ></div>\n            </div>\n\n            <!-- Message vocal -->\n            <div\n              *ngIf=\"getMessageType(message) === 'audio'\"\n              class=\"flex items-center gap-3 p-3 rounded-xl min-w-[250px] max-w-xs cursor-pointer transition-all duration-300 hover:shadow-lg group border bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600\"\n              [class.shadow-lg]=\"isVoicePlaying(message.id)\"\n              [class.ring-2]=\"isVoicePlaying(message.id)\"\n              [class.ring-blue-300]=\"isVoicePlaying(message.id)\"\n              style=\"position: relative; overflow: hidden\"\n            >\n              <!-- Bouton play/pause -->\n              <button\n                class=\"p-2 rounded-full text-white transition-all duration-300 flex-shrink-0 border-none outline-none cursor-pointer\"\n                [class.bg-blue-500]=\"!isVoicePlaying(message.id)\"\n                [class.hover:bg-blue-600]=\"!isVoicePlaying(message.id)\"\n                [class.bg-red-500]=\"isVoicePlaying(message.id)\"\n                [class.hover:bg-red-600]=\"isVoicePlaying(message.id)\"\n                [class.animate-pulse]=\"isVoicePlaying(message.id)\"\n                (click)=\"$event.stopPropagation(); toggleVoicePlayback(message)\"\n                style=\"\n                  width: 40px;\n                  height: 40px;\n                  display: flex;\n                  align-items: center;\n                  justify-content: center;\n                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n                \"\n                [style.transform]=\"\n                  isVoicePlaying(message.id) ? 'scale(1.05)' : 'scale(1)'\n                \"\n              >\n                <i\n                  class=\"fas text-sm transition-transform duration-200\"\n                  [class.fa-play]=\"!isVoicePlaying(message.id)\"\n                  [class.fa-pause]=\"isVoicePlaying(message.id)\"\n                  [class.scale-110]=\"isVoicePlaying(message.id)\"\n                ></i>\n              </button>\n\n              <div class=\"flex-1 min-w-0\">\n                <!-- Waves audio WhatsApp style avec progression -->\n                <div class=\"flex items-center gap-1 h-8 mb-2 px-1\">\n                  <div\n                    *ngFor=\"let wave of getVoiceWaves(message); let i = index\"\n                    class=\"w-1 rounded-full transition-all duration-300 cursor-pointer hover:scale-110 hover:opacity-80\"\n                    [class.bg-gray-400]=\"!isVoicePlaying(message.id)\"\n                    [class.bg-blue-500]=\"\n                      isVoicePlaying(message.id) &&\n                      i <= getVoiceProgress(message)\n                    \"\n                    [class.bg-gray-300]=\"\n                      isVoicePlaying(message.id) &&\n                      i > getVoiceProgress(message.id)\n                    \"\n                    [class.dark:bg-gray-500]=\"\n                      isVoicePlaying(message.id) &&\n                      i > getVoiceProgress(message.id)\n                    \"\n                    [style.height.px]=\"wave\"\n                    [class.animate-pulse]=\"\n                      isVoicePlaying(message.id) &&\n                      i <= getVoiceProgress(message)\n                    \"\n                    (click)=\"\n                      $event.stopPropagation(); seekVoiceMessage(message, i)\n                    \"\n                    style=\"\n                      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n                      min-height: 4px;\n                    \"\n                    [style.transform]=\"\n                      'scaleY(' +\n                      (isVoicePlaying(message.id) &&\n                      i <= getVoiceProgress(message)\n                        ? '1.3'\n                        : '1') +\n                      ')'\n                    \"\n                    [style.box-shadow]=\"\n                      isVoicePlaying(message.id) &&\n                      i <= getVoiceProgress(message)\n                        ? '0 0 8px rgba(59, 130, 246, 0.5)'\n                        : 'none'\n                    \"\n                  ></div>\n                </div>\n\n                <!-- Durée et progression -->\n                <div class=\"flex justify-between items-center text-xs mt-1\">\n                  <div class=\"flex items-center gap-2\">\n                    <span\n                      class=\"text-gray-600 dark:text-gray-300 font-mono text-xs\"\n                    >\n                      {{ getVoiceCurrentTime(message) }}\n                    </span>\n                    <span class=\"text-gray-400 dark:text-gray-500\">/</span>\n                    <span\n                      class=\"text-gray-500 dark:text-gray-400 font-mono text-xs\"\n                    >\n                      {{ getVoiceDuration(message) }}\n                    </span>\n                  </div>\n                  <div class=\"flex items-center gap-1\">\n                    <!-- Indicateur de vitesse -->\n                    <span\n                      *ngIf=\"getVoicePlaybackData(message.id).speed !== 1\"\n                      class=\"text-green-600 dark:text-green-400 font-semibold text-xs px-1 py-0.5 bg-green-100 dark:bg-green-900 rounded\"\n                    >\n                      {{ getVoicePlaybackData(message.id).speed }}x\n                    </span>\n                    <!-- Indicateur de lecture -->\n                    <div\n                      *ngIf=\"isVoicePlaying(message.id)\"\n                      class=\"flex items-center gap-0.5\"\n                    >\n                      <div\n                        class=\"w-1 h-1 bg-green-500 rounded-full animate-pulse\"\n                      ></div>\n                      <div\n                        class=\"w-1 h-1 bg-green-500 rounded-full animate-pulse\"\n                        style=\"animation-delay: 0.2s\"\n                      ></div>\n                      <div\n                        class=\"w-1 h-1 bg-green-500 rounded-full animate-pulse\"\n                        style=\"animation-delay: 0.4s\"\n                      ></div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Menu vitesse (apparaît au hover) -->\n              <div\n                class=\"opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col gap-1\"\n              >\n                <button\n                  class=\"p-1.5 rounded-full hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-500 dark:text-gray-400 transition-all duration-200\"\n                  (click)=\"$event.stopPropagation(); toggleVoiceSpeed(message)\"\n                  title=\"Vitesse de lecture\"\n                  style=\"border: none; outline: none; cursor: pointer\"\n                >\n                  <i class=\"fas fa-tachometer-alt text-xs\"></i>\n                </button>\n              </div>\n            </div>\n\n            <!-- Fichier -->\n            <div\n              *ngIf=\"\n                hasFile(message) &&\n                getMessageType(message) !== 'audio' &&\n                !hasImage(message)\n              \"\n              class=\"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors\"\n              (click)=\"downloadFile(message)\"\n            >\n              <div class=\"text-2xl text-gray-500 dark:text-gray-400\">\n                <i [class]=\"getFileIcon(message)\"></i>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <div class=\"font-medium text-sm truncate\">\n                  {{ getFileName(message) }}\n                </div>\n                <div class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {{ getFileSize(message) }}\n                </div>\n              </div>\n              <button\n                class=\"p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-400 transition-colors\"\n              >\n                <i class=\"fas fa-download text-sm\"></i>\n              </button>\n            </div>\n\n            <!-- Métadonnées -->\n            <div\n              class=\"flex items-center justify-end gap-1 mt-1 text-xs opacity-75\"\n            >\n              <span>{{ formatMessageTime(message.timestamp) }}</span>\n              <div\n                *ngIf=\"message.sender?.id === currentUserId\"\n                class=\"flex items-center\"\n              >\n                <i\n                  class=\"fas fa-clock\"\n                  *ngIf=\"message.status === 'SENDING'\"\n                  title=\"Envoi en cours\"\n                ></i>\n                <i\n                  class=\"fas fa-check\"\n                  *ngIf=\"message.status === 'SENT'\"\n                  title=\"Envoyé\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  *ngIf=\"message.status === 'DELIVERED'\"\n                  title=\"Livré\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double text-blue-400\"\n                  *ngIf=\"message.status === 'READ'\"\n                  title=\"Lu\"\n                ></i>\n              </div>\n            </div>\n\n            <!-- Réactions -->\n            <div\n              *ngIf=\"message.reactions && message.reactions.length > 0\"\n              class=\"flex gap-1 mt-2\"\n            >\n              <button\n                *ngFor=\"let reaction of message.reactions\"\n                (click)=\"toggleReaction(message.id!, reaction.emoji)\"\n                class=\"flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors\"\n                [class.bg-green-100]=\"\n                  hasUserReacted(reaction, currentUserId || '')\n                \"\n                [class.text-green-600]=\"\n                  hasUserReacted(reaction, currentUserId || '')\n                \"\n              >\n                <span>{{ reaction.emoji }}</span>\n                <span>{{ reaction.count || 1 }}</span>\n              </button>\n            </div>\n\n            <!-- Bouton de réaction rapide (apparaît au hover) -->\n            <button\n              (click)=\"showQuickReactions(message, $event)\"\n              class=\"absolute -bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-full p-1 shadow-sm hover:shadow-md\"\n              title=\"Ajouter une réaction\"\n            >\n              <i\n                class=\"fas fa-smile text-gray-500 dark:text-gray-400 text-xs\"\n              ></i>\n            </button>\n          </div>\n        </div>\n      </ng-container>\n\n      <!-- Indicateur de frappe (seulement quand l'autre personne tape) -->\n      <div *ngIf=\"otherUserIsTyping\" class=\"flex items-start gap-2\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-8 h-8 rounded-full object-cover\"\n        />\n        <div class=\"bg-white dark:bg-gray-700 px-4 py-2 rounded-2xl shadow-sm\">\n          <div class=\"flex gap-1\">\n            <div class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n            <div\n              class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n              style=\"animation-delay: 0.1s\"\n            ></div>\n            <div\n              class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n              style=\"animation-delay: 0.2s\"\n            ></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </main>\n\n  <!-- Progress bar pour upload -->\n  <div\n    *ngIf=\"isUploading\"\n    class=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2\"\n  >\n    <div class=\"flex items-center gap-3\">\n      <div class=\"flex-1\">\n        <div\n          class=\"flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1\"\n        >\n          <span>Envoi en cours...</span>\n          <span>{{ uploadProgress.toFixed(0) }}%</span>\n        </div>\n        <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n          <div\n            class=\"bg-green-500 h-2 rounded-full transition-all duration-300\"\n            [style.width.%]=\"uploadProgress\"\n          ></div>\n        </div>\n      </div>\n      <button\n        (click)=\"resetUploadState()\"\n        class=\"p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500\"\n        title=\"Annuler\"\n      >\n        <i class=\"fas fa-times text-sm\"></i>\n      </button>\n    </div>\n  </div>\n\n  <!-- Zone d'input -->\n  <footer\n    class=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4\"\n  >\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      class=\"flex items-end gap-3\"\n    >\n      <!-- Actions gauche -->\n      <div class=\"flex gap-2\">\n        <button\n          type=\"button\"\n          (click)=\"toggleEmojiPicker()\"\n          class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n          [class.bg-green-100]=\"showEmojiPicker\"\n          [class.text-green-600]=\"showEmojiPicker\"\n          title=\"Émojis\"\n        >\n          <i class=\"fas fa-smile\"></i>\n        </button>\n        <button\n          type=\"button\"\n          (click)=\"toggleAttachmentMenu()\"\n          class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n          [class.bg-green-100]=\"showAttachmentMenu\"\n          [class.text-green-600]=\"showAttachmentMenu\"\n          title=\"Joindre un fichier\"\n        >\n          <i class=\"fas fa-paperclip\"></i>\n        </button>\n      </div>\n\n      <!-- Champ de saisie -->\n      <div class=\"flex-1\">\n        <textarea\n          formControlName=\"content\"\n          #messageTextarea\n          placeholder=\"Tapez votre message...\"\n          class=\"w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400\"\n          [class.opacity-50]=\"isInputDisabled()\"\n          [class.cursor-not-allowed]=\"isInputDisabled()\"\n          (input)=\"onInputChange($event)\"\n          (keydown)=\"onInputKeyDown($event)\"\n          (focus)=\"onInputFocus()\"\n          (blur)=\"onInputBlur()\"\n          rows=\"1\"\n          maxlength=\"4096\"\n          autocomplete=\"off\"\n          spellcheck=\"true\"\n        >\n        </textarea>\n      </div>\n\n      <!-- Actions droite -->\n      <div class=\"flex gap-2\">\n        <!-- Enregistrement vocal -->\n        <button\n          *ngIf=\"!messageForm.get('content')?.value?.trim()\"\n          type=\"button\"\n          (mousedown)=\"onRecordStart($event)\"\n          (mouseup)=\"onRecordEnd($event)\"\n          (mouseleave)=\"onRecordCancel($event)\"\n          (touchstart)=\"onRecordStart($event)\"\n          (touchend)=\"onRecordEnd($event)\"\n          (touchcancel)=\"onRecordCancel($event)\"\n          [ngClass]=\"{\n            'voice-record-button': true,\n            recording: isRecordingVoice,\n            processing: voiceRecordingState === 'processing'\n          }\"\n          [disabled]=\"voiceRecordingState === 'processing'\"\n          title=\"Maintenir pour enregistrer un message vocal\"\n        >\n          <i\n            class=\"fas fa-microphone\"\n            *ngIf=\"voiceRecordingState !== 'processing'\"\n          ></i>\n          <i\n            class=\"fas fa-spinner\"\n            *ngIf=\"voiceRecordingState === 'processing'\"\n          ></i>\n        </button>\n\n        <!-- Bouton d'envoi -->\n        <button\n          *ngIf=\"messageForm.get('content')?.value?.trim()\"\n          type=\"button\"\n          (click)=\"sendMessage()\"\n          class=\"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50\"\n          [disabled]=\"isSendingMessage\"\n          title=\"Envoyer\"\n        >\n          <i class=\"fas fa-paper-plane\" *ngIf=\"!isSendingMessage\"></i>\n          <i class=\"fas fa-spinner fa-spin\" *ngIf=\"isSendingMessage\"></i>\n        </button>\n      </div>\n    </form>\n  </footer>\n\n  <!-- Interface d'enregistrement vocal style WhatsApp -->\n  <div\n    *ngIf=\"isRecordingVoice\"\n    class=\"absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4 z-50\"\n  >\n    <div class=\"flex items-center gap-4\">\n      <!-- Bouton annuler -->\n      <button\n        (click)=\"cancelVoiceRecording()\"\n        class=\"p-2 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-800 transition-colors\"\n        title=\"Annuler\"\n      >\n        <i class=\"fas fa-times\"></i>\n      </button>\n\n      <!-- Indicateur d'enregistrement -->\n      <div class=\"flex items-center gap-3 flex-1\">\n        <!-- Icône micro animée -->\n        <div class=\"relative\">\n          <i class=\"fas fa-microphone text-red-500 text-xl animate-pulse\"></i>\n          <div\n            class=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping\"\n          ></div>\n        </div>\n\n        <!-- Waves d'animation -->\n        <div class=\"flex items-center gap-1\">\n          <div\n            *ngFor=\"let wave of voiceWaves; let i = index\"\n            class=\"bg-green-500 rounded-full transition-all duration-150\"\n            [style.width.px]=\"2\"\n            [style.height.px]=\"wave\"\n            [style.animation-delay.ms]=\"i * 100\"\n          ></div>\n        </div>\n\n        <!-- Durée d'enregistrement -->\n        <div class=\"text-gray-600 dark:text-gray-300 font-mono\">\n          {{ formatRecordingDuration(voiceRecordingDuration) }}\n        </div>\n      </div>\n\n      <!-- Bouton envoyer -->\n      <button\n        (click)=\"stopVoiceRecording()\"\n        class=\"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors\"\n        title=\"Envoyer l'enregistrement\"\n      >\n        <i class=\"fas fa-paper-plane\"></i>\n      </button>\n    </div>\n\n    <!-- Barre de progression -->\n    <div class=\"mt-3 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1\">\n      <div\n        class=\"bg-green-500 h-1 rounded-full transition-all duration-300\"\n        [style.width.%]=\"(voiceRecordingDuration / 60) * 100\"\n      ></div>\n    </div>\n\n    <!-- Instructions -->\n    <div class=\"mt-2 text-center text-sm text-gray-500 dark:text-gray-400\">\n      <div class=\"flex items-center justify-center gap-2\">\n        <i class=\"fas fa-info-circle\"></i>\n        <span>Relâchez pour envoyer • Glissez vers le haut pour annuler</span>\n      </div>\n      <div class=\"mt-1 text-xs\">\n        Durée max: 60 secondes • Format: {{ getRecordingFormat() }}\n      </div>\n    </div>\n  </div>\n\n  <!-- Sélecteur d'émojis -->\n  <div\n    *ngIf=\"showEmojiPicker\"\n    class=\"absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n  >\n    <div class=\"p-4\">\n      <div class=\"flex gap-2 mb-4 overflow-x-auto\">\n        <button\n          *ngFor=\"let category of emojiCategories\"\n          (click)=\"selectEmojiCategory(category)\"\n          class=\"px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0\"\n          [class.bg-green-100]=\"selectedEmojiCategory === category\"\n          [class.text-green-600]=\"selectedEmojiCategory === category\"\n          [class.hover:bg-gray-100]=\"selectedEmojiCategory !== category\"\n          [class.dark:hover:bg-gray-700]=\"selectedEmojiCategory !== category\"\n        >\n          {{ category.icon }}\n        </button>\n      </div>\n      <div class=\"grid grid-cols-8 gap-2 max-h-48 overflow-y-auto\">\n        <button\n          *ngFor=\"let emoji of getEmojisForCategory(selectedEmojiCategory)\"\n          (click)=\"insertEmoji(emoji)\"\n          class=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\"\n          [title]=\"emoji.name\"\n        >\n          {{ emoji.emoji }}\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Menu des pièces jointes -->\n  <div\n    *ngIf=\"showAttachmentMenu\"\n    class=\"absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n  >\n    <div class=\"p-4\">\n      <div class=\"grid grid-cols-2 gap-3\">\n        <button\n          (click)=\"triggerFileInput('image')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-image text-blue-600 dark:text-blue-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Photos</span\n          >\n        </button>\n        <button\n          (click)=\"triggerFileInput('video')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-video text-purple-600 dark:text-purple-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Vidéos</span\n          >\n        </button>\n        <button\n          (click)=\"triggerFileInput('document')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-file text-orange-600 dark:text-orange-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Documents</span\n          >\n        </button>\n        <button\n          (click)=\"openCamera()\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-camera text-green-600 dark:text-green-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Caméra</span\n          >\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Input caché pour fichiers -->\n  <input\n    #fileInput\n    type=\"file\"\n    class=\"hidden\"\n    (change)=\"onFileSelected($event)\"\n    [accept]=\"getFileAcceptTypes()\"\n    multiple\n  />\n\n  <!-- Sélecteur de réaction rapide -->\n  <div\n    *ngIf=\"showReactionPicker\"\n    class=\"fixed bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 p-3\"\n    [style.left.px]=\"contextMenuPosition.x - 100\"\n    [style.top.px]=\"contextMenuPosition.y - 60\"\n  >\n    <div class=\"flex gap-2\">\n      <button\n        *ngFor=\"let emoji of ['❤️', '😂', '😮', '😢', '😡', '👍']\"\n        (click)=\"quickReact(emoji)\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\"\n      >\n        {{ emoji }}\n      </button>\n    </div>\n  </div>\n\n  <!-- Menu contextuel pour messages -->\n  <div\n    *ngIf=\"showMessageContextMenu\"\n    class=\"fixed bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-48\"\n    [style.left.px]=\"contextMenuPosition.x\"\n    [style.top.px]=\"contextMenuPosition.y\"\n  >\n    <div class=\"p-2\">\n      <button\n        (click)=\"replyToMessage(selectedMessage)\"\n        class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n      >\n        <i class=\"fas fa-reply text-blue-500\"></i>\n        <span class=\"text-gray-700 dark:text-gray-300\">Répondre</span>\n      </button>\n      <button\n        (click)=\"forwardMessage(selectedMessage)\"\n        class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n      >\n        <i class=\"fas fa-share text-green-500\"></i>\n        <span class=\"text-gray-700 dark:text-gray-300\">Transférer</span>\n      </button>\n      <button\n        (click)=\"showQuickReactions(selectedMessage, $event)\"\n        class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left\"\n      >\n        <i class=\"fas fa-smile text-yellow-500\"></i>\n        <span class=\"text-gray-700 dark:text-gray-300\">Réagir</span>\n      </button>\n      <hr class=\"my-2 border-gray-200 dark:border-gray-600\" />\n      <button\n        (click)=\"deleteMessage(selectedMessage)\"\n        class=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left text-red-600\"\n      >\n        <i class=\"fas fa-trash text-red-500\"></i>\n        <span>Supprimer</span>\n      </button>\n    </div>\n  </div>\n\n  <!-- Overlay pour fermer les menus -->\n  <div\n    *ngIf=\"\n      showEmojiPicker ||\n      showAttachmentMenu ||\n      showMainMenu ||\n      showMessageContextMenu ||\n      showReactionPicker\n    \"\n    class=\"fixed inset-0 bg-black bg-opacity-25 z-40\"\n    (click)=\"closeAllMenus()\"\n  ></div>\n</div>\n\n<!-- Interface d'appel WebRTC -->\n<app-call-interface\n  [isVisible]=\"isInCall\"\n  [activeCall]=\"activeCall\"\n  [callType]=\"callType\"\n  [otherParticipant]=\"otherParticipant\"\n  (callEnded)=\"endCall()\"\n  (callAccepted)=\"onCallAccepted($event)\"\n  (callRejected)=\"onCallRejected()\"\n></app-call-interface>\n\n<!-- Visionneuse d'images plein écran -->\n<div\n  *ngIf=\"showImageViewer\"\n  class=\"fixed inset-0 bg-black bg-opacity-95 z-50 flex items-center justify-center\"\n  (click)=\"closeImageViewer()\"\n>\n  <div class=\"relative max-w-full max-h-full p-4\">\n    <!-- Bouton fermer -->\n    <button\n      (click)=\"closeImageViewer()\"\n      class=\"absolute top-4 right-4 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all\"\n      title=\"Fermer\"\n    >\n      <i class=\"fas fa-times text-xl\"></i>\n    </button>\n\n    <!-- Bouton télécharger -->\n    <button\n      (click)=\"downloadImage(); $event.stopPropagation()\"\n      class=\"absolute top-4 left-4 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all\"\n      title=\"Télécharger\"\n    >\n      <i class=\"fas fa-download text-xl\"></i>\n    </button>\n\n    <!-- Image -->\n    <img\n      [src]=\"selectedImage?.url\"\n      [alt]=\"selectedImage?.name || 'Image'\"\n      class=\"max-w-full max-h-full object-contain rounded-lg shadow-2xl image-viewer-zoom\"\n      (click)=\"$event.stopPropagation()\"\n      style=\"max-height: 90vh; max-width: 90vw\"\n    />\n\n    <!-- Informations de l'image -->\n    <div\n      class=\"absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 text-white p-3 rounded-lg\"\n      (click)=\"$event.stopPropagation()\"\n    >\n      <div class=\"flex items-center justify-between\">\n        <div>\n          <p class=\"font-medium\">{{ selectedImage?.name || \"Image\" }}</p>\n          <p class=\"text-sm opacity-75\">\n            {{ selectedImage?.size || \"Taille inconnue\" }}\n          </p>\n        </div>\n        <div class=\"flex gap-2\">\n          <!-- Bouton zoom -->\n          <button\n            (click)=\"zoomImage(1.2); $event.stopPropagation()\"\n            class=\"p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all\"\n            title=\"Zoom +\"\n          >\n            <i class=\"fas fa-search-plus\"></i>\n          </button>\n          <button\n            (click)=\"zoomImage(0.8); $event.stopPropagation()\"\n            class=\"p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all\"\n            title=\"Zoom -\"\n          >\n            <i class=\"fas fa-search-minus\"></i>\n          </button>\n          <!-- Bouton reset zoom -->\n          <button\n            (click)=\"resetZoom(); $event.stopPropagation()\"\n            class=\"p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all\"\n            title=\"Taille originale\"\n          >\n            <i class=\"fas fa-expand-arrows-alt\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";AAQA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,QAAQ,MAAM;AAGnC,SAASC,QAAQ,QAA4B,kCAAkC;;;;;;;;;;;;ICYvEC,EAAA,CAAAC,SAAA,cAGO;;;;;IAQLD,EAAA,CAAAE,cAAA,cAGC;IACOF,EAAA,CAAAG,MAAA,6BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9BJ,EAAA,CAAAE,cAAA,cAAwB;IACtBF,EAAA,CAAAC,SAAA,cAEO;IASTD,EAAA,CAAAI,YAAA,EAAM;;;;;IAERJ,EAAA,CAAAE,cAAA,WAA4B;IAC1BF,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IALLJ,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,OAAAC,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAC,QAAA,iBAAAF,MAAA,CAAAG,gBAAA,CAAAH,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAG,UAAA,OAKF;;;;;IAmCFX,EAAA,CAAAC,SAAA,YAA+C;;;;;IAC/CD,EAAA,CAAAC,SAAA,YAA6C;;;;;IA2FrCD,EAAA,CAAAC,SAAA,YAGK;;;;;;IAnBPD,EAAA,CAAAE,cAAA,iBAQC;IANCF,EAAA,CAAAY,UAAA,mBAAAC,6EAAA;MAAA,MAAAC,WAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAF,OAAA,CAAAG,WAAA,CAAAL,SAAA,CAAAM,EAAA,CAAqB;IAAA,EAAC;IAO/BvB,EAAA,CAAAC,SAAA,QAAwD;IACxDD,EAAA,CAAAE,cAAA,eAIG;IAAAF,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAClB;IACDJ,EAAA,CAAAwB,UAAA,IAAAC,wDAAA,gBAGK;IACPzB,EAAA,CAAAI,YAAA,EAAS;;;;;IAhBPJ,EAAA,CAAA0B,WAAA,eAAAC,OAAA,CAAAC,YAAA,KAAAX,SAAA,CAAAM,EAAA,CAA8C,qBAAAI,OAAA,CAAAC,YAAA,KAAAX,SAAA,CAAAM,EAAA,mBAAAI,OAAA,CAAAC,YAAA,KAAAX,SAAA,CAAAM,EAAA,wBAAAI,OAAA,CAAAC,YAAA,KAAAX,SAAA,CAAAM,EAAA;IAK3CvB,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA6B,UAAA,CAAAZ,SAAA,CAAAa,IAAA,CAAoB;IAAC9B,EAAA,CAAA+B,WAAA,UAAAd,SAAA,CAAAe,KAAA,CAA2B;IAGjDhC,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAA0B,WAAA,kBAAAC,OAAA,CAAAC,YAAA,KAAAX,SAAA,CAAAM,EAAA,CAAiD,uBAAAI,OAAA,CAAAC,YAAA,KAAAX,SAAA,CAAAM,EAAA;IAEhDvB,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAiC,iBAAA,CAAAhB,SAAA,CAAAiB,IAAA,CAAgB;IAGhBlC,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAmC,UAAA,SAAAR,OAAA,CAAAC,YAAA,KAAAX,SAAA,CAAAM,EAAA,CAA+B;;;;;IAtBxCvB,EAAA,CAAAE,cAAA,cAGC;IAEGF,EAAA,CAAAwB,UAAA,IAAAY,oDAAA,sBAoBS;IACXpC,EAAA,CAAAI,YAAA,EAAM;;;;IApBgBJ,EAAA,CAAAK,SAAA,GAAS;IAATL,EAAA,CAAAmC,UAAA,YAAAE,OAAA,CAAAC,MAAA,CAAS;;;;;;IArDvCtC,EAAA,CAAAE,cAAA,cAGC;IAGKF,EAAA,CAAAY,UAAA,mBAAA2B,6DAAA;MAAAvC,EAAA,CAAAe,aAAA,CAAAyB,IAAA;MAAA,MAAAC,OAAA,GAAAzC,EAAA,CAAAoB,aAAA;MAASqB,OAAA,CAAAC,YAAA,EAAc;MAAA,OAAA1C,EAAA,CAAAqB,WAAA,CAAAoB,OAAA,CAAAE,YAAA,GAAiB,KAAK;IAAA,EAAC;IAG9C3C,EAAA,CAAAC,SAAA,YAA2C;IAC3CD,EAAA,CAAAE,cAAA,eAA+C;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAElEJ,EAAA,CAAAE,cAAA,iBAEC;IACCF,EAAA,CAAAC,SAAA,YAA0C;IAC1CD,EAAA,CAAAE,cAAA,eAA+C;IAAAF,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEtEJ,EAAA,CAAAE,cAAA,kBAEC;IACCF,EAAA,CAAAC,SAAA,aAA2C;IAC3CD,EAAA,CAAAE,cAAA,gBAA+C;IAAAF,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAIrEJ,EAAA,CAAAE,cAAA,eAAsB;IAElBF,EAAA,CAAAY,UAAA,mBAAAgC,8DAAA;MAAA5C,EAAA,CAAAe,aAAA,CAAAyB,IAAA;MAAA,MAAAK,OAAA,GAAA7C,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAwB,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAK/B9C,EAAA,CAAAC,SAAA,SAGK;IACLD,EAAA,CAAAE,cAAA,gBACG;IAAAF,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EACtC;IACDJ,EAAA,CAAAC,SAAA,aAGK;IACPD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAwB,UAAA,KAAAuB,2CAAA,kBA2BM;IACR/C,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,SAAA,cAAwD;IACxDD,EAAA,CAAAE,cAAA,kBAEC;IACCF,EAAA,CAAAC,SAAA,aAAwC;IACxCD,EAAA,CAAAE,cAAA,gBAA+C;IAAAF,EAAA,CAAAG,MAAA,uBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;;IApD9DJ,EAAA,CAAAK,SAAA,IAAuC;IAAvCL,EAAA,CAAA0B,WAAA,gBAAAsB,MAAA,CAAAC,iBAAA,CAAuC,qBAAAD,MAAA,CAAAC,iBAAA;IAIrCjD,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAA6B,UAAA,EAAAqB,OAAA,GAAAF,MAAA,CAAAG,eAAA,qBAAAD,OAAA,CAAApB,IAAA,CAAiC;IACjC9B,EAAA,CAAA+B,WAAA,WAAAqB,OAAA,GAAAJ,MAAA,CAAAG,eAAA,qBAAAC,OAAA,CAAApB,KAAA,CAAwC;IAGvChC,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAM,kBAAA,kBAAA+C,OAAA,GAAAL,MAAA,CAAAG,eAAA,qBAAAE,OAAA,CAAAnB,IAAA,KAAoC;IAIrClC,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAA0B,WAAA,cAAAsB,MAAA,CAAAC,iBAAA,CAAqC;IAMtCjD,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAmC,UAAA,SAAAa,MAAA,CAAAC,iBAAA,CAAuB;;;;;IAmDhCjD,EAAA,CAAAE,cAAA,cAaC;IAIGF,EAAA,CAAAC,SAAA,YAEK;IACLD,EAAA,CAAAE,cAAA,YAAqE;IACnEF,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,YAAsD;IACpDF,EAAA,CAAAG,MAAA,0CACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,cAA4C;IAC1CF,EAAA,CAAAC,SAAA,eAAoE;IAStED,EAAA,CAAAI,YAAA,EAAM;;;;;IAMVJ,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,cAEO;IACPD,EAAA,CAAAE,cAAA,eACG;IAAAF,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAC5B;;;;;IAIHJ,EAAA,CAAAE,cAAA,cAGC;IAEGF,EAAA,CAAAC,SAAA,YAA+B;IACjCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAAwE;IACtEF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAAwD;IACtDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,wCAAAgD,MAAA,CAAA9C,gBAAA,kBAAA8C,MAAA,CAAA9C,gBAAA,CAAA+C,QAAA,MACF;;;;;IAaEvD,EAAA,CAAAE,cAAA,eAGC;IAKKF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAkD,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAAC,SAAA,OACF;;;;;;IAcF3D,EAAA,CAAAE,cAAA,eAGC;IAOGF,EAAA,CAAAY,UAAA,mBAAAgD,+EAAA;MAAA5D,EAAA,CAAAe,aAAA,CAAA8C,IAAA;MAAA,MAAAH,WAAA,GAAA1D,EAAA,CAAAoB,aAAA,GAAAF,SAAA;MAAA,MAAA4C,OAAA,GAAA9D,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAyC,OAAA,CAAAC,eAAA,CAAAL,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,CAAoC;IAAA,EAAC;IANhDvB,EAAA,CAAAI,YAAA,EAOE;;;;IANAJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAmC,UAAA,SAAAuB,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAC,KAAA,yCAAAjE,EAAA,CAAAkE,aAAA,CAEC,QAAAR,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAT,QAAA;;;;;IA+BHvD,EAAA,CAAAE,cAAA,eAQC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAHJJ,EAAA,CAAA+B,WAAA,UAAAoC,OAAA,CAAAC,YAAA,CAAAV,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,EAAiD;IAEjDvB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAoD,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAT,QAAA,MACF;;;;;IAGAvD,EAAA,CAAAE,cAAA,eAAoE;IAClEF,EAAA,CAAAC,SAAA,eAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;IADCJ,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAmC,UAAA,cAAAkC,OAAA,CAAAC,oBAAA,CAAAZ,WAAA,CAAAa,OAAA,GAAAvE,EAAA,CAAAwE,cAAA,CAAmD;;;;;IAaxDxE,EAAA,CAAAC,SAAA,eAUO;;;;;IANLD,EAAA,CAAA+B,WAAA,WAAA2B,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAAkD,OAAA,CAAAC,aAAA,IAAAD,OAAA,CAAAE,UAAA,kBAIC;IAND3E,EAAA,CAAA0B,WAAA,gBAAAgC,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAAkD,OAAA,CAAAC,aAAA,CAAyD,mBAAAhB,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAAkD,OAAA,CAAAC,aAAA;IAOzD1E,EAAA,CAAAmC,UAAA,cAAAsC,OAAA,CAAAH,oBAAA,CAAAZ,WAAA,CAAAa,OAAA,GAAAvE,EAAA,CAAAwE,cAAA,CAAmD;;;;;;IAlBvDxE,EAAA,CAAAE,cAAA,UAA+B;IAI3BF,EAAA,CAAAY,UAAA,mBAAAgE,+EAAA;MAAA5E,EAAA,CAAAe,aAAA,CAAA8D,IAAA;MAAA,MAAAnB,WAAA,GAAA1D,EAAA,CAAAoB,aAAA,GAAAF,SAAA;MAAA,MAAA4D,OAAA,GAAA9E,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAyD,OAAA,CAAAC,eAAA,CAAArB,WAAA,CAAwB;IAAA,EAAC,kBAAAsB,8EAAAC,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAA8D,IAAA;MAAA,MAAAnB,WAAA,GAAA1D,EAAA,CAAAoB,aAAA,GAAAF,SAAA;MAAA,MAAAgE,OAAA,GAAAlF,EAAA,CAAAoB,aAAA;MAAA,OAC1BpB,EAAA,CAAAqB,WAAA,CAAA6D,OAAA,CAAAC,WAAA,CAAAF,MAAA,EAAAvB,WAAA,CAA4B;IAAA,EADF,mBAAA0B,+EAAAH,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAA8D,IAAA;MAAA,MAAAnB,WAAA,GAAA1D,EAAA,CAAAoB,aAAA,GAAAF,SAAA;MAAA,MAAAmE,OAAA,GAAArF,EAAA,CAAAoB,aAAA;MAAA,OAEzBpB,EAAA,CAAAqB,WAAA,CAAAgE,OAAA,CAAAC,YAAA,CAAAL,MAAA,EAAAvB,WAAA,CAA6B;IAAA,EAFJ;IAHpC1D,EAAA,CAAAI,YAAA,EAME;IAEFJ,EAAA,CAAAwB,UAAA,IAAA+D,+DAAA,mBAUO;IACTvF,EAAA,CAAAI,YAAA,EAAM;;;;;IAlBFJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAmC,UAAA,QAAAqD,OAAA,CAAAC,WAAA,CAAA/B,WAAA,GAAA1D,EAAA,CAAAkE,aAAA,CAA4B,QAAAR,WAAA,CAAAa,OAAA;IAQ3BvE,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAmC,UAAA,SAAAuB,WAAA,CAAAa,OAAA,CAAqB;;;;;;IAqDpBvE,EAAA,CAAAE,cAAA,eA0CC;IArBCF,EAAA,CAAAY,UAAA,mBAAA8E,qFAAAT,MAAA;MAAA,MAAAnE,WAAA,GAAAd,EAAA,CAAAe,aAAA,CAAA4E,IAAA;MAAA,MAAAC,KAAA,GAAA9E,WAAA,CAAA+E,KAAA;MAAA,MAAAnC,WAAA,GAAA1D,EAAA,CAAAoB,aAAA,IAAAF,SAAA;MAAA,MAAA4E,OAAA,GAAA9F,EAAA,CAAAoB,aAAA;MACyB6D,MAAA,CAAAc,eAAA,EAAwB;MAAA,OAAE/F,EAAA,CAAAqB,WAAA,CAAAyE,OAAA,CAAAE,gBAAA,CAAAtC,WAAA,EAAAkC,KAAA,CAEvE;IAAA,EADqB;IAmBF5F,EAAA,CAAAI,YAAA,EAAM;;;;;;;IA1BLJ,EAAA,CAAA+B,WAAA,WAAAkE,QAAA,OAAwB,2BAAAC,OAAA,CAAAC,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,KAAAqE,KAAA,IAAAM,OAAA,CAAAE,gBAAA,CAAA1C,WAAA,sCAAAwC,OAAA,CAAAC,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,KAAAqE,KAAA,IAAAM,OAAA,CAAAE,gBAAA,CAAA1C,WAAA;IAbxB1D,EAAA,CAAA0B,WAAA,iBAAAwE,OAAA,CAAAC,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,EAAiD,gBAAA2E,OAAA,CAAAC,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,KAAAqE,KAAA,IAAAM,OAAA,CAAAE,gBAAA,CAAA1C,WAAA,kBAAAwC,OAAA,CAAAC,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,KAAAqE,KAAA,GAAAM,OAAA,CAAAE,gBAAA,CAAA1C,WAAA,CAAAnC,EAAA,uBAAA2E,OAAA,CAAAC,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,KAAAqE,KAAA,GAAAM,OAAA,CAAAE,gBAAA,CAAA1C,WAAA,CAAAnC,EAAA,oBAAA2E,OAAA,CAAAC,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,KAAAqE,KAAA,IAAAM,OAAA,CAAAE,gBAAA,CAAA1C,WAAA;;;;;IA2DjD1D,EAAA,CAAAE,cAAA,gBAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA+F,OAAA,CAAAC,oBAAA,CAAA5C,WAAA,CAAAnC,EAAA,EAAAgF,KAAA,OACF;;;;;IAEAvG,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAC,SAAA,eAEO;IASTD,EAAA,CAAAI,YAAA,EAAM;;;;;;IA5HdJ,EAAA,CAAAE,cAAA,eAOC;IASGF,EAAA,CAAAY,UAAA,mBAAA4F,kFAAAvB,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAA0F,IAAA;MAAA,MAAA/C,WAAA,GAAA1D,EAAA,CAAAoB,aAAA,GAAAF,SAAA;MAAA,MAAAwF,OAAA,GAAA1G,EAAA,CAAAoB,aAAA;MAAS6D,MAAA,CAAAc,eAAA,EAAwB;MAAA,OAAE/F,EAAA,CAAAqB,WAAA,CAAAqF,OAAA,CAAAC,mBAAA,CAAAjD,WAAA,CAA4B;IAAA,EAAC;IAahE1D,EAAA,CAAAC,SAAA,aAKK;IACPD,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAE,cAAA,aAA4B;IAGxBF,EAAA,CAAAwB,UAAA,IAAAoF,+DAAA,oBA0CO;IACT5G,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAA4D;IAKtDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,iBAA+C;IAAAF,EAAA,CAAAG,MAAA,SAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAE,cAAA,iBAEC;IACCF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAETJ,EAAA,CAAAE,cAAA,gBAAqC;IAEnCF,EAAA,CAAAwB,UAAA,KAAAqF,iEAAA,oBAKO;IAEP7G,EAAA,CAAAwB,UAAA,KAAAsF,gEAAA,mBAeM;IACR9G,EAAA,CAAAI,YAAA,EAAM;IAKVJ,EAAA,CAAAE,cAAA,gBAEC;IAGGF,EAAA,CAAAY,UAAA,mBAAAmG,mFAAA9B,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAA0F,IAAA;MAAA,MAAA/C,WAAA,GAAA1D,EAAA,CAAAoB,aAAA,GAAAF,SAAA;MAAA,MAAA8F,OAAA,GAAAhH,EAAA,CAAAoB,aAAA;MAAS6D,MAAA,CAAAc,eAAA,EAAwB;MAAA,OAAE/F,EAAA,CAAAqB,WAAA,CAAA2F,OAAA,CAAAC,gBAAA,CAAAvD,WAAA,CAAyB;IAAA,EAAC;IAI7D1D,EAAA,CAAAC,SAAA,cAA6C;IAC/CD,EAAA,CAAAI,YAAA,EAAS;;;;;IAzIXJ,EAAA,CAAA0B,WAAA,cAAAwF,OAAA,CAAAf,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,EAA8C,WAAA2F,OAAA,CAAAf,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,oBAAA2F,OAAA,CAAAf,cAAA,CAAAzC,WAAA,CAAAnC,EAAA;IAsB5CvB,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAA+B,WAAA,cAAAmF,OAAA,CAAAf,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,+BAEC;IAhBDvB,EAAA,CAAA0B,WAAA,iBAAAwF,OAAA,CAAAf,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,EAAiD,uBAAA2F,OAAA,CAAAf,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,iBAAA2F,OAAA,CAAAf,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,uBAAA2F,OAAA,CAAAf,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,oBAAA2F,OAAA,CAAAf,cAAA,CAAAzC,WAAA,CAAAnC,EAAA;IAoB/CvB,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAA0B,WAAA,aAAAwF,OAAA,CAAAf,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,EAA6C,aAAA2F,OAAA,CAAAf,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,gBAAA2F,OAAA,CAAAf,cAAA,CAAAzC,WAAA,CAAAnC,EAAA;IAU1BvB,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAmC,UAAA,YAAA+E,OAAA,CAAAC,aAAA,CAAAzD,WAAA,EAA2B;IAkD1C1D,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA4G,OAAA,CAAAE,mBAAA,CAAA1D,WAAA,OACF;IAKE1D,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA4G,OAAA,CAAAG,gBAAA,CAAA3D,WAAA,OACF;IAKG1D,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAAmC,UAAA,SAAA+E,OAAA,CAAAZ,oBAAA,CAAA5C,WAAA,CAAAnC,EAAA,EAAAgF,KAAA,OAAkD;IAOlDvG,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmC,UAAA,SAAA+E,OAAA,CAAAf,cAAA,CAAAzC,WAAA,CAAAnC,EAAA,EAAgC;;;;;;IAmC3CvB,EAAA,CAAAE,cAAA,eAQC;IADCF,EAAA,CAAAY,UAAA,mBAAA0G,+EAAA;MAAAtH,EAAA,CAAAe,aAAA,CAAAwG,IAAA;MAAA,MAAA7D,WAAA,GAAA1D,EAAA,CAAAoB,aAAA,GAAAF,SAAA;MAAA,MAAAsG,OAAA,GAAAxH,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAmG,OAAA,CAAAC,YAAA,CAAA/D,WAAA,CAAqB;IAAA,EAAC;IAE/B1D,EAAA,CAAAE,cAAA,eAAuD;IACrDF,EAAA,CAAAC,SAAA,QAAsC;IACxCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAA4B;IAExBF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAAsD;IACpDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAE,cAAA,kBAEC;IACCF,EAAA,CAAAC,SAAA,aAAuC;IACzCD,EAAA,CAAAI,YAAA,EAAS;;;;;IAdJJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA6B,UAAA,CAAA6F,OAAA,CAAAC,WAAA,CAAAjE,WAAA,EAA8B;IAI/B1D,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAoH,OAAA,CAAAE,WAAA,CAAAlE,WAAA,OACF;IAEE1D,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAoH,OAAA,CAAAG,WAAA,CAAAnE,WAAA,OACF;;;;;IAkBA1D,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IAvBPD,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAwB,UAAA,IAAAsG,8DAAA,iBAIK;IACL9H,EAAA,CAAAwB,UAAA,IAAAuG,8DAAA,iBAIK;IACL/H,EAAA,CAAAwB,UAAA,IAAAwG,8DAAA,iBAIK;IACLhI,EAAA,CAAAwB,UAAA,IAAAyG,8DAAA,iBAIK;IACPjI,EAAA,CAAAI,YAAA,EAAM;;;;IAlBDJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAmC,UAAA,SAAAuB,WAAA,CAAAwE,MAAA,eAAkC;IAKlClI,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAmC,UAAA,SAAAuB,WAAA,CAAAwE,MAAA,YAA+B;IAK/BlI,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAmC,UAAA,SAAAuB,WAAA,CAAAwE,MAAA,iBAAoC;IAKpClI,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAmC,UAAA,SAAAuB,WAAA,CAAAwE,MAAA,YAA+B;;;;;;IAWpClI,EAAA,CAAAE,cAAA,kBAUC;IARCF,EAAA,CAAAY,UAAA,mBAAAuH,4FAAA;MAAA,MAAArH,WAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAqH,IAAA;MAAA,MAAAC,YAAA,GAAAvH,WAAA,CAAAI,SAAA;MAAA,MAAAwC,WAAA,GAAA1D,EAAA,CAAAoB,aAAA,IAAAF,SAAA;MAAA,MAAAoH,OAAA,GAAAtI,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAiH,OAAA,CAAAC,cAAA,CAAA7E,WAAA,CAAAnC,EAAA,EAAA8G,YAAA,CAAAG,KAAA,CAA2C;IAAA,EAAC;IASrDxI,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjCJ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IARtCJ,EAAA,CAAA0B,WAAA,iBAAA+G,OAAA,CAAAC,cAAA,CAAAL,YAAA,EAAAI,OAAA,CAAA/D,aAAA,QAEC,mBAAA+D,OAAA,CAAAC,cAAA,CAAAL,YAAA,EAAAI,OAAA,CAAA/D,aAAA;IAKK1E,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAiC,iBAAA,CAAAoG,YAAA,CAAAG,KAAA,CAAoB;IACpBxI,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAiC,iBAAA,CAAAoG,YAAA,CAAAM,KAAA,MAAyB;;;;;IAhBnC3I,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAwB,UAAA,IAAAoH,mEAAA,sBAaS;IACX5I,EAAA,CAAAI,YAAA,EAAM;;;;IAbmBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAmC,UAAA,YAAAuB,WAAA,CAAAmF,SAAA,CAAoB;;;;;;IAhUnD7I,EAAA,CAAA8I,uBAAA,GAMC;IAEC9I,EAAA,CAAAwB,UAAA,IAAAuH,yDAAA,mBAWM;IAGN/I,EAAA,CAAAE,cAAA,eAOC;IAFCF,EAAA,CAAAY,UAAA,mBAAAoI,yEAAA/D,MAAA;MAAA,MAAAnE,WAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAkI,IAAA;MAAA,MAAAvF,WAAA,GAAA5C,WAAA,CAAAI,SAAA;MAAA,MAAAgI,OAAA,GAAAlJ,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA6H,OAAA,CAAAC,cAAA,CAAAzF,WAAA,EAAAuB,MAAA,CAA+B;IAAA,EAAC,yBAAAmE,+EAAAnE,MAAA;MAAA,MAAAnE,WAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAkI,IAAA;MAAA,MAAAvF,WAAA,GAAA5C,WAAA,CAAAI,SAAA;MAAA,MAAAmI,OAAA,GAAArJ,EAAA,CAAAoB,aAAA;MAAA,OAC1BpB,EAAA,CAAAqB,WAAA,CAAAgI,OAAA,CAAAC,oBAAA,CAAA5F,WAAA,EAAAuB,MAAA,CAAqC;IAAA,EADX;IAIzCjF,EAAA,CAAAwB,UAAA,IAAA+H,yDAAA,mBAYM;IAGNvJ,EAAA,CAAAE,cAAA,eAqBC;IAECF,EAAA,CAAAwB,UAAA,IAAAgI,yDAAA,mBAUM;IAGNxJ,EAAA,CAAAwB,UAAA,IAAAiI,yDAAA,mBAEM;IAGNzJ,EAAA,CAAAwB,UAAA,IAAAkI,yDAAA,kBAoBM;IAGN1J,EAAA,CAAAwB,UAAA,IAAAmI,yDAAA,qBA8IM;IAGN3J,EAAA,CAAAwB,UAAA,IAAAoI,yDAAA,oBAyBM;IAGN5J,EAAA,CAAAE,cAAA,gBAEC;IACOF,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAwB,UAAA,KAAAqI,0DAAA,mBAwBM;IACR7J,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAwB,UAAA,KAAAsI,0DAAA,mBAkBM;IAGN9J,EAAA,CAAAE,cAAA,mBAIC;IAHCF,EAAA,CAAAY,UAAA,mBAAAmJ,6EAAA9E,MAAA;MAAA,MAAAnE,WAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAkI,IAAA;MAAA,MAAAvF,WAAA,GAAA5C,WAAA,CAAAI,SAAA;MAAA,MAAA8I,OAAA,GAAAhK,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA2I,OAAA,CAAAC,kBAAA,CAAAvG,WAAA,EAAAuB,MAAA,CAAmC;IAAA,EAAC;IAI7CjF,EAAA,CAAAC,SAAA,cAEK;IACPD,EAAA,CAAAI,YAAA,EAAS;IAGfJ,EAAA,CAAAkK,qBAAA,EAAe;;;;;;IAlVVlK,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmC,UAAA,SAAAgI,OAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAejCrK,EAAA,CAAAK,SAAA,GAA0D;IAA1DL,EAAA,CAAA0B,WAAA,iBAAAgC,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA,CAA0D,mBAAAhB,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA;IAE1D1E,EAAA,CAAAmC,UAAA,oBAAAuB,WAAA,CAAAnC,EAAA,CAA8B;IAM3BvB,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAAmC,UAAA,UAAAuB,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA,IAAAyF,OAAA,CAAAG,gBAAA,CAAAD,KAAA,EAAiE;IAkBlErK,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAA+B,WAAA,sBAAA2B,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA,IAAAyF,OAAA,CAAAxF,UAAA,kBAIC,WAAAjB,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA,IAAAyF,OAAA,CAAAxF,UAAA;IAND3E,EAAA,CAAA0B,WAAA,iBAAAgC,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA,CAA0D,gBAAAhB,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA,eAAAhB,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA,oBAAAhB,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA,uBAAAhB,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA,yBAAAhB,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA,oBAAAhB,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA,oBAAAhB,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA;IAsBvD1E,EAAA,CAAAK,SAAA,GAKf;IALeL,EAAA,CAAAmC,UAAA,SAAAgI,OAAA,CAAAI,mBAAA,OAAA7G,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA,IAAAyF,OAAA,CAAAK,oBAAA,CAAAH,KAAA,EAKf;IAOkBrK,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAmC,UAAA,SAAAgI,OAAA,CAAAM,cAAA,CAAA/G,WAAA,aAAwC;IAKxC1D,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAmC,UAAA,SAAAgI,OAAA,CAAAO,QAAA,CAAAhH,WAAA,EAAuB;IAwB1B1D,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAmC,UAAA,SAAAgI,OAAA,CAAAM,cAAA,CAAA/G,WAAA,cAAyC;IAiJzC1D,EAAA,CAAAK,SAAA,GAKf;IALeL,EAAA,CAAAmC,UAAA,SAAAgI,OAAA,CAAAQ,OAAA,CAAAjH,WAAA,KAAAyG,OAAA,CAAAM,cAAA,CAAA/G,WAAA,kBAAAyG,OAAA,CAAAO,QAAA,CAAAhH,WAAA,EAKf;IAyBoB1D,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAiC,iBAAA,CAAAkI,OAAA,CAAAS,iBAAA,CAAAlH,WAAA,CAAAC,SAAA,EAA0C;IAE7C3D,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAmC,UAAA,UAAAuB,WAAA,CAAAM,MAAA,kBAAAN,WAAA,CAAAM,MAAA,CAAAzC,EAAA,MAAA4I,OAAA,CAAAzF,aAAA,CAA0C;IA4B5C1E,EAAA,CAAAK,SAAA,GAAuD;IAAvDL,EAAA,CAAAmC,UAAA,SAAAuB,WAAA,CAAAmF,SAAA,IAAAnF,WAAA,CAAAmF,SAAA,CAAAgC,MAAA,KAAuD;;;;;IAkChE7K,EAAA,CAAAE,cAAA,eAA8D;IAC5DF,EAAA,CAAAC,SAAA,eAIE;IACFD,EAAA,CAAAE,cAAA,eAAuE;IAEnEF,EAAA,CAAAC,SAAA,eAAmE;IASrED,EAAA,CAAAI,YAAA,EAAM;;;;IAfNJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAmC,UAAA,SAAA2I,OAAA,CAAAtK,gBAAA,kBAAAsK,OAAA,CAAAtK,gBAAA,CAAAyD,KAAA,yCAAAjE,EAAA,CAAAkE,aAAA,CAAqE,QAAA4G,OAAA,CAAAtK,gBAAA,kBAAAsK,OAAA,CAAAtK,gBAAA,CAAA+C,QAAA;;;;;IAjW3EvD,EAAA,CAAAE,cAAA,cAAiE;IAC/DF,EAAA,CAAAwB,UAAA,IAAAuJ,mDAAA,8BA2Ve;IAGf/K,EAAA,CAAAwB,UAAA,IAAAwJ,0CAAA,mBAmBM;IACRhL,EAAA,CAAAI,YAAA,EAAM;;;;IAhXuBJ,EAAA,CAAAK,SAAA,GACZ;IADYL,EAAA,CAAAmC,UAAA,YAAA8I,OAAA,CAAAC,QAAA,CACZ,iBAAAD,OAAA,CAAAE,gBAAA;IA2VTnL,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAmC,UAAA,SAAA8I,OAAA,CAAAG,iBAAA,CAAuB;;;;;;IAwBjCpL,EAAA,CAAAE,cAAA,eAGC;IAMaF,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9BJ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE/CJ,EAAA,CAAAE,cAAA,eAAkE;IAChEF,EAAA,CAAAC,SAAA,eAGO;IACTD,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAE,cAAA,mBAIC;IAHCF,EAAA,CAAAY,UAAA,mBAAAyK,8DAAA;MAAArL,EAAA,CAAAe,aAAA,CAAAuK,IAAA;MAAA,MAAAC,OAAA,GAAAvL,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAkK,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAI5BxL,EAAA,CAAAC,SAAA,cAAoC;IACtCD,EAAA,CAAAI,YAAA,EAAS;;;;IAfCJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,kBAAA,KAAAmL,OAAA,CAAAC,cAAA,CAAAC,OAAA,SAAgC;IAKpC3L,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAA+B,WAAA,UAAA0J,OAAA,CAAAC,cAAA,MAAgC;;;;;IAwFlC1L,EAAA,CAAAC,SAAA,aAGK;;;;;IACLD,EAAA,CAAAC,SAAA,aAGK;;;;;;;;;;;;;IAxBPD,EAAA,CAAAE,cAAA,kBAgBC;IAbCF,EAAA,CAAAY,UAAA,uBAAAgL,oEAAA3G,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAA8K,KAAA;MAAA,MAAAC,QAAA,GAAA9L,EAAA,CAAAoB,aAAA;MAAA,OAAapB,EAAA,CAAAqB,WAAA,CAAAyK,QAAA,CAAAC,aAAA,CAAA9G,MAAA,CAAqB;IAAA,EAAC,qBAAA+G,kEAAA/G,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAA8K,KAAA;MAAA,MAAAI,QAAA,GAAAjM,EAAA,CAAAoB,aAAA;MAAA,OACxBpB,EAAA,CAAAqB,WAAA,CAAA4K,QAAA,CAAAC,WAAA,CAAAjH,MAAA,CAAmB;IAAA,EADK,wBAAAkH,qEAAAlH,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAA8K,KAAA;MAAA,MAAAO,QAAA,GAAApM,EAAA,CAAAoB,aAAA;MAAA,OAErBpB,EAAA,CAAAqB,WAAA,CAAA+K,QAAA,CAAAC,cAAA,CAAApH,MAAA,CAAsB;IAAA,EAFD,wBAAAqH,qEAAArH,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAA8K,KAAA;MAAA,MAAAU,QAAA,GAAAvM,EAAA,CAAAoB,aAAA;MAAA,OAGrBpB,EAAA,CAAAqB,WAAA,CAAAkL,QAAA,CAAAR,aAAA,CAAA9G,MAAA,CAAqB;IAAA,EAHA,sBAAAuH,mEAAAvH,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAA8K,KAAA;MAAA,MAAAY,QAAA,GAAAzM,EAAA,CAAAoB,aAAA;MAAA,OAIvBpB,EAAA,CAAAqB,WAAA,CAAAoL,QAAA,CAAAP,WAAA,CAAAjH,MAAA,CAAmB;IAAA,EAJI,yBAAAyH,sEAAAzH,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAA8K,KAAA;MAAA,MAAAc,QAAA,GAAA3M,EAAA,CAAAoB,aAAA;MAAA,OAKpBpB,EAAA,CAAAqB,WAAA,CAAAsL,QAAA,CAAAN,cAAA,CAAApH,MAAA,CAAsB;IAAA,EALF;IAcnCjF,EAAA,CAAAwB,UAAA,IAAAoL,2CAAA,iBAGK;IACL5M,EAAA,CAAAwB,UAAA,IAAAqL,2CAAA,iBAGK;IACP7M,EAAA,CAAAI,YAAA,EAAS;;;;IAhBPJ,EAAA,CAAAmC,UAAA,YAAAnC,EAAA,CAAA8M,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,gBAAA,EAAAD,OAAA,CAAAE,mBAAA,mBAIE,aAAAF,OAAA,CAAAE,mBAAA;IAMClN,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAmC,UAAA,SAAA6K,OAAA,CAAAE,mBAAA,kBAA0C;IAI1ClN,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAmC,UAAA,SAAA6K,OAAA,CAAAE,mBAAA,kBAA0C;;;;;IAa7ClN,EAAA,CAAAC,SAAA,aAA4D;;;;;IAC5DD,EAAA,CAAAC,SAAA,aAA+D;;;;;;IATjED,EAAA,CAAAE,cAAA,kBAOC;IAJCF,EAAA,CAAAY,UAAA,mBAAAuM,gEAAA;MAAAnN,EAAA,CAAAe,aAAA,CAAAqM,KAAA;MAAA,MAAAC,QAAA,GAAArN,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAgM,QAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAKvBtN,EAAA,CAAAwB,UAAA,IAAA+L,2CAAA,iBAA4D;IAC5DvN,EAAA,CAAAwB,UAAA,IAAAgM,2CAAA,iBAA+D;IACjExN,EAAA,CAAAI,YAAA,EAAS;;;;IALPJ,EAAA,CAAAmC,UAAA,aAAAsL,OAAA,CAAAC,gBAAA,CAA6B;IAGE1N,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAmC,UAAA,UAAAsL,OAAA,CAAAC,gBAAA,CAAuB;IACnB1N,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAmC,UAAA,SAAAsL,OAAA,CAAAC,gBAAA,CAAsB;;;;;IAiCzD1N,EAAA,CAAAC,SAAA,eAMO;;;;;IAHLD,EAAA,CAAA+B,WAAA,kBAAoB,WAAA4L,SAAA,2BAAAC,MAAA;;;;;;IA7B9B5N,EAAA,CAAAE,cAAA,eAGC;IAIKF,EAAA,CAAAY,UAAA,mBAAAiN,6DAAA;MAAA7N,EAAA,CAAAe,aAAA,CAAA+M,KAAA;MAAA,MAAAC,QAAA,GAAA/N,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA0M,QAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAIhChO,EAAA,CAAAC,SAAA,aAA4B;IAC9BD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,eAA4C;IAGxCF,EAAA,CAAAC,SAAA,aAAoE;IAItED,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAAqC;IACnCF,EAAA,CAAAwB,UAAA,IAAAyM,0CAAA,mBAMO;IACTjO,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,gBAAwD;IACtDF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAE,cAAA,mBAIC;IAHCF,EAAA,CAAAY,UAAA,mBAAAsN,8DAAA;MAAAlO,EAAA,CAAAe,aAAA,CAAA+M,KAAA;MAAA,MAAAK,QAAA,GAAAnO,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA8M,QAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAI9BpO,EAAA,CAAAC,SAAA,cAAkC;IACpCD,EAAA,CAAAI,YAAA,EAAS;IAIXJ,EAAA,CAAAE,cAAA,gBAAuE;IACrEF,EAAA,CAAAC,SAAA,gBAGO;IACTD,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,gBAAuE;IAEnEF,EAAA,CAAAC,SAAA,cAAkC;IAClCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,2EAAyD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAExEJ,EAAA,CAAAE,cAAA,gBAA0B;IACxBF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IAxCiBJ,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAmC,UAAA,YAAAkM,OAAA,CAAAC,UAAA,CAAe;IAUlCtO,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA+N,OAAA,CAAAE,uBAAA,CAAAF,OAAA,CAAAG,sBAAA,OACF;IAiBAxO,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAA+B,WAAA,UAAAsM,OAAA,CAAAG,sBAAA,iBAAqD;IAWrDxO,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,iDAAA+N,OAAA,CAAAI,kBAAA,QACF;;;;;;IAWEzO,EAAA,CAAAE,cAAA,kBAQC;IANCF,EAAA,CAAAY,UAAA,mBAAA8N,sEAAA;MAAA,MAAA5N,WAAA,GAAAd,EAAA,CAAAe,aAAA,CAAA4N,KAAA;MAAA,MAAAC,aAAA,GAAA9N,WAAA,CAAAI,SAAA;MAAA,MAAA2N,QAAA,GAAA7O,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAwN,QAAA,CAAAC,mBAAA,CAAAF,aAAA,CAA6B;IAAA,EAAC;IAOvC5O,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IANPJ,EAAA,CAAA0B,WAAA,iBAAAqN,QAAA,CAAAC,qBAAA,KAAAJ,aAAA,CAAyD,mBAAAG,QAAA,CAAAC,qBAAA,KAAAJ,aAAA,uBAAAG,QAAA,CAAAC,qBAAA,KAAAJ,aAAA,4BAAAG,QAAA,CAAAC,qBAAA,KAAAJ,aAAA;IAKzD5O,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAsO,aAAA,CAAA9M,IAAA,MACF;;;;;;IAGA9B,EAAA,CAAAE,cAAA,kBAKC;IAHCF,EAAA,CAAAY,UAAA,mBAAAqO,sEAAA;MAAA,MAAAnO,WAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAmO,KAAA;MAAA,MAAAC,UAAA,GAAArO,WAAA,CAAAI,SAAA;MAAA,MAAAkO,QAAA,GAAApP,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA+N,QAAA,CAAAC,WAAA,CAAAF,UAAA,CAAkB;IAAA,EAAC;IAI5BnP,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAHPJ,EAAA,CAAAmC,UAAA,UAAAgN,UAAA,CAAAjN,IAAA,CAAoB;IAEpBlC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA6O,UAAA,CAAA3G,KAAA,MACF;;;;;IA1BNxI,EAAA,CAAAE,cAAA,eAGC;IAGKF,EAAA,CAAAwB,UAAA,IAAA8N,6CAAA,sBAUS;IACXtP,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAA6D;IAC3DF,EAAA,CAAAwB,UAAA,IAAA+N,6CAAA,sBAOS;IACXvP,EAAA,CAAAI,YAAA,EAAM;;;;IApBmBJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAmC,UAAA,YAAAqN,OAAA,CAAAC,eAAA,CAAkB;IAarBzP,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAmC,UAAA,YAAAqN,OAAA,CAAAE,oBAAA,CAAAF,OAAA,CAAAR,qBAAA,EAA8C;;;;;;IAYxEhP,EAAA,CAAAE,cAAA,eAGC;IAIOF,EAAA,CAAAY,UAAA,mBAAA+O,6DAAA;MAAA3P,EAAA,CAAAe,aAAA,CAAA6O,KAAA;MAAA,MAAAC,QAAA,GAAA7P,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAwO,QAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnC9P,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAC,SAAA,aAA6D;IAC/DD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBACG;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAEHJ,EAAA,CAAAE,cAAA,kBAGC;IAFCF,EAAA,CAAAY,UAAA,mBAAAmP,6DAAA;MAAA/P,EAAA,CAAAe,aAAA,CAAA6O,KAAA;MAAA,MAAAI,QAAA,GAAAhQ,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA2O,QAAA,CAAAF,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnC9P,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAC,SAAA,cAAiE;IACnED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAEHJ,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAY,UAAA,mBAAAqP,8DAAA;MAAAjQ,EAAA,CAAAe,aAAA,CAAA6O,KAAA;MAAA,MAAAM,QAAA,GAAAlQ,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA6O,QAAA,CAAAJ,gBAAA,CAAiB,UAAU,CAAC;IAAA,EAAC;IAGtC9P,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAC,SAAA,cAAgE;IAClED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EACX;IAEHJ,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAY,UAAA,mBAAAuP,8DAAA;MAAAnQ,EAAA,CAAAe,aAAA,CAAA6O,KAAA;MAAA,MAAAQ,QAAA,GAAApQ,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA+O,QAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAGtBrQ,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAC,SAAA,cAAgE;IAClED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;;;;;;IAwBLJ,EAAA,CAAAE,cAAA,kBAIC;IAFCF,EAAA,CAAAY,UAAA,mBAAA0P,sEAAA;MAAA,MAAAxP,WAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAwP,KAAA;MAAA,MAAAC,UAAA,GAAA1P,WAAA,CAAAI,SAAA;MAAA,MAAAuP,QAAA,GAAAzQ,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAoP,QAAA,CAAAC,UAAA,CAAAF,UAAA,CAAiB;IAAA,EAAC;IAG3BxQ,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IADPJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAkQ,UAAA,MACF;;;;;;;;IAbJxQ,EAAA,CAAAE,cAAA,eAKC;IAEGF,EAAA,CAAAwB,UAAA,IAAAmP,6CAAA,sBAMS;IACX3Q,EAAA,CAAAI,YAAA,EAAM;;;;IAXNJ,EAAA,CAAA+B,WAAA,SAAA6O,OAAA,CAAAC,mBAAA,CAAAC,CAAA,aAA6C,QAAAF,OAAA,CAAAC,mBAAA,CAAAE,CAAA;IAKvB/Q,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAmC,UAAA,YAAAnC,EAAA,CAAAgR,eAAA,IAAAC,GAAA,EAAuC;;;;;;IAU/DjR,EAAA,CAAAE,cAAA,eAKC;IAGKF,EAAA,CAAAY,UAAA,mBAAAsQ,6DAAA;MAAAlR,EAAA,CAAAe,aAAA,CAAAoQ,KAAA;MAAA,MAAAC,QAAA,GAAApR,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA+P,QAAA,CAAAC,cAAA,CAAAD,QAAA,CAAAE,eAAA,CAA+B;IAAA,EAAC;IAGzCtR,EAAA,CAAAC,SAAA,aAA0C;IAC1CD,EAAA,CAAAE,cAAA,eAA+C;IAAAF,EAAA,CAAAG,MAAA,oBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEhEJ,EAAA,CAAAE,cAAA,iBAGC;IAFCF,EAAA,CAAAY,UAAA,mBAAA2Q,6DAAA;MAAAvR,EAAA,CAAAe,aAAA,CAAAoQ,KAAA;MAAA,MAAAK,QAAA,GAAAxR,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAmQ,QAAA,CAAAC,cAAA,CAAAD,QAAA,CAAAF,eAAA,CAA+B;IAAA,EAAC;IAGzCtR,EAAA,CAAAC,SAAA,aAA2C;IAC3CD,EAAA,CAAAE,cAAA,eAA+C;IAAAF,EAAA,CAAAG,MAAA,sBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAElEJ,EAAA,CAAAE,cAAA,kBAGC;IAFCF,EAAA,CAAAY,UAAA,mBAAA8Q,8DAAAzM,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAAoQ,KAAA;MAAA,MAAAQ,QAAA,GAAA3R,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAsQ,QAAA,CAAA1H,kBAAA,CAAA0H,QAAA,CAAAL,eAAA,EAAArM,MAAA,CAA2C;IAAA,EAAC;IAGrDjF,EAAA,CAAAC,SAAA,cAA4C;IAC5CD,EAAA,CAAAE,cAAA,gBAA+C;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE9DJ,EAAA,CAAAC,SAAA,cAAwD;IACxDD,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAY,UAAA,mBAAAgR,8DAAA;MAAA5R,EAAA,CAAAe,aAAA,CAAAoQ,KAAA;MAAA,MAAAU,QAAA,GAAA7R,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAwQ,QAAA,CAAAC,aAAA,CAAAD,QAAA,CAAAP,eAAA,CAA8B;IAAA,EAAC;IAGxCtR,EAAA,CAAAC,SAAA,cAAyC;IACzCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IA/B1BJ,EAAA,CAAA+B,WAAA,SAAAgQ,OAAA,CAAAlB,mBAAA,CAAAC,CAAA,OAAuC,QAAAiB,OAAA,CAAAlB,mBAAA,CAAAE,CAAA;;;;;;IAqCzC/Q,EAAA,CAAAE,cAAA,eAUC;IADCF,EAAA,CAAAY,UAAA,mBAAAoR,0DAAA;MAAAhS,EAAA,CAAAe,aAAA,CAAAkR,KAAA;MAAA,MAAAC,QAAA,GAAAlS,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA6Q,QAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1BnS,EAAA,CAAAI,YAAA,EAAM;;;;;;IAeTJ,EAAA,CAAAE,cAAA,eAIC;IADCF,EAAA,CAAAY,UAAA,mBAAAwR,0DAAA;MAAApS,EAAA,CAAAe,aAAA,CAAAsR,KAAA;MAAA,MAAAC,QAAA,GAAAtS,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAiR,QAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAE5BvS,EAAA,CAAAE,cAAA,eAAgD;IAG5CF,EAAA,CAAAY,UAAA,mBAAA4R,6DAAA;MAAAxS,EAAA,CAAAe,aAAA,CAAAsR,KAAA;MAAA,MAAAI,QAAA,GAAAzS,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAoR,QAAA,CAAAF,gBAAA,EAAkB;IAAA,EAAC;IAI5BvS,EAAA,CAAAC,SAAA,aAAoC;IACtCD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,kBAIC;IAHCF,EAAA,CAAAY,UAAA,mBAAA8R,6DAAAzN,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAAsR,KAAA;MAAA,MAAAM,QAAA,GAAA3S,EAAA,CAAAoB,aAAA;MAASuR,QAAA,CAAAC,aAAA,EAAe;MAAA,OAAE5S,EAAA,CAAAqB,WAAA,CAAA4D,MAAA,CAAAc,eAAA,EAAwB;IAAA,EAAC;IAInD/F,EAAA,CAAAC,SAAA,aAAuC;IACzCD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,eAME;IAFAF,EAAA,CAAAY,UAAA,mBAAAiS,0DAAA5N,MAAA;MAAA,OAASA,MAAA,CAAAc,eAAA,EAAwB;IAAA,EAAC;IAJpC/F,EAAA,CAAAI,YAAA,EAME;IAGFJ,EAAA,CAAAE,cAAA,eAGC;IADCF,EAAA,CAAAY,UAAA,mBAAAkS,0DAAA7N,MAAA;MAAA,OAASA,MAAA,CAAAc,eAAA,EAAwB;IAAA,EAAC;IAElC/F,EAAA,CAAAE,cAAA,eAA+C;IAEpBF,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC/DJ,EAAA,CAAAE,cAAA,cAA8B;IAC5BF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAENJ,EAAA,CAAAE,cAAA,eAAwB;IAGpBF,EAAA,CAAAY,UAAA,mBAAAmS,8DAAA9N,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAAsR,KAAA;MAAA,MAAAW,QAAA,GAAAhT,EAAA,CAAAoB,aAAA;MAAS4R,QAAA,CAAAC,SAAA,CAAU,GAAG,CAAC;MAAA,OAAEjT,EAAA,CAAAqB,WAAA,CAAA4D,MAAA,CAAAc,eAAA,EAAwB;IAAA,EAAC;IAIlD/F,EAAA,CAAAC,SAAA,cAAkC;IACpCD,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,mBAIC;IAHCF,EAAA,CAAAY,UAAA,mBAAAsS,8DAAAjO,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAAsR,KAAA;MAAA,MAAAc,QAAA,GAAAnT,EAAA,CAAAoB,aAAA;MAAS+R,QAAA,CAAAF,SAAA,CAAU,GAAG,CAAC;MAAA,OAAEjT,EAAA,CAAAqB,WAAA,CAAA4D,MAAA,CAAAc,eAAA,EAAwB;IAAA,EAAC;IAIlD/F,EAAA,CAAAC,SAAA,cAAmC;IACrCD,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAE,cAAA,mBAIC;IAHCF,EAAA,CAAAY,UAAA,mBAAAwS,8DAAAnO,MAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAAsR,KAAA;MAAA,MAAAgB,QAAA,GAAArT,EAAA,CAAAoB,aAAA;MAASiS,QAAA,CAAAC,SAAA,EAAW;MAAA,OAAEtT,EAAA,CAAAqB,WAAA,CAAA4D,MAAA,CAAAc,eAAA,EAAwB;IAAA,EAAC;IAI/C/F,EAAA,CAAAC,SAAA,cAAwC;IAC1CD,EAAA,CAAAI,YAAA,EAAS;;;;IA1CbJ,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAmC,UAAA,QAAAoR,OAAA,CAAAC,aAAA,kBAAAD,OAAA,CAAAC,aAAA,CAAAC,GAAA,EAAAzT,EAAA,CAAAkE,aAAA,CAA0B,SAAAqP,OAAA,CAAAC,aAAA,kBAAAD,OAAA,CAAAC,aAAA,CAAAtR,IAAA;IAcClC,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAiC,iBAAA,EAAAsR,OAAA,CAAAC,aAAA,kBAAAD,OAAA,CAAAC,aAAA,CAAAtR,IAAA,aAAoC;IAEzDlC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,OAAAiT,OAAA,CAAAC,aAAA,kBAAAD,OAAA,CAAAC,aAAA,CAAAE,IAAA,4BACF;;;ADvjCV,OAAM,MAAOC,oBAAoB;EAmJ/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,cAA8B,EAC9BC,YAA0B,EAC1BC,GAAsB;IAJtB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IAlJb;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAhJ,QAAQ,GAAU,EAAE;IACpB,KAAAxG,aAAa,GAAkB,IAAI;IACnC,KAAAyP,eAAe,GAAG,KAAK;IAEvB,KAAA3T,gBAAgB,GAAQ,IAAI;IAE5B;IACA,KAAA4T,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAlH,gBAAgB,GAAG,KAAK;IACxB,KAAAtC,iBAAiB,GAAG,KAAK;IACzB,KAAAzI,YAAY,GAAG,KAAK;IACpB,KAAAkS,sBAAsB,GAAG,KAAK;IAC9B,KAAAvD,eAAe,GAAQ,IAAI;IAC3B,KAAAT,mBAAmB,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IACpC,KAAA+D,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,qBAAqB,GAAQ,IAAI;IACjC,KAAApQ,UAAU,GAAG,KAAK;IAClB,KAAA/C,YAAY,GAAG,OAAO,CAAC,CAAC;IACxB,KAAAU,MAAM,GAAG,CACP;MAAEf,EAAE,EAAE,OAAO;MAAEW,IAAI,EAAE,OAAO;MAAEJ,IAAI,EAAE,YAAY;MAAEE,KAAK,EAAE;IAAS,CAAE,EACpE;MAAET,EAAE,EAAE,MAAM;MAAEW,IAAI,EAAE,QAAQ;MAAEJ,IAAI,EAAE,aAAa;MAAEE,KAAK,EAAE;IAAS,CAAE,EACrE;MAAET,EAAE,EAAE,MAAM;MAAEW,IAAI,EAAE,MAAM;MAAEJ,IAAI,EAAE,cAAc;MAAEE,KAAK,EAAE;IAAS,CAAE,EACpE;MAAET,EAAE,EAAE,MAAM;MAAEW,IAAI,EAAE,MAAM;MAAEJ,IAAI,EAAE,cAAc;MAAEE,KAAK,EAAE;IAAS,CAAE,CACrE;IACD,KAAAiB,iBAAiB,GAAG,KAAK;IACzB,KAAA+R,eAAe,GAAG,KAAK;IACvB,KAAAxB,aAAa,GAAQ,IAAI;IACzB,KAAA9H,cAAc,GAAG,CAAC;IAClB,KAAAuJ,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAElB;IACA,KAAAjI,gBAAgB,GAAG,KAAK;IACxB,KAAAuB,sBAAsB,GAAG,CAAC;IAC1B,KAAAtB,mBAAmB,GAAwC,MAAM;IACzD,KAAAiI,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAClC,KAAA/G,UAAU,GAAa,CACrB,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CACzD;IAED;IACQ,KAAAgH,YAAY,GAA4B,IAAI;IAC5C,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,aAAa,GAOjB,EAAE;IAEN;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAA6B,IAAI;IACzC,KAAAC,YAAY,GAAG,CAAC;IACR,KAAAC,SAAS,GAAQ,IAAI;IAE7B;IACA,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAElD;IACA,KAAAzG,eAAe,GAAU,CACvB;MACElO,EAAE,EAAE,SAAS;MACbW,IAAI,EAAE,SAAS;MACfJ,IAAI,EAAE,IAAI;MACVqU,MAAM,EAAE,CACN;QAAE3N,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAe,CAAE,EACtC;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAwB,CAAE;KAElD,EACD;MACEX,EAAE,EAAE,QAAQ;MACZW,IAAI,EAAE,QAAQ;MACdJ,IAAI,EAAE,IAAI;MACVqU,MAAM,EAAE,CACN;QAAE3N,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAW,CAAE;KAErC,EACD;MACEX,EAAE,EAAE,QAAQ;MACZW,IAAI,EAAE,QAAQ;MACdJ,IAAI,EAAE,IAAI;MACVqU,MAAM,EAAE,CACN;QAAE3N,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAY,CAAE,EACnC;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAa,CAAE,EACpC;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEsG,KAAK,EAAE,IAAI;QAAEtG,IAAI,EAAE;MAAO,CAAE;KAEjC,CACF;IACD,KAAA8M,qBAAqB,GAAG,IAAI,CAACS,eAAe,CAAC,CAAC,CAAC;IAE/C;IACiB,KAAA2G,oBAAoB,GAAG,EAAE;IAClC,KAAAC,WAAW,GAAG,CAAC;IAEvB;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,KAAK;IACZ,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAG,IAAI3W,YAAY,EAAE;IASxC,IAAI,CAAC4W,WAAW,GAAG,IAAI,CAAC7C,EAAE,CAAC8C,KAAK,CAAC;MAC/BpS,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC1E,UAAU,CAAC+W,QAAQ,EAAE/W,UAAU,CAACgX,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEA;EACAC,eAAeA,CAAA;IACb,OACE,CAAC,IAAI,CAACtW,gBAAgB,IAAI,IAAI,CAACyM,gBAAgB,IAAI,IAAI,CAACS,gBAAgB;EAE5E;EAEA;EACQqJ,gBAAgBA,CAAA;IACtB,MAAMC,cAAc,GAAG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC;IACtD,IAAI,IAAI,CAACH,eAAe,EAAE,EAAE;MAC1BE,cAAc,EAAEE,OAAO,EAAE;KAC1B,MAAM;MACLF,cAAc,EAAEG,MAAM,EAAE;;EAE5B;EAEAC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQD,eAAeA,CAAA;IACrB;IACA,MAAME,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,OAAO;IAClE,IAAI,CAAC/V,YAAY,GAAG6V,UAAU;IAC9B,IAAI,CAAC9S,UAAU,GAAG8S,UAAU,KAAK,MAAM;IACvC,IAAI,CAACG,UAAU,CAACH,UAAU,CAAC;EAC7B;EAEA;EAEQG,UAAUA,CAACC,OAAe;IAChC,MAAMC,IAAI,GAAGC,QAAQ,CAACC,eAAe;IAErC;IACAF,IAAI,CAACG,SAAS,CAACC,MAAM,CACnB,MAAM,EACN,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,YAAY,CACb;IAED;IACAJ,IAAI,CAACG,SAAS,CAACE,GAAG,CAAC,SAASN,OAAO,EAAE,CAAC;IACtC,IAAIA,OAAO,KAAK,MAAM,EAAE;MACtBC,IAAI,CAACG,SAAS,CAACE,GAAG,CAAC,MAAM,CAAC;;EAE9B;EAEAhV,eAAeA,CAAA;IACb,OAAO,IAAI,CAACb,MAAM,CAAC8V,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC9W,EAAE,KAAK,IAAI,CAACK,YAAY,CAAC;EAC5D;EAEAkB,mBAAmBA,CAAA;IACjB,IAAI,CAACG,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;EAClD;EAEA3B,WAAWA,CAACuW,OAAe;IACzB,IAAI,CAACjW,YAAY,GAAGiW,OAAO;IAC3B,IAAI,CAAClT,UAAU,GAAGkT,OAAO,KAAK,MAAM;IACpC,IAAI,CAACD,UAAU,CAACC,OAAO,CAAC;IACxBH,YAAY,CAACY,OAAO,CAAC,cAAc,EAAET,OAAO,CAAC;IAC7C,IAAI,CAAC5U,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACN,YAAY,GAAG,KAAK;IAEzB,MAAM4V,SAAS,GACb,IAAI,CAACjW,MAAM,CAAC8V,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC9W,EAAE,KAAKsW,OAAO,CAAC,EAAE3V,IAAI,IAAI2V,OAAO;IAC5D,IAAI,CAAC7D,YAAY,CAACwE,WAAW,CAAC,SAASD,SAAS,WAAW,CAAC;EAC9D;EAEQf,mBAAmBA,CAAA;IACzB,IAAI,CAACiB,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA,IAAI,CAAClC,aAAa,CAAC0B,GAAG,CACpB,IAAI,CAACpE,cAAc,CAAC6E,aAAa,CAACC,SAAS,CAAC;MAC1CC,IAAI,EAAGC,YAAY,IAAI;QACrB,IAAIA,YAAY,EAAE;UAChB1B,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyB,YAAY,CAAC;UACvD,IAAI,CAACC,kBAAkB,CAACD,YAAY,CAAC;;MAEzC,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACf5B,OAAO,CAAC4B,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACxC,aAAa,CAAC0B,GAAG,CACpB,IAAI,CAACpE,cAAc,CAACmF,WAAW,CAACL,SAAS,CAAC;MACxCC,IAAI,EAAGK,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR9B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE6B,IAAI,CAAC;UAC5C,IAAI,CAACtD,UAAU,GAAGsD,IAAI;;MAE1B,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACf5B,OAAO,CAAC4B,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC9D;KACD,CAAC,CACH;EACH;EAEQD,kBAAkBA,CAACD,YAA0B;IACnD;IACA;IACA1B,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCyB,YAAY,CAACK,MAAM,CAAC7V,QAAQ,CAC7B;IAED;IACA,IAAI,CAACwQ,cAAc,CAACsF,IAAI,CAAC,UAAU,CAAC;IAEpC;IACA;IACA;EACF;;EAEQZ,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMa,UAAU,GAAG5B,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/CN,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEgC,UAAU,CAAC;MAEzD,IAAI,CAACA,UAAU,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,WAAW,EAAE;QACtEjC,OAAO,CAAC4B,KAAK,CAAC,gCAAgC,CAAC;QAC/C,IAAI,CAACvU,aAAa,GAAG,IAAI;QACzB,IAAI,CAACyP,eAAe,GAAG,KAAK;QAC5B;;MAGF,MAAMoF,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,UAAU,CAAC;MACnCjC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEiC,IAAI,CAAC;MAE3C;MACA,MAAMG,MAAM,GAAGH,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAAChY,EAAE,IAAIgY,IAAI,CAACG,MAAM;MACjDrC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CqC,GAAG,EAAEJ,IAAI,CAACI,GAAG;QACbpY,EAAE,EAAEgY,IAAI,CAAChY,EAAE;QACXmY,MAAM,EAAEH,IAAI,CAACG,MAAM;QACnBE,SAAS,EAAEF;OACZ,CAAC;MAEF,IAAIA,MAAM,EAAE;QACV,IAAI,CAAChV,aAAa,GAAGgV,MAAM;QAC3B,IAAI,CAACvF,eAAe,GAAGoF,IAAI,CAAChW,QAAQ,IAAIgW,IAAI,CAACrX,IAAI,IAAI,KAAK;QAC1DmV,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;UACjD/V,EAAE,EAAE,IAAI,CAACmD,aAAa;UACtBnB,QAAQ,EAAE,IAAI,CAAC4Q;SAChB,CAAC;OACH,MAAM;QACLkD,OAAO,CAAC4B,KAAK,CAAC,0CAA0C,EAAEM,IAAI,CAAC;QAC/D,IAAI,CAAC7U,aAAa,GAAG,IAAI;QACzB,IAAI,CAACyP,eAAe,GAAG,KAAK;;KAE/B,CAAC,OAAO8E,KAAK,EAAE;MACd5B,OAAO,CAAC4B,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAI,CAACvU,aAAa,GAAG,IAAI;MACzB,IAAI,CAACyP,eAAe,GAAG,KAAK;;EAEhC;EAEQuE,gBAAgBA,CAAA;IACtB,MAAMmB,cAAc,GAAG,IAAI,CAAC/F,KAAK,CAACgG,QAAQ,CAACC,QAAQ,CAAC9C,GAAG,CAAC,IAAI,CAAC;IAC7DI,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuC,cAAc,CAAC;IAE5D,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAAC7F,YAAY,CAACgG,SAAS,CAAC,6BAA6B,CAAC;MAC1D;;IAGF,IAAI,CAAC5F,SAAS,GAAG,IAAI;IACrB,IAAI,CAACL,cAAc,CAACkG,eAAe,CAACJ,cAAc,CAAC,CAAChB,SAAS,CAAC;MAC5DC,IAAI,EAAG5E,YAAY,IAAI;QACrBmD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEpD,YAAY,CAAC;QACjEmD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;UACxC/V,EAAE,EAAE2S,YAAY,EAAE3S,EAAE;UACpB2Y,YAAY,EAAEhG,YAAY,EAAEgG,YAAY;UACxCC,iBAAiB,EAAEjG,YAAY,EAAEgG,YAAY,EAAErP,MAAM;UACrDuP,OAAO,EAAElG,YAAY,EAAEkG,OAAO;UAC9BlP,QAAQ,EAAEgJ,YAAY,EAAEhJ,QAAQ;UAChCmP,aAAa,EAAEnG,YAAY,EAAEhJ,QAAQ,EAAEL;SACxC,CAAC;QACF,IAAI,CAACqJ,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACoG,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;QAEnB;QACA,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC;MACDvB,KAAK,EAAGA,KAAK,IAAI;QACf5B,OAAO,CAAC4B,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAACjF,YAAY,CAACgG,SAAS,CACzB,8CAA8C,CAC/C;QACD,IAAI,CAAC5F,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQkG,mBAAmBA,CAAA;IACzB,IACE,CAAC,IAAI,CAACpG,YAAY,EAAEgG,YAAY,IAChC,IAAI,CAAChG,YAAY,CAACgG,YAAY,CAACrP,MAAM,KAAK,CAAC,EAC3C;MACAwM,OAAO,CAACoD,IAAI,CAAC,uCAAuC,CAAC;MACrD,IAAI,CAACja,gBAAgB,GAAG,IAAI;MAC5B;;IAGF6W,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC5S,aAAa,CAAC;IACnD2S,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACpD,YAAY,CAACgG,YAAY,CAAC;IAEhE;IACA;IAEA,IAAI,IAAI,CAAChG,YAAY,CAACkG,OAAO,EAAE;MAC7B;MACA;MACA,IAAI,CAAC5Z,gBAAgB,GAAG,IAAI,CAAC0T,YAAY,CAACgG,YAAY,CAAC9B,IAAI,CAAEsC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAACnZ,EAAE,IAAImZ,CAAC,CAACf,GAAG;QACnC,OAAOiB,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAAClW,aAAa,CAAC;MAC7D,CAAC,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAAClE,gBAAgB,GAAG,IAAI,CAAC0T,YAAY,CAACgG,YAAY,CAAC9B,IAAI,CAAEsC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAACnZ,EAAE,IAAImZ,CAAC,CAACf,GAAG;QACnCtC,OAAO,CAACC,GAAG,CACT,2BAA2B,EAC3BqD,aAAa,EACb,uBAAuB,EACvB,IAAI,CAACjW,aAAa,CACnB;QACD,OAAOkW,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAAClW,aAAa,CAAC;MAC7D,CAAC,CAAC;;IAGJ;IACA,IAAI,CAAC,IAAI,CAAClE,gBAAgB,IAAI,IAAI,CAAC0T,YAAY,CAACgG,YAAY,CAACrP,MAAM,GAAG,CAAC,EAAE;MACvEwM,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,IAAI,CAAC9W,gBAAgB,GAAG,IAAI,CAAC0T,YAAY,CAACgG,YAAY,CAAC,CAAC,CAAC;MAEzD;MACA,IAAI,IAAI,CAAChG,YAAY,CAACgG,YAAY,CAACrP,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMgQ,kBAAkB,GACtB,IAAI,CAACra,gBAAgB,CAACe,EAAE,IAAI,IAAI,CAACf,gBAAgB,CAACmZ,GAAG;QACvD,IAAIiB,MAAM,CAACC,kBAAkB,CAAC,KAAKD,MAAM,CAAC,IAAI,CAAClW,aAAa,CAAC,EAAE;UAC7D2S,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACD,IAAI,CAAC9W,gBAAgB,GAAG,IAAI,CAAC0T,YAAY,CAACgG,YAAY,CAAC,CAAC,CAAC;;;;IAK/D;IACA,IAAI,IAAI,CAAC1Z,gBAAgB,EAAE;MACzB6W,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QACnD/V,EAAE,EAAE,IAAI,CAACf,gBAAgB,CAACe,EAAE,IAAI,IAAI,CAACf,gBAAgB,CAACmZ,GAAG;QACzDpW,QAAQ,EAAE,IAAI,CAAC/C,gBAAgB,CAAC+C,QAAQ;QACxCU,KAAK,EAAE,IAAI,CAACzD,gBAAgB,CAACyD,KAAK;QAClCxD,QAAQ,EAAE,IAAI,CAACD,gBAAgB,CAACC;OACjC,CAAC;MAEF;MACA4W,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAC9W,gBAAgB,CAAC+C,QAAQ,CAC/B;MACD8T,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/B,IAAI,CAAC9W,gBAAgB,CAAC+C,QAAQ,CAC/B;KACF,MAAM;MACL8T,OAAO,CAAC4B,KAAK,CAAC,uDAAuD,CAAC;MACtE5B,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACpD,YAAY,CAACgG,YAAY,CAAC;MACzE7C,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC5S,aAAa,CAAC;MAEnD;MACA2S,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;IAGrD;IACA,IAAI,CAACP,gBAAgB,EAAE;EACzB;EAEQwD,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACrG,YAAY,EAAE3S,EAAE,EAAE;IAE5B;IACA,IAAI2J,QAAQ,GAAG,IAAI,CAACgJ,YAAY,CAAChJ,QAAQ,IAAI,EAAE;IAE/C;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAAC4P,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;MAC/C,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACpX,SAAS,IAAIoX,CAAC,CAACI,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,MAAMC,KAAK,GAAG,IAAIH,IAAI,CAACF,CAAC,CAACrX,SAAS,IAAIqX,CAAC,CAACG,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,OAAOH,KAAK,GAAGI,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC;;IAEFhE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CgE,KAAK,EAAE,IAAI,CAACpQ,QAAQ,CAACL,MAAM;MAC3B0Q,KAAK,EAAE,IAAI,CAACrQ,QAAQ,CAAC,CAAC,CAAC,EAAE3G,OAAO;MAChCiX,IAAI,EAAE,IAAI,CAACtQ,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACL,MAAM,GAAG,CAAC,CAAC,EAAEtG;KAChD,CAAC;IAEF,IAAI,CAAC+P,eAAe,GAAG,IAAI,CAACpJ,QAAQ,CAACL,MAAM,KAAK,IAAI,CAACuL,oBAAoB;IACzE,IAAI,CAAChC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACqH,cAAc,EAAE;EACvB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACrH,aAAa,IAAI,CAAC,IAAI,CAACC,eAAe,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAE3S,EAAE,EACvE;IAEF,IAAI,CAAC8S,aAAa,GAAG,IAAI;IACzB,IAAI,CAACgC,WAAW,EAAE;IAElB;IACA,MAAMsF,MAAM,GAAG,IAAI,CAACzQ,QAAQ,CAACL,MAAM;IAEnC,IAAI,CAACkJ,cAAc,CAAC6H,WAAW,CAC7B,IAAI,CAAClX,aAAc;IAAE;IACrB,IAAI,CAAClE,gBAAgB,EAAEe,EAAE,IAAI,IAAI,CAACf,gBAAgB,EAAEmZ,GAAI;IAAE;IAC1D,IAAI,CAACzF,YAAY,CAAC3S,EAAE,EACpB,IAAI,CAAC8U,WAAW,EAChB,IAAI,CAACD,oBAAoB,CAC1B,CAACyC,SAAS,CAAC;MACVC,IAAI,EAAG+C,WAAkB,IAAI;QAC3B,IAAIA,WAAW,IAAIA,WAAW,CAAChR,MAAM,GAAG,CAAC,EAAE;UACzC;UACA,IAAI,CAACK,QAAQ,GAAG,CAAC,GAAG2Q,WAAW,CAACC,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC5Q,QAAQ,CAAC;UAC5D,IAAI,CAACoJ,eAAe,GAClBuH,WAAW,CAAChR,MAAM,KAAK,IAAI,CAACuL,oBAAoB;SACnD,MAAM;UACL,IAAI,CAAC9B,eAAe,GAAG,KAAK;;QAE9B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACD4E,KAAK,EAAGA,KAAK,IAAI;QACf5B,OAAO,CAAC4B,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAACjF,YAAY,CAACgG,SAAS,CAAC,wCAAwC,CAAC;QACrE,IAAI,CAAC3F,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACgC,WAAW,EAAE,CAAC,CAAC;MACtB;KACD,CAAC;EACJ;;EAEQmE,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACtG,YAAY,EAAE3S,EAAE,EAAE;MAC1B8V,OAAO,CAACoD,IAAI,CAAC,kDAAkD,CAAC;MAChE;;IAGFpD,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzD,IAAI,CAACpD,YAAY,CAAC3S,EAAE,CACrB;IAED;IACA8V,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACpD,IAAI,CAACb,aAAa,CAAC0B,GAAG,CACpB,IAAI,CAACpE,cAAc,CAACgI,sBAAsB,CACxC,IAAI,CAAC7H,YAAY,CAAC3S,EAAE,CACrB,CAACsX,SAAS,CAAC;MACVC,IAAI,EAAGkD,UAAe,IAAI;QACxB3E,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE0E,UAAU,CAAC;QACpE3E,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;UACnC/V,EAAE,EAAEya,UAAU,CAACza,EAAE;UACjB0a,IAAI,EAAED,UAAU,CAACC,IAAI;UACrB1X,OAAO,EAAEyX,UAAU,CAACzX,OAAO;UAC3BP,MAAM,EAAEgY,UAAU,CAAChY,MAAM;UACzBkY,QAAQ,EAAEF,UAAU,CAACE,QAAQ;UAC7BC,UAAU,EAAEH,UAAU,CAACG,UAAU;UACjCC,WAAW,EAAEJ,UAAU,CAACI;SACzB,CAAC;QAEF;QACA/E,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnC,IAAI,CAAC7M,cAAc,CAACuR,UAAU,CAAC,CAChC;QACD3E,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC5M,QAAQ,CAACsR,UAAU,CAAC,CAAC;QAC/D3E,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC3M,OAAO,CAACqR,UAAU,CAAC,CAAC;QAC7D3E,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC7R,WAAW,CAACuW,UAAU,CAAC,CAAC;QAClE,IAAIA,UAAU,CAACI,WAAW,EAAE;UAC1BJ,UAAU,CAACI,WAAW,CAACC,OAAO,CAAC,CAACC,GAAQ,EAAEzW,KAAa,KAAI;YACzDwR,OAAO,CAACC,GAAG,CAAC,yBAAyBzR,KAAK,GAAG,EAAE;cAC7CoW,IAAI,EAAEK,GAAG,CAACL,IAAI;cACdxI,GAAG,EAAE6I,GAAG,CAAC7I,GAAG;cACZ8I,IAAI,EAAED,GAAG,CAACC,IAAI;cACdra,IAAI,EAAEoa,GAAG,CAACpa,IAAI;cACdwR,IAAI,EAAE4I,GAAG,CAAC5I;aACX,CAAC;UACJ,CAAC,CAAC;;QAGJ;QACA,MAAM8I,aAAa,GAAG,IAAI,CAACtR,QAAQ,CAACuR,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAACnb,EAAE,KAAKya,UAAU,CAACza,EAAE,CAClC;QACD,IAAI,CAACib,aAAa,EAAE;UAClB;UACA,IAAI,CAACtR,QAAQ,CAACyR,IAAI,CAACX,UAAU,CAAC;UAC9B3E,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAACpM,QAAQ,CAACL,MAAM,CACrB;UAED;UACA,IAAI,CAACoJ,GAAG,CAAC2I,aAAa,EAAE;UAExB;UACAC,UAAU,CAAC,MAAK;YACd,IAAI,CAACpB,cAAc,EAAE;UACvB,CAAC,EAAE,EAAE,CAAC;UAEN;UACA,MAAMS,QAAQ,GAAGF,UAAU,CAAChY,MAAM,EAAEzC,EAAE,IAAIya,UAAU,CAACE,QAAQ;UAC7D7E,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;YAC9D4E,QAAQ;YACRxX,aAAa,EAAE,IAAI,CAACA,aAAa;YACjCoY,gBAAgB,EAAEZ,QAAQ,KAAK,IAAI,CAACxX;WACrC,CAAC;UAEF,IAAIwX,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAACxX,aAAa,EAAE;YAC/C,IAAI,CAACqY,iBAAiB,CAACf,UAAU,CAACza,EAAE,CAAC;;;MAG3C,CAAC;MACD0X,KAAK,EAAGA,KAAK,IAAI;QACf5B,OAAO,CAAC4B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC,CACH;IAED;IACA5B,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7D,IAAI,CAACb,aAAa,CAAC0B,GAAG,CACpB,IAAI,CAACpE,cAAc,CAACiJ,0BAA0B,CAC5C,IAAI,CAAC9I,YAAY,CAAC3S,EAAE,CACrB,CAACsX,SAAS,CAAC;MACVC,IAAI,EAAGmE,UAAe,IAAI;QACxB5F,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2F,UAAU,CAAC;QAExD;QACA,IAAIA,UAAU,CAACvD,MAAM,KAAK,IAAI,CAAChV,aAAa,EAAE;UAC5C,IAAI,CAAC0G,iBAAiB,GAAG6R,UAAU,CAAC3G,QAAQ;UAC5C,IAAI,CAACrC,GAAG,CAAC2I,aAAa,EAAE;;MAE5B,CAAC;MACD3D,KAAK,EAAGA,KAAU,IAAI;QACpB5B,OAAO,CAAC4B,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACxC,aAAa,CAAC0B,GAAG,CACpB,IAAI,CAACpE,cAAc,CAACmJ,8BAA8B,CAChD,IAAI,CAAChJ,YAAY,CAAC3S,EAAE,CACrB,CAACsX,SAAS,CAAC;MACVC,IAAI,EAAGqE,kBAAuB,IAAI;QAChC9F,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE6F,kBAAkB,CAAC;QAE1D;QACA,IAAIA,kBAAkB,CAAC5b,EAAE,KAAK,IAAI,CAAC2S,YAAY,CAAC3S,EAAE,EAAE;UAClD,IAAI,CAAC2S,YAAY,GAAG;YAAE,GAAG,IAAI,CAACA,YAAY;YAAE,GAAGiJ;UAAkB,CAAE;UACnE,IAAI,CAAClJ,GAAG,CAAC2I,aAAa,EAAE;;MAE5B,CAAC;MACD3D,KAAK,EAAGA,KAAU,IAAI;QACpB5B,OAAO,CAAC4B,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D;KACD,CAAC,CACH;EACH;EAEQ8D,iBAAiBA,CAACK,SAAiB;IACzC,IAAI,CAACrJ,cAAc,CAACgJ,iBAAiB,CAACK,SAAS,CAAC,CAACvE,SAAS,CAAC;MACzDC,IAAI,EAAEA,CAAA,KAAK;QACTzB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE8F,SAAS,CAAC;MACrD,CAAC;MACDnE,KAAK,EAAGA,KAAK,IAAI;QACf5B,OAAO,CAAC4B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC;EACJ;EAEA;EACA3L,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACoJ,WAAW,CAAC2G,KAAK,IAAI,CAAC,IAAI,CAACnJ,YAAY,EAAE3S,EAAE,EAAE;IAEvD,MAAMgD,OAAO,GAAG,IAAI,CAACmS,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAEqG,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAAChZ,OAAO,EAAE;IAEd,MAAM4X,UAAU,GAAG,IAAI,CAAC3b,gBAAgB,EAAEe,EAAE,IAAI,IAAI,CAACf,gBAAgB,EAAEmZ,GAAG;IAE1E,IAAI,CAACwC,UAAU,EAAE;MACf,IAAI,CAACnI,YAAY,CAACgG,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF;IACA,IAAI,CAACtM,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACqJ,gBAAgB,EAAE;IAEvBM,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjC/S,OAAO;MACP4X,UAAU;MACVtC,cAAc,EAAE,IAAI,CAAC3F,YAAY,CAAC3S;KACnC,CAAC;IAEF,IAAI,CAACwS,cAAc,CAACzG,WAAW,CAC7B6O,UAAU,EACV5X,OAAO,EACPiZ,SAAS,EACT,MAAa,EACb,IAAI,CAACtJ,YAAY,CAAC3S,EAAE,CACrB,CAACsX,SAAS,CAAC;MACVC,IAAI,EAAG2E,OAAY,IAAI;QACrBpG,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEmG,OAAO,CAAC;QAEpD;QACA,MAAMjB,aAAa,GAAG,IAAI,CAACtR,QAAQ,CAACuR,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAACnb,EAAE,KAAKkc,OAAO,CAAClc,EAAE,CAC/B;QACD,IAAI,CAACib,aAAa,EAAE;UAClB,IAAI,CAACtR,QAAQ,CAACyR,IAAI,CAACc,OAAO,CAAC;UAC3BpG,OAAO,CAACC,GAAG,CACT,wCAAwC,EACxC,IAAI,CAACpM,QAAQ,CAACL,MAAM,CACrB;;QAGH;QACA,IAAI,CAAC6L,WAAW,CAACgH,KAAK,EAAE;QACxB,IAAI,CAAChQ,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACqJ,gBAAgB,EAAE;QAEvB;QACA,IAAI,CAAC9C,GAAG,CAAC2I,aAAa,EAAE;QACxBC,UAAU,CAAC,MAAK;UACd,IAAI,CAACpB,cAAc,EAAE;QACvB,CAAC,EAAE,EAAE,CAAC;MACR,CAAC;MACDxC,KAAK,EAAGA,KAAU,IAAI;QACpB5B,OAAO,CAAC4B,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D,IAAI,CAACjF,YAAY,CAACgG,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAACtM,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACqJ,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEA0E,cAAcA,CAAA;IACZoB,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACc,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACArd,gBAAgBA,CAACC,UAAgC;IAC/C,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IAEpC,MAAMqd,QAAQ,GAAGC,IAAI,CAACC,KAAK,CACzB,CAAChD,IAAI,CAACiD,GAAG,EAAE,GAAG,IAAIjD,IAAI,CAACva,UAAU,CAAC,CAACya,OAAO,EAAE,IAAI,KAAK,CACtD;IAED,IAAI4C,QAAQ,GAAG,CAAC,EAAE,OAAO,aAAa;IACtC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,UAAUA,QAAQ,MAAM;IAClD,IAAIA,QAAQ,GAAG,IAAI,EAAE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC,GAAG;IAClE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,IAAI,CAAC,GAAG;EACjD;EAEA;EACA1X,oBAAoBA,CAAC8W,SAAiB;IACpC,OACE,IAAI,CAAC5H,aAAa,CAAC4H,SAAS,CAAC,IAAI;MAC/BgB,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,CAAC;MACd/X,KAAK,EAAE;KACR;EAEL;EAEQgY,oBAAoBA,CAC1BnB,SAAiB,EACjBoB,IAAkD;IAElD,IAAI,CAAChJ,aAAa,CAAC4H,SAAS,CAAC,GAAG;MAC9B,GAAG,IAAI,CAAC9W,oBAAoB,CAAC8W,SAAS,CAAC;MACvC,GAAGoB;KACJ;EACH;EAEA;EAEA;EACA;EAEA;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACje,gBAAgB,EAAEe,EAAE,EAAE;MAC9B,IAAI,CAACyS,YAAY,CAACgG,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,IAAI,CAACtE,QAAQ,GAAG,OAAO;IACvB,IAAI,CAACD,QAAQ,GAAG,IAAI;IACpB4B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC9W,gBAAgB,CAAC+C,QAAQ,CAAC;EAC7E;EAEAmb,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACle,gBAAgB,EAAEe,EAAE,EAAE;MAC9B,IAAI,CAACyS,YAAY,CAACgG,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,IAAI,CAACtE,QAAQ,GAAG,OAAO;IACvB,IAAI,CAACD,QAAQ,GAAG,IAAI;IACpB4B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC9W,gBAAgB,CAAC+C,QAAQ,CAAC;EAC7E;EAEAob,OAAOA,CAAA;IACL,IAAI,CAAClJ,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACG,UAAU,GAAG,IAAI;IACtBwB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;EAEA;EACA;EAEA;EAEAsH,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOZ,IAAI,CAACa,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOZ,IAAI,CAACa,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEApX,YAAYA,CAACgW,OAAY;IACvB,MAAMsB,cAAc,GAAGtB,OAAO,CAACrB,WAAW,EAAEhE,IAAI,CAC7CkE,GAAQ,IAAK,CAACA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAID,cAAc,EAAEtL,GAAG,EAAE;MACvB,MAAMwL,IAAI,GAAGlH,QAAQ,CAACmH,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAGJ,cAAc,CAACtL,GAAG;MAC9BwL,IAAI,CAACG,QAAQ,GAAGL,cAAc,CAAC7c,IAAI,IAAI,MAAM;MAC7C+c,IAAI,CAACI,MAAM,GAAG,QAAQ;MACtBtH,QAAQ,CAACuH,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,EAAE;MACZzH,QAAQ,CAACuH,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/B,IAAI,CAACjL,YAAY,CAACwE,WAAW,CAAC,wBAAwB,CAAC;;EAE3D;EAEA;EAEAkH,WAAWA,CAAA;IACT,MAAMC,QAAQ,GAAG,IAAI,CAAChb,UAAU,GAAG,OAAO,GAAG,MAAM;IACnD,IAAI,CAACrD,WAAW,CAACqe,QAAQ,CAAC;EAC5B;EAEAjd,YAAYA,CAAA;IACV,IAAI,CAACkS,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACH,UAAU,GAAG,IAAI,CAACG,UAAU;EACnC;EAEAgL,cAAcA,CAAA;IACZ,IAAI,CAACjd,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAkd,qBAAqBA,CAAA;IACnB;IACAxI,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;EAC/C;EAEA;EAEAnF,aAAaA,CAAA;IACX,IAAI,CAACoC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAAC7R,YAAY,GAAG,KAAK;IACzB,IAAI,CAACkS,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAAC7R,iBAAiB,GAAG,KAAK;EAChC;EAEAqG,oBAAoBA,CAACmU,OAAY,EAAEqC,KAAiB;IAClDA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACzO,eAAe,GAAGmM,OAAO;IAC9B,IAAI,CAAC5M,mBAAmB,GAAG;MAAEC,CAAC,EAAEgP,KAAK,CAACE,OAAO;MAAEjP,CAAC,EAAE+O,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAACpL,sBAAsB,GAAG,IAAI;EACpC;EAEA5K,kBAAkBA,CAACwT,OAAY,EAAEqC,KAAiB;IAChDA,KAAK,CAAC/Z,eAAe,EAAE;IACvB,IAAI,CAACgP,qBAAqB,GAAG0I,OAAO;IACpC,IAAI,CAAC5M,mBAAmB,GAAG;MAAEC,CAAC,EAAEgP,KAAK,CAACE,OAAO;MAAEjP,CAAC,EAAE+O,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAACnL,kBAAkB,GAAG,IAAI;EAChC;EAEApE,UAAUA,CAAClI,KAAa;IACtB,IAAI,IAAI,CAACuM,qBAAqB,EAAE;MAC9B,IAAI,CAACxM,cAAc,CAAC,IAAI,CAACwM,qBAAqB,CAACxT,EAAE,EAAEiH,KAAK,CAAC;;IAE3D,IAAI,CAACsM,kBAAkB,GAAG,KAAK;EACjC;EAEAvM,cAAcA,CAAC6U,SAAiB,EAAE5U,KAAa;IAC7C6O,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE9O,KAAK,EAAE,cAAc,EAAE4U,SAAS,CAAC;IACtE;EACF;;EAEA1U,cAAcA,CAACwX,QAAa,EAAExG,MAAc;IAC1C,OAAOwG,QAAQ,CAACxG,MAAM,KAAKA,MAAM;EACnC;EAEArI,cAAcA,CAACoM,OAAY;IACzBpG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmG,OAAO,CAAClc,EAAE,CAAC;IAClD,IAAI,CAAC4Q,aAAa,EAAE;EACtB;EAEAV,cAAcA,CAACgM,OAAY;IACzBpG,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmG,OAAO,CAAClc,EAAE,CAAC;IACjD,IAAI,CAAC4Q,aAAa,EAAE;EACtB;EAEAL,aAAaA,CAAC2L,OAAY;IACxBpG,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmG,OAAO,CAAClc,EAAE,CAAC;IAChD,IAAI,CAAC4Q,aAAa,EAAE;EACtB;EAEA;EAEAgO,iBAAiBA,CAAA;IACf,IAAI,CAAC5L,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAzF,mBAAmBA,CAACsR,QAAa;IAC/B,IAAI,CAACpR,qBAAqB,GAAGoR,QAAQ;EACvC;EAEA1Q,oBAAoBA,CAAC0Q,QAAa;IAChC,OAAOA,QAAQ,EAAEjK,MAAM,IAAI,EAAE;EAC/B;EAEA9G,WAAWA,CAAC7G,KAAU;IACpB,MAAM6X,cAAc,GAAG,IAAI,CAAC3J,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAEqG,KAAK,IAAI,EAAE;IACnE,MAAMgD,UAAU,GAAGD,cAAc,GAAG7X,KAAK,CAACA,KAAK;IAC/C,IAAI,CAACkO,WAAW,CAAC6J,UAAU,CAAC;MAAEhc,OAAO,EAAE+b;IAAU,CAAE,CAAC;IACpD,IAAI,CAAC/L,eAAe,GAAG,KAAK;EAC9B;EAEAiM,oBAAoBA,CAAA;IAClB,IAAI,CAAChM,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA;EACA;EAEA;EACA;EAEA;EAEA;EACA;EAEA;EAEArJ,gBAAgBA,CAACtF,KAAa,EAAE4X,OAAY;IAC1C,OAAOA,OAAO,CAAClc,EAAE,IAAIkc,OAAO,CAAC9D,GAAG,IAAI9T,KAAK,CAAC4a,QAAQ,EAAE;EACtD;EAEA;EAEAC,cAAcA,CAAA;IACZrJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC,MAAMqJ,WAAW,GAAG;MAClBpf,EAAE,EAAE,QAAQ2Z,IAAI,CAACiD,GAAG,EAAE,EAAE;MACxB5Z,OAAO,EAAE,mBAAmB,IAAI2W,IAAI,EAAE,CAAC0F,kBAAkB,EAAE,EAAE;MAC7Djd,SAAS,EAAE,IAAIuX,IAAI,EAAE,CAAC2F,WAAW,EAAE;MACnC7c,MAAM,EAAE;QACNzC,EAAE,EAAE,IAAI,CAACf,gBAAgB,EAAEe,EAAE,IAAI,WAAW;QAC5CgC,QAAQ,EAAE,IAAI,CAAC/C,gBAAgB,EAAE+C,QAAQ,IAAI,WAAW;QACxDU,KAAK,EACH,IAAI,CAACzD,gBAAgB,EAAEyD,KAAK,IAAI;OACnC;MACDgY,IAAI,EAAE,MAAM;MACZ6E,MAAM,EAAE;KACT;IACD,IAAI,CAAC5V,QAAQ,CAACyR,IAAI,CAACgE,WAAW,CAAC;IAC/B,IAAI,CAAC1M,GAAG,CAAC2I,aAAa,EAAE;IACxBC,UAAU,CAAC,MAAM,IAAI,CAACpB,cAAc,EAAE,EAAE,EAAE,CAAC;EAC7C;EAEAlR,mBAAmBA,CAAA;IACjB,OACE,IAAI,CAAC2J,YAAY,EAAEkG,OAAO,IAC1B,IAAI,CAAClG,YAAY,EAAEgG,YAAY,EAAErP,MAAM,GAAG,CAAC,IAC3C,KAAK;EAET;EAEAwF,UAAUA,CAAA;IACRgH,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC,IAAI,CAAC9C,kBAAkB,GAAG,KAAK;IAC/B;EACF;;EAEAvB,SAASA,CAAC8N,MAAc;IACtB,MAAMC,YAAY,GAAGjJ,QAAQ,CAACkJ,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChB,MAAME,gBAAgB,GAAGF,YAAY,CAACG,KAAK,CAACC,SAAS,IAAI,UAAU;MACnE,MAAMC,YAAY,GAAGC,UAAU,CAC7BJ,gBAAgB,CAACK,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CACvD;MACD,MAAMC,QAAQ,GAAGvD,IAAI,CAACwD,GAAG,CAAC,GAAG,EAAExD,IAAI,CAACyD,GAAG,CAAC,CAAC,EAAEL,YAAY,GAAGN,MAAM,CAAC,CAAC;MAClEC,YAAY,CAACG,KAAK,CAACC,SAAS,GAAG,SAASI,QAAQ,GAAG;MACnD,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChBR,YAAY,CAAC/I,SAAS,CAACE,GAAG,CAAC,QAAQ,CAAC;OACrC,MAAM;QACL6I,YAAY,CAAC/I,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;;EAG7C;EAEA5E,SAASA,CAAA;IACP,MAAM0N,YAAY,GAAGjJ,QAAQ,CAACkJ,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChBA,YAAY,CAACG,KAAK,CAACC,SAAS,GAAG,UAAU;MACzCJ,YAAY,CAAC/I,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;EAE3C;EAEA;EACA;EACA;EACA;EAEApI,gBAAgBA,CAACmM,IAAa;IAC5B,MAAM0F,KAAK,GAAG,IAAI,CAACC,SAAS,EAAE/D,aAAa;IAC3C,IAAI,CAAC8D,KAAK,EAAE;MACVtK,OAAO,CAAC4B,KAAK,CAAC,8BAA8B,CAAC;MAC7C;;IAGF;IACA,IAAIgD,IAAI,KAAK,OAAO,EAAE;MACpB0F,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAI5F,IAAI,KAAK,OAAO,EAAE;MAC3B0F,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAI5F,IAAI,KAAK,UAAU,EAAE;MAC9B0F,KAAK,CAACE,MAAM,GAAG,iCAAiC;KACjD,MAAM;MACLF,KAAK,CAACE,MAAM,GAAG,KAAK;;IAGtB;IACAF,KAAK,CAACrE,KAAK,GAAG,EAAE;IAEhB;IACAqE,KAAK,CAACnC,KAAK,EAAE;IACb,IAAI,CAAChL,kBAAkB,GAAG,KAAK;EACjC;EAEA5J,iBAAiBA,CAACjH,SAAwB;IACxC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMme,IAAI,GAAG,IAAI5G,IAAI,CAACvX,SAAS,CAAC;IAChC,OAAOme,IAAI,CAAClB,kBAAkB,CAAC,OAAO,EAAE;MACtCmB,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAve,mBAAmBA,CAACE,SAAwB;IAC1C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMme,IAAI,GAAG,IAAI5G,IAAI,CAACvX,SAAS,CAAC;IAChC,MAAMse,KAAK,GAAG,IAAI/G,IAAI,EAAE;IACxB,MAAMgH,SAAS,GAAG,IAAIhH,IAAI,CAAC+G,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIN,IAAI,CAACO,YAAY,EAAE,KAAKJ,KAAK,CAACI,YAAY,EAAE,EAAE;MAChD,OAAO,aAAa;KACrB,MAAM,IAAIP,IAAI,CAACO,YAAY,EAAE,KAAKH,SAAS,CAACG,YAAY,EAAE,EAAE;MAC3D,OAAO,MAAM;KACd,MAAM;MACL,OAAOP,IAAI,CAACQ,kBAAkB,CAAC,OAAO,CAAC;;EAE3C;EAEAhe,oBAAoBA,CAACC,OAAe;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB;IACA,MAAMge,QAAQ,GAAG,sBAAsB;IACvC,OAAOhe,OAAO,CAACie,OAAO,CACpBD,QAAQ,EACR,qEAAqE,CACtE;EACH;EAEAnY,uBAAuBA,CAACvE,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,MAAM4c,cAAc,GAAG,IAAI,CAACvX,QAAQ,CAACrF,KAAK,CAAC;IAC3C,MAAM6c,eAAe,GAAG,IAAI,CAACxX,QAAQ,CAACrF,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAAC4c,cAAc,EAAE9e,SAAS,IAAI,CAAC+e,eAAe,EAAE/e,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAMgf,WAAW,GAAG,IAAIzH,IAAI,CAACuH,cAAc,CAAC9e,SAAS,CAAC,CAAC0e,YAAY,EAAE;IACrE,MAAMO,YAAY,GAAG,IAAI1H,IAAI,CAACwH,eAAe,CAAC/e,SAAS,CAAC,CAAC0e,YAAY,EAAE;IAEvE,OAAOM,WAAW,KAAKC,YAAY;EACrC;EAEAtY,gBAAgBA,CAACzE,KAAa;IAC5B,MAAM4c,cAAc,GAAG,IAAI,CAACvX,QAAQ,CAACrF,KAAK,CAAC;IAC3C,MAAMgd,WAAW,GAAG,IAAI,CAAC3X,QAAQ,CAACrF,KAAK,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACgd,WAAW,EAAE,OAAO,IAAI;IAE7B,OAAOJ,cAAc,CAACze,MAAM,EAAEzC,EAAE,KAAKshB,WAAW,CAAC7e,MAAM,EAAEzC,EAAE;EAC7D;EAEAiJ,oBAAoBA,CAAC3E,KAAa;IAChC,MAAM4c,cAAc,GAAG,IAAI,CAACvX,QAAQ,CAACrF,KAAK,CAAC;IAC3C,MAAM6c,eAAe,GAAG,IAAI,CAACxX,QAAQ,CAACrF,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAAC6c,eAAe,EAAE,OAAO,IAAI;IAEjC,OAAOD,cAAc,CAACze,MAAM,EAAEzC,EAAE,KAAKmhB,eAAe,CAAC1e,MAAM,EAAEzC,EAAE;EACjE;EAEAkJ,cAAcA,CAACgT,OAAY;IACzB;IACA,IAAIA,OAAO,CAACxB,IAAI,EAAE;MAChB,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAIwB,OAAO,CAACxB,IAAI,KAAK,eAAe,EAAE,OAAO,OAAO;MACpD,IAAIwB,OAAO,CAACxB,IAAI,KAAK,MAAM,IAAIwB,OAAO,CAACxB,IAAI,KAAK,MAAM,EAAE,OAAO,MAAM;;IAGvE;IACA,IAAIwB,OAAO,CAACrB,WAAW,IAAIqB,OAAO,CAACrB,WAAW,CAACvR,MAAM,GAAG,CAAC,EAAE;MACzD,MAAMiY,UAAU,GAAGrF,OAAO,CAACrB,WAAW,CAAC,CAAC,CAAC;MACzC,IAAI0G,UAAU,CAAC7G,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAI8D,UAAU,CAAC7G,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAI8D,UAAU,CAAC7G,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,OAAO,MAAM;;IAGf;IACA,IAAIvB,OAAO,CAACsF,QAAQ,IAAItF,OAAO,CAACuF,QAAQ,IAAIvF,OAAO,CAACwF,KAAK,EAAE,OAAO,OAAO;IAEzE,OAAO,MAAM;EACf;EAEAvY,QAAQA,CAAC+S,OAAY;IACnB;IACA,IAAIA,OAAO,CAACxB,IAAI,KAAK,OAAO,IAAIwB,OAAO,CAACxB,IAAI,KAAK,OAAO,EAAE;MACxD,OAAO,IAAI;;IAGb;IACA,MAAMiH,kBAAkB,GACtBzF,OAAO,CAACrB,WAAW,EAAEK,IAAI,CAAEH,GAAQ,IAAI;MACrC,OAAOA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,IAAI1C,GAAG,CAACL,IAAI,KAAK,OAAO;IAC/D,CAAC,CAAC,IAAI,KAAK;IAEb;IACA,MAAMkH,WAAW,GAAG,CAAC,EAAE1F,OAAO,CAAC2F,QAAQ,IAAI3F,OAAO,CAACxZ,KAAK,CAAC;IAEzD,OAAOif,kBAAkB,IAAIC,WAAW;EAC1C;EAEAxY,OAAOA,CAAC8S,OAAY;IAClB;IACA,IAAIA,OAAO,CAACxB,IAAI,KAAK,MAAM,IAAIwB,OAAO,CAACxB,IAAI,KAAK,MAAM,EAAE;MACtD,OAAO,IAAI;;IAGb;IACA,MAAMoH,iBAAiB,GACrB5F,OAAO,CAACrB,WAAW,EAAEK,IAAI,CAAEH,GAAQ,IAAI;MACrC,OAAO,CAACA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,IAAI1C,GAAG,CAACL,IAAI,KAAK,OAAO;IAChE,CAAC,CAAC,IAAI,KAAK;IAEb,OAAOoH,iBAAiB;EAC1B;EAEA5d,WAAWA,CAACgY,OAAY;IACtB;IACA,IAAIA,OAAO,CAAC2F,QAAQ,EAAE;MACpB,OAAO3F,OAAO,CAAC2F,QAAQ;;IAEzB,IAAI3F,OAAO,CAACxZ,KAAK,EAAE;MACjB,OAAOwZ,OAAO,CAACxZ,KAAK;;IAGtB;IACA,MAAMqf,eAAe,GAAG7F,OAAO,CAACrB,WAAW,EAAEhE,IAAI,CAC9CkE,GAAQ,IAAKA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,IAAI1C,GAAG,CAACL,IAAI,KAAK,OAAO,CACrE;IAED,IAAIqH,eAAe,EAAE;MACnB,OAAOA,eAAe,CAAC7P,GAAG,IAAI6P,eAAe,CAAC/G,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEA3U,WAAWA,CAAC6V,OAAY;IACtB,MAAMsB,cAAc,GAAGtB,OAAO,CAACrB,WAAW,EAAEhE,IAAI,CAC7CkE,GAAQ,IAAK,CAACA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,OAAOD,cAAc,EAAE7c,IAAI,IAAI,SAAS;EAC1C;EAEA2F,WAAWA,CAAC4V,OAAY;IACtB,MAAMsB,cAAc,GAAGtB,OAAO,CAACrB,WAAW,EAAEhE,IAAI,CAC7CkE,GAAQ,IAAK,CAACA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAErL,IAAI,EAAE,OAAO,EAAE;IAEpC,MAAMmL,KAAK,GAAGE,cAAc,CAACrL,IAAI;IACjC,IAAImL,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOZ,IAAI,CAACa,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOZ,IAAI,CAACa,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAlX,WAAWA,CAAC8V,OAAY;IACtB,MAAMsB,cAAc,GAAGtB,OAAO,CAACrB,WAAW,EAAEhE,IAAI,CAC7CkE,GAAQ,IAAK,CAACA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAE9C,IAAI,EAAE,OAAO,aAAa;IAE/C,IAAI8C,cAAc,CAAC9C,IAAI,CAAC+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAAC9C,IAAI,CAAC+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAAC9C,IAAI,CAACsH,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IACjE,IAAIxE,cAAc,CAAC9C,IAAI,CAACsH,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,kBAAkB;IACnE,IAAIxE,cAAc,CAAC9C,IAAI,CAACsH,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IACrE,OAAO,aAAa;EACtB;EAEAnf,YAAYA,CAACsV,MAAc;IACzB;IACA,MAAM8J,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAM3d,KAAK,GAAG6T,MAAM,CAAC+J,UAAU,CAAC,CAAC,CAAC,GAAGD,MAAM,CAAC3Y,MAAM;IAClD,OAAO2Y,MAAM,CAAC3d,KAAK,CAAC;EACtB;EAEA;EACAsD,cAAcA,CAACsU,OAAY,EAAEqC,KAAU;IACrCzI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEmG,OAAO,CAAC;EAC1C;EAEAiG,aAAaA,CAAC5D,KAAU;IACtB;IACA,IAAI,CAAC6D,qBAAqB,EAAE;EAC9B;EAEAC,cAAcA,CAAC9D,KAAoB;IACjC,IAAIA,KAAK,CAAC+D,GAAG,KAAK,OAAO,IAAI,CAAC/D,KAAK,CAACgE,QAAQ,EAAE;MAC5ChE,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAACzS,WAAW,EAAE;;EAEtB;EAEAyW,YAAYA,CAAA;IACV;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFC,QAAQA,CAACnE,KAAU;IACjB;IACA,MAAMlC,OAAO,GAAGkC,KAAK,CAACT,MAAM;IAC5B,IACEzB,OAAO,CAACE,SAAS,KAAK,CAAC,IACvB,IAAI,CAACxJ,eAAe,IACpB,CAAC,IAAI,CAACD,aAAa,EACnB;MACA,IAAI,CAACqH,gBAAgB,EAAE;;EAE3B;EAEA3X,eAAeA,CAAC2V,MAAc;IAC5BrC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEoC,MAAM,CAAC;EAClD;EAEAvU,WAAWA,CAAC2a,KAAU,EAAErC,OAAY;IAClCpG,OAAO,CAACC,GAAG,CACT,oDAAoD,EACpDmG,OAAO,CAAClc,EAAE,EACVue,KAAK,CAACT,MAAM,CAAC6E,GAAG,CACjB;EACH;EAEA5e,YAAYA,CAACwa,KAAU,EAAErC,OAAY;IACnCpG,OAAO,CAAC4B,KAAK,CAAC,+CAA+C,EAAEwE,OAAO,CAAClc,EAAE,EAAE;MACzE2iB,GAAG,EAAEpE,KAAK,CAACT,MAAM,CAAC6E,GAAG;MACrBjL,KAAK,EAAE6G;KACR,CAAC;IACF;IACAA,KAAK,CAACT,MAAM,CAAC6E,GAAG,GACd,4WAA4W;EAChX;EAEAnf,eAAeA,CAAC0Y,OAAY;IAC1B,MAAM6F,eAAe,GAAG7F,OAAO,CAACrB,WAAW,EAAEhE,IAAI,CAAEkE,GAAQ,IACzDA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,CAC/B;IACD,IAAIsE,eAAe,EAAE7P,GAAG,EAAE;MACxB,IAAI,CAACD,aAAa,GAAG;QACnBC,GAAG,EAAE6P,eAAe,CAAC7P,GAAG;QACxBvR,IAAI,EAAEohB,eAAe,CAACphB,IAAI,IAAI,OAAO;QACrCwR,IAAI,EAAE,IAAI,CAACkL,cAAc,CAAC0E,eAAe,CAAC5P,IAAI,IAAI,CAAC,CAAC;QACpD+J,OAAO,EAAEA;OACV;MACD,IAAI,CAACzI,eAAe,GAAG,IAAI;MAC3BqC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC9D,aAAa,CAAC;;EAEvE;EAEAjB,gBAAgBA,CAAA;IACd,IAAI,CAACyC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACxB,aAAa,GAAG,IAAI;IACzB6D,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC;EAEA1E,aAAaA,CAAA;IACX,IAAI,IAAI,CAACY,aAAa,EAAEC,GAAG,EAAE;MAC3B,MAAMwL,IAAI,GAAGlH,QAAQ,CAACmH,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAG,IAAI,CAAC3L,aAAa,CAACC,GAAG;MAClCwL,IAAI,CAACG,QAAQ,GAAG,IAAI,CAAC5L,aAAa,CAACtR,IAAI,IAAI,OAAO;MAClD+c,IAAI,CAACI,MAAM,GAAG,QAAQ;MACtBtH,QAAQ,CAACuH,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,EAAE;MACZzH,QAAQ,CAACuH,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/B,IAAI,CAACjL,YAAY,CAACwE,WAAW,CAAC,wBAAwB,CAAC;MACvDnB,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAC9D,aAAa,CAACtR,IAAI,CACxB;;EAEL;EAEA;EACA;EACA;EAEAiiB,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACzP,WAAW,CAAC6I,IAAI,EAAE,EAAE;MAC5B,IAAI,CAAC5I,aAAa,GAAG,EAAE;MACvB;;IAGF,IAAI,CAACA,aAAa,GAAG,IAAI,CAACzJ,QAAQ,CAACkZ,MAAM,CACtC3G,OAAO,IACNA,OAAO,CAAClZ,OAAO,EACX8f,WAAW,EAAE,CACdd,QAAQ,CAAC,IAAI,CAAC7O,WAAW,CAAC2P,WAAW,EAAE,CAAC,IAC3C5G,OAAO,CAACzZ,MAAM,EAAET,QAAQ,EACpB8gB,WAAW,EAAE,CACdd,QAAQ,CAAC,IAAI,CAAC7O,WAAW,CAAC2P,WAAW,EAAE,CAAC,CAC9C;EACH;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACH,cAAc,EAAE;EACvB;EAEAI,WAAWA,CAAA;IACT,IAAI,CAAC7P,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,aAAa,GAAG,EAAE;EACzB;EAEA6P,aAAaA,CAACpH,SAAiB;IAC7B,MAAMqH,cAAc,GAAG1M,QAAQ,CAAC2M,cAAc,CAAC,WAAWtH,SAAS,EAAE,CAAC;IACtE,IAAIqH,cAAc,EAAE;MAClBA,cAAc,CAACE,cAAc,CAAC;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAQ,CAAE,CAAC;MACtE;MACAJ,cAAc,CAACxM,SAAS,CAACE,GAAG,CAAC,WAAW,CAAC;MACzC0E,UAAU,CAAC,MAAK;QACd4H,cAAc,CAACxM,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;MAC9C,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;EACA;EACA;EAEA4M,gBAAgBA,CAAA;IACd,IAAI,CAACjQ,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACvD,eAAe,GAAG,IAAI;EAC7B;EAEA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EAEQyT,YAAYA,CAACrP,QAAkB;IACrC,IAAI,CAAC,IAAI,CAAClV,gBAAgB,EAAE;MAC1B,IAAI,CAACwT,YAAY,CAACgG,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,MAAMgL,WAAW,GAAG,IAAI,CAACxkB,gBAAgB,CAACe,EAAE,IAAI,IAAI,CAACf,gBAAgB,CAACmZ,GAAG;IACzE,IAAI,CAACqL,WAAW,EAAE;MAChB,IAAI,CAAChR,YAAY,CAACgG,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF3C,OAAO,CAACC,GAAG,CAAC,iBAAiB5B,QAAQ,gBAAgB,EAAEsP,WAAW,CAAC;IAEnE,IAAI,CAACvP,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,KAAK3V,QAAQ,CAACklB,KAAK,GAAG,OAAO,GAAG,OAAO;IAC/D,IAAI,CAACtP,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,CAACuP,cAAc,EAAE;IAErB;IACA,IAAI,CAACnR,cAAc,CAACgR,YAAY,CAC9BC,WAAW,EACXtP,QAAQ,EACR,IAAI,CAACxB,YAAY,EAAE3S,EAAE,CACtB,CAACsX,SAAS,CAAC;MACVC,IAAI,EAAGK,IAAU,IAAI;QACnB9B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE6B,IAAI,CAAC;QACnD,IAAI,CAACtD,UAAU,GAAGsD,IAAI;QACtB,IAAI,CAACrD,eAAe,GAAG,KAAK;QAC5B,IAAI,CAAC9B,YAAY,CAACwE,WAAW,CAC3B,SAAS9C,QAAQ,KAAK3V,QAAQ,CAACklB,KAAK,GAAG,OAAO,GAAG,OAAO,SAAS,CAClE;MACH,CAAC;MACDhM,KAAK,EAAGA,KAAK,IAAI;QACf5B,OAAO,CAAC4B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAAC0F,OAAO,EAAE;QACd,IAAI,CAAC3K,YAAY,CAACgG,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACJ;EAEAmL,UAAUA,CAACpM,YAA0B;IACnC1B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEyB,YAAY,CAAC;IAExD,IAAI,CAAChF,cAAc,CAACoR,UAAU,CAACpM,YAAY,CAAC,CAACF,SAAS,CAAC;MACrDC,IAAI,EAAGK,IAAU,IAAI;QACnB9B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE6B,IAAI,CAAC;QAClD,IAAI,CAACtD,UAAU,GAAGsD,IAAI;QACtB,IAAI,CAAC1D,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACK,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACJ,QAAQ,GAAGyD,IAAI,CAAC8C,IAAI,KAAKlc,QAAQ,CAACklB,KAAK,GAAG,OAAO,GAAG,OAAO;QAChE,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAAClR,YAAY,CAACwE,WAAW,CAAC,eAAe,CAAC;MAChD,CAAC;MACDS,KAAK,EAAGA,KAAK,IAAI;QACf5B,OAAO,CAAC4B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACjF,YAAY,CAACgG,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;EACJ;EAEAoL,UAAUA,CAACrM,YAA0B;IACnC1B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEyB,YAAY,CAAC;IAExD,IAAI,CAAChF,cAAc,CAACqR,UAAU,CAACrM,YAAY,CAACxX,EAAE,EAAE,eAAe,CAAC,CAACsX,SAAS,CAAC;MACzEC,IAAI,EAAEA,CAAA,KAAK;QACTzB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3C,IAAI,CAACtD,YAAY,CAACwE,WAAW,CAAC,cAAc,CAAC;MAC/C,CAAC;MACDS,KAAK,EAAGA,KAAK,IAAI;QACf5B,OAAO,CAAC4B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACjF,YAAY,CAACgG,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;EACJ;EAEA;EACA;EAEQkL,cAAcA,CAAA;IACpB,IAAI,CAACvP,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,SAAS,GAAGyP,WAAW,CAAC,MAAK;MAChC,IAAI,CAAC1P,YAAY,EAAE;MACnB,IAAI,CAAC1B,GAAG,CAAC2I,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV;EAEQ0I,cAAcA,CAAA;IACpB,IAAI,IAAI,CAAC1P,SAAS,EAAE;MAClB2P,aAAa,CAAC,IAAI,CAAC3P,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;EAEA;EACAwP,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC3P,UAAU,EAAE;IAEtB,IAAI,CAACE,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,IAAI,CAAChC,cAAc,CAAC0R,WAAW,CAC7B,IAAI,CAAC5P,UAAU,CAACtU,EAAE,EAClBic,SAAS;IAAE;IACX,CAAC,IAAI,CAACzH,OAAO,CAAC;KACf,CAAC8C,SAAS,CAAC;MACVC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC9E,YAAY,CAACwE,WAAW,CAC3B,IAAI,CAACzC,OAAO,GAAG,aAAa,GAAG,cAAc,CAC9C;MACH,CAAC;MACDkD,KAAK,EAAGA,KAAK,IAAI;QACf5B,OAAO,CAAC4B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACA,IAAI,CAAClD,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;QAC5B,IAAI,CAAC/B,YAAY,CAACgG,SAAS,CAAC,oCAAoC,CAAC;MACnE;KACD,CAAC;EACJ;EAEA0L,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC7P,UAAU,EAAE;IAEtB,IAAI,CAACG,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C;IACA,IAAI,CAACjC,cAAc,CAAC0R,WAAW,CAC7B,IAAI,CAAC5P,UAAU,CAACtU,EAAE,EAClB,IAAI,CAACyU,cAAc;IAAE;IACrBwH,SAAS,CAAC;KACX,CAAC3E,SAAS,CAAC;MACVC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC9E,YAAY,CAACwE,WAAW,CAC3B,IAAI,CAACxC,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACDiD,KAAK,EAAGA,KAAK,IAAI;QACf5B,OAAO,CAAC4B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;QACA,IAAI,CAACjD,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,CAAChC,YAAY,CAACgG,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACJ;EAEA2L,kBAAkBA,CAACtH,QAAgB;IACjC,MAAMuH,KAAK,GAAG3H,IAAI,CAACC,KAAK,CAACG,QAAQ,GAAG,IAAI,CAAC;IACzC,MAAMwH,OAAO,GAAG5H,IAAI,CAACC,KAAK,CAAEG,QAAQ,GAAG,IAAI,GAAI,EAAE,CAAC;IAClD,MAAMyH,OAAO,GAAGzH,QAAQ,GAAG,EAAE;IAE7B,IAAIuH,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIC,OAAO,CAACpF,QAAQ,EAAE,CAACsF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CAC9DrF,QAAQ,EAAE,CACVsF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAEvB,OAAO,GAAGF,OAAO,IAAIC,OAAO,CAACrF,QAAQ,EAAE,CAACsF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EACA;EAEMC,mBAAmBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACvB7O,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MAErD,IAAI;QACF;QACA,IAAI,CAAC6O,SAAS,CAACC,YAAY,IAAI,CAACD,SAAS,CAACC,YAAY,CAACC,YAAY,EAAE;UACnE,MAAM,IAAIC,KAAK,CACb,yDAAyD,CAC1D;;QAGH;QACA,IAAI,CAACC,MAAM,CAACC,aAAa,EAAE;UACzB,MAAM,IAAIF,KAAK,CACb,uDAAuD,CACxD;;QAGHjP,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QAEzD;QACA,MAAMmP,MAAM,SAASN,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDK,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE,IAAI;YACrBC,UAAU,EAAE,KAAK;YACjBC,YAAY,EAAE;;SAEjB,CAAC;QAEF1P,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QAEnD;QACA,IAAI0P,QAAQ,GAAG,wBAAwB;QACvC,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;UAC5CA,QAAQ,GAAG,YAAY;UACvB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;YAC5CA,QAAQ,GAAG,WAAW;YACtB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;cAC5CA,QAAQ,GAAG,EAAE,CAAC,CAAC;;;;;QAKrB3P,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE0P,QAAQ,CAAC;QAEpD;QACAf,KAAI,CAAC9Q,aAAa,GAAG,IAAIqR,aAAa,CAACC,MAAM,EAAE;UAC7CO,QAAQ,EAAEA,QAAQ,IAAIxJ;SACvB,CAAC;QAEF;QACAyI,KAAI,CAAC7Q,WAAW,GAAG,EAAE;QACrB6Q,KAAI,CAAChZ,gBAAgB,GAAG,IAAI;QAC5BgZ,KAAI,CAACzX,sBAAsB,GAAG,CAAC;QAC/ByX,KAAI,CAAC/Y,mBAAmB,GAAG,WAAW;QAEtCmK,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;QAElE;QACA2O,KAAI,CAAC5Q,cAAc,GAAGgQ,WAAW,CAAC,MAAK;UACrCY,KAAI,CAACzX,sBAAsB,EAAE;UAC7B;UACAyX,KAAI,CAACiB,iBAAiB,EAAE;UACxBjB,KAAI,CAAChS,GAAG,CAAC2I,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAER;QACAqJ,KAAI,CAAC9Q,aAAa,CAACgS,eAAe,GAAIrH,KAAK,IAAI;UAC7CzI,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEwI,KAAK,CAACtB,IAAI,CAAC9K,IAAI,EAAE,OAAO,CAAC;UACnE,IAAIoM,KAAK,CAACtB,IAAI,CAAC9K,IAAI,GAAG,CAAC,EAAE;YACvBuS,KAAI,CAAC7Q,WAAW,CAACuH,IAAI,CAACmD,KAAK,CAACtB,IAAI,CAAC;;QAErC,CAAC;QAEDyH,KAAI,CAAC9Q,aAAa,CAACiS,MAAM,GAAG,MAAK;UAC/B/P,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;UACpE2O,KAAI,CAACoB,oBAAoB,EAAE;QAC7B,CAAC;QAEDpB,KAAI,CAAC9Q,aAAa,CAACmS,OAAO,GAAIxH,KAAU,IAAI;UAC1CzI,OAAO,CAAC4B,KAAK,CAAC,iCAAiC,EAAE6G,KAAK,CAAC7G,KAAK,CAAC;UAC7DgN,KAAI,CAACjS,YAAY,CAACgG,SAAS,CAAC,iCAAiC,CAAC;UAC9DiM,KAAI,CAACjY,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACAiY,KAAI,CAAC9Q,aAAa,CAACoS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/BlQ,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QAExD2O,KAAI,CAACjS,YAAY,CAACwE,WAAW,CAAC,iCAAiC,CAAC;OACjE,CAAC,OAAOS,KAAU,EAAE;QACnB5B,OAAO,CAAC4B,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAE5D,IAAIuO,YAAY,GAAG,+CAA+C;QAElE,IAAIvO,KAAK,CAAC/W,IAAI,KAAK,iBAAiB,EAAE;UACpCslB,YAAY,GACV,iGAAiG;SACpG,MAAM,IAAIvO,KAAK,CAAC/W,IAAI,KAAK,eAAe,EAAE;UACzCslB,YAAY,GACV,6DAA6D;SAChE,MAAM,IAAIvO,KAAK,CAAC/W,IAAI,KAAK,mBAAmB,EAAE;UAC7CslB,YAAY,GACV,0DAA0D;SAC7D,MAAM,IAAIvO,KAAK,CAACwE,OAAO,EAAE;UACxB+J,YAAY,GAAGvO,KAAK,CAACwE,OAAO;;QAG9BwI,KAAI,CAACjS,YAAY,CAACgG,SAAS,CAACwN,YAAY,CAAC;QACzCvB,KAAI,CAACjY,oBAAoB,EAAE;;IAC5B;EACH;EAEAI,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC+G,aAAa,IAAI,IAAI,CAACA,aAAa,CAACsS,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAACtS,aAAa,CAACuS,IAAI,EAAE;MACzB,IAAI,CAACvS,aAAa,CAACsR,MAAM,CAACkB,SAAS,EAAE,CAACtL,OAAO,CAAEuL,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAACrS,cAAc,EAAE;MACvBkQ,aAAa,CAAC,IAAI,CAAClQ,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACpI,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,YAAY;EACzC;EAEAc,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACmH,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACsS,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAACtS,aAAa,CAACuS,IAAI,EAAE;;MAE3B,IAAI,CAACvS,aAAa,CAACsR,MAAM,CAACkB,SAAS,EAAE,CAACtL,OAAO,CAAEuL,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;MACtE,IAAI,CAACvS,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvBkQ,aAAa,CAAC,IAAI,CAAClQ,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACpI,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACuB,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACtB,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACkI,WAAW,GAAG,EAAE;EACvB;EAEciS,oBAAoBA,CAAA;IAAA,IAAAQ,MAAA;IAAA,OAAA3B,iBAAA;MAChC7O,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MAEtD,IAAI;QACF;QACA,IAAIuQ,MAAI,CAACzS,WAAW,CAACvK,MAAM,KAAK,CAAC,EAAE;UACjCwM,OAAO,CAAC4B,KAAK,CAAC,sCAAsC,CAAC;UACrD4O,MAAI,CAAC7T,YAAY,CAACgG,SAAS,CAAC,wBAAwB,CAAC;UACrD6N,MAAI,CAAC7Z,oBAAoB,EAAE;UAC3B;;QAGFqJ,OAAO,CAACC,GAAG,CACT,0BAA0B,EAC1BuQ,MAAI,CAACzS,WAAW,CAACvK,MAAM,EACvB,WAAW,EACXgd,MAAI,CAACrZ,sBAAsB,CAC5B;QAED;QACA,IAAIqZ,MAAI,CAACrZ,sBAAsB,GAAG,CAAC,EAAE;UACnC6I,OAAO,CAAC4B,KAAK,CACX,iCAAiC,EACjC4O,MAAI,CAACrZ,sBAAsB,CAC5B;UACDqZ,MAAI,CAAC7T,YAAY,CAACgG,SAAS,CACzB,+CAA+C,CAChD;UACD6N,MAAI,CAAC7Z,oBAAoB,EAAE;UAC3B;;QAGF;QACA,IAAIgZ,QAAQ,GAAG,wBAAwB;QACvC,IAAIa,MAAI,CAAC1S,aAAa,EAAE6R,QAAQ,EAAE;UAChCA,QAAQ,GAAGa,MAAI,CAAC1S,aAAa,CAAC6R,QAAQ;;QAGxC3P,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE0P,QAAQ,CAAC;QAEvE;QACA,MAAMc,SAAS,GAAG,IAAIC,IAAI,CAACF,MAAI,CAACzS,WAAW,EAAE;UAC3C6G,IAAI,EAAE+K;SACP,CAAC;QAEF3P,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5C5D,IAAI,EAAEoU,SAAS,CAACpU,IAAI;UACpBuI,IAAI,EAAE6L,SAAS,CAAC7L;SACjB,CAAC;QAEF;QACA,IAAI+L,SAAS,GAAG,OAAO;QACvB,IAAIhB,QAAQ,CAACzD,QAAQ,CAAC,KAAK,CAAC,EAAE;UAC5ByE,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIhB,QAAQ,CAACzD,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnCyE,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIhB,QAAQ,CAACzD,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnCyE,SAAS,GAAG,MAAM;;QAGpB;QACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CACxB,CAACJ,SAAS,CAAC,EACX,SAAS5M,IAAI,CAACiD,GAAG,EAAE,GAAG6J,SAAS,EAAE,EACjC;UACE/L,IAAI,EAAE+K;SACP,CACF;QAED3P,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CpV,IAAI,EAAE+lB,SAAS,CAAC/lB,IAAI;UACpBwR,IAAI,EAAEuU,SAAS,CAACvU,IAAI;UACpBuI,IAAI,EAAEgM,SAAS,CAAChM;SACjB,CAAC;QAEF;QACA4L,MAAI,CAAC3a,mBAAmB,GAAG,YAAY;QACvC,MAAM2a,MAAI,CAACM,gBAAgB,CAACF,SAAS,CAAC;QAEtC5Q,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzDuQ,MAAI,CAAC7T,YAAY,CAACwE,WAAW,CAAC,yBAAyB,CAAC;OACzD,CAAC,OAAOS,KAAU,EAAE;QACnB5B,OAAO,CAAC4B,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D4O,MAAI,CAAC7T,YAAY,CAACgG,SAAS,CACzB,2CAA2C,IACxCf,KAAK,CAACwE,OAAO,IAAI,iBAAiB,CAAC,CACvC;OACF,SAAS;QACR;QACAoK,MAAI,CAAC3a,mBAAmB,GAAG,MAAM;QACjC2a,MAAI,CAACrZ,sBAAsB,GAAG,CAAC;QAC/BqZ,MAAI,CAACzS,WAAW,GAAG,EAAE;QACrByS,MAAI,CAAC5a,gBAAgB,GAAG,KAAK;QAE7BoK,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;;IAClE;EACH;EAEc6Q,gBAAgBA,CAACF,SAAe;IAAA,IAAAG,MAAA;IAAA,OAAAlC,iBAAA;MAC5C,MAAM/J,UAAU,GAAGiM,MAAI,CAAC5nB,gBAAgB,EAAEe,EAAE,IAAI6mB,MAAI,CAAC5nB,gBAAgB,EAAEmZ,GAAG;MAE1E,IAAI,CAACwC,UAAU,EAAE;QACf,MAAM,IAAImK,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAI+B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCH,MAAI,CAACrU,cAAc,CAACzG,WAAW,CAC7B6O,UAAU,EACV,EAAE,EACF8L,SAAS,EACT,OAAc,EACdG,MAAI,CAAClU,YAAY,CAAC3S,EAAE,CACrB,CAACsX,SAAS,CAAC;UACVC,IAAI,EAAG2E,OAAY,IAAI;YACrB2K,MAAI,CAACld,QAAQ,CAACyR,IAAI,CAACc,OAAO,CAAC;YAC3B2K,MAAI,CAAC3M,cAAc,EAAE;YACrB6M,OAAO,EAAE;UACX,CAAC;UACDrP,KAAK,EAAGA,KAAU,IAAI;YACpB5B,OAAO,CAAC4B,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChEsP,MAAM,CAACtP,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA1K,uBAAuBA,CAAC8P,QAAgB;IACtC,MAAMwH,OAAO,GAAG5H,IAAI,CAACC,KAAK,CAACG,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMyH,OAAO,GAAGzH,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAGwH,OAAO,IAAIC,OAAO,CAACrF,QAAQ,EAAE,CAACsF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EAEAha,aAAaA,CAAC+T,KAAY;IACxBA,KAAK,CAACC,cAAc,EAAE;IACtB1I,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvCrK,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CsB,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;MACnD2G,aAAa,EAAE,CAAC,CAAC,IAAI,CAACA;KACvB,CAAC;IAEF;IACA,IAAI,IAAI,CAACjI,mBAAmB,KAAK,YAAY,EAAE;MAC7CmK,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,IAAI,CAACtD,YAAY,CAACwU,WAAW,CAAC,wBAAwB,CAAC;MACvD;;IAGF,IAAI,IAAI,CAACvb,gBAAgB,EAAE;MACzBoK,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D,IAAI,CAACtD,YAAY,CAACwU,WAAW,CAAC,iCAAiC,CAAC;MAChE;;IAGF;IACA,IAAI,CAACxU,YAAY,CAACyU,QAAQ,CAAC,2CAA2C,CAAC;IAEvE;IACA,IAAI,CAACzC,mBAAmB,EAAE,CAAC0C,KAAK,CAAEzP,KAAK,IAAI;MACzC5B,OAAO,CAAC4B,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAACjF,YAAY,CAACgG,SAAS,CACzB,iDAAiD,IAC9Cf,KAAK,CAACwE,OAAO,IAAI,iBAAiB,CAAC,CACvC;IACH,CAAC,CAAC;EACJ;EAEAvR,WAAWA,CAAC4T,KAAY;IACtBA,KAAK,CAACC,cAAc,EAAE;IACtB1I,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,IAAI,CAAC,IAAI,CAACrK,gBAAgB,EAAE;MAC1BoK,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD;;IAGF;IACA,IAAI,CAAClJ,kBAAkB,EAAE;EAC3B;EAEA/B,cAAcA,CAACyT,KAAY;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtB1I,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD,IAAI,CAAC,IAAI,CAACrK,gBAAgB,EAAE;MAC1BoK,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD;;IAGF;IACA,IAAI,CAACtJ,oBAAoB,EAAE;EAC7B;EAEAS,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC0G,aAAa,EAAE6R,QAAQ,EAAE;MAChC,IAAI,IAAI,CAAC7R,aAAa,CAAC6R,QAAQ,CAACzD,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,MAAM;MAC/D,IAAI,IAAI,CAACpO,aAAa,CAAC6R,QAAQ,CAACzD,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAACpO,aAAa,CAAC6R,QAAQ,CAACzD,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAACpO,aAAa,CAAC6R,QAAQ,CAACzD,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;;IAE/D,OAAO,MAAM;EACf;EAEA;EAEQ2D,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAAC5Y,UAAU,GAAG,IAAI,CAACA,UAAU,CAACqa,GAAG,CAAC,MAAK;MACzC,OAAO1K,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC2K,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;EACJ;;EAEAC,cAAcA,CAAC/I,KAAU;IACvBzI,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD,MAAMwR,KAAK,GAAGhJ,KAAK,CAACT,MAAM,CAACyJ,KAAK;IAEhC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACje,MAAM,KAAK,CAAC,EAAE;MAChCwM,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C;;IAGFD,OAAO,CAACC,GAAG,CAAC,eAAewR,KAAK,CAACje,MAAM,oBAAoB,EAAEie,KAAK,CAAC;IAEnE,KAAK,IAAIC,IAAI,IAAID,KAAK,EAAE;MACtBzR,OAAO,CAACC,GAAG,CACT,gCAAgCyR,IAAI,CAAC7mB,IAAI,WAAW6mB,IAAI,CAACrV,IAAI,WAAWqV,IAAI,CAAC9M,IAAI,EAAE,CACpF;MACD,IAAI,CAAC+M,UAAU,CAACD,IAAI,CAAC;;EAEzB;EAEQC,UAAUA,CAACD,IAAU;IAC3B1R,OAAO,CAACC,GAAG,CAAC,yCAAyCyR,IAAI,CAAC7mB,IAAI,EAAE,CAAC;IAEjE,MAAMia,UAAU,GAAG,IAAI,CAAC3b,gBAAgB,EAAEe,EAAE,IAAI,IAAI,CAACf,gBAAgB,EAAEmZ,GAAG;IAE1E,IAAI,CAACwC,UAAU,EAAE;MACf9E,OAAO,CAAC4B,KAAK,CAAC,kCAAkC,CAAC;MACjD,IAAI,CAACjF,YAAY,CAACgG,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF3C,OAAO,CAACC,GAAG,CAAC,4BAA4B6E,UAAU,EAAE,CAAC;IAErD;IACA,MAAM8M,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,IAAIF,IAAI,CAACrV,IAAI,GAAGuV,OAAO,EAAE;MACvB5R,OAAO,CAAC4B,KAAK,CAAC,+BAA+B8P,IAAI,CAACrV,IAAI,QAAQ,CAAC;MAC/D,IAAI,CAACM,YAAY,CAACgG,SAAS,CAAC,oCAAoC,CAAC;MACjE;;IAGF;IACA,IAAI+O,IAAI,CAAC9M,IAAI,CAAC+C,UAAU,CAAC,QAAQ,CAAC,IAAI+J,IAAI,CAACrV,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;MAC7D;MACA2D,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtCyR,IAAI,CAAC7mB,IAAI,EACT,gBAAgB,EAChB6mB,IAAI,CAACrV,IAAI,CACV;MACD,IAAI,CAACwV,aAAa,CAACH,IAAI,CAAC,CACrBI,IAAI,CAAEC,cAAc,IAAI;QACvB/R,OAAO,CAACC,GAAG,CACT,8DAA8D,EAC9D8R,cAAc,CAAC1V,IAAI,CACpB;QACD,IAAI,CAAC2V,gBAAgB,CAACD,cAAc,EAAEjN,UAAU,CAAC;MACnD,CAAC,CAAC,CACDuM,KAAK,CAAEzP,KAAK,IAAI;QACf5B,OAAO,CAAC4B,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE;QACA,IAAI,CAACoQ,gBAAgB,CAACN,IAAI,EAAE5M,UAAU,CAAC;MACzC,CAAC,CAAC;MACJ;;IAGF;IACA,IAAI,CAACkN,gBAAgB,CAACN,IAAI,EAAE5M,UAAU,CAAC;EACzC;EAEQkN,gBAAgBA,CAACN,IAAU,EAAE5M,UAAkB;IACrD,MAAMmN,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACR,IAAI,CAAC;IACjD1R,OAAO,CAACC,GAAG,CAAC,wCAAwCgS,WAAW,EAAE,CAAC;IAClEjS,OAAO,CAACC,GAAG,CAAC,gCAAgC,IAAI,CAACpD,YAAY,CAAC3S,EAAE,EAAE,CAAC;IAEnE,IAAI,CAACmM,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACuH,WAAW,GAAG,IAAI;IACvB,IAAI,CAACvJ,cAAc,GAAG,CAAC;IACvB2L,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhE;IACA,MAAMkS,gBAAgB,GAAGnE,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC3Z,cAAc,IAAIuS,IAAI,CAAC2K,MAAM,EAAE,GAAG,EAAE;MACzC,IAAI,IAAI,CAACld,cAAc,IAAI,EAAE,EAAE;QAC7B6Z,aAAa,CAACiE,gBAAgB,CAAC;;MAEjC,IAAI,CAACvV,GAAG,CAAC2I,aAAa,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,CAAC7I,cAAc,CAACzG,WAAW,CAC7B6O,UAAU,EACV,EAAE,EACF4M,IAAI,EACJO,WAAW,EACX,IAAI,CAACpV,YAAY,CAAC3S,EAAE,CACrB,CAACsX,SAAS,CAAC;MACVC,IAAI,EAAG2E,OAAY,IAAI;QACrBpG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEmG,OAAO,CAAC;QAC7DpG,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;UAChD/V,EAAE,EAAEkc,OAAO,CAAClc,EAAE;UACd0a,IAAI,EAAEwB,OAAO,CAACxB,IAAI;UAClBG,WAAW,EAAEqB,OAAO,CAACrB,WAAW;UAChC1R,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC+S,OAAO,CAAC;UAChC9S,OAAO,EAAE,IAAI,CAACA,OAAO,CAAC8S,OAAO,CAAC;UAC9B2F,QAAQ,EAAE,IAAI,CAAC3d,WAAW,CAACgY,OAAO;SACnC,CAAC;QAEF8H,aAAa,CAACiE,gBAAgB,CAAC;QAC/B,IAAI,CAAC9d,cAAc,GAAG,GAAG;QAEzBmR,UAAU,CAAC,MAAK;UACd,IAAI,CAAC3R,QAAQ,CAACyR,IAAI,CAACc,OAAO,CAAC;UAC3B,IAAI,CAAChC,cAAc,EAAE;UACrB,IAAI,CAACzH,YAAY,CAACwE,WAAW,CAAC,4BAA4B,CAAC;UAC3D,IAAI,CAAChN,gBAAgB,EAAE;QACzB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDyN,KAAK,EAAGA,KAAU,IAAI;QACpB5B,OAAO,CAAC4B,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDsM,aAAa,CAACiE,gBAAgB,CAAC;QAC/B,IAAI,CAACxV,YAAY,CAACgG,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAACxO,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEQ+d,kBAAkBA,CAACR,IAAU;IACnC,IAAIA,IAAI,CAAC9M,IAAI,CAAC+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAI+J,IAAI,CAAC9M,IAAI,CAAC+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAI+J,IAAI,CAAC9M,IAAI,CAAC+C,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,OAAO,MAAa;EACtB;EAEAyK,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEAje,gBAAgBA,CAAA;IACd,IAAI,CAACkC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACuH,WAAW,GAAG,KAAK;IACxB,IAAI,CAACvJ,cAAc,GAAG,CAAC;EACzB;EAEA;EAEAge,UAAUA,CAAC5J,KAAgB;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAAC/Z,eAAe,EAAE;IACvB,IAAI,CAACmP,UAAU,GAAG,IAAI;EACxB;EAEAyU,WAAWA,CAAC7J,KAAgB;IAC1BA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAAC/Z,eAAe,EAAE;IACvB;IACA,MAAM6jB,IAAI,GAAI9J,KAAK,CAAC+J,aAA6B,CAACC,qBAAqB,EAAE;IACzE,MAAMhZ,CAAC,GAAGgP,KAAK,CAACE,OAAO;IACvB,MAAMjP,CAAC,GAAG+O,KAAK,CAACG,OAAO;IAEvB,IAAInP,CAAC,GAAG8Y,IAAI,CAACG,IAAI,IAAIjZ,CAAC,GAAG8Y,IAAI,CAACI,KAAK,IAAIjZ,CAAC,GAAG6Y,IAAI,CAACK,GAAG,IAAIlZ,CAAC,GAAG6Y,IAAI,CAACM,MAAM,EAAE;MACtE,IAAI,CAAChV,UAAU,GAAG,KAAK;;EAE3B;EAEAiV,MAAMA,CAACrK,KAAgB;IACrBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAAC/Z,eAAe,EAAE;IACvB,IAAI,CAACmP,UAAU,GAAG,KAAK;IAEvB,MAAM4T,KAAK,GAAGhJ,KAAK,CAACsK,YAAY,EAAEtB,KAAK;IACvC,IAAIA,KAAK,IAAIA,KAAK,CAACje,MAAM,GAAG,CAAC,EAAE;MAC7BwM,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEwR,KAAK,CAACje,MAAM,CAAC;MAE1D;MACAwf,KAAK,CAACC,IAAI,CAACxB,KAAK,CAAC,CAACzM,OAAO,CAAE0M,IAAI,IAAI;QACjC1R,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCyR,IAAI,CAAC7mB,IAAI,EACT6mB,IAAI,CAAC9M,IAAI,EACT8M,IAAI,CAACrV,IAAI,CACV;QACD,IAAI,CAACsV,UAAU,CAACD,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,IAAI,CAAC/U,YAAY,CAACwE,WAAW,CAC3B,GAAGsQ,KAAK,CAACje,MAAM,8BAA8B,CAC9C;;EAEL;EAEA;EAEQqe,aAAaA,CAACH,IAAU,EAAEwB,OAAA,GAAkB,GAAG;IACrD,OAAO,IAAIlC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMiC,MAAM,GAAGzS,QAAQ,CAACmH,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMuL,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;MAEvBD,GAAG,CAACE,MAAM,GAAG,MAAK;QAChB;QACA,MAAMC,QAAQ,GAAG,IAAI;QACrB,MAAMC,SAAS,GAAG,IAAI;QACtB,IAAI;UAAEC,KAAK;UAAEC;QAAM,CAAE,GAAGN,GAAG;QAE3B,IAAIK,KAAK,GAAGF,QAAQ,IAAIG,MAAM,GAAGF,SAAS,EAAE;UAC1C,MAAMG,KAAK,GAAGjN,IAAI,CAACyD,GAAG,CAACoJ,QAAQ,GAAGE,KAAK,EAAED,SAAS,GAAGE,MAAM,CAAC;UAC5DD,KAAK,IAAIE,KAAK;UACdD,MAAM,IAAIC,KAAK;;QAGjBV,MAAM,CAACQ,KAAK,GAAGA,KAAK;QACpBR,MAAM,CAACS,MAAM,GAAGA,MAAM;QAEtB;QACAR,GAAG,EAAEU,SAAS,CAACR,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEK,KAAK,EAAEC,MAAM,CAAC;QAExC;QACAT,MAAM,CAACY,MAAM,CACVC,IAAI,IAAI;UACP,IAAIA,IAAI,EAAE;YACR,MAAMjC,cAAc,GAAG,IAAIlB,IAAI,CAAC,CAACmD,IAAI,CAAC,EAAEtC,IAAI,CAAC7mB,IAAI,EAAE;cACjD+Z,IAAI,EAAE8M,IAAI,CAAC9M,IAAI;cACfqP,YAAY,EAAEpQ,IAAI,CAACiD,GAAG;aACvB,CAAC;YACFmK,OAAO,CAACc,cAAc,CAAC;WACxB,MAAM;YACLb,MAAM,CAAC,IAAIjC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;QAEjD,CAAC,EACDyC,IAAI,CAAC9M,IAAI,EACTsO,OAAO,CACR;MACH,CAAC;MAEDI,GAAG,CAACrD,OAAO,GAAG,MAAMiB,MAAM,CAAC,IAAIjC,KAAK,CAAC,sBAAsB,CAAC,CAAC;MAC7DqE,GAAG,CAACzG,GAAG,GAAGqH,GAAG,CAACC,eAAe,CAACzC,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA;EAEQpF,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACrN,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB;MACA,IAAI,CAACmV,mBAAmB,CAAC,IAAI,CAAC;;IAGhC;IACA,IAAI,IAAI,CAACjV,aAAa,EAAE;MACtBkV,YAAY,CAAC,IAAI,CAAClV,aAAa,CAAC;;IAGlC,IAAI,CAACA,aAAa,GAAGqG,UAAU,CAAC,MAAK;MACnC,IAAI,CAACvG,QAAQ,GAAG,KAAK;MACrB;MACA,IAAI,CAACmV,mBAAmB,CAAC,KAAK,CAAC;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;EAEQA,mBAAmBA,CAACnV,QAAiB;IAC3C;IACA,MAAM6F,UAAU,GAAG,IAAI,CAAC3b,gBAAgB,EAAEe,EAAE,IAAI,IAAI,CAACf,gBAAgB,EAAEmZ,GAAG;IAC1E,IAAIwC,UAAU,IAAI,IAAI,CAACjI,YAAY,EAAE3S,EAAE,EAAE;MACvC8V,OAAO,CAACC,GAAG,CACT,gCAAgChB,QAAQ,YAAY6F,UAAU,EAAE,CACjE;MACD;MACA;;EAEJ;EAEA;EAEAwP,cAAcA,CAACxS,IAAU;IACvB9B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE6B,IAAI,CAAC;IACrD,IAAI,CAACtD,UAAU,GAAGsD,IAAI;IACtB,IAAI,CAAC1D,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACK,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACoP,cAAc,EAAE;IACrB,IAAI,CAAClR,YAAY,CAACwE,WAAW,CAAC,eAAe,CAAC;EAChD;EAEAoT,cAAcA,CAAA;IACZvU,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAACqH,OAAO,EAAE;IACd,IAAI,CAAC3K,YAAY,CAACyU,QAAQ,CAAC,cAAc,CAAC;EAC5C;EAEA;EAEAoD,gBAAgBA,CAACpO,OAAY;IAC3BpG,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEmG,OAAO,CAAClc,EAAE,CAAC;IAC5D,IAAI,CAACoF,mBAAmB,CAAC8W,OAAO,CAAC;EACnC;EAEAtX,cAAcA,CAACiX,SAAiB;IAC9B,OAAO,IAAI,CAAC7H,gBAAgB,KAAK6H,SAAS;EAC5C;EAEAzW,mBAAmBA,CAAC8W,OAAY;IAC9B,MAAML,SAAS,GAAGK,OAAO,CAAClc,EAAE;IAC5B,MAAMyhB,QAAQ,GAAG,IAAI,CAAC8I,WAAW,CAACrO,OAAO,CAAC;IAE1C,IAAI,CAACuF,QAAQ,EAAE;MACb3L,OAAO,CAAC4B,KAAK,CAAC,4CAA4C,EAAEmE,SAAS,CAAC;MACtE,IAAI,CAACpJ,YAAY,CAACgG,SAAS,CAAC,2BAA2B,CAAC;MACxD;;IAGF;IACA,IAAI,IAAI,CAAC7T,cAAc,CAACiX,SAAS,CAAC,EAAE;MAClC,IAAI,CAAC2O,iBAAiB,EAAE;MACxB;;IAGF;IACA,IAAI,CAACA,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,kBAAkB,CAACvO,OAAO,EAAEuF,QAAQ,CAAC;EAC5C;EAEQgJ,kBAAkBA,CAACvO,OAAY,EAAEuF,QAAgB;IACvD,MAAM5F,SAAS,GAAGK,OAAO,CAAClc,EAAE;IAE5B,IAAI;MACF8V,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnC8F,SAAS,EACT,MAAM,EACN4F,QAAQ,CACT;MAED,IAAI,CAAC1N,YAAY,GAAG,IAAI2W,KAAK,CAACjJ,QAAQ,CAAC;MACvC,IAAI,CAACzN,gBAAgB,GAAG6H,SAAS;MAEjC;MACA,MAAM8O,WAAW,GAAG,IAAI,CAAC5lB,oBAAoB,CAAC8W,SAAS,CAAC;MACxD,IAAI,CAACmB,oBAAoB,CAACnB,SAAS,EAAE;QACnCgB,QAAQ,EAAE,CAAC;QACXE,WAAW,EAAE,CAAC;QACd/X,KAAK,EAAE2lB,WAAW,CAAC3lB,KAAK,IAAI,CAAC;QAC7B8X,QAAQ,EAAE6N,WAAW,CAAC7N,QAAQ,IAAI;OACnC,CAAC;MAEF;MACA,IAAI,CAAC/I,YAAY,CAAC6W,YAAY,GAAGD,WAAW,CAAC3lB,KAAK,IAAI,CAAC;MAEvD;MACA,IAAI,CAAC+O,YAAY,CAAC8W,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QACxD,IAAI,IAAI,CAAC9W,YAAY,EAAE;UACrB,IAAI,CAACiJ,oBAAoB,CAACnB,SAAS,EAAE;YACnCiB,QAAQ,EAAE,IAAI,CAAC/I,YAAY,CAAC+I;WAC7B,CAAC;UACFhH,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAAChC,YAAY,CAAC+I,QAAQ,CAC3B;;MAEL,CAAC,CAAC;MAEF,IAAI,CAAC/I,YAAY,CAAC8W,gBAAgB,CAAC,YAAY,EAAE,MAAK;QACpD,IAAI,IAAI,CAAC9W,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAK6H,SAAS,EAAE;UAC5D,MAAMkB,WAAW,GAAG,IAAI,CAAChJ,YAAY,CAACgJ,WAAW;UACjD,MAAMF,QAAQ,GAAIE,WAAW,GAAG,IAAI,CAAChJ,YAAY,CAAC+I,QAAQ,GAAI,GAAG;UACjE,IAAI,CAACE,oBAAoB,CAACnB,SAAS,EAAE;YAAEkB,WAAW;YAAEF;UAAQ,CAAE,CAAC;UAC/D,IAAI,CAACnK,GAAG,CAAC2I,aAAa,EAAE;;MAE5B,CAAC,CAAC;MAEF,IAAI,CAACtH,YAAY,CAAC8W,gBAAgB,CAAC,OAAO,EAAE,MAAK;QAC/C/U,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE8F,SAAS,CAAC;QACxD,IAAI,CAAC2O,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF,IAAI,CAACzW,YAAY,CAAC8W,gBAAgB,CAAC,OAAO,EAAGnT,KAAK,IAAI;QACpD5B,OAAO,CAAC4B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACjF,YAAY,CAACgG,SAAS,CAAC,iCAAiC,CAAC;QAC9D,IAAI,CAAC+R,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF;MACA,IAAI,CAACzW,YAAY,CACd+D,IAAI,EAAE,CACN8P,IAAI,CAAC,MAAK;QACT9R,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,IAAI,CAACtD,YAAY,CAACwE,WAAW,CAAC,6BAA6B,CAAC;MAC9D,CAAC,CAAC,CACDkQ,KAAK,CAAEzP,KAAK,IAAI;QACf5B,OAAO,CAAC4B,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAACjF,YAAY,CAACgG,SAAS,CAAC,qCAAqC,CAAC;QAClE,IAAI,CAAC+R,iBAAiB,EAAE;MAC1B,CAAC,CAAC;KACL,CAAC,OAAO9S,KAAK,EAAE;MACd5B,OAAO,CAAC4B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,IAAI,CAACjF,YAAY,CAACgG,SAAS,CAAC,iCAAiC,CAAC;MAC9D,IAAI,CAAC+R,iBAAiB,EAAE;;EAE5B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACzW,YAAY,EAAE;MACrB+B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC/B,gBAAgB,CAAC;MACvE,IAAI,CAACD,YAAY,CAAC+W,KAAK,EAAE;MACzB,IAAI,CAAC/W,YAAY,CAACgJ,WAAW,GAAG,CAAC;MACjC,IAAI,CAAChJ,YAAY,GAAG,IAAI;;IAE1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACtB,GAAG,CAAC2I,aAAa,EAAE;EAC1B;EAEAkP,WAAWA,CAACrO,OAAY;IACtB;IACA,IAAIA,OAAO,CAACsF,QAAQ,EAAE,OAAOtF,OAAO,CAACsF,QAAQ;IAC7C,IAAItF,OAAO,CAACuF,QAAQ,EAAE,OAAOvF,OAAO,CAACuF,QAAQ;IAC7C,IAAIvF,OAAO,CAACwF,KAAK,EAAE,OAAOxF,OAAO,CAACwF,KAAK;IAEvC;IACA,MAAMqJ,eAAe,GAAG7O,OAAO,CAACrB,WAAW,EAAEhE,IAAI,CAC9CkE,GAAQ,IAAKA,GAAG,CAACL,IAAI,EAAE+C,UAAU,CAAC,QAAQ,CAAC,IAAI1C,GAAG,CAACL,IAAI,KAAK,OAAO,CACrE;IAED,IAAIqQ,eAAe,EAAE;MACnB,OAAOA,eAAe,CAAC7Y,GAAG,IAAI6Y,eAAe,CAAC/P,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEApV,aAAaA,CAACsW,OAAY;IACxB;IACA,MAAML,SAAS,GAAGK,OAAO,CAAClc,EAAE,IAAI,EAAE;IAClC,MAAMgrB,IAAI,GAAGnP,SAAS,CACnBoP,KAAK,CAAC,EAAE,CAAC,CACTC,MAAM,CAAC,CAACC,GAAW,EAAEC,IAAY,KAAKD,GAAG,GAAGC,IAAI,CAAClJ,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,MAAMmJ,KAAK,GAAa,EAAE;IAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAM5B,MAAM,GAAG,CAAC,GAAI,CAACsB,IAAI,GAAGM,CAAC,GAAG,CAAC,IAAI,EAAG;MACxCD,KAAK,CAACjQ,IAAI,CAACsO,MAAM,CAAC;;IAGpB,OAAO2B,KAAK;EACd;EAEAxmB,gBAAgBA,CAACqX,OAAY;IAC3B,MAAMe,IAAI,GAAG,IAAI,CAAClY,oBAAoB,CAACmX,OAAO,CAAClc,EAAE,CAAC;IAClD,MAAMurB,UAAU,GAAG,EAAE;IACrB,OAAO7O,IAAI,CAACC,KAAK,CAAEM,IAAI,CAACJ,QAAQ,GAAG,GAAG,GAAI0O,UAAU,CAAC;EACvD;EAEA1lB,mBAAmBA,CAACqW,OAAY;IAC9B,MAAMe,IAAI,GAAG,IAAI,CAAClY,oBAAoB,CAACmX,OAAO,CAAClc,EAAE,CAAC;IAClD,OAAO,IAAI,CAACwrB,eAAe,CAACvO,IAAI,CAACF,WAAW,CAAC;EAC/C;EAEAjX,gBAAgBA,CAACoW,OAAY;IAC3B,MAAMe,IAAI,GAAG,IAAI,CAAClY,oBAAoB,CAACmX,OAAO,CAAClc,EAAE,CAAC;IAClD,MAAM8c,QAAQ,GAAGG,IAAI,CAACH,QAAQ,IAAIZ,OAAO,CAACuP,QAAQ,EAAE3O,QAAQ,IAAI,CAAC;IAEjE,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAOA,QAAQ,CAAC,CAAC;;;IAGnB,OAAO,IAAI,CAAC0O,eAAe,CAAC1O,QAAQ,CAAC;EACvC;EAEQ0O,eAAeA,CAACjH,OAAe;IACrC,MAAMD,OAAO,GAAG5H,IAAI,CAACC,KAAK,CAAC4H,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMmH,gBAAgB,GAAGhP,IAAI,CAACC,KAAK,CAAC4H,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGD,OAAO,IAAIoH,gBAAgB,CAACxM,QAAQ,EAAE,CAACsF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEA/f,gBAAgBA,CAACyX,OAAY,EAAEyP,SAAiB;IAC9C,MAAM9P,SAAS,GAAGK,OAAO,CAAClc,EAAE;IAE5B,IAAI,CAAC,IAAI,CAAC+T,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAK6H,SAAS,EAAE;MAC7D;;IAGF,MAAM0P,UAAU,GAAG,EAAE;IACrB,MAAMK,cAAc,GAAID,SAAS,GAAGJ,UAAU,GAAI,GAAG;IACrD,MAAMM,QAAQ,GAAID,cAAc,GAAG,GAAG,GAAI,IAAI,CAAC7X,YAAY,CAAC+I,QAAQ;IAEpE,IAAI,CAAC/I,YAAY,CAACgJ,WAAW,GAAG8O,QAAQ;IACxC/V,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE8V,QAAQ,EAAE,SAAS,CAAC;EAC5D;EAEAnmB,gBAAgBA,CAACwW,OAAY;IAC3B,MAAML,SAAS,GAAGK,OAAO,CAAClc,EAAE;IAC5B,MAAMid,IAAI,GAAG,IAAI,CAAClY,oBAAoB,CAAC8W,SAAS,CAAC;IAEjD;IACA,MAAMiQ,QAAQ,GAAG7O,IAAI,CAACjY,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGiY,IAAI,CAACjY,KAAK,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;IAEpE,IAAI,CAACgY,oBAAoB,CAACnB,SAAS,EAAE;MAAE7W,KAAK,EAAE8mB;IAAQ,CAAE,CAAC;IAEzD,IAAI,IAAI,CAAC/X,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAK6H,SAAS,EAAE;MAC5D,IAAI,CAAC9H,YAAY,CAAC6W,YAAY,GAAGkB,QAAQ;;IAG3C,IAAI,CAACrZ,YAAY,CAACwE,WAAW,CAAC,YAAY6U,QAAQ,GAAG,CAAC;EACxD;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC7W,aAAa,CAAC8W,WAAW,EAAE;IAEhC;IACA,IAAI,IAAI,CAAC3X,SAAS,EAAE;MAClB2P,aAAa,CAAC,IAAI,CAAC3P,SAAS,CAAC;;IAE/B,IAAI,IAAI,CAACP,cAAc,EAAE;MACvBkQ,aAAa,CAAC,IAAI,CAAClQ,cAAc,CAAC;;IAEpC,IAAI,IAAI,CAACmB,aAAa,EAAE;MACtBkV,YAAY,CAAC,IAAI,CAAClV,aAAa,CAAC;;IAGlC;IACA,IAAI,IAAI,CAACrB,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACsS,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAACtS,aAAa,CAACuS,IAAI,EAAE;;MAE3B,IAAI,CAACvS,aAAa,CAACsR,MAAM,EAAEkB,SAAS,EAAE,CAACtL,OAAO,CAAEuL,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGzE;IACA,IAAI,CAACqE,iBAAiB,EAAE;EAC1B;;;uBAp6EWpY,oBAAoB,EAAA3T,EAAA,CAAAwtB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1tB,EAAA,CAAAwtB,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5tB,EAAA,CAAAwtB,iBAAA,CAAAK,EAAA,CAAA9Z,cAAA,GAAA/T,EAAA,CAAAwtB,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAA/tB,EAAA,CAAAwtB,iBAAA,CAAAxtB,EAAA,CAAAguB,iBAAA;IAAA;EAAA;;;YAApBra,oBAAoB;MAAAsa,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAA3D,GAAA;QAAA,IAAA2D,EAAA;;;;;;;;;;;;;;;UClBjCpuB,EAAA,CAAAE,cAAA,aAEC;UAOKF,EAAA,CAAAY,UAAA,mBAAAytB,sDAAA;YAAA,OAAS5D,GAAA,CAAA5K,qBAAA,EAAuB;UAAA,EAAC;UAGjC7f,EAAA,CAAAC,SAAA,WAAkE;UACpED,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,aAA8C;UAMxCF,EAAA,CAAAY,UAAA,mBAAA0tB,mDAAA;YAAA,OAAS7D,GAAA,CAAA1mB,eAAA,CAAA0mB,GAAA,CAAAjqB,gBAAA,kBAAAiqB,GAAA,CAAAjqB,gBAAA,CAAAe,EAAA,CAAsC;UAAA,EAAC;UAJlDvB,EAAA,CAAAI,YAAA,EAKE;UACFJ,EAAA,CAAAwB,UAAA,IAAA+sB,mCAAA,iBAGO;UACTvuB,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAE,cAAA,aAA4B;UAExBF,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,eAAsD;UACpDF,EAAA,CAAAwB,UAAA,KAAAgtB,oCAAA,kBAkBM;UACNxuB,EAAA,CAAAwB,UAAA,KAAAitB,qCAAA,mBAMO;UACTzuB,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAE,cAAA,eAAqC;UAEjCF,EAAA,CAAAY,UAAA,mBAAA8tB,uDAAA;YAAA,OAASjE,GAAA,CAAAhM,cAAA,EAAgB;UAAA,EAAC;UAI1Bze,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAIC;UAHCF,EAAA,CAAAY,UAAA,mBAAA+tB,uDAAA;YAAA,OAASlE,GAAA,CAAA/L,cAAA,EAAgB;UAAA,EAAC;UAI1B1e,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAMC;UALCF,EAAA,CAAAY,UAAA,mBAAAguB,uDAAA;YAAA,OAASnE,GAAA,CAAA/nB,YAAA,EAAc;UAAA,EAAC;UAMxB1C,EAAA,CAAAC,SAAA,aAA6B;UAC/BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAIC;UAHCF,EAAA,CAAAY,UAAA,mBAAAiuB,uDAAA;YAAA,OAASpE,GAAA,CAAA/K,WAAA,EAAa;UAAA,EAAC;UAIvB1f,EAAA,CAAAwB,UAAA,KAAAstB,kCAAA,gBAA+C;UAC/C9uB,EAAA,CAAAwB,UAAA,KAAAutB,kCAAA,gBAA6C;UAC/C/uB,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAMC;UALCF,EAAA,CAAAY,UAAA,mBAAAouB,uDAAA;YAAA,OAASvE,GAAA,CAAA7K,cAAA,EAAgB;UAAA,EAAC;UAM1B5f,EAAA,CAAAC,SAAA,aAAiC;UACnCD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAIC;UAHCF,EAAA,CAAAY,UAAA,mBAAAquB,uDAAA;YAAA,OAASxE,GAAA,CAAA/J,cAAA,EAAgB;UAAA,EAAC;UAI1B1gB,EAAA,CAAAC,SAAA,aAA2B;UAC7BD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAwB,UAAA,KAAA0tB,oCAAA,oBAqFM;UACRlvB,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,oBAQC;UALCF,EAAA,CAAAY,UAAA,oBAAAuuB,sDAAAlqB,MAAA;YAAA,OAAUwlB,GAAA,CAAAxG,QAAA,CAAAhf,MAAA,CAAgB;UAAA,EAAC,sBAAAmqB,wDAAAnqB,MAAA;YAAA,OACfwlB,GAAA,CAAAf,UAAA,CAAAzkB,MAAA,CAAkB;UAAA,EADH,uBAAAoqB,yDAAApqB,MAAA;YAAA,OAEdwlB,GAAA,CAAAd,WAAA,CAAA1kB,MAAA,CAAmB;UAAA,EAFL,kBAAAqqB,oDAAArqB,MAAA;YAAA,OAGnBwlB,GAAA,CAAAN,MAAA,CAAAllB,MAAA,CAAc;UAAA,EAHK;UAO3BjF,EAAA,CAAAwB,UAAA,KAAA+tB,oCAAA,mBAsCM;UAINvvB,EAAA,CAAAwB,UAAA,KAAAguB,oCAAA,kBAUM;UAGNxvB,EAAA,CAAAwB,UAAA,KAAAiuB,oCAAA,kBAaM;UAGNzvB,EAAA,CAAAwB,UAAA,KAAAkuB,oCAAA,kBAmXM;UACR1vB,EAAA,CAAAI,YAAA,EAAO;UAGPJ,EAAA,CAAAwB,UAAA,KAAAmuB,oCAAA,mBA2BM;UAGN3vB,EAAA,CAAAE,cAAA,kBAEC;UAGGF,EAAA,CAAAY,UAAA,sBAAAgvB,wDAAA;YAAA,OAAYnF,GAAA,CAAAnd,WAAA,EAAa;UAAA,EAAC;UAI1BtN,EAAA,CAAAE,cAAA,eAAwB;UAGpBF,EAAA,CAAAY,UAAA,mBAAAivB,uDAAA;YAAA,OAASpF,GAAA,CAAAtK,iBAAA,EAAmB;UAAA,EAAC;UAM7BngB,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAOC;UALCF,EAAA,CAAAY,UAAA,mBAAAkvB,uDAAA;YAAA,OAASrF,GAAA,CAAAjK,oBAAA,EAAsB;UAAA,EAAC;UAMhCxgB,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAE,cAAA,eAAoB;UAQhBF,EAAA,CAAAY,UAAA,mBAAAmvB,yDAAA9qB,MAAA;YAAA,OAASwlB,GAAA,CAAA/G,aAAA,CAAAze,MAAA,CAAqB;UAAA,EAAC,qBAAA+qB,2DAAA/qB,MAAA;YAAA,OACpBwlB,GAAA,CAAA7G,cAAA,CAAA3e,MAAA,CAAsB;UAAA,EADF,mBAAAgrB,yDAAA;YAAA,OAEtBxF,GAAA,CAAA1G,YAAA,EAAc;UAAA,EAFQ,kBAAAmM,wDAAA;YAAA,OAGvBzF,GAAA,CAAAzG,WAAA,EAAa;UAAA,EAHU;UASjChkB,EAAA,CAAAG,MAAA;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAIbJ,EAAA,CAAAE,cAAA,eAAwB;UAEtBF,EAAA,CAAAwB,UAAA,KAAA2uB,uCAAA,qBAyBS;UAGTnwB,EAAA,CAAAwB,UAAA,KAAA4uB,uCAAA,qBAUS;UACXpwB,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAwB,UAAA,KAAA6uB,oCAAA,mBAqEM;UAGNrwB,EAAA,CAAAwB,UAAA,KAAA8uB,oCAAA,kBA6BM;UAGNtwB,EAAA,CAAAwB,UAAA,KAAA+uB,oCAAA,mBA4DM;UAGNvwB,EAAA,CAAAE,cAAA,qBAOE;UAHAF,EAAA,CAAAY,UAAA,oBAAA4vB,uDAAAvrB,MAAA;YAAA,OAAUwlB,GAAA,CAAA5B,cAAA,CAAA5jB,MAAA,CAAsB;UAAA,EAAC;UAJnCjF,EAAA,CAAAI,YAAA,EAOE;UAGFJ,EAAA,CAAAwB,UAAA,KAAAivB,oCAAA,kBAeM;UAGNzwB,EAAA,CAAAwB,UAAA,KAAAkvB,oCAAA,mBAqCM;UAGN1wB,EAAA,CAAAwB,UAAA,KAAAmvB,oCAAA,kBAUO;UACT3wB,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAE,cAAA,8BAQC;UAHCF,EAAA,CAAAY,UAAA,uBAAAgwB,uEAAA;YAAA,OAAanG,GAAA,CAAA9L,OAAA,EAAS;UAAA,EAAC,0BAAAkS,0EAAA5rB,MAAA;YAAA,OACPwlB,GAAA,CAAAkB,cAAA,CAAA1mB,MAAA,CAAsB;UAAA,EADf,0BAAA6rB,0EAAA;YAAA,OAEPrG,GAAA,CAAAmB,cAAA,EAAgB;UAAA,EAFT;UAGxB5rB,EAAA,CAAAI,YAAA,EAAqB;UAGtBJ,EAAA,CAAAwB,UAAA,KAAAuvB,oCAAA,mBAyEM;;;;;UAplCI/wB,EAAA,CAAAK,SAAA,GAAqE;UAArEL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAAjqB,gBAAA,kBAAAiqB,GAAA,CAAAjqB,gBAAA,CAAAyD,KAAA,yCAAAjE,EAAA,CAAAkE,aAAA,CAAqE,QAAAumB,GAAA,CAAAjqB,gBAAA,kBAAAiqB,GAAA,CAAAjqB,gBAAA,CAAA+C,QAAA;UAMpEvD,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAAjqB,gBAAA,kBAAAiqB,GAAA,CAAAjqB,gBAAA,CAAAC,QAAA,CAAgC;UAOjCT,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,OAAAmqB,GAAA,CAAAjqB,gBAAA,kBAAAiqB,GAAA,CAAAjqB,gBAAA,CAAA+C,QAAA,wBACF;UAGKvD,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAAlU,YAAA,CAAkB;UAkBdvW,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAmC,UAAA,UAAAsoB,GAAA,CAAAlU,YAAA,CAAmB;UA8B5BvW,EAAA,CAAAK,SAAA,GAAiC;UAAjCL,EAAA,CAAA0B,WAAA,iBAAA+oB,GAAA,CAAA7V,UAAA,CAAiC,mBAAA6V,GAAA,CAAA7V,UAAA;UAWT5U,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAmC,UAAA,UAAAsoB,GAAA,CAAA9lB,UAAA,CAAiB;UAClB3E,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAA9lB,UAAA,CAAgB;UAKvC3E,EAAA,CAAAK,SAAA,GAAmC;UAAnCL,EAAA,CAAA0B,WAAA,iBAAA+oB,GAAA,CAAA9nB,YAAA,CAAmC,mBAAA8nB,GAAA,CAAA9nB,YAAA;UAmBpC3C,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAA9nB,YAAA,CAAkB;UA+FrB3C,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAA0B,WAAA,cAAA+oB,GAAA,CAAAvV,UAAA,CAA8B;UAI3BlV,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAAvV,UAAA,CAAgB;UA0ChBlV,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAArW,SAAA,CAAe;UAafpU,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAmC,UAAA,UAAAsoB,GAAA,CAAArW,SAAA,IAAAqW,GAAA,CAAAvf,QAAA,CAAAL,MAAA,OAAyC;UAetC7K,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAAmC,UAAA,UAAAsoB,GAAA,CAAArW,SAAA,IAAAqW,GAAA,CAAAvf,QAAA,CAAAL,MAAA,KAAuC;UAwX5C7K,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAAxV,WAAA,CAAiB;UAiChBjV,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAmC,UAAA,cAAAsoB,GAAA,CAAA/T,WAAA,CAAyB;UAUrB1W,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAA0B,WAAA,iBAAA+oB,GAAA,CAAAlW,eAAA,CAAsC,mBAAAkW,GAAA,CAAAlW,eAAA;UAUtCvU,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAA0B,WAAA,iBAAA+oB,GAAA,CAAAjW,kBAAA,CAAyC,mBAAAiW,GAAA,CAAAjW,kBAAA;UAezCxU,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAA0B,WAAA,eAAA+oB,GAAA,CAAA3T,eAAA,GAAsC,uBAAA2T,GAAA,CAAA3T,eAAA;UAkBrC9W,EAAA,CAAAK,SAAA,GAAgD;UAAhDL,EAAA,CAAAmC,UAAA,YAAA6uB,QAAA,GAAAvG,GAAA,CAAA/T,WAAA,CAAAO,GAAA,8BAAA+Z,QAAA,CAAA1T,KAAA,kBAAA0T,QAAA,CAAA1T,KAAA,CAAAC,IAAA,IAAgD;UA4BhDvd,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAAmC,UAAA,UAAA8uB,QAAA,GAAAxG,GAAA,CAAA/T,WAAA,CAAAO,GAAA,8BAAAga,QAAA,CAAA3T,KAAA,kBAAA2T,QAAA,CAAA3T,KAAA,CAAAC,IAAA,GAA+C;UAgBrDvd,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAAxd,gBAAA,CAAsB;UAwEtBjN,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAAlW,eAAA,CAAqB;UAgCrBvU,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAAjW,kBAAA,CAAwB;UAmEzBxU,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAAmC,UAAA,WAAAsoB,GAAA,CAAAhB,kBAAA,GAA+B;UAM9BzpB,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAA3V,kBAAA,CAAwB;UAkBxB9U,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAA5V,sBAAA,CAA4B;UAwC5B7U,EAAA,CAAAK,SAAA,GAOL;UAPKL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAAlW,eAAA,IAAAkW,GAAA,CAAAjW,kBAAA,IAAAiW,GAAA,CAAA9nB,YAAA,IAAA8nB,GAAA,CAAA5V,sBAAA,IAAA4V,GAAA,CAAA3V,kBAAA,CAOL;UAOE9U,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAmC,UAAA,cAAAsoB,GAAA,CAAAhV,QAAA,CAAsB,eAAAgV,GAAA,CAAA5U,UAAA,cAAA4U,GAAA,CAAA/U,QAAA,sBAAA+U,GAAA,CAAAjqB,gBAAA;UAWrBR,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAmC,UAAA,SAAAsoB,GAAA,CAAAzV,eAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}