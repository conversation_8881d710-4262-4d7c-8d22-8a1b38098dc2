{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class ThemeService {\n  constructor() {\n    this.darkMode = new BehaviorSubject(false);\n    this.darkMode$ = this.darkMode.asObservable();\n    this.currentTheme = new BehaviorSubject({\n      name: 'Bleu Gris',\n      color: 'blue-gray',\n      isDark: false\n    });\n    this.currentTheme$ = this.currentTheme.asObservable();\n    this.availableThemes = [{\n      name: 'Bleu Gris Clair',\n      color: 'blue-gray',\n      isDark: false\n    }, {\n      name: 'Bleu Gris Sombre',\n      color: 'blue-gray',\n      isDark: true\n    }, {\n      name: '<PERSON>',\n      color: 'pink',\n      isDark: false\n    }, {\n      name: 'Rose Sombre',\n      color: 'pink',\n      isDark: true\n    }, {\n      name: '<PERSON><PERSON>',\n      color: 'cyan',\n      isDark: false\n    }, {\n      name: '<PERSON><PERSON>',\n      color: 'cyan',\n      isDark: true\n    }, {\n      name: '<PERSON>',\n      color: 'purple',\n      isDark: false\n    }, {\n      name: '<PERSON> Sombre',\n      color: 'purple',\n      isDark: true\n    }];\n    // Check if user has a theme preference in localStorage\n    const savedTheme = localStorage.getItem('currentTheme');\n    const savedDarkMode = localStorage.getItem('darkMode');\n    if (savedTheme) {\n      const theme = JSON.parse(savedTheme);\n      this.currentTheme.next(theme);\n      this.darkMode.next(theme.isDark);\n      this.applyTheme(theme);\n    } else if (savedDarkMode) {\n      const isDark = savedDarkMode === 'true';\n      const theme = {\n        name: isDark ? 'Bleu Gris Sombre' : 'Bleu Gris Clair',\n        color: 'blue-gray',\n        isDark\n      };\n      this.darkMode.next(isDark);\n      this.currentTheme.next(theme);\n      this.applyTheme(theme);\n    } else {\n      // Check if user prefers dark mode at OS level\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      const theme = {\n        name: prefersDark ? 'Bleu Gris Sombre' : 'Bleu Gris Clair',\n        color: 'blue-gray',\n        isDark: prefersDark\n      };\n      this.darkMode.next(prefersDark);\n      this.currentTheme.next(theme);\n      this.applyTheme(theme);\n    }\n  }\n  toggleDarkMode() {\n    const newValue = !this.darkMode.value;\n    this.darkMode.next(newValue);\n    localStorage.setItem('darkMode', String(newValue));\n    this.applyTheme(newValue);\n  }\n  applyTheme(isDark) {\n    if (isDark) {\n      document.documentElement.classList.add('dark');\n      console.log('Dark mode enabled');\n    } else {\n      document.documentElement.classList.remove('dark');\n      console.log('Dark mode disabled');\n    }\n  }\n  static {\n    this.ɵfac = function ThemeService_Factory(t) {\n      return new (t || ThemeService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ThemeService,\n      factory: ThemeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "ThemeService", "constructor", "darkMode", "darkMode$", "asObservable", "currentTheme", "name", "color", "isDark", "currentTheme$", "availableThemes", "savedTheme", "localStorage", "getItem", "savedDarkMode", "theme", "JSON", "parse", "next", "applyTheme", "prefersDark", "window", "matchMedia", "matches", "toggleDarkMode", "newValue", "value", "setItem", "String", "document", "documentElement", "classList", "add", "console", "log", "remove", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\theme.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\n\nexport type ThemeColor = 'blue-gray' | 'pink' | 'cyan' | 'purple';\n\nexport interface Theme {\n  name: string;\n  color: ThemeColor;\n  isDark: boolean;\n}\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ThemeService {\n  private darkMode = new BehaviorSubject<boolean>(false);\n  darkMode$ = this.darkMode.asObservable();\n\n  private currentTheme = new BehaviorSubject<Theme>({\n    name: 'Bleu Gris',\n    color: 'blue-gray',\n    isDark: false,\n  });\n  currentTheme$ = this.currentTheme.asObservable();\n\n  availableThemes: Theme[] = [\n    { name: 'Bleu Gris Clair', color: 'blue-gray', isDark: false },\n    { name: 'Bleu Gris Sombre', color: 'blue-gray', isDark: true },\n    { name: '<PERSON>', color: 'pink', isDark: false },\n    { name: 'Rose Sombre', color: 'pink', isDark: true },\n    { name: '<PERSON><PERSON>', color: 'cyan', isDark: false },\n    { name: '<PERSON><PERSON>', color: 'cyan', isDark: true },\n    { name: '<PERSON>', color: 'purple', isDark: false },\n    { name: 'Violet Sombre', color: 'purple', isDark: true },\n  ];\n\n  constructor() {\n    // Check if user has a theme preference in localStorage\n    const savedTheme = localStorage.getItem('currentTheme');\n    const savedDarkMode = localStorage.getItem('darkMode');\n\n    if (savedTheme) {\n      const theme = JSON.parse(savedTheme);\n      this.currentTheme.next(theme);\n      this.darkMode.next(theme.isDark);\n      this.applyTheme(theme);\n    } else if (savedDarkMode) {\n      const isDark = savedDarkMode === 'true';\n      const theme = {\n        name: isDark ? 'Bleu Gris Sombre' : 'Bleu Gris Clair',\n        color: 'blue-gray' as ThemeColor,\n        isDark,\n      };\n      this.darkMode.next(isDark);\n      this.currentTheme.next(theme);\n      this.applyTheme(theme);\n    } else {\n      // Check if user prefers dark mode at OS level\n      const prefersDark = window.matchMedia(\n        '(prefers-color-scheme: dark)'\n      ).matches;\n      const theme = {\n        name: prefersDark ? 'Bleu Gris Sombre' : 'Bleu Gris Clair',\n        color: 'blue-gray' as ThemeColor,\n        isDark: prefersDark,\n      };\n      this.darkMode.next(prefersDark);\n      this.currentTheme.next(theme);\n      this.applyTheme(theme);\n    }\n  }\n\n  toggleDarkMode(): void {\n    const newValue = !this.darkMode.value;\n    this.darkMode.next(newValue);\n    localStorage.setItem('darkMode', String(newValue));\n    this.applyTheme(newValue);\n  }\n\n  private applyTheme(isDark: boolean): void {\n    if (isDark) {\n      document.documentElement.classList.add('dark');\n      console.log('Dark mode enabled');\n    } else {\n      document.documentElement.classList.remove('dark');\n      console.log('Dark mode disabled');\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAatC,OAAM,MAAOC,YAAY;EAsBvBC,YAAA;IArBQ,KAAAC,QAAQ,GAAG,IAAIH,eAAe,CAAU,KAAK,CAAC;IACtD,KAAAI,SAAS,GAAG,IAAI,CAACD,QAAQ,CAACE,YAAY,EAAE;IAEhC,KAAAC,YAAY,GAAG,IAAIN,eAAe,CAAQ;MAChDO,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE,WAAW;MAClBC,MAAM,EAAE;KACT,CAAC;IACF,KAAAC,aAAa,GAAG,IAAI,CAACJ,YAAY,CAACD,YAAY,EAAE;IAEhD,KAAAM,eAAe,GAAY,CACzB;MAAEJ,IAAI,EAAE,iBAAiB;MAAEC,KAAK,EAAE,WAAW;MAAEC,MAAM,EAAE;IAAK,CAAE,EAC9D;MAAEF,IAAI,EAAE,kBAAkB;MAAEC,KAAK,EAAE,WAAW;MAAEC,MAAM,EAAE;IAAI,CAAE,EAC9D;MAAEF,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAK,CAAE,EACpD;MAAEF,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAI,CAAE,EACpD;MAAEF,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAK,CAAE,EACpD;MAAEF,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAI,CAAE,EACpD;MAAEF,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAK,CAAE,EACxD;MAAEF,IAAI,EAAE,eAAe;MAAEC,KAAK,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAI,CAAE,CACzD;IAGC;IACA,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACvD,MAAMC,aAAa,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAEtD,IAAIF,UAAU,EAAE;MACd,MAAMI,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACN,UAAU,CAAC;MACpC,IAAI,CAACN,YAAY,CAACa,IAAI,CAACH,KAAK,CAAC;MAC7B,IAAI,CAACb,QAAQ,CAACgB,IAAI,CAACH,KAAK,CAACP,MAAM,CAAC;MAChC,IAAI,CAACW,UAAU,CAACJ,KAAK,CAAC;KACvB,MAAM,IAAID,aAAa,EAAE;MACxB,MAAMN,MAAM,GAAGM,aAAa,KAAK,MAAM;MACvC,MAAMC,KAAK,GAAG;QACZT,IAAI,EAAEE,MAAM,GAAG,kBAAkB,GAAG,iBAAiB;QACrDD,KAAK,EAAE,WAAyB;QAChCC;OACD;MACD,IAAI,CAACN,QAAQ,CAACgB,IAAI,CAACV,MAAM,CAAC;MAC1B,IAAI,CAACH,YAAY,CAACa,IAAI,CAACH,KAAK,CAAC;MAC7B,IAAI,CAACI,UAAU,CAACJ,KAAK,CAAC;KACvB,MAAM;MACL;MACA,MAAMK,WAAW,GAAGC,MAAM,CAACC,UAAU,CACnC,8BAA8B,CAC/B,CAACC,OAAO;MACT,MAAMR,KAAK,GAAG;QACZT,IAAI,EAAEc,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;QAC1Db,KAAK,EAAE,WAAyB;QAChCC,MAAM,EAAEY;OACT;MACD,IAAI,CAAClB,QAAQ,CAACgB,IAAI,CAACE,WAAW,CAAC;MAC/B,IAAI,CAACf,YAAY,CAACa,IAAI,CAACH,KAAK,CAAC;MAC7B,IAAI,CAACI,UAAU,CAACJ,KAAK,CAAC;;EAE1B;EAEAS,cAAcA,CAAA;IACZ,MAAMC,QAAQ,GAAG,CAAC,IAAI,CAACvB,QAAQ,CAACwB,KAAK;IACrC,IAAI,CAACxB,QAAQ,CAACgB,IAAI,CAACO,QAAQ,CAAC;IAC5Bb,YAAY,CAACe,OAAO,CAAC,UAAU,EAAEC,MAAM,CAACH,QAAQ,CAAC,CAAC;IAClD,IAAI,CAACN,UAAU,CAACM,QAAQ,CAAC;EAC3B;EAEQN,UAAUA,CAACX,MAAe;IAChC,IAAIA,MAAM,EAAE;MACVqB,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;MAC9CC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;KACjC,MAAM;MACLL,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACI,MAAM,CAAC,MAAM,CAAC;MACjDF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;EAErC;;;uBAzEWlC,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAAoC,OAAA,EAAZpC,YAAY,CAAAqC,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}